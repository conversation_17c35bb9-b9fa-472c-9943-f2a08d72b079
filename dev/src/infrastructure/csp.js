const getCspConfig = () => {
    const environment = process.env.APP_ENV;
    const isDevelopment = environment === 'development';

    const policies = [
        {
            directive: 'default-src',
            sources: [
                "'self'",
                "https://*.cloudflare.com",
                "https://fonts.gstatic.com"
            ]
        },
        {
            directive: 'script-src',
            sources: [
                "'self'",
                "https://*.cloudflare.com",
                "http://chat.sociomile.com",
                "https://chat.sociomile.com",
                "https://www.googletagmanager.com",
                "https://*.midtrans.com",
                "https://www.google.com",
                "https://www.gstatic.com",
                "https://apis.google.com",
                "https://cdn.jsdelivr.net",
                "'unsafe-eval'",
                process.env.MAJOO_CHAT_BASE_URL
            ]
        },
        {
            directive: 'worker-src',
            sources: [
                "'self'",
                "data:",
                "blob:"
            ]
        },
        {
            directive: 'style-src',
            sources: [
                "'self'",
                "http://chat.sociomile.com",
                "https://chat.sociomile.com",
                "https://smcdn.s45.in",
                "https://*.cloudflare.com",
                "https://fonts.googleapis.com",
                "http://fonts.googleapis.com",
                "'unsafe-inline'"
            ]
        },
        {
            directive: 'img-src',
            sources: [
                "'self'",
                "data:",
                "blob:",
                "https://majooshop.id",
                "https://smcdn.s45.in",
                "https://*.googleapis.com",
                "https://www.googletagmanager.com",
                "https://i.ytimg.com",
                "https://*.mangkujagat.com",
                "https://*.majoo.id",
                "https://majoo.id",
                "https://mayang.klopos.com",
                "https://*.googleusercontent.com",
                "https://*.tokopedia.net",
                "https://*.digitaloceanspaces.com",
                "https://*.openstreetmap.org",
                "https://*.youtube.com"
            ]
        },
        {
            directive: 'connect-src',
            sources: [
                "'self'",
                "data:",
                "blob:",
                "wss:",
                "https://*.majoo.id",
                "https://majoo.id",
                "https://*.mangkujagat.com",
                "https://www.googletagmanager.com",
                "https://*.googleapis.com",
                "https://www.google-analytics.com",
                "https://nominatim.openstreetmap.org",
                process.env.IP_WEB_SOCKET,
                "https://chat.sociomile.com",
                "https://superchat.sociomile.net",
                "https://socket-chat.sociomile.com",
                ...(isDevelopment ? [
                    "ws://localhost:*",
                    "wss://localhost:*",
                    "http://localhost:*"
                ] : []),
                ...(environment === 'testing' ? [
                    "https://majoo-infra.cloudflareaccess.com"
                ] : [])
            ]
        },
        {
            directive: 'frame-src',
            sources: [
                "https://*.cloudflare.com",
                "https://www.google.com",
                "https://www.youtube.com",
                "https://*.midtrans.com",
                "https://*.cardinalcommerce.com",
                "https://*.klikbca.com",
                process.env.MAJOO_CHAT_BASE_URL
            ]
        },
        {
            directive: 'object-src',
            sources: ["'none'"]
        },
        {
            directive: 'base-uri',
            sources: ["'self'"]
        },
        {
            directive: 'form-action',
            sources: ["'self'"]
        },
        {
            directive: 'frame-ancestors',
            sources: ["'self'", process.env.MAJOO_CHAT_BASE_URL, "https://docs.google.com"]
        }
    ];

    const cspStrings = policies.map(({ directive, sources }) => 
        `${directive} ${sources.join(' ')}`
    );

    return {
        directives: cspStrings.join('; '),
        reportOnly: isDevelopment
    };
};

module.exports = { getCspConfig };