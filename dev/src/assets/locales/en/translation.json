{"general": {"feature": "feature", "feature_titleCase": "Feature"}, "save": "Save", "legend": {"sales": "Sales", "transaction": "Transaction", "grossProfit": "Gross Profit", "product": "Product", "profit": "Profit", "employee": "Employee"}, "period": {"hour": "Hour", "hours": "Hours", "day": "Day", "week": "Week", "month": "Month", "months": "Months", "monthly": "Monthly", "year": "Year", "jam": "Hour", "hari": "Day", "minggu": "Week", "bulan": "Month", "tahun": "Year", "menit": "Minutes", "second": "Second", "seconds": "Seconds", "daily": "Daily", "weekly": "Weekly", "monthly2": "Monthly", "yearly": "Yearly"}, "label": {"weight": "Weight", "attention": "Attention", "group": "Group", "saveAsDraft": "Save as Draft", "comparison": "Comparison", "export": "Export", "exportReport": "Export Report", "chooseCsvDelimiter": "<PERSON><PERSON> Delimiter", "downloadReport": "Download Report", "downloadProduct": "Download Product List", "exportTemplate": "Export Template", "importData": "Import Data", "exportData": "Export Data", "cancel": "Cancel", "cancelled": "Cancelled", "continue": "Yes, Continue", "continue2": "Continue", "complete": "Complete", "apply": "Apply", "confirm": "Ok, I Understand", "waiting": "Waiting...", "deleting": "Deleting", "processing": "Processing", "importConfirmation": "Import Confirmation", "deleteConfirmation": "Delete Confirmation", "close": "Close", "showDetail": "Show Detail", "nextOption": "Next Option", "advanceOptions": "Advance Options", "delete": "Delete", "add": "Add", "added": "added", "saved": "saved", "edited": "edited", "changed": "changed", "changes": "changes", "deleted": "deleted", "exporting": "Exporting", "addProduct": "Add Product", "addCategory": "Add Category", "product": "Product", "setProduct": "Set Product", "products": "Products", "process": "Process", "noData": "No data available", "edit": "Edit", "editProduct": "Edit Product", "add2": "Add", "detail": "Details", "phoneNo": "Phone No", "address": "Address", "country": "Country", "province": "Province", "city": "City", "accountNumber": "Account Number", "description": "Description", "settings": "Settings", "period": "Period", "category": "Category", "stock": "Stock", "price": "Price", "save": "Save", "productName": "Product Name", "categoryName": "Category Name", "chooseCategory": "Choose Category", "active": "Active", "inactive": "Not Active", "deleteProduct": "Delete Product", "importProductData": "Impor Product Data", "choose": "<PERSON><PERSON>", "basicPrice": "Average Price", "averagePrice": "Average Price", "purchasePrice": "Purchase Price", "sellingPrice": "Selling <PERSON>", "normalPrice": "Normal Price", "materialName": "Material Name", "unitName": "Unit Name", "back": "Back", "department": "Department", "next": "Next", "nextThen": "Next", "done": "Done", "yes": "Yes", "no": "No", "information": "Information", "max": "Max", "learnMore": "Learn More", "hide": "<PERSON>de", "show": "Show", "unhide": "Show", "showing": "showing", "loading": "Loading...", "submit": "Submit", "request": "Request", "claim": "<PERSON><PERSON><PERSON>", "allOutlets": "All Outlets", "imageUpload": "Upload Image", "imageChange": "Change Image", "buyNow": "Buy Now", "dateRange": "Date Range", "selectDate": "Select Date", "showAll": "Show All", "showLess": "Show Less", "outletCounter": "Outlets selected", "showBy": "Show by", "inRupiah": "in Rupiah", "trxType": "Transaction Type", "person": "Person", "persons": "Persons", "reportByDate": "Report by Date", "reportByTime": "Report by Time", "statusPayment": "Status Payment", "orderType": "Order Type", "priceType": "Price Type", "adding": "adding", "approve": "Agree", "integrationProcess": "Process Integration", "callUs": "Call Us", "upgradeNow": "Upgrade Now", "sync": "Synchronization", "syncOrder": "Order Synchronization", "sku": "SKU", "unit": "Unit", "processIntegration": "Process Integration", "productCategory": "Product Category", "otherMenu": "More", "chatPanelButton": "Chat 24 Hours", "draft": "Draft", "emptyDataDesc": "<paragraph>There is no data to display<br /> on this page yet</paragraph>", "mainPhoto": "Main Photo", "showcase": "Showcase", "tryAgain": "Try Again", "selected": "Selected", "noDataYet": "No data", "resubmit": "Resubmit", "sort": "Sort by", "share": "Share", "chooseTime": "Select Time", "startTime": "Time From", "endTime": "Time Until", "logActivity": "Log Activity", "lastUpdated": "Last updated {{date}}", "lastUpdatedBy": "Last updated {{date}} by {{by}}", "lastUpdatedBy_empty": "Last updated -", "import": "Import", "itemSelected": "item selected", "sequence": "Sequence", "package": "Package", "withoutParentGroup": "Without Group Parent", "noCategory": "Tidak Ada Kategori", "print": "Print", "copy": "Copy", "copied": "Copied!", "or": "Or", "use": "Use", "wait": "Loading...", "ok": "Ok", "skip": "skip", "send": "Send", "selectedData": "selected data", "set": "Set", "mainOutlet": "Main Outlet", "lastPurchasePrice": "Last Purchase Price", "maximum": "Maximum", "setView": "Set View", "filterBy": "Filter <PERSON>", "compareWith": "Compare with", "perDate": "Per Date", "start": "Start", "end": "End", "confirmation": "Confirmation", "importAddData": "Import Add Data", "deactivate": "Deactivate", "activate": "Activate", "duplicate": "Duplicate", "createdBy": "Created By", "createdDate": "Created Date", "selectAll": "Select All", "maxRangeDate": "Maximum range is {{maxRange}} days", "autoGenerateNote": "If empty will be filled automatically by the system"}, "placeholder": {"search": "Search ...", "searchWithoutDots": "Search", "select": "Select", "example": "Example: ", "category": "Choose Category", "allCategory": "All Categories", "merchant": "<PERSON><PERSON>", "findCategory": "Find Category", "status": "All Status", "allDepartment": "All Departments", "input": "Input", "noImage": "No image", "type": "Write", "inputHere": "Input here", "selectDate": "Select date", "selectTime": "Select time"}, "status": {"active": "Active", "inactive": "Inactive", "category": "Choose Category", "allCategory": "All Categories", "all": "All", "showInMenu": "Shown in Menu", "hideInMenu": "Hidden in Menu", "allowed": "Allowed", "notAllowed": "Not Allowed", "high": "High", "medium": "Medium", "low": "Low", "integrated": "Integrated", "notIntegrated": "Not Integrated"}, "select": {"categoryDefaultSelectOption": "All Categories", "allOrderType": "All Order Type", "typeSelectTitle": "Choose Type", "select": "Select", "typeSelectOption": {"default": "All Types", "product": "Product", "material": "Material"}, "showOnMenuOption": {"semua": "All", "tampilMenu": "Show on menu", "tidakTampil": "Not Show on Menu"}, "timeOption": {"today": "Today", "yesterday": "Yesterday", "3day": "Last 3 day", "7day": "Last 7 day"}, "product": {"option": {"product": "Product", "package": "Package"}}}, "warning": {"capitalMoreThanPurchasePrice": "There is some data that has a average price greater than selling price.", "blockDownloadLaporan": "Upgrade your current service to <strong>Starter</strong>, <strong>Advance</strong> or <strong>Prime</strong> to get report export access.", "apkVersion": "The {{ context }} can only be used on application version {{version}}."}, "suggest": {"continueAddMaterialOrCancel": "Please select continue to add material, or press Cancel to cancel."}, "error": {"saveFailed": "Fail saving data", "changeFailed": "Fail change data", "fileNotFound": "File not found on server", "exportFail": "Fail exporting file", "importFail": "Fail importing data", "uploadFail": "Fail to Upload!", "unchoosedFile": "Please choose file first", "unmatchLengthRequirementsItem": "Item not meet requirement {{notMatchReq}}", "unregisteredValueQty": "Quantity {{entity}} {{notMatchReq}}", "fileUploadNotXLS": "Files are not supported, make sure the file is of type XLS / XLSX.", "unchoosedOutlet": "Not Choose Outlet Yet", "unchoosedFileWarning": "Not Choose File to upload yet", "duplicateNames": "Duplication of names", "duplicateSKUs": "Duplication of SKU", "oneOfDimensionProductNotMatched": "There are {{ skuLength }} product units whose size does not comply with provisions.", "someSKUOrNameAreSame": "There are several rows with same SKU code / name (duplication).<br />Please check your data again, make sure there are no blank spaces / characters behind the SKU code or product name.", "someSKUAreNotRegistered": "There are several SKU codes that were not listed before, please check your file again.", "someSKUOrNameAreRegistered": "There are several rows with registered SKU codes / names.", "loginSessionIsOver": "Login session has ended", "failedGetCategoryList": "Get Category List Failed", "failedGetData": "Get Data Failed", "failedGetDetailData": "Get Data Detail Failed", "failedFetch": "<PERSON><PERSON> failed", "failedGetDataCustom": "Get data {{ data }} failed", "failedEditData": "Failed editing data!", "failedGetTotalProduct": "Failed get product total data", "failedGetServiceHour": "Failed get Service Hour", "failedUploadImage": "Failed to Upload Image!", "smallImageResolution": "The Image resolution is too small (min: {{minWidth}}x{{minHeight}} px)", "largeImageResolution": "The image resolution is too large (max: {{minWidth}}x{{minHeight}} px)", "largeImageFile": "The image file is too large (max: 1 MB)", "smallImageFile": "The image file is too small (min: 10 Kb)", "unsuportedImageFile": "Unsupported file format !", "supportedImageFile": "Make sure to use a supported file format (JPG, JPEG, PNG)", "formatUnsupported": "File format not supported"}, "success": {"export": "Report <strong>{{ data }}</strong> succesfully exported", "exportProcess": "Export of <strong>{{ data }}</strong> is in progress", "failedGetTotalProduct": "Failed get product total data", "failedGetServiceHour": "Failed get Service Hour"}, "toast": {"success": "Succeed!", "error": "Failed!", "error2": "Error!", "disabled": "Disabled!", "somethingWrong": "Something Wrong Happen", "unactivated": "Unactivated!", "failGotData": "Failed to get data", "successSaveSetting": "Setting<PERSON> saved successfully", "successSaveData": "Success saving data!", "successEditData": "Success editing data!", "successDeleteData": "Success delete data!", "failDeleteData": "Failed delete data!", "successOrderSynchronization": "Order Synchronization is running, please wait a few moments until all orders are successfully synced", "successAddProduct": "Success adding product", "successEditProduct": "Success editing product", "successDeleteProduct": "Success deleting product", "successAutoStock": "Auto stock has been activated", "errorAddProduct": "Error adding product", "errorEditProduct": "Error editing product", "errorDeleteProduct": "Error deleting product", "errorTemplate": "Template does not match, use the Template provided", "online": {"title": "Online!", "description": "You are already connected to the internet network"}, "offline": {"title": "No Connection", "description": "Please check your internet connection and try again"}, "successAddFavorite": "<strong>{{menu}}</strong> menu successfully added to favorite menus", "favoriteLimit": "You have reached the limit for the number of Favorite Menus (max. 10)<br/>Remove other favorite menus and try again", "successRemoveFavorite": "<strong>{{menu}}</strong> menu successfully removed from the favorite menus", "cancelled": "Cancelled", "redirectToPrevPage": "You are redirected to the previous page", "failedToGetData": "Failed to retrieve data, please try again in a few seconds or contact our support team", "errorMaxDate": "An Error Occured!", "errorMaxDateRange": "Maximum time period that can be select: 3 months", "errorMaxDateRangeYear": "Maximum time period that can be select: 1 years", "errorMax2MonthsDateRange": "Maximum time period that can be select: 2 months", "process": "Process!"}, "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thusday": "Thusday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "online": {"title": "Online!", "description": "You are already connected to the internet network"}, "offline": {"title": "No Connection", "description": "Please check your internet connection and try again"}, "successAddProduct": "Success adding product", "successEditProduct": "Success editing product", "successDeleteProduct": "Success deleting product", "errorAddProduct": "Error adding product", "errorEditProduct": "Error editing product", "errorDeleteProduct": "Error deleting product", "successEditData": "Success editing data!", "successDeleteData": "Success delete data!", "successOrderSynchronization": "Order Synchronization is running, please wait a few moments until all orders are successfully synced"}, "favorite": {"title": "Favorite Menus", "favoriteEmpty": "Add frequently accessed menus to the Favorite Menu list by clicking the Star icon on each page. Select up to 10 menus", "desktopOnly": "Can only be opened in desktop view"}, "subscription": {"unauthorizedBlocker": {"description": "Unfortunately, this feature can only be accessed by", "upgradeButton": "Upgrade Subscription", "and": "and"}, "expiredSupport": {"description": "The {{accountType}} subscription package at {{outlets}} branch has ended.", "descriptionPopup": "The <strong>{{accountType}}</strong> subscription package at <strong>{{outletName}}</strong> branch has ended.", "updateButton": "<PERSON>w", "desciptionDialog": "The active period of the <b>{{accountType}}</b> package at <b>{{outletName}}</b> has expired since <b>{{expiredDate}}</b>. Please update your subscription package.", "thisOutlet": "this outlet", "activePeriod": "The active period of your {{accountType}} account", "expireInDays": "will expire in {{day}} days", "expireToday": "ends today", "toastTitle": "Subscription Has Expired!", "toastDesc": "Failed to retrieve outlet data. To continue, reactivate your main outlet's subscription"}, "additionalSupport": {"description": "The subscription of additional <strong>{{supportName}}</strong> support service package has ended.", "noSupport": "You don't have additional <strong>{{supportName}}</strong> support service", "buyBackButton": "Buy Again", "buySupportButton": "Buy {{supportName}} Service Now"}, "trial": {"description": "The active period of {{accountType}} account {{dayText}} buy a subscription immediately before the trial period ends to get a discount of up to 35%", "expireInDays": "is {{day}} days left", "expireToday": "ends today"}}, "chooseOutlet": {"title": "Change Outlet?", "confirm": "Yes, Change the outlet", "cancel": "Cancel", "description": "Continuing to change the outlet will reset the filled form", "placeholder": "Select Outlet"}, "salesIncreasement": {"label": "By using majoo, your sales this month increased", "tooltip": "Value taken from total sales with this month's promo"}, "export": {"description": "<strong>The {{reportName}}</strong> for the period <strong>{{startDate}}</strong> to <strong>{{endDate}}</strong> will be exported <strong>{{type}}</strong> and stored on the device used. Continue?", "singleDatedescription": "<strong>The {{reportName}}</strong> for the period <strong>{{date}}</strong> will be exported <strong>{{type}}</strong> and stored on the device used. Continue?", "descriptionConfirmExport": "Please wait, the system is currently processing the <strong>{{menu}}</strong>. You will receive notifications and email alerts when the data is ready to download."}, "csvDelimiter": {"semicolon": "Semicolon ( ; )", "tab": "Tab", "pipe": "Piepe ( | )"}, "evidenceUploader": {"sectionTitle": "Upload File", "label": "Select File", "placeholder": "Select or drop file here", "rules": {"fileTypes": "Files can be Excel, Word, PDF, JPG, PNG, or ZIP", "maxFiles": "Maximum 5 Files", "maxSize": "Maximum 2 MB per file"}, "error": {"fileSizeExceeded": "Unable to upload the file. Please ensure that the file size is not more than 2 MB", "unsupportedFormat": "Unsupported file format. Please upload a file with the appropriate type.", "maxFilesExceeded": "Unable to add files. The number of uploaded files must not exceed 5"}, "deleteConfirm": {"title": "Delete File", "description": "Deleting file <strong>{{name}}</strong> permanently removes it from the list and cannot be undone. Continue?"}, "toastSuccess": "File <strong>{{name}}</strong> successfully uploaded", "toastDelete": "File <strong>{{name}}</strong> successfully deleted", "attachment": "Attachment"}}