{"title": "WhatsApp Number Settings", "button": {"checkAuth": "Authentication Check", "addWhatsApp": "Add Number", "sendMessage": "Send Testing Message"}, "filter": {"allFunctions": "All Functions", "reservation": "Reservation", "loyalti": "Loyalty", "report": "Report", "merchant": "Merchant Notification", "customer": "Customer Notification"}, "card": {"loyalti": {"title": "Loyalty Number Quota", "tooltip": "Number quota obtained by\nsubscribing to the Loyalty\nadd on"}, "reservation": {"title": "Reservation Number Quota", "tooltip": "Number quota obtained by\nsubscribing to the Prime\npackage"}, "report": {"title": "Report Number Quota"}, "merchant": {"title": "Merchant Notification Number Quota"}, "customer": {"title": "Customer Notification Number Quota"}, "desc": "{{value}} out of {{limit}} numbers used"}, "column": {"name": "NAME", "whatsAppNumber": "WHATSAPP NUMBER", "auth": "AUTHENTICATION", "func": "USED FOR", "desc": "DESCRIPTION", "active": "Active", "inactive": "Inactive", "login": "Connected", "logout": "Disconnected", "notConnectedYet": "Not Connected Yet"}, "action": {"edit": "Edit", "deactivate": "Deactivate", "activate": "Activate", "reauth": "Authenticate", "delete": "Delete"}, "dialog": {"title": {"add": "Add Whatsapp Number", "edit": "Edit Whatsapp Number Information", "auth": "WhatsApp Authentication Number"}}, "form": {"register": {"title": "WhatsApp Number Info", "desc": "Number will be used to send broadcast message"}, "auth": {"title": "Whatsapp Authentication", "title2": "Authenticate with QR Code", "banner": "Authenticate first before continue", "instruction": {"1": "Open WhatsApp on your phone", "2": "Select  <strong>Menu</strong> <icon1></icon1> or  <strong>Settings</strong> <icon2></icon2> and select <strong>Connected Devices</strong>", "3": "Click on  <strong>Connected Devices</strong>", "4": "Point your phone at this screen to scan the QR Code"}, "needHelp": "Need Help?", "seeTutorial": "View Video Tutorial", "verificationSuccess": "Phone number successfully verified"}, "complete": {"title": "Complete Settings", "banner": "Do a delivery test to ensure the broadcast can be sent."}}, "field": {"usedFor": {"label": "Use of Number", "desc": "Registered WhatsApp number function", "error": "Use of Number is required"}, "outlet": {"label": "Outlet List", "desc": "Select an outlet that can use this number", "error": "Outlet is required"}, "label": {"label": "Number Label", "error": "Number Label is required", "errorUsed": "Number labels has been used on Outlet {{outlet}}"}, "phone": {"label": "Broadcast Number", "label2": "Country Code", "label3": "Phone Number", "desc": "To avoid numbers being blocked, use WhatsApp number that has been active more than 3 months", "error": "Phone number is required", "errorMin": "Required to fill in 9 numbers", "errorUsed": "Phone number has been used on Outlet {{outlet}}", "errorUsedOtherMerchant": "Phone number has been used on outlet in other merchant", "helper": "Phone number cannot be changed after saved."}, "description": {"label": "Description"}, "status": {"label": "Status", "desc": "Activate to use number", "activeNotificationDesc": "This number is being used as a notification sender number", "limitBanner": "{{limit}} active numbers in selected function in the outlet. These numbers will be saved with status \"Inactive\"", "limitUpdateBanner": "Changes cannot be made because the active number quota for this function in the outlet has reached its limit.", "notYet": "Not yet tested", "send": "Not yet tested", "canSave": "Number can be saved", "failSend": "Send Failed", "lastTest": "Last testing today on {{time}}"}, "sender": {"label": "Sender Phone No", "desc": "Phone number has beenn verified"}, "recipient": {"label": "Recipient Phone No", "desc": "Target cellphone number for testing", "error": "Phone number is required"}, "textExample": {"label": "Testing Message", "error": "Testing Message is required"}}, "alert": {"buyAddon": {"title": "Buy Add-On", "desc": "You will be redirected to the {{optionName}} Notification purchase page. Continue?"}, "delete": {"title": "Delete Number", "desc": "Deleting <strong>{{name}}</strong> number will permanently remove number from the list and will stop the integration process that was carried out previously. Continue?", "desc2": "Deleting <strong>{{name}}</strong> number will permanently remove number from the list and will stop the integration process that was carried out previously. Continue?"}, "closeEditDialog": {"title": "Cancel Edit", "desc": "Canceling <strong>{{name}}</strong> will delete all data that has been inputted and cannot be undone. Continue?"}, "confirmSave": {"title": "Save Changes", "desc": "The WhatsApp number information <strong>{{phone}}</strong> will be saved and appear in the WhatsApp number list (including POS) according to the settings that have been made. Continue?"}, "deactivate": {"title": "Deactivate Number", "desc": "Deactivated numbers will affect previously integrated settings and become inactive. Continue?"}, "activate": {"title": "Activate Number", "desc": "Active numbers can be used as WhatsApp message senders. Continue?"}}, "toast": {"deleteSuccess": "<strong>{{number}}</strong>number successfully deleted", "deleteFailed": "Failed to delete <strong>{{number}}</strong> number", "deactivateSuccess": "<strong>{{number}}</strong> number successfully deactivated", "deactivateFailed": "Failed to deactivate <strong>{{number}}</strong> number", "activateSuccess": "<strong>{{number}}</strong> number successfully activated", "activateFailed": "Only 2 numbers allowed in 1 function", "editSuccess": "WhatsApp sender number information successfully updated", "editFailed": "Failed to update Whatsapp sender number information", "registerSuccess": "WhatsApp number <strong>{{number}}</strong> added successfully", "registerFailed": "failed to add WhatsApp number <strong>{{number}}</strong>", "loginSuccess": "Authentication successful"}}