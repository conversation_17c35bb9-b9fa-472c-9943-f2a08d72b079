{"tutup_kasir_notif": {"form": {"title": "Cashier Closing Report Notification", "sendNotif": {"title": "Send Cashier Closing Report Notification"}, "senderNumber": {"title": "Sender Number", "description": "The number used to send notifications", "placeholder": "Select sender number"}, "timing": {"title": "Notification Send Time", "options": {"immediate": {"title": "When closing cashier", "description": "Notifications will be sent to the recipient's number every time a cashier closing action is taken from the POS."}, "scheduled": {"title": "At a specific time", "description": "Notifications will be sent to the recipient's number every day at the specified time. If there is more than 1 report before the sending time, a number of cashier closing action notifications will be sent from the POS."}}}, "recipients": {"title": "Recipient Numbers", "description": "Select up to 10 numbers to receive notifications. Make sure the recipient saves the sender number so the notification is sent", "addRecipient": "Add Recipient"}, "preview": {"title": "Message Preview"}, "validation": {"senderRequired": "Sender number is required", "timingRequired": "Send time is required", "timeRequired": "Time is required", "recipientsRequired": "At least one recipient number is required", "recipientRequired": "Recipient number is required", "recipientSameAsSender": "Recipient number cannot be the same as sender number", "minPhoneDigit": "Recipient phone number must be at least 10 digits"}}}, "penjualan_per_jam_notif": {"form": {"title": "Hourly Sales Report Notification", "sendNotif": {"title": "Send Hourly Sales Report Notification"}, "senderNumber": {"title": "Sender Number", "description": "Number used to send notifications", "placeholder": "Select sender number"}, "timing": {"title": "Notification Send Time", "options": {"operational": {"title": "During outlet operational hours", "description": "Recipients will receive notifications every hour during outlet operation. If the operational hours are not set, the sending time range is 00.00 - 23.00"}, "scheduled": {"title": "During", "description": "Notifications will be sent every hour during the specified time range"}}}, "recipients": {"title": "Recipient Numbers", "description": "Select up to 10 numbers to receive notifications. Make sure recipients save the sender number so notifications can be delivered", "addRecipient": "Add Recipient"}, "preview": {"title": "Message Preview"}}}, "laporan_absensi_notif": {"form": {"title": "Attendance Report Notification", "sendNotif": {"title": "Send Attendance Report Notification"}, "senderNumber": {"title": "Sender Number", "description": "Number used to send notifications", "placeholder": "Select sender number"}, "scheduledTime": {"title": "Notification Send Time", "description": "Notifications will be sent to the recipient's number at the specified time. Make sure the send time is appropriate to obtain more accurate data."}, "recipients": {"title": "Recipient Number", "description": "Select up to 10 numbers to receive notifications. Make sure the recipient saves the sender number so the notification is sent", "addRecipient": "Add Recipient"}, "preview": {"title": "Message Preview"}}}, "modalConfirmation": {"title": "Notification Settings", "description": "Notification settings for outlet <strong>{{outlet}}</strong> will be saved according to the settings that have been made. Continue?", "cancel": "Cancel", "continue": "Yes, Continue"}}