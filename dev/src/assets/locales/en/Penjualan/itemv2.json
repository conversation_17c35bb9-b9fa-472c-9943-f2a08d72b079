{"main": {"title": "Product List", "dialog": {"exportDialog": {"title": "Export Product List", "description": "<strong>{{ type }}</strong> will be stored and saved on the device used. Continue?"}, "exportRecipeDialog": {"title": "Export Recipe", "description": "All products with variants cannot be exported. <strong>Products with recipes and inventory monitoring off</strong> will be exported and saved to the device used. Continue?"}, "exportTemplateDialog": {"description": "{{ type }} will be stored and saved on the device used. Continue?"}, "processExportTemplate": {"description": "Please wait, the system is processing the {{type}}. You will receive notifications and emails when the template is ready to download."}, "filterDialog": {"title": "Product Filter", "titleListProduct": "Data Filter", "showAllOnMenu": "All Show on Menu", "showOnMenuLabel": "Shown On Menu", "filterDisplay": {"label": "Choose <PERSON>", "option": {"00": "All Show On Menu", "10": "Shown In Menus", "01": "Hidden In Menus"}}}, "detailDialog": {"productName": "Product Name"}, "saveConfirmDialog": {"inventoryMonitoring": {"title": "Inventory Monitors", "activateMessage": "Enabling <strong>Inventory Monitor</strong> feature will only affect products that do not have a recipe. Continue?", "zeroStockProductMessage": "Applying <strong>Unmonitored Inventory</strong> will only affect to product which had 0 stock. Continue?", "dialogOnMonitoringProduct": "Product Stock <strong>{{product}}</strong> is not equal to 0 at the following outlets:", "dialogOnMonitoringRecipe": "Recipes will be deleted if inventory monitor is enabled. Continue?"}}, "deleteDialog": {"title": "Delete Product", "descriptionSingle": "Deleting a <strong>{{productName}}</strong> product will permanently remove that product from the list and cannot be undone. Continue?", "descriptionMultiple": "Deleting <strong>{{productSelectedLength}} products</strong> will permanently remove that products from the list and cannot be undone. Continue?"}, "importDialog": {"outletChoosed": "{{choosedOutlet}} outlet selected", "customCaptionMsg": {"importMaterialData": {"message": "Add ingredients simultaneously, please press the export template button if you don't have an excel file template to add ingredients", "warning": "please note that ingredients will be registered at all outlets."}}}}, "toast": {"succeedProductListExport": "Product List successfully exported", "succeedProductListImport": "Product List imported successfully", "succeedDeleteProduct": "Product <strong>{{ name }}</strong> deleted successfully", "succeedDeleteBulkProduct": "{{dataSuccess}} products deleted successfully", "succeedEditProduct": "Product edited successfully", "succeedEditProductMultiple": "products updated successfully", "failedEditProduct": "Product not edited successfully", "failedGetProducts": "Failed to get data", "failedOpenAddProduct": "Failed to open add product dialog", "failedOpenEditProduct": "Failed to open edit product dialog", "failedOpenProductDetail": "Failed to get detail data", "failedOpenPreviewProduct": "Failed to open product detail dialog", "failedExportDataCausedJWT": "Export Data Failed", "failedExportData": "Failed to export product data", "failedDuplicateProductSKU": "Failed to Upload Data, there are duplicates in the product name / SKU code, please check your file again.", "failedGetCategoryData": "Failed to get category data"}, "selectMode": {"statusSettings": "Choose <PERSON>", "statusActionOption": {"show": "Shown in Menus", "unshow": "Hidden in Menus", "favorite": "Favorite", "unfavorite": "Not Favorite", "cogs": "Enable Inventory Monitor", "noncogs": "Disable Inventory Monitor"}, "statusOperationLabel": {"edit": "Change Status {{itemLength}} products", "delete": "Delete {{itemLength}} products", "apply": "Apply {{itemLength}} products"}}, "outlet": {"allOutlet": "All Outlet", "perOutletSetting": "Set Per Outlet", "mainOutletDescription": "Data displayed based on Main Outlet <strong>({{name}})</strong>"}}, "alertDialogCancel": {"description": "Canceling <strong>{{name}}</strong> will delete all data that has been inputted and cannot be undone. Continue?"}, "form": {"contactSupportBanner": "Can only be used on mobile application {{ version }} version. Please contact our support team for its use.", "starterBasicBanner": "{{feature}} features are only available on the <strong>Starter</strong>, <strong>Advance</strong>, and <strong>Prime</strong> package. Please upgrade your package to use this feature.", "discountProductBanner": "Changing the selling price will have an impact on the amount of discount in the strike-through price promotion.", "step": {"productInformation": "Product Information", "variant": "<PERSON><PERSON><PERSON>", "extra": "Extra", "recipe": "Recipe", "onlineStore": "Online Store"}, "stepOne": {"outletList": "Outlet List", "productDesc": "Product Description", "productPhoto": "Product Photo", "productPhotoDesc": "Use 1:1 aspect ratio with min. size 10Kb and max 1Mb. Image format .jpg .jpeg .png and the min size is 100px x 100px (For optimal images use a min size of 1000px x 1000px). Max 5 photos", "inventoryMonitor": "Inventory Monitor", "serialNumberDesc": "The cashier must manually enter the serial number during the transaction", "activateProductHas": "Activate the product has a ", "setAsParent": "Set as parent", "productHasTransaction": "This product already has a transaction so it cannot activate the Serial Number and Batch Number", "conversion": "Conversion", "minimumPurchase": "<PERSON><PERSON>", "productDimension": "Product Dimension", "productDimensionVolume": "Volume (in Length x Width x Height)", "editSellingPriceDesc": "Allow the cashier to change the selling price", "editSellingPriceInfo": "Make sure the selling price is higher than the purchase price", "wholesalePrice": "Wholesale Price", "wholesalePriceDesc": "Maximum 5 Wholesale Price", "categoryNotAvailable": "Category not available?", "createNewCategory": "Create New Category", "favoriteProduct": "Favorite Product", "inventoryMonitorDesc": "Enable inventory monitor", "productMinimumStock": "Product Minimum Stock", "hasExpiredDate": "Has an Expiration Date", "minimumQty": "Minimum Quantity", "unitPrice": "Unit Price", "food": "Food", "alertStockDesc": "You can set a notification when your stock is running low", "groupDefault": "Without Group Parent", "minimumStock": {"label": "Product Minimum Stock", "tooltip": "Enable stock alert notification via<br />Notification Settings menu", "placeholder": "Example: 20"}, "snbnContent": {"serialNumber": {"prime": "The Serial Number feature is only available on the prime Package. Please upgrade to the <strong>Prime</strong> package to use this feature.", "tooltip": "Activate the inventory monitor to enable serial number", "title": "Serial Number", "caption": "The cashier must manually enter the serial number during the transaction", "snSwitch": "Activate the product has a Serial Number", "disableSnWhenBnEnabled": "Serial Number is disabled when Batch Number is enabled"}, "batchNumber": {"prime": "The Batch Number feature is only available on the prime Package. Please upgrade to the <strong>Prime</strong> package to use this feature.", "tooltip": "Activate the inventory monitor to enable batch number", "title": "Batch Number", "bnSwitch": "Activate the product having a Batch Number", "hasExpiredDate": "Has an Expiration Date", "disableBnWhenSnEnabled": "Batch Number is disabled when Serial Number is enabled"}, "hasRecipe": "This product already has a transaction so it cannot activate the Serial Number and Batch Number", "subtitle": "Product Stock <strong>{{ name }}</strong> does not equal 0 at outlet:", "note": "Please do a stock opname with actual stock of 0 at each of the above outlets to be able to deactivate the ", "createStockOpname": "Create stock opname", "upgradeNow": "Upgrade Now"}, "productGroup": {"label": "Group", "setAsParentCheckbox": "Set as parent"}, "productTax": {"label": "Product Tax", "description": "To Determine Tax on this product, please access the page", "link": "Tax Settings", "noTaxSubject": "Not subject to tax"}, "wholesalePriceSection": {"title": "Wholesale Price", "note": "Wholesale prices can only be applied to sales with the base unit<br />and cannot be used for special customer group prices and ojek online prices", "maxWholesalePrice": "Maximum 5 Wholesale Price", "minQty": {"label": "Minimum Quantity", "placeholder": "Example: 1"}, "unitPrice": "Unit Price", "addWholesalePrice": "Add Wholesale Price", "isHaveVariant": "Products with variants cannot activate wholesale prices", "starterAdvanceUpgrade": "The Wholesale Pricing feature makes it easy to provide lower prices to customers who purchase a certain amount (the larger the purchase quantity, the lower the selling price). "}, "validationSchema": {"categoryName": "*Please fill in the Category Name", "productName": "*Please fill in the Product Name", "outletName": "*Please select an outlet", "category": "*Please select a Product Category", "minimumStock": "*Nominal Stock Monitoring Not yet filled", "sku": "*Please fill in SKU", "unitName": "*Please fill in Unit Name", "purchasePrice": "*Please fill in Purchase Price", "sellingPrice": "*Please fill in <PERSON><PERSON>", "sellingPriceLessThanPromo": "The selling price must not be below the price after the discounted promo strike-through price.", "minimumPurchase": "*Please fill in Minimum Purchase", "unitPrice": "*Please fill in Unit Price", "minQty": "*Please fill in Minimum Quantity", "sameUnitName": "*Unit cannot be the same", "sameSkuName": "*SKU cannot be the same", "conversion": "*Conversion must be greater than 1", "lessSellingPrice": "*Selling price may not be less than the wholesale unit price", "moreMinPurchase": "*Minimum purchase must not be greater than minimum wholesale price purchase"}, "notForSaleAccess": "Allow to Mark Product Unsellable", "notForSaleAccessDesc": "Allow cashiers to mark products as unavailable/unsellable in POS/online stores", "weightTooltip": "Input the weight by weighing the product after it is packaged", "dimensionTooltip": "Input the product after it is packaged to calculate the volumetric weight. Use the format Length x Width x Height, for example: 10 x 10 x 10 in centimeters (cm)."}, "stepTwo": {"heading": "Product Variant", "validationSchema": {"variantFields": {"name": {"emptyName": "*Please Fill in Variant Name"}, "option": {"emptyOption": "*Please Fill in Variant Option", "emptyOptionName": "*Please Fill in Variant Option Name"}, "haveNameWithComma": "*Please Fill in without comma (,)", "addVariantOption": "Add Variant Options"}, "variants": {"price": {"emptyPrice": "*Please Fill in Price", "emptyCapital": "*Please Fill in Selling Price", "emptyPurchase": "*Please Fill in Purchase Price"}, "sku": {"emptySKU": "*Please Fill in SKU"}}, "errorSetter": {"duplicateSKU": {"mobile": "SKUs cannot be the same", "desktop": "*Use a different SKU for each variant."}}}, "form": {"variantSwitch": {"label": "Product Has Variant", "caption": "Enable to add a choice of product variations", "hasWebOrder": "Unable to enable product variant feature on Marketplace"}, "variantType": {"label": "Variant Type", "name": {"label": "Variant Name", "placeholder": "Example: Color, Flavour, etc"}, "option": {"label": "Variant Option", "placeholder": "Example: <PERSON>illa, White, <PERSON>, etc", "instruction": "Press {{addKey}} to add variant options", "error": {"maximumVariantOption": "*The maximum number of product variants is 50"}}}, "addVariantTypeButton": "<PERSON><PERSON>", "variantList": {"title": "<PERSON><PERSON><PERSON>", "label": "Variant List", "description": "The changes to be made will affect the variant data on", "variantOption": "VARIANT OPTION", "purchasePrice": "PURCHASE PRICE", "sellingPrice": "SELLING PRICE", "capitalPrice": "AVERAGE PRICE", "sku": "SKU", "showOnMenu": "SHOWN IN MENUS", "emptyVariants": "There is no variant list yet"}, "toast": {"duplicateVariantOption": "Variant options may not be the same", "duplicateVariantName": "Variant name may not be the same"}}, "subDialog": {"warningVariantActive": {"title": "<PERSON><PERSON><PERSON>", "description": {"warning": "Multi Unit and Wholesale Price cannot be used if the variant is activated", "info": "The price and SKU on the <strong>Product Information</strong> page will be adjusted as in the <strong>Variant</strong> section"}}, "deleteVariant": {"title": "Delete Variant", "stillHaveVariant": "Deleting this variant will remove the variant from the product list and the SKU price will be adjusted on the Price and Unit page. Continue?", "lastVariant": "Deleting this variant will delete all data that has been inputted and cannot be undone. Continue?"}, "failDeleteVariant": {"title": "Failed Delete V<PERSON>t", "description": "Variants cannot be deleted, because the product already has a transaction"}, "disabledVariant": {"title": "Disabled <PERSON><PERSON><PERSON>", "description": "Variant features will be <strong>deactivated</strong>, data that has been input will be deleted and cannot be undone. Continue?"}, "outletAlert": {"title": "Outlet List", "description": "Changes made will change the data on:"}}, "cantActivateVariantStockNotZero": "Cannot activate variant because stock is not equal to 0", "variantHaveTransaction": "Cannot add variant name because it already has a transaction.", "starterAdvanceUpgrade": "The Variants feature makes it easy for you to add products that have multiple variations (size, color, flavour, etc). Please upgrade to <strong>Prime</strong> package to use this feature.", "allVariants": "All Variants"}, "stepThree": {"heading": "Extra", "ekstraInfo": "The Product Variants feature has now been renamed to Extra, ", "validationSchema": {"ekstraUnchoosed": "*Please Select Extra", "ekstraNotAddedYet": "*Not Added Extra Yet", "ekstraQtyEmpty": "*Please Fill in Measure", "ekstraQtyUnderSize": "*Minimum Measure is 0.01", "ekstraSellingPriceEmpty": "*Please Fill in Selling Price", "ekstraSameName": "*Extra names cannot be the same"}, "form": {"title": "Extra", "ekstraSwitch": {"label": "Product Has Extra", "tooltip": "Changing the Extra data on this product will not affect the default Extra data settings", "mobileInfo": "Changing the Extra data on this product will not affect the default Extra data settings", "alertSwitchOffHasExtra": "Disabling toggle extra data will return the extra data as default."}, "extraBanner": {"desc": "Can only be used in version 3.0 of the application. Please contact our operations team for further assistance.", "link": "Contact Us"}, "ekstraField": {"label": "Set Extra", "fields": {"extraName": {"label": "Extra Name", "placeholder": "Select Extra"}, "extraOptionName": "Extra Option Name", "extraMeasure": "Measurement", "extraCapitalPrice": "Average Price", "extraSellingPrice": "Selling <PERSON>", "extraIngredient": "Ingredient Name", "extraUnit": "Unit", "emptyIngredientTitle": "Ingredient Unavailable", "emptyIngredientDesc": "The extra option has no ingredients"}, "addExtraButton": "Add Extra", "editData": "Edit Data", "editDataTooltip": "Serves to change the extra data (dose* and selling price) and does not change the extra master measure* and selling price. If deactivated, the extra data will return according to the extra master settings", "exactSameValueWithMaster": "Prices and extra rates are in accordance with the master data. The change extra data feature will be disabled", "subExtra": "Sub Extra", "ingredient": "Ingredients"}}}, "stepFour": {"heading": "Recipe", "validationSchema": {"unchoosedRawIngredient": "Not Select Ingredient", "emptyRawIngredientMeasure": "Please fill in quantity", "zeroRawIngredientQuantity": "Quantity Still 0", "notAddRawIngredientYet": "Please add ingredient", "ingredientCannotBeTheSame": "*Ingredient cannot be the same"}, "form": {"recipeSwitch": {"label": "Product Recipe", "caption": "Enable to recipes to products"}, "recipeField": {"title": "Ingredients", "label": "Set Ingredients", "fields": {"recipeRawIngredient": "Ingredient", "makeRecipeRawIngredient": "Create Ingredient", "capitalPriceRecipe": "Average Price", "recipeMeasure": "Quantity", "recipeUnit": "Unit"}}, "addRecipeButton": "Add Ingredients"}, "monitoringMustOffAlert": "Recipe only applies to products with Inventory Monitor OFF", "monitoringMustOffAndNotHaveVariantAlert": "Recipe only applies to products with <strong>Inventory Monitoring OFF and products without Variants</strong>", "isVariantActive": "Recipes only apply to products with <strong>Inventory Monitoring OFF</strong>", "subDialog": {"activeRecipeAlertOff": {"title": "Disabled <PERSON><PERSON><PERSON>", "description": "Deactivating recipes will automatically delete recipes on {{mainFormDataName}} at all outlets. Continue?"}, "activeRecipeAlertOn": {"title": "Enabled Recipe", "description": "Activating the recipe will automatically activate the recipe on {{mainFormDataName}} in all outlets. Continue?"}, "createRawIngredient": {"title": "Add Ingredients", "rawMaterialName": {"label": "Ingredient Name", "placeholder": "Example: <PERSON><PERSON><PERSON>"}, "rawMaterialMonitoring": "Inventory Monitor", "rawMaterialMinimumStock": {"label": "Minimum Stock", "tooltip": "Enable stock alert notification via Notification Settings menu"}, "validationSchema": {"name": "Please fill in the Ingredient Name", "stockAlert": "Please fill in Inventory Monitoring Quantity", "multiunit": {"unitName": "Please fill in the Unit Name", "unitSKU": "Please fill in SKU", "unitPurchasePrice": "Please fill in Purchase Price"}}, "rawMaterialField": {"unit": {"label": "Unit", "placeholder": "Select Unit"}, "conversion": "Conversion", "purchasePrice": "Selling <PERSON>", "sku": {"label": "SKU", "placeholder": "Example: P123"}, "defaultUnit": "Default Unit", "purchaseUnit": "Unit Purchasing", "addUnit": "Add Unit"}, "successAddMaterialToast": "Add Recipe <strong>{{newMaterialName}}</strong> succeeded"}}, "productHasVariants": "Product has <strong>{{count}}</strong> variants", "settingVariantRecipes": "Set ingredients for each variant"}, "stepFifth": {"heading": "Marketplace", "validationSchema": {"longDescMoreFiveThousand": "*The number of characters in the product description/specifications cannot be more than 5000 characters", "pricePercentageMoreThanHundred": "*Price cannot be less than 100", "emptyMinimumStock": "*Please fill in minimum stock value", "outletNotChoosed": "*Please choose outlet"}, "banner": {"title": "Data Change Information", "description": "Changing the product data will affect the data in the online store as well."}, "form": {"outletList": "Outlet List", "unintegrated": {"note": "This outlet is not yet integrated with any marketplace, please do the integration first to use this feature", "action": "Apply for Integration"}, "weborderSwitch": {"label": "Web Order", "caption": "Activate the product on the selected marketplace that has been integrated"}, "manualPriceCheckbox": {"label": "Manual", "caption": "All data changes will affect the data on the integrated platform and active products for selected outlet.", "note": "All data changes will affect the data on the integrated platform and active products for selected outlet."}, "presetButton": "View Presets", "favoriteProductCheckbox": {"label": "Shows Favorite Products", "tooltip": "Select as favorite product,<br />if you want to display this favorite product on your web order."}, "minimumStockCheckbox": {"label": "Minimum Stock", "tooltip": "Activating minimum stock will lock stock at the position you have set,<br />so you can still sell."}, "productSpecs": "Product Description/Specifications"}, "subDialog": {"editMultipricePage": {"title": "Edit Multiprice", "heading": "Web Order Detail", "fields": {"outletList": "Outlet List", "statusSwitch": "Status", "typeName": "Type Name", "description": "Description", "customerGroup": "Customer Group"}}, "productAndPrice": {"heading": "Set Product and Price", "subtitle": "Select Product", "addProductButton": "Add Product", "priceSetting": "Price Settings", "productPricePresetButton": "Product Price Preset", "editableTableSelectedList": {"product": "PRODUCT", "unit": "UNIT", "price": "NORMAL PRICE", "customPrice": "CUSTOM PRICE"}}, "presetPrice": {"title": "Product Price Preset", "markupPrice": "<PERSON><PERSON>", "markdownPrice": "<PERSON><PERSON>", "resetToManualRadio": "Reset and set manually"}, "addWeborderProduct": {"title": "Add Product", "table": {"productName": "Product Name", "category": "Category", "sku": "SKU", "unit": "Unit", "sellingPrice": "Selling <PERSON>"}}}, "toast": {"errorGetOnlineShopData": "Failed to get Online Shop Outlet data", "errorGetOnlineShopProviderData": "Failed to get Marketplace Provider data", "failedToGetMultipriceData": "Failed to get Multiprice Detail data"}}, "priceUnitStep": {"heading": "Price And Units", "validationSchema": {"name": {"emptyNameValue": "*Please fill in the unit name", "usedName": "Name has used"}, "conversion": {"emptyConversionValue": "*Please fill in conversion value", "duplicatedToFirstConversion": "Conversion must greather than 1"}, "sku": "*Please fill in the SKU", "duplicateSKU": "*SKU has taken", "purchasePrice": "*Please fill in Purchase Price", "sellingPrice": "*Please fill in <PERSON><PERSON>", "minSellingQty": {"emptyValue": "*Please fill in <PERSON><PERSON>"}, "dimension": {"emptyDimensionValue": "*P<PERSON>ce fill in with 1 on each input field", "dimensionZeroValue": "*Min. volume: 1x1x1 cm"}, "weight": {"minimumWeight": "*Min. weight: 100 grams", "emptyWeightValue": "*Please fill in <PERSON><PERSON>"}, "wholeSalePrice": {"minimumWholeSaleValue": "*Minimum Quantity can't be minus", "emptyWholeSaleValue": "*Please fill in Minimum Quantity", "sellingPriceMoreThanEqualZero": "*Unit Price must be equal or more than 0", "emptySellingPrice": "*Please fill in Unit Price", "zeroSellingPrice": "*Please Set Selling Price more than 0", "emptyPercentageValue": "*Please fill in Percentage input field", "percentageMinimumValue": "*Minimum 0,1%", "percentageMaximumValue": "*Maximum 100%", "minimumPurchaseMoreOrEqualWithWholeSalePrice": "*Minimum purchase must not be greater than minimum wholesale price purchase", "sellingPriceForbidLessThanWholeSalePrice": "*Selling price may not be less than the wholesale unit price", "minimumQuantityMoreThanPrevious": "*Minimum Quantity must be greater than previous", "unitPriceMustCheaperThanPrevious": "*Unit Price must be cheaper than previous", "minimumQuantityMustMoreThanMinimumOrder": "*Minimum Quantity must be greater than Minimum Order", "unitPriceMustCheaperThanSellingPrice": "*Unit Price must be lower than Selling Price"}}}}, "modal": {"detailProduct": {"heading": "Product Details", "productInformation": {"title": "Product Information:", "outletList": "Outlet List", "productName": "Product Name", "description": "Product Description", "category": "Category", "productImages": "Product Image", "groupParent": "Parent Group"}, "priceAndUnit": {"title": "Price And Unit:", "table": {"unit": "UNIT", "averagePrice": "AVERAGE PRICE", "purchasePrice": "PURCHASE PRICE", "lastPurchasePrice": "LAST PURCHASE PRICE", "sellingPrice": "SELLING PRICE", "sku": "SKU", "volume": "Volume", "weight": "Weight", "minPurchase": "MIN. PURCHASE"}}, "wholesalePrice": {"title": "Wholesale Price", "table": {"minQty": "MIN QUANTITY", "unitPrice": "UNIT PRICE"}}, "variant": {"title": "<PERSON><PERSON><PERSON>", "table": {"variant": "VARIANT", "capitalPrice": "AVERAGE PRICE", "purchasePrice": "PURCHASE PRICE", "sellingPrice": "SELLING PRICE", "sku": "SKU", "status": "STATUS"}}, "ekstra": {"title": "Extra", "table": {"extraName": "EXTRA NAME", "option": "OPTION", "quantity": "MEASUREMENT", "capitalPrice": "AVERAGE PRICE"}}, "recipe": {"title": "Recipe", "table": {"ingredients": "INGREDIENTS", "quantity": "MEASUREMENT", "unit": "UNIT"}}, "weborder": {"title": "Web Order:", "productSpecifications": "Product Specifications"}, "toast": {"errorGetCategoryData": "Failed to get category data", "errorGenerateDetailProduct": {"title": "Failed to get product details", "description": "There was an error generating product detail data"}, "errorGetOnlineShopData": "Failed to get Online Shop Outlet data", "errorGetOnlineShopProviderData": "Failed to get Marketplace Provider data", "failedToGetMultipriceData": "Failed to get Multiprice Detail data"}}}, "importExportProduct": {"toast": {"onProcess": "On Process!"}, "mainButton": {"importNewProduct": "Import New Product", "importChangeProduct": "Import Product Changes", "importVariantNewProduct": "Import New Product Variant", "importChangeRecipe": "Impor Recipe Changes", "exportProduct": "Export Product", "exportRecipe": "Export Recipe"}, "banner": {"frontDescription": "The \"Import Data\" button will be disabled temporarily until", "backDescription": "process is complete", "isBeingProcessed": "is being processed"}, "validation": {"wrongFormat": "Unsupported file format, please use .xls or .xlsx", "templateNotMatch": "The imported template is not from the selected outlet", "fileSize": "Maximal size is no more than 5Mb", "selectAnOutlet": "*Please select an Outlet", "importFileFirst": "Please import the file first"}, "import": {"heading": "Import Template", "title": "Import Data", "process": "Process", "cancel": "Cancel Import Data", "cancelFrontDescription": "Exiting this page will cancel the", "cancelBackDescription": "process, you have to repeat the process from the beginning. Continue?", "successDescription": "is running. You will receive notifications & emails when the daa has been changed", "description": "To import data, make sure the format matches the provided template file.", "select": "Select", "selectFile": "Select or drop files here", "changeFile": "Change File", "uploadFilePlaceHolder": "Select or drop files here", "alertDialogSuccessFrontDescription": "Please wait, the system is processing", "alertDialogSuccessBackDescription": "You will receive notifications & email alerts when the data has been changed.", "newProductImports": "new product imports", "asyncExportTemplateSuccess": "is running. You will receive notifications and emails when the template is ready to download", "asyncExportTemplateProcessed": "is being processed, you will receive a notification once the process is complete"}, "export": {"heading": "Export Template", "selectOutlet": "Select Outlet", "selectOutletDescription": "Select the outlet that will be imported", "selectOutletPlaceHolder": "Select", "selectAllOutletPlaceHolder": "Select All", "templateDescription": "Specify the template file to use", "dontHaveATemplate": "Don't have a <PERSON><PERSON><PERSON> yet", "dontHaveATemplateDescription": "Use the template provided below", "alreadyHaveATemplate": "Already have a Template", "alreadyHaveATemplateDescription": "Make sure to use the template provided by majoo", "attention": "Attention:", "allOutlet": "All Outlet", "importChangeProductRules": {"first": "To optimize the import process, please only import the product data that you want to change", "second": "Complete every column marked with an asterisk (*) because it is mandatory", "third": "For other optional fields without an asterisk (*), you can complete or leave them blank as needed", "fourth": "Make sure to import the file in Microsoft Excel format (.xls or .xlsx)", "fifth": "Multi-units are not available in the starter package", "sixth": "By activating the serial/batch number, the on hand qty cannot be filled in because the template does not facilitate serial/batch number input. Fill in and complete it via Inventory > Purchase Invoice", "seventh": "Product variants cannot be changed in the Import Change Products template", "eighth": "Recipe and Extra cannot be changed in the Import Change Product template"}, "importNewProductRules": {"first": "Complete every column marked with an asterisk (*) because it is mandatory", "second": "For other optional fields without an asterisk (*), you can complete or leave them blank as needed", "third": "If you fill in the Unit Purchase Price #1 column and Initial Stock QTY, it will automatically form an Inventory Opening Balance transaction on the Sales Invoice menu", "fourth": "Initial Stock QTY column is filled with numbers without thousands separator (example: 1000)", "fifth": "If the number is a decimal, use a comma (,) as a separator with a maximum of 2 digits after it (example: 8.25)", "sixth": "Make sure to import the file in Microsoft Excel format (.xls or .xlsx)", "seventh": "Multi-units are not available in the starter package", "eighth": "By activating the serial/batch number, the on hand qty cannot be filled in because the template does not facilitate serial/batch number input. Fill in and complete it via Inventory > Purchase Invoice", "ninth": "Product variants cannot be changed in the Import Change Products template", "tenth": "Recipe and Extra cannot be changed in the Import Change Product template"}, "importChangeRecipe": {"first": "To optimize the import process, please only import product recipe data that you want to modify.", "second": "Recipes can be updated per outlet or applied to all outlets. Fill in: All in the outlet column to update all outlets.", "third": "The system will reject the update process if there is data redundancy.", "third1": "A product has the same recipe inputted twice.", "third2": "The recipe is not registered in raw materials.", "third3": "There is a product with active inventory monitoring.", "fourth": "Products cannot use both master recipes and raw materials simultaneously.", "fourth1": "Leave the master recipe column empty if using raw materials.", "fourth2": "Leave the raw materials column empty if using a master recipe.", "fifth": "Adding a product recipe can be done by adding a new row with the same SKU and product name.", "sixth": "Ensure to import files in Microsoft Excel format (.xls or .xlsx).", "seventh": "Recipes for variant products cannot be imported.", "eighth": "Products with active inventory monitoring cannot have recipes."}}, "exportRecipeTemplateDesc": "The template uses data from outlet", "uploadLabel": "Upload Data", "recipeOutletsAlertConfirm": "Are you sure you want to import recipes to outlet", "productOutletsAlertConfirm": "Are you sure you want to import data to outlet"}, "alertDialog": {"saveProductDialog": {"onSaveProduct": "<strong>{{productName}}</strong> product will be saved and appear in the product list (including POS, Marketplace, Outlet, etc) according to the settings that have been made. ", "onSaveMoreThanTenOutlet": "<br /><br /><strong>To display a new product on the POS, please wait a while or synchronize data.</strong><br />", "onEditProduct": "<strong>{{productName}}</strong> products data changes will be saved and appear in the product list (including POS, Marketplace, Outlet, etc) on all outlet. Continue?"}, "quitProductDialog": {"descriptionFormAdd": "Canceling <strong>Add Product</strong> will delete all data that has been inputted and cannot be undone. Continue?", "descriptionFormEdit": "Canceling <strong>Edit Product</strong> will delete all data that has been inputted and cannot be undone. Continue?"}, "deleteProductDialog": {"title": "Delete Product", "description": "Deleting a <strong>{{productName}}</strong> product will permanently remove that product and cannot be undone. Continue?"}, "monitoringDialog": {"title": "Inventory Monitoring", "description": "Your Recipe List will all be deleted if Inventory Monitoring is Active. Would you like to continue ?", "subtitle": "Product Stock <strong>{{ name }}</strong> does not equal 0 at outlet:", "monitoringOff": {"note": "Please do a stock opname with actual stock of 0 at each of the above outlets to be able to deactivate the inventory monitor.", "createStockOpname": "Create stock opname"}, "monitoringOn": {"note": "Recipes will be deleted if stock monitor is enabled. Continue?"}}, "snbnDialog": {"on": "{{ snbnStateReverse }} cannot be activated because this product already has a transaction using {{ snbnState }}", "off": "Disabling {{ snbnState }} will change the data in inventory and all outlets. Continue?", "monitoringOff": "Disabling Supply Monitor will disable {{ snbnState }}. Are you sure you want to make changes?"}, "cancelQuitMobileDialog": {"title": "Additional Options"}}, "toast": {"failedSubmitCheckForm": "There was an error filling out the form, please retry to check form", "succeedAddProduct": "Product <strong>{{ name }}</strong> added successfully", "succeedEditProduct": "Product <strong>{{ name }}</strong> edited successfully", "succeedDeleteProduct": "Product <strong>{{ name }}</strong> deleted successfully", "activityLog": {"add": {"title": "Currently Adding Product!", "description": "Product <strong>{{ product_name }}</strong> being added {{ multiple_outlet_length }}. Please check product activity log."}, "edit": {"title": "Currently Editing Product!", "description": "Product <strong>{{ product_name }}</strong> at outlet {{ multiple_outlet_length }} are beings editing. Please check product activity log.", "noOutletDescription": "Product <strong>{{ product_name }}</strong> are beings editing. Please check product activity log."}, "multipleSentence": "and {{ outlet_length }} other outlets"}}, "dialogInfoExport": {"title": "Export Product List", "description": "Please wait, the system is processing the <strong>Product List</strong>. You will receive notifications and emails when the data is ready to download."}, "dialogInfoExportRecipe": {"title": "Export Recipe", "description": "Please wait, the system is processing the Recipe on the Product List. You will receive a notification and email when the data is ready to download."}, "coachMark": {"skip": "Skip All", "next": "Continue", "ok": "Okay", "modal": {"title": "Guide Completed!", "description": "You have completed the product creation guide. Are you ready to add your own product?", "cancelLabel": "Repeat Guide"}, "outletList": {"title": "Outlet List", "content": "Add products to selected outlet"}, "productInformation": {"title": "Product Information", "content": "<label><b>Tip: </b>Use a different product name for each product</label>"}, "productCategory": {"title": "Product Category", "content": "Create categories to grup similar products"}, "nextOption": {"title": "Advanced Options", "content": "<label><b>Favorite Product: </b>Activate to make the product a favorite item </label><label><b>Shown in Menu: </b>Activate to display the product on the cashier page</label>"}, "monitorInventory": {"title": "Inventory Monitor", "content": "Enable inventory monitor to monitor product quantity"}, "unitAndConversion": {"title": "Units and Conversions", "content": "<label>Adjust product units to facilitate purchases based on size/quantity. </label><label><b>Example: </b>Pieces, Kg, Grams, etc</label><label>(This feature is available only in <b>Prime</b> subscription plan)</label>"}, "sellingPrice": {"title": "Selling <PERSON>", "content": "<label>Selling Price is the price that will be paid by the buyer and will appear on the menu at cashier</label>"}, "productDimension": {"title": "Product Dimensions", "content": "Set dimensions for products that can be sent using courier services"}, "variantProduct": {"title": "Product Variant", "content": "<label>Activate if the product has variations such as color, flavour, size, etc.</label><label>(This feature is available only in <b>Prime</b> subscription plan)</label>"}, "addSelectVariant": {"title": "Add Variant Option", "content": "Click Add if variant has more than 1"}, "variantList": {"title": "Variant List", "content": "Set price, SKU, and variant option status on variant list"}, "extraSection": {"title": "Extra", "content": "<label>Use this feature if there are other items that can be added (extra fork, sugar, sauce, etc)</label>", "productLabel": "Product", "variantLabel": "<PERSON><PERSON><PERSON>", "extraLabel": "Extra", "colorLabel": "Color", "whiteLabel": "White", "sauceLabel": "Sauce"}, "recipeSection": {"title": "Recipe", "content": "Recipes are used for premade product and cannot be activated at the same time with inventory monitor"}, "marketplaceStep": {"title": "Marketplace", "content": "If products are sold in the Online Store, enable this option to integrate all incoming orders directly"}}, "perOutletSettingForm": {"title": "<PERSON><PERSON><PERSON>", "step": {"priceUnit": "Price And Units", "variant": "<PERSON><PERSON><PERSON>", "recipe": "Recipe", "nextOption": "Next Option", "wholesalePrice": "Wholesale Price", "extra": "Extra"}, "searchOutletPlaceholder": "Search Outlet...", "outletGroup": "Outlet Group", "currentData": "Current data", "outletGroupBanner": {"title": "New!", "description": "Create Outlet Group to make it easier to centrally manage multiple outlets", "linkText": "Set Outlet Group"}, "manualRecipeBanner": {"description": "Recipe data is taken from product details. Use the 'Edit' menu if you want to select Manual Recipe With Raw Material"}, "editVariantBanner": {"description": "Variant type is taken from the product details. Use 'Edit' menu if you want to change the variant type."}, "notSellabelProduct": "Unsellable Product", "maxChangeablePrice": "Maximum Change", "pleaseFillRecipe": "Please select recipe", "noRecipe": "Without Recipe", "perVariantRecipe": "Recipe per <PERSON>", "selectRecipe": "Select Recipe", "equalized": "Equalized", "selectReferenceRecipe": "Select recipe to be used as a reference", "recipeMaster": "Recipe Master", "recipeMasterCode": "RECIPE MASTER CODE", "recipeMasterName": "RECIPE MASTER NAME", "materialCount": "MATERIAL COUNT"}}