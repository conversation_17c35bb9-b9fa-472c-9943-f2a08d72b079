{"tutup_kasir_notif": {"form": {"title": "Notifikasi Lapor<PERSON>", "sendNotif": {"title": "<PERSON><PERSON>"}, "senderNumber": {"title": "<PERSON>mor <PERSON>", "description": "Nomor yang digunakan untuk mengirim notifikasi", "placeholder": "<PERSON><PERSON>h nomor pengirim"}, "timing": {"title": "<PERSON><PERSON><PERSON>", "options": {"immediate": {"title": "Saat tutup kasir", "description": "Notifikasi akan dikirim ke nomor penerima setiap ada aksi tutup kasir dari POS."}, "scheduled": {"title": "<PERSON><PERSON><PERSON> pukul", "description": "Notifikasi akan dikirim ke nomor penerima setiap harinya pada waktu yang ditentukan. Jika ada lebih dari 1 laporan sebelum waktu pengiri<PERSON>, notifikasi akan dikirim sejumlah aksi tutup kasir dari POS."}}}, "recipients": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> hingga 10 nomor untuk menerima notifikasi. Pastikan penerima menyimpan nomor pengirim agar notifikasi terkirim", "addRecipient": "Tambah Penerima"}, "preview": {"title": "<PERSON><PERSON><PERSON>"}, "validation": {"senderRequired": "Nomor pengirim harus dipilih", "timingRequired": "<PERSON><PERSON><PERSON> kirim harus dipilih", "timeRequired": "<PERSON><PERSON><PERSON> harus diisi", "recipientsRequired": "Minimal satu nomor penerima harus diisi", "recipientRequired": "Nomor penerima harus diisi", "recipientSameAsSender": "Nomor penerima tidak boleh sama dengan nomor pengirim", "minPhoneDigit": "Nomor penerima minimal 10 digit"}}}, "penjualan_per_jam_notif": {"form": {"title": "Notifikasi Laporan Penjualan Per Jam", "sendNotif": {"title": "<PERSON><PERSON> Laporan Penjualan Per Jam"}, "senderNumber": {"title": "<PERSON>mor <PERSON>", "description": "Nomor yang digunakan untuk mengirim notifikasi", "placeholder": "<PERSON><PERSON>h nomor pengirim"}, "timing": {"title": "<PERSON><PERSON><PERSON>", "options": {"operational": {"title": "Selama operasional outlet", "description": "Penerima akan mendapatkan notifikasi setiap jam selama outlet beroperasi. Apabila jam operasional tidak diatur, rentang waktu pengiriman adalah 00.00 - 23.00"}, "scheduled": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> akan mendapatkan notifikasi setiap jam dalam rentang waktu yang diatur"}}}, "recipients": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> hingga 10 nomor untuk menerima notifikasi. Pastikan penerima menyimpan nomor pengirim agar notifikasi terkirim", "addRecipient": "Tambah Penerima"}, "preview": {"title": "<PERSON><PERSON><PERSON>"}}}, "laporan_absensi_notif": {"form": {"title": "Notifikasi Lapor<PERSON>", "sendNotif": {"title": "<PERSON><PERSON>"}, "senderNumber": {"title": "<PERSON>mor <PERSON>", "description": "Nomor yang digunakan untuk mengirim notifikasi", "placeholder": "<PERSON><PERSON>h nomor pengirim"}, "scheduledTime": {"title": "<PERSON><PERSON><PERSON>", "description": "Notifikasi akan dikirim ke nomor penerima pada waktu yang ditentukan. Pastikan waktu kirim sesuai kebutuhan untuk mendapatkan data yang lebih akurat."}, "recipients": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> hingga 10 nomor untuk menerima notifikasi. Pastikan penerima menyimpan nomor pengirim agar notifikasi terkirim", "addRecipient": "Tambah Penerima"}, "preview": {"title": "<PERSON><PERSON><PERSON>"}}}, "modalConfirmation": {"title": "Pengatura<PERSON>", "description": "Notifikasi outlet <strong>{{outlet}}</strong> akan disimpan sesuai dengan pengaturan yang telah dilakukan. Lanjutkan?", "cancel": "<PERSON><PERSON>", "continue": "Ya, Lanjutkan"}}