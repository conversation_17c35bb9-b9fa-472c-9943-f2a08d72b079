{"main": {"title": "Daftar Produk", "dialog": {"exportDialog": {"title": "Ekspor Daftar Produk", "description": "<strong>{{ type }}</strong> akan diekspor dan disimpan di perangkat yang digunakan. Lanjutkan?"}, "exportRecipeDialog": {"title": "Ekspor Resep", "description": "Semua produk yang memiliki varian tidak bisa di ekspor. <strong>Produk yang memiliki resep dan monitoring persediaan off</strong> akan diekspor dan disimpan di perangkat yang digunakan. Lanjutkan?"}, "exportTemplateDialog": {"description": "{{ type }} akan diekspor dan disimpan di perangkat yang digunakan. Lanjutkan?"}, "processExportTemplate": {"description": "<PERSON><PERSON>, sistem sedang memproses {{type}}. <PERSON>a akan menerima pemberitahuan pada halaman notifikasi dan email saat template siap untuk diunduh."}, "filterDialog": {"title": "<PERSON><PERSON>", "titleListProduct": "Filter Data", "showAllOnMenu": "<PERSON><PERSON><PERSON>", "showOnMenuLabel": "Status", "filterDisplay": {"label": "Pilih Status", "option": {"00": "<PERSON><PERSON><PERSON>", "10": "Tampil di Menu", "01": "Tidak Tampil di Menu"}}}, "detailDialog": {"productName": "<PERSON><PERSON>"}, "saveConfirmDialog": {"inventoryMonitoring": {"title": "Monitor <PERSON><PERSON><PERSON><PERSON>", "activateMessage": "Mengaktifkan fitur <strong>Monitor <PERSON><PERSON><PERSON><PERSON></strong>hanya akan mempeng<PERSON>hi produk yang tidak memiliki resep. Lanjutkan?", "zeroStockProductMessage": "Menetapkan bukan <strong>Monitor <PERSON><PERSON><PERSON><PERSON></strong> hanya akan berpeng<PERSON>h terhadap produk yang memiliki stok 0. Lanjutkan?", "dialogOnMonitoringProduct": "Stok Produk <strong>{{product}}</strong> tidak sama dengan 0 pada outlet:", "dialogOnMonitoringRecipe": "<PERSON><PERSON><PERSON> akan ter<PERSON><PERSON> jika monitor per<PERSON><PERSON><PERSON> diak<PERSON>. Lanjutkan?"}}, "deleteDialog": {"title": "Hapus Produk", "descriptionSingle": "Menghapus produk <strong>{{productName}}</strong> akan menghilangkan produk tersebut secara permanen dan tidak dapat dibatalkan. Lanjutkan?", "descriptionMultiple": "Menghapus <strong>{{productSelectedLength}} produk</strong> akan menghilangkan beberapa produk tersebut secara permanen dan tidak dapat dibatalkan. Lanjutkan?"}, "importDialog": {"outletChoosed": "{{choosedOutlet}} outlet dipilih", "customCaptionMsg": {"importMaterialData": {"message": "<PERSON><PERSON><PERSON> bahan baku secara be<PERSON>, silah<PERSON> tekan tombol ekspor template apabila anda belum memiliki template file excel untuk menambahkan bahan baku", "warning": "harap dip<PERSON><PERSON><PERSON>n bahwa bahan baku akan didaftarkan di semua outlet."}}}}, "toast": {"succeedProductListExport": "Daftar Produk berhasil diekspor", "succeedProductListImport": "Daftar Produk berhasil diimpor", "succeedDeleteProduct": "Produk <strong>{{ name }}</strong> ber<PERSON><PERSON> di<PERSON>pus", "succeedDeleteBulkProduct": "{{dataSuccess}} produk ber<PERSON><PERSON> di<PERSON>pus", "succeedEditProduct": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "succeedEditProductMultiple": "produk be<PERSON><PERSON><PERSON>", "failedEditProduct": "Produk tidak ber<PERSON><PERSON>", "failedGetProducts": "Gagal mengambil data produk", "failedOpenAddProduct": "Gagal membuka dialog tambah produk", "failedOpenEditProduct": "<PERSON>l membuka dialog edit produk", "failedOpenProductDetail": "Gagal mengambil data detail", "failedOpenPreviewProduct": "Gagal membuka dialog detail produk", "failedExportDataCausedJWT": "Ekspor data gagal", "failedExportData": "Gagal mengekspor data produk", "failedDuplicateProductSKU": "Upload data gagal, terdapat duplikasi pada nama produk / kode SKU, harap periksa kembali file anda.", "failedGetCategoryData": "Gagal mengambil data kategori"}, "selectMode": {"statusSettings": "Pilih Status", "statusActionOption": {"show": "Tampil di Menu", "unshow": "Tidak Tampil di Menu", "favorite": "<PERSON><PERSON><PERSON><PERSON>", "unfavorite": "<PERSON><PERSON><PERSON>", "cogs": "Aktifkan Monitor Persediaan", "noncogs": "Nonaktifkan Monitor Persediaan"}, "statusOperationLabel": {"edit": "Ubah Status {{itemLength}} produk", "delete": "Hapus {{itemLength}} produk", "apply": "Terapkan {{itemLength}} produk"}}, "outlet": {"allOutlet": "<PERSON><PERSON><PERSON>", "perOutletSetting": "<PERSON><PERSON>", "mainOutletDescription": "Data yang tampil berdasarkan Outlet Utama <strong>({{name}})</strong>"}}, "alertDialogCancel": {"description": "Membatalkan <strong>{{name}}</strong> akan menghapus seluruh data yang telah diinput dan tidak dapat dibatalkan. Lanjutkan?"}, "form": {"contactSupportBanner": "<PERSON>ya dapat digunakan pada aplikasi versi {{ version }}. <PERSON><PERSON> hubungi tim support kami untuk penggunaannya.", "starterBasicBanner": "Fitur {{feature}} hanya tersedia di paket <strong>Starter</strong>, <strong>Advance</strong>, <strong>Prime</strong>. Silahkan upgrade paket untuk menggunakan fitur ini.", "discountProductBanner": "Mengubah harga jual akan berdampak pada besarnya potongan dalam promosi harga coret.", "step": {"productInformation": "Informasi Produk", "variant": "<PERSON><PERSON>", "extra": "Ekstra", "recipe": "<PERSON><PERSON><PERSON>", "onlineStore": "Toko Online"}, "stepOne": {"outletList": "Daftar Outlet", "productDesc": "Deskripsi Produk", "productPhoto": "Foto Produk", "productPhotoDesc": "Gunakan rasio foto 1:1 dengan ukuran 10Kb dan maksimal 1Mb, Format foto .jpg .jpeg .png ukuran minimum 100px x 100px (Untuk gambar optimal gunakan ukuran maksimum 1000px x 1000px). Maksimal 5 foto", "inventoryMonitor": "Monitor <PERSON><PERSON><PERSON><PERSON>", "serialNumberDesc": "<PERSON><PERSON> wajib memilih manual serial number saat penjualan", "activateProductHas": "Aktifkan produk memiliki ", "setAsParent": "Tetapkan sebagai Induk", "productHasTransaction": "Produk ini telah memiliki transaksi sehingga tidak dapat mengaktifkan Serial Number dan <PERSON>ch Number", "conversion": "Kon<PERSON><PERSON>", "minimumPurchase": "<PERSON><PERSON>", "productDimension": "<PERSON><PERSON><PERSON>", "productDimensionVolume": "Volume (<PERSON><PERSON> x Lebar x Tingg<PERSON>)", "editSellingPriceDesc": "Izinkan kasir untuk mengubah harga jual", "editSellingPriceInfo": "Pastikan harga jual lebih tinggi dari harga beli", "wholesalePrice": "<PERSON><PERSON>", "wholesalePriceDesc": "Maksimal 5 harga grosir", "categoryNotAvailable": "<PERSON><PERSON><PERSON> belum tersedia?", "createNewCategory": "Buat Kategori <PERSON>", "favoriteProduct": "Produk Favorit", "inventoryMonitorDesc": "Aktifkan Monitor Persediaan", "productMinimumStock": "Stok Minimum Produk", "hasExpiredDate": "<PERSON><PERSON><PERSON><PERSON>", "minimumQty": "<PERSON><PERSON><PERSON>", "unitPrice": "<PERSON><PERSON>", "food": "<PERSON><PERSON><PERSON>", "alertStockDesc": "<PERSON><PERSON> dapat menambahkan pengingat ketika stokmu akan habis", "groupDefault": "Tanpa Induk Group", "minimumStock": {"tooltip": "Aktifkan notifikasi pengingat inventori melalui<br />menu Pengaturan Notifikasi", "placeholder": "Contoh: 20"}, "snbnContent": {"serialNumber": {"prime": "Fitur Serial Number hanya tersedia pada paket prime. Silahkan upgrade ke paket <strong>Prime</strong> untuk menggunakan fitur ini.", "tooltip": "Aktifkan monitor persediaan untuk mengaktifkan serial number", "title": "Serial Number", "caption": "<PERSON><PERSON> wajib memilih manual serial number saat penjualan", "snSwitch": "Aktifkan produk memiliki Serial Number", "disableSnWhenBnEnabled": "Serial Number dinonaktifkan ketika Batch Number diaktifkan"}, "batchNumber": {"prime": "Fitur Batch Number hanya tersedia pada paket prime. Silahkan upgrade ke paket <strong>Prime</strong> untuk menggunakan fitur ini.", "tooltip": "Aktifkan monitor persediaan untuk mengaktifkan batch number", "title": "Batch Number", "bnSwitch": "Aktifkan produk memiliki Batch Number", "hasExpiredDate": "<PERSON><PERSON><PERSON><PERSON>", "disableBnWhenSnEnabled": "Batch Number dinonaktifkan ketika Serial Number diaktifkan"}, "hasRecipe": "Produk ini telah memiliki tranksaksi sehingga tidak dapat mengaktifkan Serial Number dan <PERSON>ch <PERSON>", "subtitle": "Stok Produk <strong>{{ name }}</strong> tidak sama dengan 0 pada outlet:", "note": "Harap lakukan stok opname dengan stok aktual 0 pada masing-masing outlet diatas untuk dapat menonaktifkan ", "createStockOpname": "Buat Stok Opname", "upgradeNow": "Upgrade Sekarang"}, "productGroup": {"label": "Grup", "setAsParentCheckbox": "Tetapkan sebagai induk"}, "productTax": {"label": "<PERSON><PERSON>", "description": "Untuk Menentukan Pajak pada produk ini, silah<PERSON> akses halaman", "link": "<PERSON><PERSON><PERSON><PERSON>", "noTaxSubject": "Tidak dikenai <PERSON>"}, "wholesalePriceSection": {"title": "<PERSON><PERSON>", "note": "Harga grosir hanya dapat diterapkan untuk penjualan dengan satuan dasar<br />dan tidak dapat digunakan pada grup harga spesial pelanggan dan harga<br />ojek online.", "maxWholesalePrice": "Maksimal 5 harga grosir", "minQty": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Contoh: 1"}, "unitPrice": "<PERSON><PERSON>", "addWholesalePrice": "Tambah Harga Grosir", "isHaveVariant": "Produk memiliki varian tidak dapat mengaktifkan harga grosir", "starterAdvanceUpgrade": "Fitur Harga Grosir memudahkan untuk memberikan harga lebih rendah kepada pelanggan yang melakukan pembelian dalam jumlah tertentu (semakin besar kuantitas pembelian, semakin rendah harga jual). "}, "validationSchema": {"categoryName": "*<PERSON><PERSON> le<PERSON>", "productName": "*<PERSON><PERSON> le<PERSON>du<PERSON>", "outletName": "*Mohon pilih outlet", "category": "*<PERSON><PERSON>", "minimumStock": "*Nominal Stok Monitoring Belum diisi", "sku": "*Mohon lengkapi SKU", "unitName": "*<PERSON><PERSON> le<PERSON>", "purchasePrice": "*<PERSON><PERSON> le<PERSON><PERSON>", "sellingPrice": "*<PERSON><PERSON> le<PERSON><PERSON>", "sellingPriceLessThanPromo": "Harga jual tidak boleh di bawah harga setelah diskon promo harga coret.", "minimumPurchase": "*Mohon lengkapi Minimal Pembelian", "unitPrice": "*<PERSON><PERSON> le<PERSON><PERSON>", "minQty": "*<PERSON><PERSON>", "sameUnitName": "*Satuan tidak boleh sama", "sameSkuName": "*SKU tidak boleh sama", "conversion": "*Konversi harus lebih dari 1", "lessSellingPrice": "*Harga jual tidak boleh lebih kecil dari harga satuan grosir", "moreMinPurchase": "*Minimal pembelian tidak boleh lebih besar da<PERSON>ada minimal pembelian harga grosir"}, "notForSaleAccess": "Izinkan Ubah Produk Tidak Dijual", "notForSaleAccessDesc": "Izinkan kasir mengubah produk menjadi tidak tersedia/tidak dapat dijual di POS/toko online", "weightTooltip": "<PERSON><PERSON><PERSON><PERSON> berat dengan menimbang produk setelah dikemas", "dimensionTooltip": "Masukkan produk setelah dikemas untuk menghitung berat volume. Dengan format penulisan <PERSON> x Lebar x Tinggi, contoh : 10 x 10 x 10 dalam satuan cm (centimeter)"}, "stepTwo": {"heading": "<PERSON><PERSON>", "validationSchema": {"variantFields": {"name": {"emptyName": "*<PERSON><PERSON> le<PERSON>"}, "option": {"emptyOption": "*<PERSON><PERSON>", "emptyOptionName": "*<PERSON><PERSON> le<PERSON>"}, "haveNameWithComma": "*Silahkan isi tanpa tanda koma (,)", "addVariantOption": "<PERSON><PERSON> Pilihan V<PERSON>"}, "variants": {"price": {"emptyPrice": "*<PERSON><PERSON> le<PERSON>", "emptyCapital": "*<PERSON><PERSON> le<PERSON><PERSON>", "emptyPurchase": "*<PERSON><PERSON> le<PERSON><PERSON>"}, "sku": {"emptySKU": "*Mohon lengkapi SKU"}}, "errorSetter": {"duplicateSKU": {"mobile": "SKU tidak boleh sama", "desktop": "*Gunakan SKU yang berbeda untuk masing-masing varian."}}}, "form": {"variantSwitch": {"label": "Produk Memiliki Varian", "caption": "Aktifkan untuk menambahkan pilihan variasi produk", "hasWebOrder": "Tidak dapat mengaktifkan fitur varian produk pada toko online"}, "variantType": {"label": "<PERSON><PERSON><PERSON>", "name": {"label": "<PERSON><PERSON>", "placeholder": "Contoh: <PERSON><PERSON>, <PERSON><PERSON>, dll."}, "option": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Contoh: <PERSON><PERSON>, <PERSON><PERSON>, dll.", "instruction": "<PERSON><PERSON> {{addKey}} untuk menambahkan pilihan varian", "error": {"maximumVariantOption": "*<PERSON><PERSON><PERSON> varian produk maksima<PERSON> 50"}}}, "addVariantTypeButton": "Tambah Varian", "variantList": {"title": "<PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>", "description": "<PERSON>bahan yang akan dilakukan akan mempengaruhi data varian pada", "variantOption": "PILIHAN VARIAN", "purchasePrice": "HARGA BELI", "sellingPrice": "HARGA JUAL", "capitalPrice": "HARGA MODAL", "sku": "SKU", "showOnMenu": "TAMPIL DI MENU", "emptyVariants": "Belum ada daftar varian"}, "toast": {"duplicateVariantOption": "<PERSON><PERSON><PERSON> varian tidak boleh sama", "duplicateVariantName": "<PERSON>a varian tidak boleh sama"}}, "subDialog": {"warningVariantActive": {"title": "<PERSON><PERSON>", "description": {"warning": "<PERSON> Satuan, <PERSON>rga Grosir tidak dapat digunakan jika Varian diaktifkan.", "info": "<PERSON>rga dan <PERSON>U pada step <strong>Informasi Produk</strong> akan disesuaikan seperti pada step <strong><PERSON><PERSON></strong>"}}, "deleteVariant": {"title": "<PERSON><PERSON>", "stillHaveVariant": "Menghapus varian ini juga akan menghilangkan varian dari daftar produk serta harga SKU akan disesuaikan pada halaman Harga dan Sa<PERSON>. Lanjutkan?", "lastVariant": "Menghapus varian ini juga akan menghapus seluruh data yang telah diinput. Lanjutkan?"}, "failDeleteVariant": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> tidak dapa<PERSON>, karena produk telah memiliki trans<PERSON>i"}, "disabledVariant": {"title": "Nonaktif<PERSON>", "description": "<PERSON><PERSON> varian akan <strong>din<PERSON><PERSON><PERSON><PERSON></strong>, data yang telah diinput akan terhapus dan tidak dapat dibatalkan. Lanjutkan?"}, "outletAlert": {"title": "Daftar Outlet", "description": "Perubahan yang dilakukan akan mengubah data pada:"}}, "cantActivateVariantStockNotZero": "Tidak dapat mengaktifkan varian karna stok tidak sama dengan 0", "variantHaveTransaction": "Tidak dapat menambahkan nama varian karena telah memiliki transaksi.", "starterAdvanceUpgrade": "Fitur Varian memudahkan Anda untuk menambahkan produk yang memiliki beragam variasi (ukuran, warna, rasa, dll). Silahkan upgrade ke paket <strong>Prime</strong> untuk menggunakan fitur ini.", "allVariants": "<PERSON><PERSON><PERSON>"}, "stepThree": {"heading": "Ekstra", "ekstraInfo": "<PERSON>tur Produ<PERSON> kini berganti nama men<PERSON>, ", "validationSchema": {"ekstraUnchoosed": "*Belum Memilih Ekstra", "ekstraNotAddedYet": "*Belum Menambah Ekstra", "ekstraQtyEmpty": "*Takaran Belum Diisi", "ekstraQtyUnderSize": "*Minimal isi takaran 0.01", "ekstraSellingPriceEmpty": "*Harga <PERSON>al Belum Diisi", "ekstraSameName": "*Nama Ekstra tidak boleh sama"}, "form": {"ekstraSwitch": {"label": "Produk Memiliki Ekstra", "tooltip": "Mengubah data Ekstra pada produk ini tidak mempengaruhi pengaturan awal data Ekstra", "mobileInfo": "Mengubah data Ekstra pada produk ini tidak mempengaruhi pengaturan awal data Ekstra", "alertSwitchOffHasExtra": "Menonaktifkan ubah data ekstra akan mengembalikan data ekstra sesuai pengaturan awal."}, "extraBanner": {"desc": "Hanya dapat digunakan pada aplikasi versi 3.0. <PERSON><PERSON> hubungi tim operasional kami untuk penggunaannya.", "link": "<PERSON><PERSON><PERSON><PERSON>"}, "ekstraField": {"label": "Atur Ekstra", "fields": {"title": "Ekstra", "extraName": {"label": "Nama Ekstra", "placeholder": "Pilih Ekstra"}, "extraOptionName": "<PERSON><PERSON>", "extraMeasure": "Takaran", "extraCapitalPrice": "<PERSON><PERSON>", "extraSellingPrice": "<PERSON><PERSON>", "extraIngredient": "<PERSON><PERSON>", "extraUnit": "Satuan", "emptyIngredientTitle": "<PERSON><PERSON> baku tidak tersedia", "emptyIngredientDesc": "Pilihan ekstra tidak memiliki bahan baku"}, "addExtraButton": "Tambah Ekstra", "editData": "Ubah Data Ekstra", "editDataTooltip": "Berfungsi untuk mengubah data ekstra (takaran* dan harga jual) dan tidak menghubah takaran* dan harga jual master ekstra. Apabila dinonaktifkan maka data ekstra akan kembali sesuai pengaturan master ekstra", "exactSameValueWithMaster": "Harga dan takaran ekstra sudah sesuai dengan data master.Fitur ubah data ekstra akan dinonaktifkan", "subExtra": "<PERSON><PERSON><PERSON>", "ingredient": "Bahan Baku"}}}, "stepFour": {"heading": "<PERSON><PERSON><PERSON>", "validationSchema": {"unchoosedRawIngredient": "<PERSON><PERSON>", "emptyRawIngredientMeasure": "Belum <PERSON>", "zeroRawIngredientQuantity": "Isi takaran masih 0", "notAddRawIngredientYet": "Belum Menambah Bahan Baku", "ingredientCannotBeTheSame": "*Bahan Baku tidak boleh sama"}, "form": {"recipeSwitch": {"label": "<PERSON>sep <PERSON>", "caption": "Aktifkan untuk menambahkan resep pada produk"}, "recipeField": {"title": "Bahan Baku", "label": "Atur Bahan Baku", "fields": {"recipeRawIngredient": "Bahan Baku", "makeRecipeRawIngredient": "Buat Bahan Baku", "capitalPriceRecipe": "<PERSON><PERSON>", "recipeMeasure": "Takaran", "recipeUnit": "Satuan"}}, "addRecipeButton": "Tambah Bahan Baku"}, "monitoringMustOffAlert": "Resep hanya berlaku untuk produk dengan Monitoring Persediaan OFF", "monitoringMustOffAndNotHaveVariantAlert": "Resep hanya berlaku untuk produk dengan <strong>Monitoring persediaan OFF</strong>", "isVariantActive": "Resep hanya berlaku untuk produk dengan <strong>Monitoring persediaan OFF</strong>", "subDialog": {"activeRecipeAlertOff": {"title": "Menonaktifkan Resep", "description": "Menonaktifkan resep otomatis akan menonaktifkan/<br />menghapus resep pada produk {{mainFormDataName}} di semua outlet. Lanjutkan?"}, "activeRecipeAlertOn": {"title": "Mengaktifkan Resep", "description": "Mengaktifkan resep otomatis akan mengaktifkan resep  pada produk {{mainFormDataName}} di semua outlet. Lanjutkan?"}, "createRawIngredient": {"title": "Tambah Bahan Baku", "rawMaterialName": {"label": "<PERSON><PERSON>", "placeholder": "Contoh: <PERSON><PERSON><PERSON>"}, "rawMaterialMonitoring": "Monitoring <PERSON><PERSON><PERSON><PERSON>", "rawMaterialMinimumStock": {"label": "Stok Minimum", "tooltip": "Aktifkan notifikasi pengingat inventori melalui menu Pengaturan Notifikasi"}, "validationSchema": {"name": "<PERSON><PERSON> Bahan Baku tidak boleh kosong", "stockAlert": "Nominal Stok Monitoring Belum diisi", "multiunit": {"unitName": "<PERSON><PERSON>", "unitSKU": "Mohon lengkapi SKU", "unitPurchasePrice": "<PERSON><PERSON>"}}, "rawMaterialField": {"unit": {"label": "Satuan", "placeholder": "<PERSON><PERSON><PERSON>"}, "conversion": "Kon<PERSON><PERSON>", "purchasePrice": "<PERSON><PERSON>", "sku": {"label": "SKU", "placeholder": "Example: P123"}, "defaultUnit": "<PERSON><PERSON><PERSON>", "purchaseUnit": "<PERSON><PERSON><PERSON>", "addUnit": "Tambah Satuan"}, "successAddMaterialToast": "<PERSON><PERSON> baku <strong>{{newMaterialName}}</strong> ber<PERSON>il ditambah"}}, "productHasVariants": "<PERSON><PERSON><PERSON> memi<PERSON> <strong>{{count}}</strong> varian", "settingVariantRecipes": "<PERSON><PERSON> bahan baku untuk masing-masing varian"}, "stepFifth": {"heading": "Toko Online", "validationSchema": {"longDescMoreFiveThousand": "*Ju<PERSON><PERSON> karakter pada keterangan/spesifikasi produk tidak bisa lebih dari 5000 karakter", "pricePercentageMoreThanHundred": "*Harga tidak boleh kurang dari 100", "emptyMinimumStock": "*Mohon lengkapi Stok Minimum", "outletNotChoosed": "Silahkan pilih outlet"}, "banner": {"title": "Informasi Ubah Data", "description": "Mengubah data produk akan mempengaruhi data yang di toko online juga."}, "form": {"outletList": "<PERSON><PERSON><PERSON>", "unintegrated": {"note": "Outlet ini belum terintegrasi dengan marketplace, silakan lakukan proses integrasi terlebih dahulu untuk menggunakan fitur ini", "action": "<PERSON><PERSON><PERSON>"}, "weborderSwitch": {"label": "Web Order", "caption": "Aktifkan produk pada Toko Online pilihan yang telah terintegrasi"}, "manualPriceCheckbox": {"label": "Manual", "caption": "Seluruh perubahan data akan mempengaruhi data pada platform yang terintegrasi serta produk aktif sesuai dengan outlet terpilih", "note": "Seluruh perubahan data akan mempengaruhi data pada platform yang terintegrasi serta produk aktif sesuai dengan outlet terpilih"}, "presetButton": "<PERSON><PERSON>", "favoriteProductCheckbox": {"label": "<PERSON><PERSON>lkan Produk Favorit", "tooltip": "<PERSON><PERSON><PERSON> sebagai produk favorit,<br />jika ingin menampilkan produk ini favorit pada web order anda."}, "minimumStockCheckbox": {"label": "Stok Minimum", "tooltip": "Mengaktifkan minimum stok akan mengunci stok pada posisi yang telah anda set,<br />se<PERSON>ga anda tetap dapat ber<PERSON>."}, "productSpecs": "Keterangan/Spesifikasi Produk"}, "subDialog": {"editMultipricePage": {"title": "Ubah Multiprice", "heading": "Detail Web Order", "fields": {"outletList": "Daftar Outlet", "statusSwitch": "Status", "typeName": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "customerGroup": "Grup Pelanggan"}}, "productAndPrice": {"heading": "<PERSON><PERSON>duk dan <PERSON>", "subtitle": "<PERSON><PERSON><PERSON>", "addProductButton": "Tambah Produk", "priceSetting": "<PERSON><PERSON>", "productPricePresetButton": "Preset Harga Produk", "editableTableSelectedList": {"product": "PRODUK", "unit": "UNIT", "price": "HARGA NORMAL", "customPrice": "ATUR HARGA"}}, "presetPrice": {"title": "Preset Harga Produk", "markupPrice": "<PERSON><PERSON>", "markdownPrice": "<PERSON><PERSON>", "resetToManualRadio": "Reset dan tentukan manual"}, "addWeborderProduct": {"title": "Tambah Produk", "table": {"productName": "<PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "sku": "SKU", "unit": "Satuan", "sellingPrice": "<PERSON><PERSON>"}}}, "toast": {"errorGetOnlineShopData": "Gagal mendapatkan data Outlet Toko Online", "errorGetOnlineShopProviderData": "Gagal mendapatkan data Marketplace Provider", "failedToGetMultipriceData": "Gagal mendapatkan data Multi Price Detail"}}, "priceUnitStep": {"heading": "<PERSON><PERSON> dan <PERSON>", "validationSchema": {"name": {"emptyNameValue": "*<PERSON><PERSON> le<PERSON>", "usedName": "<PERSON><PERSON> sudah terpakai"}, "conversion": {"emptyConversionValue": "*Konversi belum diisi", "duplicatedToFirstConversion": "Konversi harus lebih dari 1"}, "sku": "*Mohon lengkapi SKU", "duplicateSKU": "*Nama SKU sudah terpakai", "purchasePrice": "*<PERSON><PERSON> le<PERSON><PERSON>", "sellingPrice": "*<PERSON><PERSON> le<PERSON><PERSON>", "minSellingQty": {"emptyValue": "*<PERSON>hon leng<PERSON> Min. <PERSON>"}, "dimension": {"emptyDimensionValue": "*Minimal diisikan dengan angka 1 masing-masing kolom", "dimensionZeroValue": "*Angka tidak boleh 0"}, "weight": {"minimumWeight": "*Minimal berat produk 100 gram", "emptyWeightValue": "*<PERSON><PERSON> le<PERSON><PERSON>"}, "wholeSalePrice": {"minimumWholeSaleValue": "*Ju<PERSON>lah <PERSON> tidak boleh negatif", "emptyWholeSaleValue": "*Jumlah Minimal tidak boleh kosong", "sellingPriceMoreThanEqualZero": "*Harga satuan harus sama dengan atau lebih besar dari 0", "emptySellingPrice": "*Harga Sa<PERSON>an tidak boleh kosong", "zeroSellingPrice": "*<PERSON>rga jual tidak boleh 0", "emptyPercentageValue": "*Persentase Belum diisi", "percentageMinimumValue": "*Minimal 0,1%", "percentageMaximumValue": "*Maximum 100%", "minimumPurchaseMoreOrEqualWithWholeSalePrice": "*Minimal pembelian tidak boleh lebih besar da<PERSON>ada minimal pembelian harga grosir", "sellingPriceForbidLessThanWholeSalePrice": "*Harga jual tidak boleh lebih kecil dari harga satuan grosir", "minimumQuantityMoreThanPrevious": "*<PERSON><PERSON><PERSON> harus lebih besar dari sebelumnya", "unitPriceMustCheaperThanPrevious": "*Harga Satuan harus lebih murah dari sebelumnya", "minimumQuantityMustMoreThanMinimumOrder": "*Jumlah Minimal harus lebih besar dari Minimal Order", "unitPriceMustCheaperThanSellingPrice": "*Harga Sa<PERSON>an harus lebih murah dari <PERSON>"}}}}, "modal": {"detailProduct": {"heading": "Detail Produk", "productInformation": {"title": "Informasi Produk:", "outletList": "Daftar Outlet", "productName": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "productImages": "Foto Produk", "groupParent": "Grup Induk"}, "priceAndUnit": {"title": "Harga dan <PERSON>:", "table": {"unit": "SATUAN", "averagePrice": "HARGA MODAL", "purchasePrice": "HARGA BELI", "lastPurchasePrice": "HARGA BELI TERAKHIR", "sellingPrice": "HARGA JUAL", "sku": "SKU", "volume": "Volume", "weight": "<PERSON><PERSON>", "minPurchase": "MIN. PEMBELIAN"}}, "wholesalePrice": {"title": "<PERSON><PERSON>", "table": {"minQty": "JUMLAH MINIMAL", "unitPrice": "HARGA SATUAN"}}, "variant": {"title": "<PERSON><PERSON>", "table": {"variant": "VARIAN", "capitalPrice": "HARGA MODAL", "purchasePrice": "HARGA BELI", "sellingPrice": "HARGA JUAL", "sku": "SKU", "status": "STATUS"}}, "ekstra": {"title": "Ekstra", "table": {"extraName": "NAMA EKSTRA", "option": "PILIHAN", "quantity": "TAKARAN", "capitalPrice": "HARGA MODAL"}}, "recipe": {"title": "<PERSON><PERSON><PERSON>", "table": {"ingredients": "BAHAN BAKU", "quantity": "TAKARAN", "unit": "SATUAN"}}, "weborder": {"title": "Toko online:", "productSpecifications": "Spesifikasi Produk"}, "toast": {"errorGetCategoryData": "Gagal mendapatkan data kategori", "errorGenerateDetailProduct": {"title": "Gagal mendapatkan detail produk", "description": "Terdapat kesalahan ketika menggenerate data detail produk"}, "errorGetOnlineShopData": "Gagal mendapatkan data Outlet Toko Online", "errorGetOnlineShopProviderData": "Gagal mendapatkan data Marketplace Provider", "failedToGetMultipriceData": "Gagal mendapatkan data Multi Price Detail"}}}, "importExportProduct": {"toast": {"onProcess": "Proses!"}, "mainButton": {"importNewProduct": "Impor <PERSON>", "importChangeProduct": "Impor Ubah Produk", "importVariantNewProduct": "<PERSON><PERSON><PERSON>", "importChangeRecipe": "Impor <PERSON>", "exportProduct": "Ekspor Produk", "exportRecipe": "Ekspor Resep"}, "banner": {"frontDescription": "Tombol 'Impor Data' akan dinonaktifkan sementara hingga proses", "backDescription": "<PERSON><PERSON><PERSON>", "isBeingProcessed": "sedang diproses"}, "validation": {"wrongFormat": "Format berkas tidak didukung, hanya mendukung format .xls dan .xlsx", "templateNotMatch": "Template t<PERSON><PERSON>", "fileSize": "Ukuran file tidak boleh lebih dari 5Mb", "selectAnOutlet": "*<PERSON><PERSON> pilih Outlet terlebih dahulu", "importFileFirst": "Impor template terlebih dahulu"}, "import": {"heading": "Impor Template", "title": "Impor Data", "process": "Proses", "cancel": "Batal Impor Data", "cancelFrontDescription": "<PERSON><PERSON><PERSON> dari halaman ini mengakibatkan proses", "cancelBackDescription": "batal, anda harus mengulang proses dari awal. Lanjut<PERSON>?", "successDescription": "sedang berjalan. <PERSON>a akan menerima notifikasi & email saat data berhasil diubah", "description": "Saat mengimpor data produk, pastikan format sesuai dengan file template yang disediakan.", "select": "<PERSON><PERSON><PERSON>", "selectFile": "<PERSON><PERSON><PERSON> dan letakkan template disini", "changeFile": "Ubah File", "uploadFilePlaceHolder": "<PERSON><PERSON><PERSON> dan letakkan template disini", "alertDialogSuccessFrontDescription": "<PERSON><PERSON>, sistem sedang memproses", "alertDialogSuccessBackDescription": "Anda akan menerima pemberitahuan notifikasi & email saat data selesai diubah", "newProductImports": "impor produk baru", "asyncExportTemplateSuccess": "sedang berjalan. Anda akan menerima pemberitahuan pada halaman notifikasi dan email saat template siap untuk diunduh", "asyncExportTemplateProcessed": "sedang diproses. <PERSON><PERSON> akan menerima notifikasi jika proses sudah selesai"}, "export": {"heading": "Ekspor Template", "selectOutlet": "<PERSON><PERSON><PERSON>", "selectOutletDescription": "Pilih outlet yang akan dilakukan Impor Produk", "selectOutletPlaceHolder": "Contoh: Cafe", "selectAllOutletPlaceHolder": "<PERSON><PERSON><PERSON>", "templateDescription": "Tentukan template berkas yang akan digunakan", "dontHaveATemplate": "Belum memiliki Template", "dontHaveATemplateDescription": "Gunakan template yang telah disediakan di bawah ini", "alreadyHaveATemplate": "Sudah memiliki Template", "alreadyHaveATemplateDescription": "Pastikan telah menggunakan template yang disediakan <PERSON>", "attention": "<PERSON><PERSON><PERSON><PERSON>:", "allOutlet": "<PERSON><PERSON><PERSON>", "importChangeProductRules": {"first": "<PERSON><PERSON><PERSON> men<PERSON><PERSON><PERSON><PERSON><PERSON> proses impor, mohon hanya mengimpor data produk yang ingin diubah", "second": "<PERSON><PERSON><PERSON><PERSON> setiap kolom yang memiliki tanda bintang (*) karena bersifat wajib diisi", "third": "Untuk kolom lain yang bersifat opsional tanpa tanda bintang (*), <PERSON><PERSON> dapat melengkapi atau mengosongkannya sesuai kebutuhan", "fourth": "Pastikan mengimpor berkas dalam format Microsoft Excel (.xls atau .xlsx)", "fifth": "Multi satuan tidak tersedia pada paket starter", "sixth": "Dengan mengaktifkan serial/batch number, QTY Stok Awal tidak dapat diisi karena template belum memfasilitasi input serial/batch number. Isi dan lengkapi melalui Inventory > Faktur Pembelian", "seventh": "Produk varian tidak dapat diubah pada template Impor Ubah Produk", "eighth": "Data Resep dan Ekstra tidak dapat diubah pada template Impor Ubah Produk"}, "importNewProductRules": {"first": "<PERSON><PERSON><PERSON><PERSON> setiap kolom yang memiliki tanda bintang (*) karena bersifat wajib diisi", "second": "Untuk kolom lain yang bersifat opsional tanpa tanda bintang (*), <PERSON><PERSON> dapat melengkapi atau mengosongkannya sesuai kebutuhan", "third": "Jika Anda mengisi kolom Harga Beli Satuan #1 dan QTY Stok Awal, maka secara otomatis akan membentuk transaksi Saldo Awal Persediaan pada menu Faktur Pembelian", "fourth": "Kolom QTY Stok Awal diisi dengan angka tanpa pemisahan <PERSON>uan (contoh: 1000)", "fifth": "<PERSON><PERSON><PERSON><PERSON> bi<PERSON> desimal, gunakan tanda koma (,) sebagai pemisah dengan maksimal 2 angka dibelakangnya (contoh: 8,25)", "sixth": "Pastikan mengimpor berkas dalam format Microsoft Excel (.xls atau .xlsx)", "seventh": "Multi satuan tidak tersedia pada paket starter", "eighth": "Dengan mengaktifkan serial/batch number, QTY Stok Awal tidak dapat diisi karena template belum memfasilitasi input serial/batch number. Isi dan lengkapi melalui Inventory > Faktur Pembelian", "ninth": "Produk varian tidak dapat diubah pada template Impor Ubah Produk", "tenth": "Data Resep dan Ekstra tidak dapat diubah pada template Impor Ubah Produk"}, "importChangeRecipe": {"first": "<PERSON><PERSON><PERSON> men<PERSON><PERSON><PERSON><PERSON><PERSON> proses impor, mohon hanya mengimpor data resep produk yang ingin diubah", "second": "Resep bisa diupdate per outlet atau langsung diberlakukan untuk seluruh outlet. Isi: All pada kolom outlet untuk melakukan update di seluruh outlet", "third": "Sistem akan men<PERSON> proses update jika terdapat redundansi data", "third1": "Produk memiliki resep yang sama diinput dua kali", "third2": "Resep tidak terdaftar di bahan baku", "third3": "Terdapat produk dengan monitoring inventory aktif", "fourth": "Produk tidak bisa menggunakan master resep dan bahan baku bersa<PERSON>an", "fourth1": "Kosongkan kolom master resep jika menggunakan bahan baku", "fourth2": "Kosongkan kolom bahan baku jika menggunakan master resep", "fifth": "<PERSON><PERSON><PERSON>an resep produk dapat dilakukan dengan menambahkan baris baru dengan SKU dan nama produk yang sama", "sixth": "Pastikan mengimpor berkas dalam format Microsoft Excel (.xls atau .xlsx)", "seventh": "Produk varian tidak bisa dilakukan impor resep", "eighth": "Produk dengan monitoring inventory aktif tidak dapat memiliki resep"}}, "exportRecipeTemplateDesc": "Template menggunakan data dari outlet", "uploadLabel": "Unggah Data", "recipeOutletsAlertConfirm": "<PERSON><PERSON><PERSON><PERSON> anda yakin akan mengimpor resep pada outlet", "productOutletsAlertConfirm": "A<PERSON><PERSON><PERSON> anda yakin akan mengimpor data pada outlet"}, "alertDialog": {"saveProductDialog": {"onSaveProduct": "Produk <strong>{{ productName }}</strong> akan disimpan dan tampil di daftar produk (termasuk POS, Toko Online, Outlet, dll) sesuai dengan pengaturan yang telah dilakukan. ", "onSaveMoreThanTenOutlet": "<br /><br /><strong>Untuk menampilkan produk baru di POS, mohon tunggu beberapa saat atau lakukan sinkronisasi data.</strong><br />", "onEditProduct": "Perubahan data produk <strong>{{ productName }}</strong> akan disimpan dan tampil di daftar produk (termasuk POS, Toko Online, Outlet, dll) pada semua outlet. Lanjutkan?"}, "quitProductDialog": {"descriptionFormAdd": "Membatalkan <strong><PERSON><PERSON> Produk</strong> akan menghapus seluruh data yang telah diinputkan dan tidak dapat dibatalkan. Lanjutkan?", "descriptionFormEdit": "Membatalkan <strong>Ubah Produk</strong> akan mengh<PERSON>us seluruh perubahan data yang telah diinputkan dan tidak dapat dibatalkan. Lanjutkan?"}, "deleteProductDialog": {"title": "Hapus Produk", "description": "Menghapus produk <strong>{{productName}}</strong> akan menghilangkan produk tersebut secara permanen dan tidak dapat dibatalkan. Lanjutkan?"}, "monitoringDialog": {"title": "Monitoring <PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>ar Re<PERSON>p anda akan terhapus semua apabila Monitoring Persediaan Aktif. Apakah anda ingin melanju<PERSON>kan ?", "subtitle": "Stok Produk <strong>{{ name }}</strong> tidak sama dengan 0 pada outlet:", "monitoringOff": {"note": "Harap lakukan stok opname dengan stok aktual 0 pada masing-masing outlet diatas untuk dapat menonaktifkan monitor persediaan.", "createStockOpname": "Buat Stok Opname"}, "monitoringOn": {"note": "<PERSON><PERSON><PERSON> akan ter<PERSON><PERSON> jika monitor per<PERSON><PERSON><PERSON> diak<PERSON>. Lanjutkan?"}}, "snbnDialog": {"on": "{{ snbnStateReverse }} tidak dapat diaktifkan karena produk ini sudah memiliki transaksi menggunakan {{ snbnState }}", "off": "Menonaktifkan {{ snbnState }} akan mengubah data di inventori dan semua outlet. Lanjutkan?", "monitoringOff": "Menonaktifkan Monitoring Persediaan akan menonaktifkan {{ snbnState }}. <PERSON><PERSON><PERSON><PERSON> Anda yakin ingin melakukan perubahan?"}, "cancelQuitMobileDialog": {"title": "Opsi Lanjutan"}}, "toast": {"failedSubmitCheckForm": "Terdapat kesalahan pengisian form, mohon check form kembali", "succeedAddProduct": "Produk <strong>{{ name }}</strong> ber<PERSON><PERSON> di<PERSON>", "succeedEditProduct": "Produk <strong>{{ name }}</strong> ber<PERSON><PERSON> di<PERSON>h", "succeedDeleteProduct": "Produk <strong>{{ name }}</strong> ber<PERSON><PERSON> di<PERSON>pus", "activityLog": {"add": {"title": "Sedang Menambah Produk!", "description": "Produk <strong>{{ product_name }}</strong> sedang ditambahkan ke outlet {{ main_outlet }}{{ multiple_outlet_length }}. Silakan periksa log aktivitas produk."}, "edit": {"title": "Sedang Mengubah Produk!", "description": "Produk <strong>{{ product_name }}</strong> pada outlet {{ main_outlet }}{{ multiple_outlet_length }} sedang diubah. Silakan periksa log aktivitas produk.", "noOutletDescription": "Produk <strong>{{ product_name }}</strong> sedang diubah. Silakan periksa log aktivitas produk."}, "multipleSentence": "dan {{ outlet_length }} outlet lainnya"}}, "dialogInfoExport": {"title": "Ekspor Daftar Produk", "description": "<PERSON><PERSON>, sistem sedang memproses <strong>Daftar Produk</strong>. <PERSON>a akan menerima pemberitahuan notifikasi dan email saat data siap untuk diunduh."}, "dialogInfoExportRecipe": {"title": "Ekspor Resep", "description": "<PERSON><PERSON>, sistem sedang memproses Resep pada Daftar Produk. Anda akan menerima pemberitahuan notifikasi dan email saat data siap untuk diunduh."}, "coachMark": {"skip": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "ok": "<PERSON>e", "modal": {"title": "Panduan Selesai!", "description": "Anda telah menyelesaikan panduan pembuatan produk. Sudah siap untuk menambahkan produk anda sendiri?", "cancelLabel": "<PERSON><PERSON><PERSON>"}, "outletList": {"title": "Daftar Outlet", "content": "Tambahkan produk pada outlet yang diinginkan"}, "productInformation": {"title": "Informasi Produk", "content": "<label><b>Tips: </b> Gunakan nama produk yang berbeda untuk setiap produk</label>"}, "productCategory": {"title": "<PERSON><PERSON><PERSON>", "content": "Buat kategori untuk mengelompokan produk-produk yang serupa"}, "nextOption": {"title": "Opsi Lanjutan", "content": "<label><b>Produk Favorit: </b>Aktifkan untuk menjadikan produk menjadi produk favorit </label><label><b>Tampil di menu: </b>Aktifkan untuk menampilkan produk di halaman kasir</label>"}, "monitorInventory": {"title": "Monitor <PERSON><PERSON><PERSON><PERSON>", "content": "Aktifkan monitor persediaan untuk memantau kuantitas produk"}, "unitAndConversion": {"title": "Satuan & Konversi", "content": "<label>Sesuaikan satuan produk untuk memudahkan pembelian berdasarkan ukuran/jumlah</label><label><b>Contoh: </b>Pieces, Kg, Gram, dll</label>"}, "sellingPrice": {"title": "<PERSON><PERSON>", "content": "<label><PERSON><PERSON>al adalah harga yang akan dibayar oleh pembeli dan akan tampil pada menu di kasir</label>"}, "productDimension": {"title": "<PERSON><PERSON><PERSON>", "content": "Atur dimensi untuk produk yang dapat dikirim menggunakan jasa ekspedisi"}, "variantProduct": {"title": "<PERSON><PERSON>", "content": "<label>Aktifkan jika produk memiliki variasi seperti warna, rasa, ukuran, dll</label><label>(Fitur ini hanya tersedia di paket langganan <b>Prime</b>)</label>"}, "addSelectVariant": {"title": "<PERSON><PERSON> Pilihan V<PERSON>", "content": "Klik Tambah jika varian memiliki lebih dari 1 pilihan"}, "variantList": {"title": "<PERSON><PERSON><PERSON> varian", "content": "<PERSON><PERSON> harga, SKU dan status pilihan varian pada daftar varian"}, "extraSection": {"title": "Ekstra", "content": "<label><PERSON><PERSON><PERSON> fitur ini jika ada item lain yang dapat ditambahkan (ekstra garpu, gula, saus, dll)</label>", "productLabel": "Produk", "variantLabel": "<PERSON><PERSON>", "extraLabel": "Ekstra", "colorLabel": "<PERSON><PERSON>", "whiteLabel": "<PERSON><PERSON>", "sauceLabel": "Saus"}, "recipeSection": {"title": "<PERSON><PERSON><PERSON>", "content": "Resep digunakan untuk produk yang diracik dan tidak dapat aktif dengan monitor persediaan"}, "marketplaceStep": {"title": "Toko Online", "content": "Jika produk dijual di Toko Online, aktifkan opsi ini untuk mengintegrasikan seluruh pesanan yang masuk secara langsung"}}, "perOutletSettingForm": {"title": "<PERSON><PERSON><PERSON><PERSON>", "step": {"priceUnit": "<PERSON><PERSON> dan <PERSON>", "variant": "<PERSON><PERSON>", "recipe": "<PERSON><PERSON><PERSON>", "nextOption": "Opsi Lanjutan", "wholesalePrice": "<PERSON><PERSON>", "extra": "Ekstra"}, "searchOutletPlaceholder": "<PERSON><PERSON>...", "outletGroup": "Grup Outlet", "currentData": "Data saat ini", "outletGroupBanner": {"title": "Baru!", "description": "Buat Grup Outlet untuk mempermudah pengaturan multi-outlet secara terpusat", "linkText": "Atur Grup Outlet"}, "manualRecipeBanner": {"description": "Data resep diambil dari detail produk. Gunakan menu 'Ubah Produk' jika ingin memilih Resep <PERSON> Bahan Baku"}, "editVariantBanner": {"description": "Tipe varian diambil dari detail produk. <PERSON><PERSON><PERSON> menu 'Ubah Produk' jika ingin mengubah tipe varian"}, "notSellabelProduct": "Produk Tidak Dijual", "maxChangeablePrice": "<PERSON><PERSON><PERSON><PERSON>", "pleaseFillRecipe": "<PERSON><PERSON> le<PERSON> resep", "noRecipe": "<PERSON><PERSON>", "perVariantRecipe": "<PERSON><PERSON><PERSON>", "selectRecipe": "<PERSON><PERSON><PERSON>", "equalized": "Disama<PERSON>", "selectReferenceRecipe": "<PERSON><PERSON><PERSON> resep yang akan dijadikan acuan", "recipeMaster": "Master <PERSON><PERSON><PERSON>", "recipeMasterCode": "KODE MASTER RESEP", "recipeMasterName": "NAMA MASTER RESEP", "materialCount": "JUMLAH BAHAN"}}