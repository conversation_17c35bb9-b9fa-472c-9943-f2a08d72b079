export const setContentHeaderFilterCalendar = payload => ({
    type: 'SET_CONTENT_HEADER_FILTER_CALENDAR',
    payload,
});

export const setContentHeaderCalendar = payload => ({
    type: 'SET_CONTENT_HEADER_CALENDAR',
    payload,
});

export const setContentHeaderButtons = buttons => ({
    type: 'SET_CONTENT_HEADER_BUTTONS',
    buttons,
});

export const setContentHeaderFloat = payload => ({
    type: 'SET_CONTENT_HEADER_FLOAT',
    payload,
});

export const setWindowHeight = height => ({
    type: 'SET_WINDOW_HEIGHT',
    height,
});

export const setContentOverlay = state => ({
    type: 'SET_OVERLAY_BLOCK',
    state,
});

export const setLoaderState = state => ({
    type: 'SET_LOADING_BLOCK',
    state,
});

export const setFlashMessage = payload => ({
    type: 'SET_FLASH_MESSAGE',
    payload,
});


export const setShowNotifSupport = payload => ({
    type: 'SET_SHOW_NOTIF_SUPPORT',
    payload,
});

export const setSupportNeeded = payload => ({
    type: 'SET_SUPPORT_NEEDED',
    payload,
});

export const setCustomBreadCrumbs = payload => ({
    type: 'SET_CUSTOM_BREADCRUMBS',
    payload,
});
