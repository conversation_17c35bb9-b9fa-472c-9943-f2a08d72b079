import { getCashBookMutasiList, postCashBookMutasi, getCashBookMutasiDetail } from '../data/cashbook';

const cashBookListRequest = () => ({
    type: 'CASH_BOOK_LIST::GET_REQUEST',
    isFetching: true,
    status: false,
    message: '',
});
const cashBookListSuccess = (status, message, payload) => ({
    type: 'CASH_BOOK_LIST::GET_SUCCESS',
    isFetching: false,
    status,
    message,
    payload,
});
const cashBookListFailure = (status, message) => ({
    type: 'CASH_BOOK_LIST::GET_FAILURE',
    isFetching: false,
    status,
    message,
});

const cashBookMutasiListRequest = () => ({
    type: 'CASH_BOOK_MUTASI_LIST::GET_REQUEST',
    isFetching: true,
    status: false,
    message: '',
});
const cashBookMutasiListSuccess = (status, message, payload) => ({
    type: 'CASH_BOOK_MUTASI_LIST::GET_SUCCESS',
    isFetching: false,
    status,
    message,
    payload,
});
const cashBookMutasiListFailure = (status, message) => ({
    type: 'CASH_BOOK_MUTASI_LIST::GET_FAILURE',
    isFetching: false,
    status,
    message,
});


export const fetchCashBookMutasiList = (payload) => (dispatch) => {
    dispatch(cashBookMutasiListRequest());

    return getCashBookMutasiList(payload).then(res => {
        if(!res.status){
            dispatch(cashBookMutasiListFailure(res.status, res.msg));
        }
        dispatch(cashBookMutasiListSuccess(res.status, res.msg, res.data));
    },
    err => {
        dispatch(cashBookMutasiListFailure(false, err.message));
    })
};

const cashBookMutasiPostRequest = () => ({
    type: 'CASH_BOOK_MUTASI::POST_REQUEST',
    isPosting: true,
    status: false,
    message: '',
});

const cashBookMutasiPostSuccess = (status, message, payload) => ({
    type: 'CASH_BOOK_MUTASI::POST_SUCCESS',
    isPosting: false,
    status,
    message,
    payload,
});

const cashBookMutasiPostFailure = (status, message) => ({
    type: 'CASH_BOOK_MUTASI::POST_FAILURE',
    isPosting: false,
    status,
    message,
});

export const createCashBookMutasi = (payload) => (dispatch) => {
    dispatch(cashBookMutasiPostRequest());

    return postCashBookMutasi(payload).then(res => {
        if(!res.status){
            dispatch(cashBookMutasiPostFailure(res.status, res.msg));
        }
        dispatch(cashBookMutasiPostSuccess(res.status, res.msg, res.data));
    },
    err => {
        dispatch(cashBookMutasiPostFailure(false, err.message));
    })
};

const cashBookMutasiDetailRequest = () => ({
    type: 'CASH_BOOK_MUTASI_DETAIL::GET_REQUEST',
    isFetching: true,
    status: false,
    message: '',
});
const cashBookMutasiDetailSuccess = (status, message, payload) => ({
    type: 'CASH_BOOK_MUTASI_DETAIL::GET_SUCCESS',
    isFetching: false,
    status,
    message,
    payload,
});
const cashBookMutasiDetailFailure = (status, message) => ({
    type: 'CASH_BOOK_MUTASI_DETAIL::GET_FAILURE',
    isFetching: false,
    status,
    message,
});

export const fetchCashBookMutasiDetail = (payload) => (dispatch) => {
    dispatch(cashBookMutasiDetailRequest());

    return getCashBookMutasiDetail(payload).then(res => {
        if(!res.status){
            dispatch(cashBookMutasiDetailFailure(res.status, res.msg));
        }
        dispatch(cashBookMutasiDetailSuccess(res.status, res.msg, res.data));
    },
    err => {
        dispatch(cashBookMutasiDetailFailure(false, err.message));
    })
};
