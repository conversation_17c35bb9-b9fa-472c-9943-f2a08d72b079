import { getHelp } from '../data/help';

const helpRequest = () => ({
		type: 'HELP::GET_REQUEST',
		isFetching: true,
		status: false,
        message: '',
	});
const helpSuccess = (status, message, payload, helpType) => ({
		type: 'HELP::GET_SUCCESS',
		isFetching: false,
		status,
		message,
        payload,
        helpType,
	});
const helpFailure = (status, message, helpType) => ({
		type: 'HELP::GET_FAILURE',
		isFetching: false,
		status,
        message,
	});

export const fetchHelp = (payload, helpType) => (dispatch) => {
		dispatch(helpRequest());

        return getHelp(payload).then(res => {
            if(!res.status){
                dispatch(helpFailure(res.status, res.msg));
            }
			dispatch(helpSuccess(res.status, res.msg, res.data, helpType));
			return res
        },
        err => {
            dispatch(helpFailure(false, err.message));
			return err
        })
	};
