import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { SketchPicker } from "react-color";

import "./style.css";

const ColorPicker = ({ defaultColor, onHandleChange }) => {
    const [open, setOpen] = useState(false);
    const [color, setColor] = useState(defaultColor);

    const handleShowPallete = (flag) => setOpen(flag);

    const handleChange = (temp) => setColor(temp.hex);

    useEffect(() => {
        onHandleChange(color);
    }, [color]);

    useEffect(() => {
        setColor(defaultColor);
    }, [defaultColor]);
    

    return (
        <div>
            <div className="swatch" onClick={() => handleShowPallete(!open)}>
                <input className="color" style={{ background: color }}></input>
                <span className="color-code">{color}</span>
            </div>

            {open && (
                <div className="over">
                    <div
                        className="cover"
                        onClick={() => handleShowPallete(false)}
                    />
                    <SketchPicker color={color} onChange={handleChange} />
                </div>
            )}
        </div>
    );
};

ColorPicker.propTypes = {
    defaultColor: PropTypes.string,
    onHandleChange: PropTypes.func,
};

ColorPicker.defaultProps = {
    defaultColor: "#04C99E",
    onHandleChange: () => {},
};
export default ColorPicker;
