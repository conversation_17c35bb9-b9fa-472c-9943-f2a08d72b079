import React from 'react';
import PropTypes from 'prop-types';
// import shouldUpdate from 'recompose/shouldUpdate';
import { numSeparator } from '../../../utils/helper';

const PercentageColumn = (props) => {
    const { data, value } = props; // TODO: should be no need data props 
    let param = data;

    if (typeof value !== 'undefined') param = value;
    
    return (
        <div>{`${numSeparator(param) || param}%`}</div>
    );
};

// const checkPropsChange = (props, nextProps) => {
//     let pProps = props.data;
//     let nProps = nextProps.data;

//     if (props.value) pProps = props.value;
//     if (nextProps.value) nProps = nextProps.value;

//     return pProps !== nProps;
// };

// export default shouldUpdate(checkPropsChange)(PercentageColumn);

PercentageColumn.propTypes = {
    data: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number,
        PropTypes.arrayOf(PropTypes.shape({})),
    ]),
    value: PropTypes.oneOfType([
        PropTypes.number,
        PropTypes.string,
    ]),
};

PercentageColumn.defaultProps = {
    data: 0,
    value: 0,
};

// class PercentageColumn extends React.Component {
//     render() {
//         return <div>{`${this.props.data}%`}</div>;
//     }
// }

export default PercentageColumn;
