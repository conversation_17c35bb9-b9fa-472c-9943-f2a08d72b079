export const PROVIDER_TYPE = {
    SHOPEE: 3,
    <PERSON><PERSON><PERSON><PERSON><PERSON>K: 6,
    TOKOPEDIA: 1,
    GRAB: 2,
    GRABMART: 8,
    GOFOOD: 9,
    WEB_ORDER: 5,
    CONSUMER_APP: 10,
    SUPPLIES: 11,
};

export const SUBMISSION_TYPE = {
    INTEGRATED: 7,
    CANCELED: 0,
    SUSPENDED: 5,
};

export const OUTLET_STATUS = {
    INACTIVE: 0,
    ACTIVE: 1,
};

export const CALLBACK_TYPE = {
    AUTH: 'auth',
    UNAUTH: 'unauth',
};

export const PRODUCT_SYNC_TYPE = {
    MAPPED: 3,
    UNMAPPED: 1,
    ONQUEUE: 2,
    FAILED: 4,
};

export const STEP_INTEGRATION = {
    CHOOSE_OUTLET: 1,
    INTEGRATE: 2,
};

export const MarkTypeTokopediaMarketplace = [
    { text: 'Powered Merchant', value: 1 },
    { text: 'Official Merchant', value: 2 },
];

export const AUTH_STATUS = {
    VALID: 'valid',
    EXPIRED: 'expired',
};

export const MARKETPLACE_FOOD_ORDER_PROVIDER = [
    PROVIDER_TYPE.SHOPEE,
    PROVIDER_TYPE.TOKOPEDIA,
    PROVIDER_TYPE.GRAB,
    PROVIDER_TYPE.GRABMART,
    PROVIDER_TYPE.GOFOOD,
];
