import React from 'react';
import update from 'immutability-helper';
import CircleProgressBar from '../../../../CircleProgressBar';

import { catchError } from '../../../../../utils/helper';

import * as marketplaceApi from '../../../../../data/marketplace';
import { reformatAttributeDataV2 } from '../utils';
import { ATTRIBUTE_TYPE, INPUT_TYPE } from './enums';

import AttributeRow from './AttributeRow';

import '../style.less';

class Attribute extends React.Component {
    constructor(props) {
        super(props);

        this.state = {
            isLoading: false,
        };
    }

    componentDidMount = () => {
        const { categoryId } = this.props;

        if (categoryId) this.handleFetchAttribute(String(categoryId));
    }

    componentWillReceiveProps = (nextProps) => {
        const { categoryId } = this.props;

        if (String(categoryId) !== String(nextProps.categoryId)) this.handleFetchAttribute(String(nextProps.categoryId));
    }

    doShowingNotification = (val) => {
        const { addNotification } = this.props;
        addNotification(val);
    }

    startLoading = () => {
        this.setState({ isLoading: true }, () => {
            this.circleProgressBar.startProgress();
        });
    }

    stopLoading = () => {
        if (!this.circleProgressBar) return;

        this.circleProgressBar.stopProgress(() => {
            this.setState({ isLoading: false });
        });
    }

    finishLoading = () => {
        if (!this.circleProgressBar) return;

        const callbackAfterFinished = () => {
            this.setState({ isLoading: false });
        };

        this.circleProgressBar.finishProgress(callbackAfterFinished);
    }

    handleFetchAttribute = async (categoryId) => {
        const { outletId, onChange, isInitialFetching, i18n } = this.props;

        this.startLoading();

        let attributeResources = [];
        let attribute = {};

        try {
            const lang = i18n && i18n.language ? i18n.language : 'id';
            const res = await marketplaceApi.getAttribute(outletId, categoryId, { lang });

            attributeResources = reformatAttributeDataV2(res.data);
            attribute = this.createAttributeState(attributeResources);

            this.finishLoading();
        } catch (e) {
            this.doShowingNotification({
                title: t('baseProductSetting.toast.errorGetAttributeData', 'Gagal Mengambil Data Atribut'),
                message: catchError(e),
                level: 'error',
            });
            this.stopLoading();
        } finally {
            if (isInitialFetching) onChange('initializeAttributeData', { attribute, attributeResources });
            if (!isInitialFetching) {
                onChange('attributeResources', attributeResources);
                onChange('attribute', attribute);
            }
        }
    }

    createAttributeState = (attributeResources) => {
        const { data } = this.props;
        const { attribute } = data;
        let retval = {};

        attributeResources.forEach((x) => {
            let newValue = '';

            if (Array.isArray(attribute)) {
                const found = attribute.find(attr => (String(attr.id) === String(x.id)));
                if (found) newValue = found.value;
                // perlu proses tambahan untuk attribute combobox
                if (found && x.input_type === INPUT_TYPE.COMBO_BOX) {
                    let temp = found.value;
                    temp = temp.replaceAll(', ', '__');
                    const arrTemp = temp.split(',').map(opt => opt.replaceAll('__', ', '));
                    temp = arrTemp.join(';');
                    newValue = temp;
                }

                if (found && x.attribute_type === ATTRIBUTE_TYPE.FLOAT_TYPE) {
                    newValue = {
                        value: found.value,
                        value_unit: found.value_unit,
                    };
                }

                if (found && x.input_type === INPUT_TYPE.DROP_DOWN) {
                    newValue = String(found.value_id);
                }
            }

            if (!Array.isArray(attribute) && x.attribute_type === ATTRIBUTE_TYPE.FLOAT_TYPE) {
                newValue = {
                    value: '',
                    value_unit: x.attribute_unit[0],
                };
            }

            retval = { ...retval, [x.id]: newValue };
        });

        return retval;
    };

    handleChangeAttribute = async (type, val, e) => {
        if (e) {
            const { validateInput } = this.props;
            const { target } = e;

            validateInput(target);
        }

        const { data: { attribute }, onChange } = this.props;
        const newAttribute = update(attribute, {
            [type]: {
                $set: val,
            },
        });

        onChange('attribute', newAttribute);
    }

    renderAttributeComponents = () => {
        const { data, data: { attribute, attributeResources }, t } = this.props;

        return attributeResources.map((x) => {
            const attributeRowProps = {
                data,
                resourceData: x,
                attribute,
                onChange: this.handleChangeAttribute,
                t,
            };

            return (<AttributeRow key={x.id} {...attributeRowProps} />);
        });
    }

    renderLoading = () => {
        const { t } = this.props;
        return (
            <div className="sidepopup-data-loader mt-sm">
                <div className="row">
                    <div className="col-xs-2">
                        <CircleProgressBar
                            ref={(c) => {
                                this.circleProgressBar = c;
                            }}
                        />
                    </div>
                    <div className="col-xs-10">
                        <h5>{t('baseProductSetting.toast.loadingAttributeData', 'Sedang memuat data atribut ...')}</h5>
                    </div>
                </div>
            </div>
        )
    }

    render() {
        const { data } = this.props;
        const { attributeResources } = data;
        const { isLoading } = this.state;

        if (isLoading) return this.renderLoading();

        if (attributeResources.length === 0) return null;

        return (
            <div>{this.renderAttributeComponents()}</div>
        );
    }
}

export default Attribute;
