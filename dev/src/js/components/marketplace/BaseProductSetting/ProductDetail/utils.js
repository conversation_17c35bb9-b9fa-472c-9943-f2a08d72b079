import PriceColumn from '~/components/table/components/PriceColumn';
import BreakAllTextColumn from '~/components/table/components/BreakAllTextColumn';

import {
  COMPONENT, SALES_PRICE, SALES_STOCK, PRODUCT_CONDITION,
  PRE_ORDER, PRE_ORDER_VALIDATION, SALES_PRICE_MARK,
  WEIGHT_UNIT,
} from './enums';
import { PROVIDER_TYPE } from '../../enums';
import { generateVariantItemId } from '../../BaseProductDetail/ProductVariant/utils';

export const calculatePrice = (majooProductPrice, priceValue, priceType, priceSubType) => {
  // markup harga tipe nominal
  if (priceType === SALES_PRICE.MARKUP && priceSubType === SALES_PRICE_MARK.NOMINAL) {
    return ((+majooProductPrice) + (+priceValue));
  }

  // markup harga tipe persen
  if (priceType === SALES_PRICE.MARKUP && priceSubType === SALES_PRICE_MARK.PERCENT) {
    const markupNominal = ((priceValue / 100) * majooProductPrice);
    return (+majooProductPrice) + markupNominal;
  }

  // markdown harga tipe nominal
  if (priceType === SALES_PRICE.MARKDOWN && priceSubType === SALES_PRICE_MARK.NOMINAL) {
    return ((+majooProductPrice) - (+priceValue));
  }

  // markdown harga tipe persen
  if (priceType === SALES_PRICE.MARKDOWN && priceSubType === SALES_PRICE_MARK.PERCENT) {
    const markdownNominal = ((priceValue / 100) * majooProductPrice);
    return (+majooProductPrice) - markdownNominal;
  }

  if (priceType === SALES_PRICE.MANUAL) {
    return +priceValue;
  }

  return 0;
};

export const assignProductColumns = t => [
  {
    columnName: 'sku',
    displayName: t('label.sku', 'SKU', { ns: 'translation' }),
    width: '100%',
    customComponent: BreakAllTextColumn,
  },
  {
    columnName: 'name',
    displayName: t('label.product', 'Produk', { ns: 'translation' }),
    width: '100%',
    customComponent: BreakAllTextColumn,
  },
  {
    columnName: 'category_item_name',
    displayName: t('label.category', 'Kategori', { ns: 'translation' }),
    width: '100%',
    customComponent: BreakAllTextColumn,
  },
  {
    columnName: 'satuan',
    displayName: t('label.unit', 'Satuan', { ns: 'translation' }),
    width: '100%',
    customComponent: BreakAllTextColumn,
  },
  {
    columnName: 'item_harga_jual',
    displayName: t('label.price', 'Harga', { ns: 'translation' }),
    width: '100%',
    customComponent: PriceColumn,
  },
];

export const createStaticData = (type, disabled = false, t) => {
  let retval = [];

  switch (type) {
    case COMPONENT.SWITCH_BOX_SALES_PRICE:
      retval = [
        { text: t('baseProductDetail.salesInformation.input.itemPrice.tab.setManually', 'Tentukan Manual'), value: SALES_PRICE.MANUAL, disabled },
        { text: t('baseProductDetail.salesInformation.input.itemPrice.tab.markupPrice', 'Markup Harga'), value: SALES_PRICE.MARKUP, disabled },
        { text: t('baseProductDetail.salesInformation.input.itemPrice.tab.markdownPrice', 'Markdown Harga'), value: SALES_PRICE.MARKDOWN, disabled },
      ];
      break;
    case COMPONENT.SWITCH_BOX_SALES_STOCK:
      retval = [
        { text: t('baseProductDetail.salesInformation.input.stock.tab.autoUpdateStock', 'Perbarui Stok Otomatis'), value: SALES_STOCK.AUTOMATIC, disabled },
        { text: t('baseProductDetail.salesInformation.input.stock.tab.minimumStock', 'Minimum Stok'), value: SALES_STOCK.MANUAL, disabled },
      ];
      break;
    case COMPONENT.SWITCH_BOX_SALES_PRICE_TYPE:
      retval = [
        { text: '%', value: SALES_PRICE_MARK.PERCENT, disabled },
        { text: 'Rp', value: SALES_PRICE_MARK.NOMINAL, disabled },
      ];
      break;
    case COMPONENT.SWITCH_BOX_CONDITION:
      retval = [
        { text: t('baseProductDetail.other.input.condition.tab.new', 'Baru'), value: PRODUCT_CONDITION.NEW, disabled },
        { text: t('baseProductDetail.other.input.condition.tab.second', 'Pernah Dipakai'), value: PRODUCT_CONDITION.USED, disabled },
      ];
      break;
    case COMPONENT.SWITCH_BOX_PREORDER:
      retval = [
        { text: t('label.no', 'Tidak', { ns: 'translation' }), value: PRE_ORDER.NO, disabled },
        { text: t('label.yes', 'Ya', { ns: 'translation' }), value: PRE_ORDER.YES, disabled },
      ];
      break;
    case COMPONENT.SWITCH_BOX_WEIGHT_UNIT:
      retval = [
        { text: 'Kg', value: WEIGHT_UNIT.KILOGRAM, disabled },
        { text: 'Gram', value: WEIGHT_UNIT.GRAM, disabled },
      ];
      break;
    case COMPONENT.SWITCH_BOX_INSURANCE:
      retval = [
        { text: t('baseProductDetail.weightShipping.input.insurance.tab.optional', 'Opsional'), value: false, disabled },
        { text: t('baseProductDetail.weightShipping.input.insurance.tab.yes', 'Ya'), value: true, disabled },
      ];
      break;
    default:
      retval = [];
  }

  return retval;
};

const shippingResources = [
  {
    key: 'JNE_reguler_cashless',
    text: 'JNE Reguler (Cashless)',
  },
  {
    key: 'JNT_express',
    text: 'J&T Express',
  },
  {
    key: 'pos_kilat_khusus',
    text: 'Pos Kilat Khusus',
  },
  {
    key: 'si_cepat_reg',
    text: 'SiCepat REG',
  },
];

const productInformationState = (data, provider) => {
  let productUnit = {};

  if (data) {
    productUnit = {
      unit_no: data.unit.no,
      unit_name: data.unit.name,
      unit_conversion: data.unit.conversion,
    };
  }

  let categoryId = '';
  let categoryCode = '';
  let storeFrontId = '';
  let storeFrontName = '';
  let aggregatorCategoryId;

  if (data && data.departments && data.departments.length > 0) {
    if (provider !== PROVIDER_TYPE.TOKOPEDIA) {
      categoryId = data.departments[0].provider_department_id;
      categoryCode = data.departments[0].id;
    }

    if (provider === PROVIDER_TYPE.TOKOPEDIA && data.departments && data.departments.length > 0) {
      categoryId = data.departments[0].provider_department_id;
      if (data.category) {
        const storeFront = data.category;
        storeFrontId = storeFront.provider_category_id;
        storeFrontName = storeFront.name;
        aggregatorCategoryId = storeFront.id;
      }
    }
  }

  let attribute = {};

  if (data && data.attributes) (attribute = data.attributes);

  let description = '';

  if (data && data.description) ({ description } = data);

  return {
    itemNo: data ? data.core.no : '',
    itemId: data ? data.core.id : '',
    staticData: {
      productName: data ? data.core.name : '',
    },
    readOnlyProductName: data ? data.core.name : '',
    isCogs: true,
    name: data ? data.name : '',
    description,
    categoryId,
    categoryCode,
    majooCategoryId: 0,
    majooCategoryName: '',
    readOnlyDepartment: data ? data.departments : null,
    readOnlyProductPrice: data ? data.core.price : '',
    productUnit,
    attribute,
    attributeResources: [],
    majooStock: data ? data.core.stock : 0,
    majooSku: data ? data.core.sku : '',
    storeFrontId,
    storeFrontName,
    aggregatorCategoryId,
    isConnected: data ? !!data.core.id : true,
  };
};

const otherInformationState = (data) => {
  let isPreOrder = false;
  let preOrderPeriod = 0;
  const specialTypes = data ? data.special_type || [] : [];

  if (data) {
    isPreOrder = data.is_preorder;
  }

  let conditionType = PRODUCT_CONDITION.NEW;

  if (data && data.condition) {
    conditionType = data.condition;
  }

  if (data && data.days_to_ship) {
    preOrderPeriod = data.days_to_ship ? Number(data.days_to_ship) : 2;
  }

  return {
    conditionType,
    sku: data ? data.sku : '',
    isPreOrder,
    preOrderPeriod,
    minimumOrderQty: data ? data.minimum_selling_qty : 1,
    specialTypes,
  };
};

const salesInformationState = (data) => {
  let salesPrice = {
    type: SALES_PRICE_MARK.NOMINAL,
    value: data ? data.price : 0,
    setupAmount: 0,
  };

  let priceSubType = SALES_PRICE_MARK.NOMINAL;

  if (data && String(data.price_setup.type) !== SALES_PRICE.UNDEFINED && String(data.price_setup.type) !== SALES_PRICE.MANUAL) {
    priceSubType = data.price_setup.sub_type;
    salesPrice = {
      type: priceSubType,
      value: data.price || 0,
      setupAmount: data.price_setup.amount || 0,
    };
  } else if (data && String(data.price_setup.type) === SALES_PRICE.MANUAL) {
    salesPrice = {
      type: priceSubType,
      value: data.price,
      setupAmount: 0,
    };
  }

  const stockQty = data ? data.stock : 0;
  let salesStockType = SALES_STOCK.AUTOMATIC,
    minStock = 10;

  // if (data && !data.minimum_stock) salesStockType = SALES_STOCK.AUTOMATIC;
  if (data && data.minimum_stock) {
    salesStockType = SALES_STOCK.MANUAL;
    minStock = data.minimum_stock;
  }

  let salesPriceType = SALES_PRICE.MANUAL;

  if (data && String(data.price_setup.type) !== SALES_PRICE.UNDEFINED) {
    salesPriceType = String(data.price_setup.type);
  }

  let isWholesaleActive = false;
  let wholesaleList = [];
  if (data && data.wholesale) {
    isWholesaleActive = true;
    wholesaleList = data.wholesale;
  }

  let weight = 0;
  if (data && data.weight) weight = data.weight.value;

  return {
    minStock,
    salesStockType,
    stockQty,
    weight,
    salesPriceType,
    salesPrice,
    shopeePrice: {
      price: data ? data.price : 0,
      price_sub_type_amount: data ? data.price_setup.amount : 0,
      price_sub_type: priceSubType,
    },
    isWholesaleActive,
    wholesaleList,
  };
};

const shippingInformationState = (data) => {
  const logistic = {};

  if (data && data.logistics && Array.isArray(data.logistics)) {
    data.logistics.forEach((eachLogistic) => {
      logistic[eachLogistic.id] = eachLogistic.enabled;
    });
  }

  const dimensions = data ? data.dimensions : {
    height: 0,
    length: 0,
    width: 0,
  };

  let weightUnit = WEIGHT_UNIT.GRAM;
  if (data && data.weight) weightUnit = data.weight.unit;

  return {
    weightUnit,
    shippingResources: [],
    shipping: logistic,
    productInsurance: data ? data.is_assurance : true,
    dimensions,
  };
};

export const productVariantState = (data = null, { provider, variantTypeData }) => {
  const variants = [];
  let tableVariantItems = [];

  if (data && data.variant_types) {
    data.variant_types.forEach((varType) => {
      const variant = { type: null, items: [] };

      if (provider === PROVIDER_TYPE.TOKOPEDIA) {
        const ref = variantTypeData.find(varTypeDat => String(varTypeDat.name).toLowerCase() === String(varType.name).toLowerCase());
        
        variant.type = !ref.is_custom ? ref.id : varType.name || 0;
        variant.items = varType.attributes.map(attr => ({
          id: attr.id,
          text: attr.name,
        }));
      } else {
        variant.type = varType.name;
        variant.items = varType.attributes.map(attr => ({
          id: generateVariantItemId(),
          text: attr.name,
        }));
      }

      variant.original = varType;
      variants.push(variant);
    });
  }

  if (data && data.variations) {
    tableVariantItems = data.variations.map((x) => {
      const variantTypes = x.variant_types.map(variantType => ({
        attribute_id: variantType.type_attribute_id,
        attribute_name: variantType.value,
        variant_id: variantType.id,
        variant_type_id: variantType.type_id,
        variant_type_name: variantType.attribute_name,
      }));
      const variantName = variantTypes.reduce((prev, curr, index) => `${prev}${index > 0 ? ' / ' : ''}${curr.attribute_name}`, '');

      let majooProduct = null;

      if (x.core.name) {
        const splits = String(x.core.name).split('-');

        majooProduct = {
          id: !x.core.id ? '' : x.core.id,
          name: splits.length > 0 ? splits[0] : '',
          item_no: !x.core.no ? '' : x.core.no,
          sku: !x.core.sku ? '' : x.core.sku,
          harga: !x.core.price ? 0 : x.core.price,
          formatted_name: x.core.name,
        };
      }

      return {
        ...x,
        name: variantName,
        image: x.image,
        majoo_product: majooProduct,
        variant_types: variantTypes,
        status: x.core.active_status,
      };
    });
  }

  return {
    variantTypeData,
    variants,
    tableVariantItems,
  };
};

export const createFormData = (data = null, { provider, variantTypeData }) => ({
  id: data ? data.id : '',
  images: data ? data.images : [],
  isMapped: data ? Boolean(data.core.id) : false,
  videoUrl: data ? data.video : '',
  isPromo: data ? data.disable_price : false,
  ...productVariantState(data, { provider, variantTypeData }),
  ...productInformationState(data, provider),
  ...salesInformationState(data),
  ...otherInformationState(data),
  ...shippingInformationState(data),
  product_id: data ? data.product_id : undefined,
  provider_item_id: data ? data.provider_item_id : undefined,
  sku_id: data ? data.sku_id : undefined,
});

export const handlePreOrderValidation = (type, val) => {
  const parsedVal = JSON.parse(val);
  let isError = false;

  if (!parsedVal.isPreOrder) return false;

  switch (type) {
    case PRE_ORDER_VALIDATION.MIN_DAYS:
      isError = (parsedVal.preOrderPeriod < 7);
      break;
    case PRE_ORDER_VALIDATION.MAX_DAYS:
      isError = (parsedVal.preOrderPeriod > 30);
      break;
    default:
      isError = false;
  }

  return isError;
};

const isYoutubeURLValid = (val) => {
  if (String(val).toLowerCase().includes('https://www.youtube.com')) return true;
  if (String(val).toLowerCase().includes('https://youtube.com')) return true;
  if (String(val).toLowerCase().includes('https://youtu.be')) return true;
  if (val === '') return true;
  return false;
};

export const handleRequiredInputValidation = (data, val, minLength = 1) => String(data.readOnlyProductName).length > 0 && String(val).length < minLength;
export const handleMaxInputValidation = (data, val, maxLenght) => String(data.readOnlyProductName).length > 0 && String(val).length >= maxLenght + 1;

export const handleImagesValidation = (data, val) => {
  const parsedData = JSON.parse(val);

  return String(data.readOnlyProductName).length > 0 && parsedData.length === 0;
};

export const handleImagesValidationMaxLength = (data, val, maxLength) => {
  const parsedData = JSON.parse(val);

  return String(data.readOnlyProductName).length > 0 && parsedData.length > maxLength;
};

export const handleShippingValidation = (data, val) => {
  const parsedData = JSON.parse(val);
  const keys = Object.keys(parsedData);
  const enabledFound = keys.find(x => (parsedData[x]));

  return String(data.readOnlyProductName).length > 0 && !enabledFound;
};

export const handleMinimumStockValidation = (data, val) => {
  if (data.salesStockType === SALES_STOCK.AUTOMATIC) return false;

  return String(data.readOnlyProductName).length > 0 && +val < 10;
};

export const handleYoutubeURLValidation = (data, val) => String(data.readOnlyProductName).length > 0 && !isYoutubeURLValid(val);

export const handleTableVariantValidationPrice = (val) => {
  const parsedData = JSON.parse(val);

  if (parsedData.length === 0) return false;

  const isPriceZero = !!parsedData.find(x => !(+x.price));

  return isPriceZero;
};

export const handleTableVariantValidationSKU = (val) => {
  const parsedData = JSON.parse(val);

  if (parsedData.length === 0) return false;

  const isSKUEmpty = !!parsedData.find(x => !(x.sku));

  return isSKUEmpty;
};

export const handleTableVariantValidationImage = (val) => {
  const parsedData = JSON.parse(val);

  if (parsedData.length === 0) return false;

  const isImageEmpty = !!parsedData.find(x => !(x.image));

  return isImageEmpty;
};

export const reformatAttributeDataV2 = rawAttributes => rawAttributes.map(
  (attribute) => {
    const { value } = attribute;

    return ({
      id: String(value.attribute_id),
      name: value.display_attribute_name || '',
      original_name: value.original_attribute_name || '',
      is_mandatory: value.is_mandatory,
      attribute_type: value.input_validation_type,
      input_type: value.input_type,
      attribute_unit: value.attribute_unit,

      options: value.attribute_value_list.map(attributeValue => ({
        id: attributeValue.value_id,
        name: attributeValue.original_value_name,
        label: attributeValue.display_value_name,
        value: attributeValue.original_value_name,
        value_unit: attributeValue.value_unit,
      })),
    });
  },
);
