.steps {
  display: flex;
  align-items: flex-start;
  width: 100%;

  .steps-step {
    flex: 1;

    .steps-step-indicator {
      display: flex;
      align-items: center;
      width: 100%;
    }

    &:first-child .steps-line {
      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
    }

    &:last-child .steps-line {
      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
    }

    .steps-title {
      margin-top: 12px;

      .steps-number {
        width: 20px;
        height: 20px;
        background: #fff;
        border: 1px solid #B0BEC5;
        border-radius: 50%;
        color: #000;
        text-align: center;
        display: inline-block;
        margin-right: 4px;
        line-height: 18px;

        &.steps-number__active {
          color: #fff;
          background: #04C99E;
          border: 1px solid #04C99E;
        }
      }
    }
  }

  .steps-line {
    width: 100%;
    height: 4px;
    background: #ECEFF1;

    &.steps-line__complete {
      background: #04C99E;
    }
  }
}
