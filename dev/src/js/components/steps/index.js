import React from 'react';
import PropTypes from 'prop-types';

import './steps.less';

const getActiveLine = (activeIndex, index) => {
  if (activeIndex === index) {
    return 'steps-line__active';
  }
  if (activeIndex > index) {
    return 'steps-line__complete';
  }
  return '';
};

const Steps = ({
  activeIndex, steps, className, onChange,
}) => (
  <div className={`steps ${className}`}>
    {steps.map((step, index) => (
      <div key={step} className="steps-step" role="button" aria-hidden="true" onClick={() => onChange(index + 1)}>
        <div className="steps-step-indicator">
          <div className={`steps-line ${getActiveLine(activeIndex, index)}`} />
        </div>
        <div className="steps-title">
          <div className={`steps-number ${activeIndex === index + 1 ? 'steps-number__active' : ''}`}>
            {index + 1}
          </div>
          {step}
        </div>
      </div>
    ))}
  </div>
);

Steps.propTypes = {
  activeIndex: PropTypes.number.isRequired,
  steps: PropTypes.arrayOf(PropTypes.string).isRequired,
  className: PropTypes.string,
  onChange: PropTypes.func,
};

Steps.defaultProps = {
  className: '',
  onChange: () => {},
};

export default Steps;
