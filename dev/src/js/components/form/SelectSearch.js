import React from 'react';
import PropTypes from 'prop-types';
import Select from "react-select";
import 'react-select/dist/react-select.css';

const Label = (props) => {
    const {
        label, required,
    } = props;

    if (!label) return null;

    return (
        <label className="control-label">
            {label}
            {' '}
            {required && <span className="text-red">*</span>}
        </label>
    );
};

class InputSelectSearch extends React.Component {
    static propTypes = {
        label: PropTypes.string,
        classes: PropTypes.string,
        placeholder: PropTypes.string,
        data: PropTypes.arrayOf(PropTypes.oneOfType([
            PropTypes.string,
            PropTypes.number,
            PropTypes.array,
            PropTypes.object,
        ])),
        changeEvent: PropTypes.func,
        disabled: PropTypes.oneOfType([
            PropTypes.bool,
            PropTypes.string,
        ]),
        name: PropTypes.string,
        value: PropTypes.oneOfType([
            PropTypes.string,
            PropTypes.number,
        ]).isRequired,
        styleSearch: PropTypes.bool,
        id: PropTypes.string,
        required: PropTypes.oneOfType([
            PropTypes.bool,
            PropTypes.string,
        ]),
        prependAddon: PropTypes.string,
        appendAddon: PropTypes.string,
        handleFetch: PropTypes.func,
    }

    static defaultProps = {
        label: undefined,
        classes: undefined,
        placeholder: undefined,
        data: [],
        changeEvent: () => {},
        disabled: undefined,
        name: undefined,
        styleSearch: false,
        id: undefined,
        required: undefined,
        prependAddon: undefined,
        appendAddon: undefined,
        handleFetch: () => {},
    }

    shouldComponentUpdate = ({ data, value, disabled }) => {
        const { data: pData, value: pValue, disabled: pDisabled } = this.props;

        if (pData !== data || pValue !== value || pDisabled !== disabled) {
            return true;
        }

        return false;
    }

    handleChange = (data) => {
        const { changeEvent } = this.props;

        changeEvent(data.value);
    }

    loadOptions = async (inputValue) => {
        const { handleFetch } = this.props;

        const res = await handleFetch({ search: inputValue });

        return Promise.resolve({ options: res });
      };

    render() {
        let style = {};
        let formGroupClass = 'form-group';
        let formControlClass = 'form-control style-select';
        const {
            label, styleSearch, value, data, classes, name, disabled, placeholder, id, required, 
            prependAddon, appendAddon,
        } = this.props;

        if (classes) {
            formGroupClass = `${formGroupClass} ${classes}`;
        }

        if (disabled) {
            formControlClass = `${formControlClass} isDisabled`;
        }

        if ((!value || value === '') && data.filter(item => Object.values(item)[0] === '').length <= 0) {
            formControlClass = `${formControlClass} isEmpty`;
        }

        if (styleSearch) {
            style = { height: '46px' };
        }

        if (prependAddon || appendAddon) formControlClass = `${formControlClass} input-group`;

        return (
            <div className={formGroupClass}>
                <Label {...{ label, required }} />
                <div
                    className={`${formControlClass}`}
                    style={style}
                >
                    { prependAddon && <span className="input-group-addon">{prependAddon}</span> }
                    <Select.Async
                        name={name}
                        id={id}
                        value={value}
                        required={required}
                        onChange={this.handleChange}
                        disabled={disabled}
                        loadOptions={this.loadOptions}
                        placeholder={placeholder}
                    />
                    { appendAddon && <span className="input-group-addon append">{appendAddon}</span> }
                </div>
            </div>
        );
    }
}

export default InputSelectSearch;
