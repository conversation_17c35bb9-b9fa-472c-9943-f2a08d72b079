/**
 *
 */
import React, { Component } from 'react';
import PropTypes from 'prop-types';

export default class Tabs extends Component {
    static propTypes= {
        renderProps: PropTypes.func,
        headers: PropTypes.arrayOf(PropTypes.shape({
            title: PropTypes.node,
        })).isRequired,
        validateEvent: PropTypes.func,
    }

    static defaultProps = {
        renderProps: undefined,
        validateEvent: undefined,
    }

    state = {
        activeIndex: 0,
    }

    updateactiveIndex = async (index) => {
        const { activeIndex } = this.state;
        const { validateEvent } = this.props;

        if (
            index > activeIndex
            && validateEvent
        ) {
            const isFormValid = await validateEvent();
            if (!isFormValid) return;
        }

        this.setState({
            activeIndex: index,
        });
    }

    render() {
        const { activeIndex } = this.state;
        const { headers, renderProps } = this.props;

        return (
            <div>
                <div className="panel-heading borderless">
                    <ul className="nav nav-tabs custom-tabs plain-heading-tabs">
                        {headers.map((header, index) => (
                            <li key={`priv-tab-${header.title}`}>
                                <a
                                    className={index === activeIndex && 'active'}
                                    tabIndex="0"
                                    role="button"
                                    onClick={() => this.updateactiveIndex(index)}
                                    onKeyDown={() => this.updateactiveIndex(index)}
                                >
                                    {header.title}
                                </a>
                            </li>
                        ))}
                    </ul>
                </div>
                {renderProps({ activeIndex, gotoIndex: this.updateactiveIndex, length: headers.length })}
            </div>
        );
    }
}
