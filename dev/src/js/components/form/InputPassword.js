/**
 * Created by <PERSON><PERSON> on 05/12/2016.
 */
import React from 'react';
import PropTypes from 'prop-types';

class InputPassword extends React.Component {
    state = {
        show: false,
    };

    handleChange(event) {
        const { changeEvent, name } = this.props;
        changeEvent(event.target.value, event, name);
    }

    buttonChangeHandler() {
        this.setState(prevState => ({
            show: !prevState.show,
        }));
    }

    render() {
        const {
            label: controlLabel, value, classes, hasTooltip, name, placeholder, maxlength, disabled, readOnly, minLength, onPaste,
        } = this.props;
        const { show } = this.state;
        const tooltipText = 'Password minimal 8 karakter dan terdiri dari huruf, angka, dan simbol.';
        return (
            <div className="form-group">
                {controlLabel && <label className="control-label">{controlLabel}</label>}
                {hasTooltip
                    && (
                    <i
                        className="fa fa-question-circle"
                        aria-hidden="true"
                        style={{
                            marginLeft: '8px',
                            color: '#04C99E',
                        }}
                        title={tooltipText}
                    />
                    )
                }
                <div className="input-password">
                    <input
                        type={(show) ? 'text' : 'password'}
                        className={`form-control${classes ? ` ${classes}` : ''}`}
                        name={name}
                        onChange={e => this.handleChange(e)}
                        placeholder={placeholder}
                        value={value}
                        maxLength={maxlength}
                        minLength={minLength}
                        disabled={disabled}
                        autoComplete="new-password"
                        readOnly={readOnly}
                        onPaste={onPaste}
                    />
                    {!disabled
                        && (
                        <a className="show-toggle c-pointer" onClick={() => this.buttonChangeHandler()} role="presentation">
                            <span><i className={`ci ci-eye${show ? ' hide-state' : ''}`} /></span>
                        </a>
                        )
                    }
                </div>
            </div>
        );
    }
}

InputPassword.propTypes = {
    label: PropTypes.string,
    classes: PropTypes.string,
    placeholder: PropTypes.string,
    value: PropTypes.string,
    changeEvent: PropTypes.func,
    name: PropTypes.string,
    hasTooltip: PropTypes.bool,
    onPaste: PropTypes.func,
};

InputPassword.defaultProps = {
    label: '',
    classes: '',
    placeholder: '',
    value: '',
    changeEvent: () => { },
    name: undefined,
    hasTooltip: false,
    onPaste: () => {},
};

export default InputPassword;
