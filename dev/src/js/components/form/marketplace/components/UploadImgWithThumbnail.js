import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { uploadImageMarketplace } from '~/data/upload';
import ImageCropper from '../../ImageCropper';
import './UploadImgWithThumbnail.css';

// TODO: ADD UPLOAD INDICATOR - DONE
// TODO: ADD CROP Function - DONE

export default class UploadImgWithThumbnail extends PureComponent {
    static dataURItoBlob(dataURI) {
        // convert base64/URLEncoded data component to raw binary data held in a string
        let byteString;
        if (dataURI.split(',')[0].indexOf('base64') >= 0) {
            byteString = atob(dataURI.split(',')[1]);
        } else {
            byteString = unescape(dataURI.split(',')[1]);
        }

        const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];

        const ia = new Uint8Array(byteString.length);
        for (let i = 0; i < byteString.length; i += 1) {
            ia[i] = byteString.charCodeAt(i);
        }

        return new Blob([ia], { type: mimeString });
    }

    static propTypes = {
        value: PropTypes.shape({
            value: PropTypes.objectOf(PropTypes.object),
        }),
        deleteImageVal: PropTypes.func.isRequired,
    };

    constructor(props) {
        super(props);

        this.state = {
            image: null, // binary image data
            imageSrc: null,
            imageName: null,
            imageBase64: null,
            uploadIndicatorShow: false,
            cropperOpen: false,
        };
    }

    componentWillMount = () => {
        const { value } = this.props;
        this._setInitState(value);
    };

    componentWillReceiveProps(nextProps, nextContext) {
        const { value } = this.props;
        if (value !== nextProps.value) {
            this._setInitState(nextProps.value);
        }
    }

    _setInitState = (value) => {
        if (value) {
            this.setState({
                imageSrc: value.filepath,
                imageName: value.filename,
            });
        }
    };

    _changeHandler = (event) => {
        const file = event.target.files[0];

        if (file !== undefined) {
            this.setState(
                prevState => ({
                    ...prevState,
                    image: file,
                }),
                () => {
                    this.setupCroppingProcess();
                },
            );
        }
    };

    setupCroppingProcess = () => {
        const { image } = this.state;
        const oFReader = new FileReader();

        oFReader.readAsDataURL(image);

        oFReader.onload = async function readFileImage(oFREvent) {
            this.setState({
                imageBase64: oFREvent.target.result,
                cropperOpen: true,
            });
        }.bind(this);
    };

    dataURLtoFile = dataURI => fetch(dataURI)
            .then(res => res.arrayBuffer())
            .then(
                buf => new File(
                        [buf],
                        `${[...Array(30)]
                            .map(() => Math.random().toString(36)[2])
                            .join('')}.png`,
                        { type: 'image/png' },
                    ),
            );

    handleCrop = async (cropedImage) => {
        const binaryImageFile = await this.dataURLtoFile(cropedImage);

        this.setState(
            prevState => ({
                ...prevState,
                cropperOpen: false,
                image: binaryImageFile,
            }),
            () => {
                this._createThumbnail();
            },
        );
    };

    handleRequestHide = () => {
        this.setState(prevState => ({
            ...prevState,
            cropperOpen: false,
            imageBase64: null,
        }));
    };

    _createThumbnail = () => {
        this._showUploadIndicator();

        const { image } = this.state;
        const {
            maximumWidthInPx,
            maximumHeightInPx,
            minimumWidthInPx,
            minimumHeightInPx,
            maximumFilesizeInBytes,
        } = this.props;

        const oFReader = new FileReader();

        oFReader.readAsDataURL(image);

        oFReader.onload = async function readFileImage(oFREvent) {
            const validDimensions = await this._checkImageDimensions(oFREvent);
            const validFileSize = this._checkImageFileSize(oFREvent);

            if (validDimensions && validFileSize) {
                this._uploadProgress(image.name, oFREvent.target.result);
            }
            if (!validDimensions) {
                this._onErrorHandler({
                    message: `Ukuran dimensi gambar optimal ${maximumWidthInPx}px x ${maximumHeightInPx}px &
                    Ukuran dimensi gambar minimal ${minimumWidthInPx}px x ${minimumHeightInPx}px`,
                });
                this._hideUploadIndicator();
            }

            if (!validFileSize) {
                this._onErrorHandler({
                    message: `Ukuran file maksimal ${
                        maximumFilesizeInBytes / 1000000
                    }MB`,
                });
                this._hideUploadIndicator();
            }
        }.bind(this);
    };

    _onErrorHandler = (message = {}) => {
        const { onError } = this.props;
        onError(message);
    };

    _checkImageDimensions = async (binaryImage) => {
        const rawImage = new Image();
        const {
 maximumWidthInPx, maximumHeightInPx, minimumWidthInPx, minimumHeightInPx,
} = this.props;
        rawImage.src = binaryImage.target.result;
        const imageValidateHandler = new Promise((solved, reject) => {
            rawImage.onload = function checkImage() {
                const { height, width } = this;

                if (height >= minimumHeightInPx && width >= minimumWidthInPx) {
                        return solved(true);
                }
                return solved(false);
            };
        });

        return imageValidateHandler;
    };

    _checkImageFileSize = () => {
        const { image } = this.state;
        const { maximumFilesizeInBytes } = this.props;
        if (image.size < maximumFilesizeInBytes) {
            return true;
        }
        return false;
    };

    _showUploadIndicator = () => {
        this.setState(prevState => ({
            ...prevState,
            uploadIndicatorShow: true,
        }));
    };

    _hideUploadIndicator = () => {
        this.setState(prevState => ({
            ...prevState,
            uploadIndicatorShow: false,
        }));
    };

    _uploadProgress = async (fileName, dataURI) => {
        const { name, api, changeEvent } = this.props;
        const fileUpload = this.constructor.dataURItoBlob(dataURI);

        try {
            const formdata = new FormData();
            const mimeType = fileUpload.type.split('/');
            formdata.append('file', fileUpload, `image.${mimeType[1]}`);
            const { data } = await uploadImageMarketplace(formdata);

            this.setState(
                prevState => ({
                    ...prevState,
                    imageSrc: data.file_url,
                    imageName: data.file_name,
                }),
                () => {
                    const { imageSrc, imageName } = this.state;
                    changeEvent({
                        filepath: imageSrc,
                        fileName: imageName,
                    });
                },
            );
        } catch (error) {
            this._onErrorHandler(error);
        } finally {
            this._hideUploadIndicator();
        }
    };

    deleteImage = (val) => {
        const { deleteImageVal } = this.props;
        deleteImageVal(val);
    }

    render() {
        const {
            imageSrc,
            uploadIndicatorShow,
            cropperOpen,
            imageBase64,
        } = this.state;
        const {
            disabled,
            targetedCropWidthInPX,
            targetedCropHeightInPX,
        } = this.props;
        return (
            <div>
                <div
                    className="image__cropper"
                    style={{
                        position: 'absolute',
                    }}
                >
                    <ImageCropper
                        onRequestHide={() => this.handleRequestHide()}
                        isShow={cropperOpen}
                        onCrop={val => this.handleCrop(val)}
                        image={imageBase64}
                        width={targetedCropWidthInPX}
                        height={targetedCropHeightInPX}
                        imageQualityRatio="0.7"
                    />
                     {(imageSrc) && (
                        <i
                            className="glyphicon glyphicon-remove-circle btn-tutup"
                            onClick={() => this.deleteImage(imageSrc)}
                            role="presentation"
                        />
                )}
                </div>
                {/* {(imageSrc) && (
                        <i
                            className="glyphicon glyphicon-remove-circle btn-tutup"
                            onClick={() => this.deleteImage(imageSrc)}
                            role="presentation"
                        />
                )} */}
                <label className={disabled ? 'upload__container upload__NoDrop' : 'upload__container'}>
                    <input
                        type="file"
                        className="upload__button"
                        accept="image/*"
                        onChange={(e) => {
                            this._changeHandler(e);
                        }}
                        disabled={disabled}
                        value=""
                        autoComplete="new-password"
                    />
                    <div
                        className="upload__indicator spinner"
                        style={
                            uploadIndicatorShow
                                ? {
                                      display: 'block',
                                  }
                                : {
                                      display: 'none',
                                  }
                        }
                    >
                        <div className="double-bounce1" />
                        <div className="double-bounce2" />
                    </div>
                    <div className="image__container">
                        <img
                            src={imageSrc}
                            alt=""
                            srcSet=""
                            className="image__preview"
                            onLoad={this._hideUploadIndicator}
                        />
                    </div>
                </label>
            </div>
        );
    }
}

UploadImgWithThumbnail.defaultProps = {
    value: {
        pathname: '',
        filename: '',
    },
    name: '',
    api: '',
    maximumWidthInPx: 0,
    maximumHeightInPx: 0,
    minimumWidthInPx: 0,
    minimumHeightInPx: 0,
    maximumFilesizeInBytes: 0,
    disabled: false,
};
