/**
 * Created by <PERSON><PERSON> on 05/12/2016.
 */
import React from 'react';
import PropTypes from 'prop-types';

export default class InlineInput extends React.Component {
    renderChildren() {
        const { children } = this.props;

        const filteredChildren = Array.isArray(children) ? children.filter(c => c !== false) : children;
        const width = `${100 / filteredChildren.length}%`;

        if (filteredChildren.length > 1) {
            return filteredChildren.map((input, index) => (
                <div key={index} className="form-wrap" style={{ width }}>
                    {input}
                </div>
            ));
        }
        return (
            filteredChildren
        );
    }

    render() {
        return (
            <div className="inline-form form-group">
                {this.renderChildren()}
            </div>
        );
    }
}

InlineInput.propTypes = {
    children: PropTypes.oneOfType([
        PropTypes.node,
        PropTypes.arrayOf(PropTypes.node),
    ]).isRequired,
};
