// TODO: seharusnya componentnya digeneralkan dulu, baru bikin yang spesific untuk pilihan hari
/**
 * Created by <PERSON><PERSON> on 05/12/2016.
 */
/**
 * important! feed input value with array will automatically convert to string by delimiter koma, so in validation make sure to split by comma ','; then count them
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

import CheckBox from './InputCheckbox';
import CheckBoxHari from './CheckBoxHari';

import { setNativeValue } from '../../utils/helper';

class PilihHariList extends PureComponent {
    tempValue = [];

    state = {
        dataWithStatus: [],
    }

    componentDidMount() {
        const { value, daysList } = this.props;

        this.setState({
            dataWithStatus: this.addCheckStatusToData(daysList, value),
        }, () => { this.tempValue = value; });
    }

    componentWillReceiveProps({ value, daysList }) {
        const { value: pValue, daysList: pDaysList } = this.props;

        if (
            pValue !== value
            || pDaysList !== daysList
        ) {
            this.setState({
                dataWithStatus: this.addCheckStatusToData(daysList, value),
            }, () => { this.tempValue = value; });
        }
    }

    _triggerInputChangeEvent = (value) => {
        const e = new Event('input', { bubbles: true });
        const { input } = this;
        setNativeValue(input, value);
        input.dispatchEvent(e);
    }

    _changeHandler = (data) => {
        this.tempValue = data;

        this._triggerInputChangeEvent(data.length);
    }

    checkboxChangeHandler = (newVal, id) => {
        const { dataWithStatus } = this.state;

        const updatedData = dataWithStatus.map((item) => {
            if (item.id === id) {
                return Object.assign({}, item, { status: newVal });
            }

            return item;
        });

        const newValue = updatedData.filter(item => item.status).map(item => item.id);

        this._changeHandler(newValue);
    }

    addCheckStatusToData = (data, value) => data.map(item => Object.assign({}, item, { status: value.includes(item.id) }))

    checkAllChangeHandler = (val) => {
        const { dataWithStatus } = this.state;
        let days = [];

        if (val) {
            days = dataWithStatus.filter(x => !x.disabled).map(x => x.id);
        }

        this._changeHandler(days);
    }

    _changeEvent = (e) => {
        const { changeEvent } = this.props;

        changeEvent(this.tempValue, e);
    }

    render() {
        const {
            label: controlLabel, disabled, isShowCheckAll, value, name, required, isFullWidth, numOfColumns, translation,
        } = this.props;
        const { dataWithStatus } = this.state;

        return (
            <div className="form-group">
                <input
                    type="text"
                    name={name}
                    required={required}
                    value={value.length}
                    ref={(c) => { this.input = c; }}
                    onChange={this._changeEvent}
                    style={{ position: 'absolute', opacity: 0, left: '-300px' }}
                />
                {
                    controlLabel
                    && (
                        <label
                            className="control-label"
                        >
                            {controlLabel}
                        </label>
                    )
                }
                {
                    isShowCheckAll
                    && (
                        <CheckBox
                            disabled={disabled}
                            id="check-all-btn"
                            changeEvent={this.checkAllChangeHandler}
                            checked={dataWithStatus.filter(x => !x.disabled).length === value.length}
                            label={translation('discountTerms.label.allDay', 'Pilih Semua Hari')}
                        />
                    )
                }
                <div className="checkbox-list btn-type">
                    {
                        dataWithStatus.map(column => (
                            <div
                                key={column.id}
                                className={isFullWidth ? 'col-xs-12 pr-0 pl-0 mb-xs vertical-choose-day' : 'checkbox-list-column'}
                                style={isFullWidth ? {} : { width: `${100 / numOfColumns}%` }}
                            >
                                <CheckBoxHari
                                    key={column.id}
                                    disabled={disabled || column.disabled}
                                    changeEvent={val => this.checkboxChangeHandler(val, column.id)}
                                    checked={column.status}
                                >
                                    {translation(`discountTerms.days.${column.keyDays}.short`, column.title)}
                                </CheckBoxHari>
                            </div>
                        ))
                    }
                </div>
            </div>
        );
    }
}

PilihHariList.propTypes = {
    label: PropTypes.string,
    value: PropTypes.arrayOf(PropTypes.string).isRequired,
    changeEvent: PropTypes.func,
    disabled: PropTypes.bool,
    daysList: PropTypes.arrayOf(
        PropTypes.shape({
            id: PropTypes.string,
            title: PropTypes.string,
        }),
    ),
    numOfColumns: PropTypes.number,
    isShowCheckAll: PropTypes.bool,
    name: PropTypes.string,
    required: PropTypes.bool,
    isFullWidth: PropTypes.bool,
    translation: PropTypes.func,
};

PilihHariList.defaultProps = {
    label: undefined,
    changeEvent: () => { },
    disabled: false,
    isShowCheckAll: true,
    name: undefined,
    required: false,
    isFullWidth: false,
    daysList: [
        {
            id: '1',
            title: 'Senin',
            keyDays: 'Mon',
            disabled: false,
        },
        {
            id: '2',
            title: 'Selasa',
            keyDays: 'Tue',
            disabled: false,
        },
        {
            id: '3',
            title: 'Rabu',
            keyDays: 'Wed',
            disabled: false,
        },
        {
            id: '4',
            title: 'Kamis',
            keyDays: 'Thu',
            disabled: false,
        },
        {
            id: '5',
            title: 'Jumat',
            keyDays: 'Fri',
            disabled: false,
        },
        {
            id: '6',
            title: 'Sabtu',
            keyDays: 'Sat',
            disabled: false,
        },
        {
            id: '0',
            title: 'Minggu',
            keyDays: 'Sun',
            disabled: false,
        },
    ],
    numOfColumns: 7,
    translation: (__, text) => text,
};

export default PilihHariList;
