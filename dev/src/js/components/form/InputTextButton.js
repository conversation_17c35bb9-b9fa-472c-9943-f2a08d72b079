import React from 'react';
import PropTypes from 'prop-types';

const InputTextButton = ({
    label, placeholder, value, clickEvent, disabled, isFile, changeEvent, deleteEvent, fileName,
}) => {
    let textValue;

    if (value && value !== '') {
        textValue = value;
    }

    if (!textValue && placeholder) {
        textValue = placeholder;
    }

    return (
        <div className="form-control-btn">
            {label && <label className="control-label">{label}</label>}
            <div>
                <div className="input-group">
                    <div className="form-control">
                        {textValue}
                    </div>
                    <span className="input-group-btn">
                        <button
                            className="btn btn-input"
                            type="button"
                            onClick={() => {
                                if (!disabled) {
                                    if (isFile && fileName) {
                                        deleteEvent();
                                    } else {
                                        clickEvent();
                                    }
                                }
                            }}
                            disabled={disabled}
                        >
                            <i
                                className={`ci ${isFile && fileName ? 'ci-close' : 'ci-browse'}`}
                            />
                            {
                                isFile && !fileName
                                && (
                                    <input
                                        type="file"
                                        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                                        onChange={changeEvent}
                                        disabled={disabled}
                                    />
                                )
                            }
                        </button>
                    </span>
                </div>
            </div>
        </div>
    );
};

InputTextButton.propTypes = {
    label: PropTypes.string,
    placeholder: PropTypes.string,
    value: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number,
    ]).isRequired,
    clickEvent: PropTypes.func,
    disabled: PropTypes.bool,
    isFile: PropTypes.bool,
    changeEvent: PropTypes.func,
    deleteEvent: PropTypes.func,
    fileName: PropTypes.string,
};

InputTextButton.defaultProps = {
    label: undefined,
    placeholder: undefined,
    clickEvent: () => {},
    disabled: false,
    isFile: undefined,
    changeEvent: () => {},
    deleteEvent: () => {},
    fileName: undefined,
};

export default InputTextButton;
