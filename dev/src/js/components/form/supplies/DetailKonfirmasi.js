/* eslint-disable no-tabs */
import React from 'react';
import PropTypes from 'prop-types';
import { currency, numSeparator } from '../../../utils/helper';
import InputNumber from '../InputNumber';

class DetailKonfirmasi extends React.Component {
	static propTypes = {
		data: PropTypes.arrayOf(PropTypes.shape({})),
		edited: PropTypes.bool,
		summary: PropTypes.bool,
		dataCart: PropTypes.arrayOf(PropTypes.shape({})),
		eventHandle: PropTypes.func,
		deleteHandle: PropTypes.func,
		tax: PropTypes.number,
		additionaCost: PropTypes.number,
		discount: PropTypes.number,
		ongkir: PropTypes.number,
		detail: PropTypes.bool,
	}

	static defaultProps = {
		data: [],
		edited: false,
		summary: false,
		dataCart: [],
		eventHandle: () => {},
		deleteHandle: () => {},
		tax: 0,
		additionaCost: 0,
		discount: 0,
		ongkir: 0,
		detail: false,
	}

	constructor(props) {
        super(props);

        this.state = {
            ongkir: 0,
        };
	}

	componentWillMount() {
		const { ongkir } = this.props;
		this.setState({
			ongkir,
		});
	}

	componentWillReceiveProps(nextProps) {
		const { ongkir } = this.props;
		if (ongkir !== nextProps.ongkir) {
			this.setState({
				ongkir: nextProps.ongkir,
			});
		}
	}

	eventHandle(val) {
		const { eventHandle } = this.props;
		eventHandle(val);
	}

	deleteHandle(val) {
		const { deleteHandle } = this.props;
		deleteHandle(val);
	}

	render() {
		const {
			edited, data, dataCart, tax, discount, additionaCost, summary, detail,
		} = this.props;
		const { ongkir } = this.state;
		// const ongkir = 2000;
		let taxRp = 0;
		let discountRp = 0;
		let grandTotal = 0;
		let subTotal = 0;
		if (data.length > 0) {
			grandTotal = data.map(x => parseInt(x.total, 10)).reduce((prev, next) => prev + next);
			subTotal = grandTotal;
		}

		if (data.length > 0 && discount > 0) {
			discountRp = (grandTotal * (discount / 100));
		}

		if (data.length > 0 && tax > 0) {
			taxRp = (grandTotal * (tax / 100));
		}
		if (!detail) {
			grandTotal = grandTotal + additionaCost + Math.ceil(taxRp) - parseInt(discountRp, 10) + parseInt(ongkir, 10);
		}
		let styling = {};
		if (summary) {
			styling = {
				position: 'relative', maxHeight: '500px', overflow: 'auto', display: 'block',
			};
		}
		return (
			<div className="belanja-cart" style={styling}>
				<table className="table interactive-table summary-list-table" style={{ marginBottom: '0px' }}>
					<thead>
						<tr>
							<th>NO</th>
							<th>BARANG</th>
							{!summary && (
								<th className="text-right">HARGA</th>
							)}
							{!summary && (
								<th className="text-right">DISKON</th>
							)}
							<th className="text-right">HARGA SETELAH DISKON</th>
							<th className="text-right">PAJAK</th>
							<th className={edited ? 'text-center' : 'text-right'}>JUMLAH BELI</th>
							{ /*
								<th className="text-right">HARGA TAMBAHAN</th>
							*/ }
							<th className="text-right">TOTAL</th>
							{edited && <th style={{ width: '38px' }}>&nbsp;</th>}
						</tr>
					</thead>
					<tbody>
						{data.length > 0 && data.map((d, index) => {
							return (
								<DetailKonfirmasiRow
									key={d.id_supply_produk}
									index={index}
									data={d}
									dataCart={dataCart}
									eventHandle={(val) => { this.eventHandle(val); }}
									deleteHandle={(val) => { this.deleteHandle(val); }}
									edited={edited}
									summary={summary}
								/>
							);
						})}
						<br />
						{data.length > 0 && subTotal > 0 && (
							<tr>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }}>&nbsp;</td>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }} colSpan={summary ? '4' : '6'}><b>Sub Total Produk</b></td>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }} colSpan="2" className="text-right"><b>{`${currency({ value: subTotal })}`}</b></td>
								{edited && <td style={{ borderBottom: '0px', padding: '10px 15px' }}>&nbsp;</td>}
							</tr>
						)}
						{data.length > 0 && discount > 0 && (
							<tr>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }}>&nbsp;</td>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }} colSpan={summary ? '4' : '6'}><b>{`Potongan ${discount}%`}</b></td>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }} colSpan="2" className="text-right"><b>{`${currency({ value: discountRp })}`}</b></td>
								{edited && <td style={{ borderBottom: '0px', padding: '10px 15px' }}>&nbsp;</td>}
							</tr>
						)}
						{data.length > 0 && tax > 0 && (
							<tr>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }}>&nbsp;</td>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }} colSpan={summary ? '4' : '6'}><b>{`Pajak sebelum diskon ${tax}%`}</b></td>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }} colSpan="2" className="text-right"><b>{currency({ value: taxRp })}</b></td>
								{edited && <td style={{ borderBottom: '0px', padding: '10px 15px' }}>&nbsp;</td>}
							</tr>
						)}
						{data.length > 0 && additionaCost > 0 && (
							<tr>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }}>&nbsp;</td>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }} colSpan={summary ? '4' : '6'}><b>Biaya Administrasi</b></td>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }} colSpan="2" className="text-right"><b>{`${currency({ value: additionaCost })}`}</b></td>
								{edited && <td style={{ borderBottom: '0px', padding: '10px 15px' }}>&nbsp;</td>}
							</tr>
						)}
						{data.length > 0 && ongkir > 0 && (
							<tr>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }}>&nbsp;</td>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }} colSpan={summary ? '4' : '6'}><b>Ongkos Kirim</b></td>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }} colSpan="2" className="text-right"><b>{`${currency({ value: ongkir })}`}</b></td>
								{edited && <td style={{ borderBottom: '0px', padding: '10px 15px' }}>&nbsp;</td>}
							</tr>
						)}
						{data.length > 0 && (
							<tr>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }}>&nbsp;</td>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }} colSpan={summary ? '4' : '6'}><b>{ edited && ongkir === 0 ? 'TOTAL BELANJA (Belum termasuk ongkir)' : 'TOTAL BELANJA'}</b></td>
								<td style={{ borderBottom: '0px', padding: '10px 15px' }} colSpan="2" className="text-right"><b>{currency({ value: (grandTotal) })}</b></td>
								{edited && <td style={{ borderBottom: '0px', padding: '10px 15px' }}>&nbsp;</td>}
							</tr>
						)}
						{data.length === 0 && (
							<tr>
								<td colSpan={edited ? '10' : '8'} className="text-center">Tidak ada data</td>
							</tr>
						)}
					</tbody>
				</table>
			</div>
		);
	}
}
export default DetailKonfirmasi;

class DetailKonfirmasiRow extends React.Component {
	static propTypes = {
		data: PropTypes.shape({}),
		dataCart: PropTypes.arrayOf(PropTypes.shape({})),
		eventHandle: PropTypes.func,
		deleteHandle: PropTypes.func,
		edited: PropTypes.bool,
		summary: PropTypes.bool,
		index: PropTypes.number,
	}

	static defaultProps = {
		data: [],
		dataCart: [],
		eventHandle: () => {},
		deleteHandle: () => {},
		edited: false,
		summary: false,
		index: 0,
	}

	hitungTotal = (data) => {
		const { price_after_discount: pad, tax, additional_price: ap } = data;
		const price = parseInt(pad, 10) + parseInt((pad * (tax / 100)), 10) + parseInt(ap, 10);
		return price;
	}

	eventButton(type) {
		const {
			dataCart, data, eventHandle, deleteHandle,
		} = this.props;
		const cart = [...dataCart];
		const indexProduct = cart.findIndex(x => x.id_supply_produk === data.id_supply_produk);

		if (indexProduct === -1) {
			if (type === '+') {
				const obj = {
					id_supply_produk: data.id_supply_produk,
					supply_produk_name: data.supply_produk_name,
					price_after_discount: parseInt(data.price_after_discount, 10),
					tax: parseFloat(data.tax, 10),
					supply_produk_price: parseFloat(data.supply_produk_price, 10),
					discount: parseFloat(data.discount, 10),
					weight: parseFloat(data.weight, 10),
					additional_price: parseInt(data.additional_price, 10),
					buy_qty: 1,
					total: this.hitungTotal(data),
				};
				cart.push(obj);
				eventHandle(cart);
			}
		} else if (indexProduct !== -1 && type === '+') {
			cart[indexProduct].buy_qty += 1;
			cart[indexProduct].total = this.hitungTotal(data) * parseInt(cart[indexProduct].buy_qty, 10);
			eventHandle(cart);
		} else if (indexProduct !== -1 && type === '-') {
			if (cart[indexProduct].buy_qty === 1) {
				cart.splice(indexProduct, 1);
				deleteHandle(cart);
			} else {
				cart[indexProduct].buy_qty -= 1;
				cart[indexProduct].total = this.hitungTotal(data) * parseInt(cart[indexProduct].buy_qty, 10);
				eventHandle(cart);
			}
		}
	}

	changeQty(val) {
		const {
			dataCart, data, eventHandle, deleteHandle,
		} = this.props;
		const cart = [...dataCart];
		const indexProduct = cart.findIndex(x => x.id_supply_produk === data.id_supply_produk);
		const value = Number.isNaN(parseInt(val, 10)) ? 0 : parseInt(val, 10);

		if (indexProduct === -1) {
			const obj = {
				id_supply_produk: data.id_supply_produk,
				supply_produk_name: data.supply_produk_name,
				price_after_discount: parseInt(data.price_after_discount, 10),
				tax: parseFloat(data.tax, 10),
				supply_produk_price: parseFloat(data.supply_produk_price, 10),
				discount: parseFloat(data.discount, 10),
				weight: parseFloat(data.weight, 10),
				additional_price: parseInt(data.additional_price, 10),
				buy_qty: value,
				total: this.hitungTotal(data),
			};
			cart.push(obj);
			if (value > 0) {
				eventHandle(cart);
			}
		} else if (indexProduct !== -1 && value > 0) {
			cart[indexProduct].buy_qty = value;
			cart[indexProduct].total = this.hitungTotal(data) * parseInt(cart[indexProduct].buy_qty, 10);
			eventHandle(cart);
		} else if (indexProduct !== -1 && value <= 0) {
			cart.splice(indexProduct, 1);
			deleteHandle(cart);
		}
	}

	deleteButton(index) {
		const {
			dataCart, deleteHandle,
		} = this.props;
		const cart = [...dataCart];
		cart.splice(index, 1);
		deleteHandle(cart);
	}

	render() {
		const {
			index, data, edited, summary,
		} = this.props;
		const { price_after_discount: pad, tax, additional_price: ap } = data;
		const pajak = parseFloat(pad) * parseFloat((tax / 100));
		const tambahan = parseFloat(ap) * parseInt(data.buy_qty, 10);
		return (
			<tr>
				<td style={{ padding: '10px 15px' }}>{index + 1}</td>
                <td style={{ padding: '10px 15px' }}>{data.supply_produk_name}</td>
				{!summary && (
					<td className="text-right" style={{ padding: '10px 15px' }}>{currency({ value: data.supply_produk_price })}</td>
				)}
				{!summary && (
					<td className="text-right" style={{ padding: '10px 15px' }}>{`${data.discount}%`}</td>
				)}
				<td className="text-right" style={{ padding: '10px 15px' }}>{currency({ value: data.price_after_discount })}</td>
                <td className="text-right" style={{ padding: '10px 15px' }}>{`${data.tax}% (${currency({ value: pajak })})`}</td>
                {edited && (
					<td style={{ width: '20%', padding: '10px', padding: '10px 15px' }}>
						<div className="row" style={{ marginBottom: '0px' }}>
							<div className="col-sm-3" style={{ paddingRight: '0px' }}>
								<button
									className="btn text-main "
									style={{
										fontWeight: 100,
										width: '100%',
										paddingTop: '7px',
										height: '34px',
										borderRight: 'none',
										borderRadius: '3px 0px 0px 3px',
									}}
									onClick={() => { this.eventButton('-'); }}
								>
									<i className="fa fa-minus green" />
								</button>
							</div>
							<div className="col-sm-6" style={{ paddingRight: '0px', paddingLeft: '0px' }}>
								<InputNumber
									value={data.buy_qty.toString()}
									classes="text-center input-qty-supplies"
									changeEvent={(val) => { this.changeQty(val); }}
								/>
							</div>
							<div className="col-sm-3" style={{ paddingLeft: '0px' }}>
								<button
									className="btn text-main"
									style={{
										fontWeight: 100,
										width: '100%',
										paddingTop: '7px',
										height: '34px',
										borderLeft: 'none',
										borderRadius: '0px 3px 3px 0px',
									}}
									onClick={() => { this.eventButton('+'); }}
								>
									<i className="fa fa-plus green" />
								</button>
							</div>
						</div>
					</td>
				)}
                {!edited && (
					<td className="text-right" style={{ padding: '10px 15px' }}>{numSeparator(data.buy_qty)}</td>
				)}
				{ /*
					<td className="text-right">{currency({ value: tambahan })}</td>
				*/ }
				{data.total < 0 && (
					<td className="text-right" style={{ padding: '10px 15px' }}>{`(${currency({ value: (data.total * -1) })})`}</td>
				)}
				{data.total > 0 && (
					<td className="text-right" style={{ padding: '10px 15px' }}>{currency({ value: data.total })}</td>
				)}
                {edited && (
					<td style={{ padding: '10px' }}>
						<button
							className="btn"
							style={{
								fontWeight: 100,
								width: '38px',
								paddingTop: '7px',
								height: '34px',
							}}
							onClick={() => { this.deleteButton(index); }}
						>
							<i className="fa fa-trash" />
						</button>
					</td>
				)}
			</tr>
		);
	}
}
