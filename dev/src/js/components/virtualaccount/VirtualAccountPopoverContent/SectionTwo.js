// TODO: remove commented code
import React from 'react';
import {
    Flex, Box, Text,
    IconButton,
} from '@majoo-ui/react';
import { CopyOutline } from '@majoo-ui/icons';
import moment from 'moment';
import { PAYMENT_PROVIDER_VA } from '~/pages/Support/BuyV2/enum';
import ClickableText from '../ClickableText';
import { colors } from '../../../stitches.config';

const SectionTwo = ({
    translationData, bill, router,
}) => {
    const { LANG_DATA, TransComponent, LANG_KEY, t } = translationData;
    const data = bill;

    const renderBankName = () => {
        const findBank = PAYMENT_PROVIDER_VA.find(x => String(x.id) === String(data.bank_id));
        if (findBank) return (
            <TransComponent i18nKey={LANG_KEY.VA}>
                Virtual Account
                {' '}
                {{ name: findBank.name || '' }}
            </TransComponent>
        );
        return 'Link Payment';
    };

    const handleChangePayment = () => {
        if (bill.isActivePayment) {
            localStorage.setItem('DETAIL::TRANSACTION', JSON.stringify({ ...bill, isChange: true }));
            router.push(`/support/buy?detail=${bill.id}`);
        } else router.push(`/support/buy?change_payment=${data.reff}`);
    };

    return (
        <React.Fragment>
            {data.summaryNoteCRM === '' && data.va_number !== null && (
                <Flex direction="column" css={{ gap: 6 }}>
                    <Box
                        css={{
                            alignItems: 'center',
                            display: 'grid',
                            gridTemplateColumns: 'auto 1fr',
                        }}
                    >
                        <Box>
                            <Text variant="caption" color="secondary">
                                {renderBankName()}
                            </Text>
                        </Box>
                        {!data.isCRM && (
                            <Box>
                                <ClickableText
                                    onClick={handleChangePayment}
                                >
                                    {LANG_DATA.POPOVER_CHANGE}
                                </ClickableText>
                            </Box>
                        )}
                    </Box>
                    <Box
                        css={{
                            alignItems: 'center',
                            display: 'flex',
                            gap: 8,
                        }}
                    >
                        <Text color="primary" variant="contentButton">
                            {data.va_number}
                        </Text>
                        <IconButton onClick={() => { navigator.clipboard.writeText(data.va_number); }}>
                            <CopyOutline color={colors.iconGreen} />
                        </IconButton>
                    </Box>
                    <Box>
                        <Text variant="caption" color="red">{`${LANG_DATA.POPOVER_DUE}: ${moment(data.va_expired_date, 'YYYY-MM-DD HH:mm').format('DD MMMM YYYY, HH:mm')}`}</Text>
                    </Box>
                </Flex>
            )}
            {data.summaryNoteCRM !== '' && data.isCRM && (
                <Box>
                    {data.isContactMajoo ? t('virtualAccountPayment:popover.callMajooTeam') : t('virtualAccountPayment:popover.callOwner')}
                </Box>
            )}
        </React.Fragment>
    );
};

export default SectionTwo;
