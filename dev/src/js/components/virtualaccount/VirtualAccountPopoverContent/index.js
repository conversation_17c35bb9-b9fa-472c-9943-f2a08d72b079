import React, { useMemo } from 'react';
import {
    Flex, Separator, Text, Heading,
    Box,
} from '@majoo-ui/react';
import { connect } from 'react-redux';
import { get } from 'lodash';
import userUtil from '~/utils/user.util';
import { PERMISSIONS } from '~/constants/permissions';
import moment from 'moment';
import SectionOne from './SectionOne';
import SectionTwo from './SectionTwo';
import SectionThree from './SectionThree'; // TODO: remove this code
import { getSingleSoonExpiredOutlet, getSoonExpiredOutlets } from '../utils';
import { useTranslationHook } from '../lang.utils';
import { PAYMENT_TYPE } from '../enums';
import { LIFECYCLE_STATUS } from '../../../config/enum';

const VirtualAccountPopoverContent = ({
    outlets,
    virtualAccountPayment,
    businessName,
    userStrap,
    router,
    openDetailOutletModel,
    isExpired,
    lifecycleStatus,
}) => {
    const translationData = useTranslationHook();
    const { TransComponent, LANG_KEY, LANG_DATA, currLang } = translationData;
    const { virtualAccountOutlets, virtualAccountSummary, activePayment } = virtualAccountPayment;
    const { permissionId } = userStrap;

    const isManagerAllowedSummary =
            (permissionId === PERMISSIONS.ADMIN && String(get(virtualAccountSummary, 'is_crm')) !== '1')
            || (String(get(virtualAccountSummary, 'is_crm')) === '1');

    const soonExpiredOutlets = useMemo(() => getSoonExpiredOutlets({ virtualAccountOutlets, outlets }).filter(X => X.bill.status === PAYMENT_TYPE.UNPAID), [virtualAccountOutlets, outlets]);

    const soonExpiredOutlet = soonExpiredOutlets.length === 0 ? null : getSingleSoonExpiredOutlet({ soonExpiredOutlets, userStrap });

    const bill = useMemo(() => {
        if (activePayment) {
            return {
                ...activePayment,
                id: activePayment.id,
                invoice: activePayment.inv_number,
                transactions_number: activePayment.transactions_number,
                total: activePayment.total_amount_biller,
                details: activePayment.detail,
                va_number: activePayment.va_number,
                va_expired_date: activePayment.va_expired_date,
                bank_id: activePayment.payment_method_bank_id,
                payment_method: activePayment.payment_method,
                isActivePayment: true,
                summaryNoteCRM: '', // hardcode for show VA number if exist
            };
        }
        if (get(virtualAccountSummary, 'id') && String(get(virtualAccountSummary, 'status')) === '2' && isManagerAllowedSummary) {
            return {
                total: virtualAccountSummary.total_amount,
                total_strikeout: virtualAccountSummary.strikeout_price,
                discount: virtualAccountSummary.total_discount,
                details: virtualAccountSummary.details,
                va_number: virtualAccountSummary.va_number,
                va_expired_date: virtualAccountSummary.va_expired_date,
                bank_id: virtualAccountSummary.payment_method_bank_id,
                payment_method: virtualAccountSummary.payment_method,
                reff: virtualAccountSummary.reff_no,
                isSummary: true,
                isCRM: +virtualAccountSummary.is_crm === 1,
                isContactMajoo: virtualAccountSummary.summary_note === 'hubungi tim majoo',
                summaryNoteCRM: virtualAccountSummary.summary_note,
            };
        }
        if (soonExpiredOutlet) {
            return {
                total: soonExpiredOutlet.bill.total,
                va_number: soonExpiredOutlet.bill.va_number,
                va_expired_date: soonExpiredOutlet.bill.va_expire_date,
                bank_id: soonExpiredOutlet.bill.bank_id,
                payment_method: soonExpiredOutlet.bill.payment_method,
                reff: soonExpiredOutlet.bill.reference_number,
                summaryNoteCRM: '', // hardcode for show VA number if exist
            };
        }
        return null;
    }, [soonExpiredOutlet, virtualAccountSummary, activePayment]);

    const outletListDetail = useMemo(() => {
        if (activePayment) {
            const outletExp = userUtil.getLocalConfigByKey('outletExp');
            return activePayment.detail.map((x) => {
                const findId = get(outlets.list.find(outlet => outlet.cabang_name === x.outlet_name), 'id_cabang');
                const exp_date = get(outletExp, findId);
                return {
                    ...x,
                    cabang: x.outlet_name,
                    since: x.active_date,
                    exp_date,
                    total: (x.buy_qty * x.amount) + x.upgrade_price,
                    date: x.transaction_date,
                    expiredDate: x.va_expired_date,
                    package: x.Support_package,
                };
            });
        }
        if (get(virtualAccountSummary, 'id') && get(virtualAccountSummary, 'status') === '2' && virtualAccountSummary.details) {
            return virtualAccountSummary.details.map(x => ({
                ...x,
                cabang: x.outlet_name,
                since: x.active_date,
                exp_date: x.support_exp_date,
                total: virtualAccountSummary.total_amount,
                date: virtualAccountSummary.created_at,
                expiredDate: virtualAccountSummary.va_expired_date,
                package: virtualAccountSummary.package,
                ref: virtualAccountSummary.reff_no,
            }));
        }
        return [];
    }, [soonExpiredOutlet, virtualAccountSummary, outlets, activePayment]);

    const handleOpenDetail = () => openDetailOutletModel(outletListDetail);

    const outletExp = userUtil.getLocalConfigByKey('outletExp');
    const billMonth = useMemo(() => {
        if (outletExp) {
            const listExp = Object.values(outletExp);
            listExp.sort((a, b) => a - b);
            return listExp.length ? listExp[0] : null;
        }
        return null;
    }, [outletExp]);

    const getTitle = () => (<Heading heading="sectionTitle">{lifecycleStatus === LIFECYCLE_STATUS.GRACE ? LANG_DATA.POPOVER_GRACE_TITLE : LANG_DATA.POPOVER_HOT_TITLE}</Heading>);

    const getDesc = (withBill = false) => {
        if (lifecycleStatus === LIFECYCLE_STATUS.HOT) {
            return (
                <TransComponent
                    i18nKey={withBill ? LANG_KEY.HOT_DESC : LANG_KEY.HOT_DESC_NO_BILL}
                    components={{ a: <a /> }}
                    values={{
                        link: `${process.env.PORTAL_BASE_URL}/syarat-dan-ketentuan`,
                    }}
                />
            )
        }
        const formatDate = currLang === 'en' ? 'MMMM DD, YYYY, HH:mm' : 'DD MMMM YYYY, HH:mm'
        return (
            <TransComponent
                i18nKey={withBill ? LANG_KEY.GRACE_DESC : LANG_KEY.GRACE_DESC_NO_BILL}
                components={{ a: <a /> }}
                values={{
                    monthExpired: billMonth ? moment(billMonth).format('MMMM YYYY') : '',
                    link: `${process.env.PORTAL_BASE_URL}/syarat-dan-ketentuan`,
                    ...(withBill ? { expiredDate: moment(bill.va_expired_date, 'YYYY-MM-DD HH:mm').format(formatDate) } : { }),
                }}
            >
                Tagihan bulan Agustus 2024 Anda belum dibayar.Segera lakukan pembayaran agar akun Anda tidak diblokir.
            </TransComponent>
        );
    };

    if (!bill) {
        return (
            <React.Fragment>
                {lifecycleStatus && (
                    <Flex direction="column" gap={3} align="center" justify="center" mb={6}>
                        {getTitle()}
                        <Text color="primary" align="center">
                            {getDesc(!!bill)}
                        </Text>
                    </Flex>
                )}
            </React.Fragment>
        );
    }
    return (
        <React.Fragment>
            {lifecycleStatus && (
                <React.Fragment>
                    {getTitle()}
                    <Text align="center" css={{ whiteSpace: 'pre-line' }}>
                        {getDesc(!!bill)}
                    </Text>
                    <Separator />
                    <Text align="center" variant="contentButton" color="primary">
                        {LANG_DATA.POPOVER_WARNING}
                    </Text>
                </React.Fragment>
            )}
            <Flex
                direction="column"
                css={{
                    backgroundColor: isExpired ? '$bgFailed' : '$bgGray',
                    border: `1px solid ${isExpired ? '$formError' : '#f0f1f1'}`,
                    borderRadius: '8px',
                    padding: '12px',
                    gap: 12,
                    marginTop: 12,
                    marginBottom: 12,
                    ...lifecycleStatus && { width: '$full' },
                }}
            >
                <SectionOne
                    {...{
                        soonExpiredOutlet,
                        translationData,
                        businessName,
                        virtualAccountSummary,
                        handleOpenDetail,
                        bill,
                    }}
                />
                <Separator />
                <SectionTwo
                    {...{
                        soonExpiredOutlet,
                        translationData,
                        router,
                        virtualAccountSummary,
                        bill,
                    }}
                />
                {activePayment && (
                    <React.Fragment>
                        <Separator />
                        <SectionThree
                            {...{
                                bill,
                                translationData,
                                router,
                            }}
                        />
                    </React.Fragment>
                )}
            </Flex>
        </React.Fragment>
    );
};

export default connect(store => ({
    outlets: store.outlets,
    virtualAccountPayment: store.virtualAccountPayment,
    userStrap: store.users.strap,
}))(VirtualAccountPopoverContent);
