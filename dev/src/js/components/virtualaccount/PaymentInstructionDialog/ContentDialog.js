import React, { useMemo } from 'react';
import {
    PageDialog, PageDialogTitle,
    PageDialogContent,
    Flex, Button, Box, PageDialogFooter, Separator,
} from '@majoo-ui/react';
import ResponsivePaper from '../../../pages/Inventory/StockEntryV2/StockEntryDetail/ResponsivePaper'; // TODO: akan dibuatkan global component di dev/src/js/v2-components
import { matchMediaChecker } from '../../../v2-utils';
import { MATCH_MEDIA_TYPE } from '../../../v2-enum';
import SectionOne from './SectionOne';
import SectionTwo from './SectionTwo';
import CustomPageDialogFooter from '../../../v2-components/CustomPageDialogFooter';
import SectionThree from './SectionThree';
import { useTranslationHook } from '../lang.utils';
import { PAYMENT_PROVIDER_VA } from './enum';

const ContentDialog = ({
    isOpen, onClose, data, onOpenDetail,
}) => {
    const translationData = useTranslationHook();
    const { LANG_DATA } = translationData;
    const isInMobileView = useMemo(() => !matchMediaChecker(MATCH_MEDIA_TYPE.MD), []);
    const bankVA = useMemo(() => (data ? PAYMENT_PROVIDER_VA.find(x => String(x.id) === String(data.bill.bank_id)) : null), [data]);

    if (!data) return null;

    return (
        <PageDialog
            modal
            layer={1}
            size="full"
            open={isOpen}
            defaultOpen={false}
            onOpenChange={onClose}
            isMobile={isInMobileView}
        >
            <PageDialogTitle>
                <Flex direction="row" align="center" gap={4}>
                    <Box css={{ maxWidth: '30vw', textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}>
                        {LANG_DATA.INSTRUCTION_TITLE}
                    </Box>
                </Flex>
            </PageDialogTitle>
            <PageDialogContent wrapperSize="lg">
                <ResponsivePaper css={{ alignItems: 'center' }}>
                    <Flex
                        direction="column"
                        css={{
                            gap: 18, color: '$textPrimary', width: '100%', maxWidth: 662,
                        }}
                    >
                        <SectionOne {...{ data, translationData }} />
                        <SectionTwo
                            {...{
                                data, translationData, bankVA, onOpenDetail,
                            }}
                        />
                        <Separator />
                        <SectionThree {...{ data, translationData, bankVA }} />
                    </Flex>
                </ResponsivePaper>
            </PageDialogContent>
            <PageDialogFooter
                css={{
                    padding: '16px',
                    '@md': {
                        padding: '16px 24px',
                    },
                }}
            >
                <CustomPageDialogFooter
                    {...{ pageDialogContentHasIndicator: true }}
                    renderGroups={[
                        () => (
                            <Box
                                css={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'flex-end',
                                }}
                            >
                                <Button
                                    type="button"
                                    buttonType="ghost"
                                    onClick={onClose}
                                >
                                    {LANG_DATA.LABEL_CLOSE}
                                </Button>
                            </Box>
                        ),
                    ]}
                />
            </PageDialogFooter>
        </PageDialog>
    );
};

export default ContentDialog;
