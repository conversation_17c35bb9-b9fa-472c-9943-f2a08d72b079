import React from 'react';

import ModalPopup from './Container';
import SwitchBox from '../form/SwitchBox';
import InputNumber from '../form/InputNumber';

const pilihPeriode = {
    color: '#263238',
    margin: '0px 0px 10px 0px',
    fontWeight: '500'
}

const defaultButton = { padding: "7px" };
const activeButton = {
    background: '#04C99E',
    padding: '7px',
}
const colorWhite = { color: '#FFF' }

export default class ChoosePeriode extends React.Component {

    constructor(props) {
        super(props);

        this.state = {
            periodChecked: ''
        }
    }

    showPopup = () => {
        this.setState({ periodChecked: this.props.periodChecked });
        this.refs.confirmSaveData.showPopup();
    }

    hidePopup() {
        this.refs.confirmSaveData.hidePopup();
    }

    periodSwitchHandler = (val) => {
        this.setState({ periodChecked: val });
        this.props.periodSwitchEvent(val)
    }

    changePeriodeValue = (val) => {
        this.props.changePeriodeValueEvent(val);
    }

    render() {
        const { periodChecked } = this.state;

        return (
            <ModalPopup
                type='upload'
                show={this.props.show}
                confirmText={this.props.confirmText || 'Proses'}
                cancelText={this.props.cancelText || 'Batal'}
                confirmHandle={this.props.confirmHandle}
                headerTitle={this.props.headerTitle}
                ref='confirmSaveData'
            >
                <h4 className="modal-title" style={pilihPeriode}>Pilih Periode Belanja</h4>

                <div className="stack">
                    <div className="form-stack inline-form">
                        <div className="form-group">
                            <div className="form-wrap text-center" style={{ width: "25%" }}>
                                <div className="form-group">
                                    <InputNumber
                                        classes="text-center"
                                        value={this.props.periodeValue}
                                        changeEvent={this.changePeriodeValue}
                                    />
                                </div>
                            </div>
                            <div className="form-wrap text-center" style={{ width: "25%" }}>
                                <div className="form-group">
                                    <div
                                        className="form-control cursor-pointer text-main"
                                        style={periodChecked === 'hari' ? activeButton : defaultButton}
                                        onClick={val => this.periodSwitchHandler("hari")}
                                    >
                                        <span style={periodChecked === 'hari' ? colorWhite : {}}>HARI</span>
                                    </div>
                                </div>
                            </div>
                            <div className="form-wrap text-center" style={{ width: "25%" }}>
                                <div className="form-group">
                                    <div
                                        className="form-control cursor-pointer text-main"
                                        style={periodChecked === 'minggu' ? activeButton : defaultButton}
                                        onClick={val => this.periodSwitchHandler("minggu")}
                                    >
                                        <span style={periodChecked === 'minggu' ? colorWhite : {}}>MINGGU</span>
                                    </div>
                                </div>
                            </div>
                            <div className="form-wrap text-center" style={{ width: "25%" }}>
                                <div className="form-group">
                                    <div
                                        className="form-control cursor-pointer text-main"
                                        style={periodChecked === 'bulan' ? activeButton : defaultButton}
                                        onClick={val => this.periodSwitchHandler("bulan")}
                                    >
                                        <span style={periodChecked === 'bulan' ? colorWhite : {}}>BULAN</span>
                                    </div>
                                </div>
                            </div>
                            {/* <div className="form-wrap text-center" style={{ width: "60%" }}>
                                <div className="form-group">
                                    <SwitchBox
                                        dataset={[
                                            { text: 'hari', value: 'hari' },
                                            { text: 'minggu', value: 'minggu' },
                                            { text: 'bulan', value: 'bulan' }
                                        ]}
                                        value={this.props.periodChecked}
                                        changeEvent={val => this.periodSwitchHandler(val)}
                                    />
                                </div>
                            </div> */}
                        </div>
                    </div>
                </div>

            </ModalPopup>
        );
    }
}