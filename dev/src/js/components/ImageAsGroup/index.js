import React from 'react';
import PropTypes from 'prop-types';
import update from 'immutability-helper';
import { uploadImageMarketplace } from '~/data/upload';
import { withTranslation } from 'react-i18next';
import <PERSON>ropper from './Cropper';
import Group from './Group';

import { COMPONENT } from './enums';
import { dataURItoBlob, getImgDimension } from './utils';

import './style.less';

class ImageAsGroup extends React.Component {
    static propTypes = {
        images: PropTypes.arrayOf(PropTypes.string).isRequired,
        onChange: PropTypes.func,
        qty: PropTypes.number,
        label: PropTypes.string,
        qualityRatio: PropTypes.number,
        minWidth: PropTypes.number,
        minHeight: PropTypes.number,
        maxWidth: PropTypes.number,
        maxHeight: PropTypes.number,
        disabled: PropTypes.bool,
        hidePrimaryLabel: PropTypes.bool,
        hideRequiredLabel: PropTypes.bool,
        addNotification: PropTypes.func,
        t: PropTypes.func,
    }

    static defaultProps = {
        onChange: () => {},
        qty: 3,
        label: undefined,
        disabled: false,
        qualityRatio: 0.8,
        minWidth: 300,
        maxHeight: 700,
        maxWidth: 700,
        minHeight: 300,
        hidePrimaryLabel: false,
        hideRequiredLabel: false,
        addNotification: () => {},
        t: (__, text) => text,
    }

    constructor(props) {
        super(props);

        this.state = {
            img: null,
            clickedIndex: -1,
        };
    }

    clearImage = () => {
        this.setState({ img: null });
    }

    handleCrop = (dataURI) => {
        this.doUploadingData(dataURI);
    }

    doUploadingData = async (dataURI) => {
        const { addNotification, minHeight, minWidth, maxHeight, maxWidth, t } = this.props;
        try {
            const blob = dataURItoBlob(dataURI);

            const allowedSize = 1000 * 1000; // 1MB
            const { width, height } = await getImgDimension(blob);

            if (width < minWidth || height < minHeight) {
                addNotification({
                    title: t('error.failedUploadImage', 'Upload Image Gagal!'),
                    message: t('error.smallImageResolution', `Resolusi image terlalu kecil (min: ${minWidth}x${minHeight} px)`, { minWidth, minHeight }),
                    level: 'error',
                });
                return;
            }

            if (width > maxWidth || height > maxHeight) {
                addNotification({
                    title: t('error.failedUploadImage', 'Upload Image Gagal!'),
                    message: t('error.largeImageResolution', `Resolusi image terlalu besar (max: ${minWidth}x${minHeight} px)`, { minWidth, minHeight }),
                    level: 'error',
                });
                return;
            }
            
            if (blob.size > allowedSize) {
                addNotification({
                    title: t('error.failedUploadImage', 'Upload Image Gagal!'),
                    message: t('error.largeImageFile', 'File image terlalu besar (max: 1 MB)'),
                    level: 'error',
                });
                return;
            }

            const formdata = new FormData();
            const mimeType = blob.type.split('/');
            formdata.append('file', blob, `image.${mimeType[1]}`);
            const res = await uploadImageMarketplace(formdata);
            this.handleUpdateImage(res.data.file_url);

            this.cropper.hide();
            this.clearImage();
        } catch (e) {
            console.log(e);
        }
    }

    handleUpdateImage = (val) => {
        const { clickedIndex } = this.state;
        const { images, onChange } = this.props;

        let newImages = update(images, {
            [clickedIndex]: {
                $set: val,
            },
        });

        if (clickedIndex > images.length) {
            newImages = update(images, {
                $push: [val],
            });
        }

        onChange(newImages);
    }

    handleRemoveImage = (index) => {
        const { images, onChange } = this.props;

        const newImages = images.filter((x, i) => (i !== index));

        onChange(newImages);
    }

    handleRequestHide = () => { this.clearImage(); }

    handleFileChange = (dataURI, index) => {
        this.setState({ img: dataURI, clickedIndex: index }, () => {
            this.cropper.show();
        });
    }

    doValidateFileChange = (/** @type {File} */ file) => {
        const { addNotification } = this.props;

        const fileExtension = file.name.split('.').pop().toLowerCase();
        const allowedExtensions = ['png', 'jpeg', 'jpg'];
        const result = allowedExtensions.includes(fileExtension);

        if (!result) {
            addNotification({
                title: t('error.unsuportedImageFile', 'Format file tidak didukung !'),
                message: t('error.supportedImageFile', 'Pastikan file memiliki format yg didukung (JPG, JPEG, PNG)'),
                level: 'error',
            });
        }

        return result;
    }

    createComponentProps = (type) => {
        const {
            images, qty, disabled, hidePrimaryLabel, hideRequiredLabel, addNotification, t: translation
        } = this.props;
        const { img } = this.state;
        let retval = {};

        switch (type) {
            case COMPONENT.CROPPER:
                retval = {
                    ref: (c) => {
                        this.cropper = c;
                    },
                    image: img,
                    onCrop: this.handleCrop,
                    onRequestHide: this.handleRequestHide,
                };
                break;
            case COMPONENT.GROUP:
                retval = {
                    images,
                    onChange: this.handleFileChange,
                    onRemove: this.handleRemoveImage,
                    qty,
                    disabled,
                    addNotification,
                    hidePrimaryLabel,
                    hideRequiredLabel,
                    doValidateFileChange: this.doValidateFileChange,
                    translation,
                };
                break;
            default:
                retval = {};
        }

        return retval;
    }

    render() {
        const { label, qualityRatio } = this.props;

        return (
            <div className="form-group">
                {label && <h5 className="color--263238"><b>{label}</b></h5>}
                <Group {...this.createComponentProps(COMPONENT.GROUP)} />
                <Cropper
                    {...this.createComponentProps(COMPONENT.CROPPER)}
                    aspectRatio={1}
                    imageQualityRatio={qualityRatio}
                />
            </div>
        );
    }
}

export default withTranslation(['translation'], { withRef: true })(ImageAsGroup);
