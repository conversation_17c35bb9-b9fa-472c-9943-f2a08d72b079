import React from 'react';
import PropTypes from 'prop-types';
import { Scrollbars } from 'react-custom-scrollbars';
import { FormWithConstraints } from 'react-form-with-constraints';

import ScrollWatcher from '../helper/ScrollWatcher';
import { isMobile } from '../../config/config';
// TODO:remove all react validations

import PopupCancelConfirmation from './PopupCancelConfirmation';

const style = {
    nanoContent: {
        position: 'relative',
    },
};
export default class SidePopup extends React.Component {
    static propTypes = {
        t: PropTypes.func,
        type: PropTypes.string,
        // show: PropTypes.bool,
        children: PropTypes.node,
        removeHandle: PropTypes.oneOfType([
            PropTypes.func,
            PropTypes.bool,
        ]),
        callDetail: PropTypes.oneOfType([
            PropTypes.bool,
            PropTypes.func,
        ]),
        saveHandle: PropTypes.oneOfType([
            PropTypes.bool,
            PropTypes.func,
        ]),
        submitHandle: PropTypes.oneOfType([
            PropTypes.bool,
            PropTypes.func,
        ]),
        voidHandle: PropTypes.oneOfType([
            PropTypes.bool,
            PropTypes.func,
        ]),
        aktifasiCabang: PropTypes.bool,
        width: PropTypes.number,
        saveDraft: PropTypes.oneOfType([
            PropTypes.bool,
            PropTypes.func,
        ]),
        btnDraftText: PropTypes.string,
        btnSaveText: PropTypes.string,
        msg: PropTypes.string,
        btnCancelText: PropTypes.oneOfType([
            PropTypes.string,
            PropTypes.bool,
        ]),
        btnDetailText: PropTypes.string,
        btnDetailDisabled: PropTypes.bool,
        onHide: PropTypes.func,
        render: PropTypes.func,
        titleWithBackground: PropTypes.string,
        confirmButtonClass: PropTypes.string,
        withCancelConfirmation: PropTypes.bool,
        actionsButtonRenderer: PropTypes.func,
        beforePerformingValidation: PropTypes.func,
        customLeftButtons: PropTypes.arrayOf(PropTypes.node),
        cancelHandle: PropTypes.func,
        showSaveButton: PropTypes.bool,
        loadingText: PropTypes.string,
        btnOtherDraftText: PropTypes.string,
    }

    static childContextTypes = {
        scrollBars: PropTypes.shape({}),
    };

    static defaultProps = {
        t: (_, text) => text,
        type: '',
        // show: false,
        children: (null),
        removeHandle: false,
        callDetail: false,
        saveHandle: false,
        submitHandle: false,
        voidHandle: false,
        aktifasiCabang: false,
        width: undefined,
        saveDraft: false,
        btnDraftText: '',
        btnSaveText: '',
        msg: '',
        btnCancelText: false,
        confirmButtonClass: undefined,
        btnDetailText: '',
        btnDetailDisabled: false,
        render: undefined,
        titleWithBackground: undefined,
        withCancelConfirmation: false,
        actionsButtonRenderer: null,
        onHide: undefined,
        beforePerformingValidation: () => { },
        customLeftButtons: undefined,
        cancelHandle: undefined,
        showSaveButton: false,
        loadingText: 'Tunggu ...',
        btnOtherDraftText: 'Draf',
    }

    constructor(props) {
        super(props);

        this.state = {
            // type: '',
            show: false,
            hiding: false,
            myHeight: 0,
            isScrolling: false,
            // timer: null,
            btnSaveDisabled: false,
            btnSaveClicked: null,
            btnSaveDraftClicked: null,
            scrollPosition: 0,
            isSubmitted: false,
        };

        // this.removeHandler = this.removeHandler.bind(this);
        // this.saveHandler = this.saveHandler.bind(this);
        // this.saveDraft = this.saveDraft.bind(this);
        // this.cancelHandler = this.cancelHandler.bind(this);
        // this.preventDefault = this.preventDefault.bind(this);
        // this.hidePopup = this.hidePopup.bind(this);
        // this.callDetail = this.callDetail.bind(this);
    }

    getChildContext() {
        return { scrollBars: this.scrollBars };
    }

    // componentDidMount() {
    //     this.setState({
    //         show: this.props.show ? this.props.show : false,
    //     });
    // }
    componentWillReceiveProps({ children }) {
        const { children: pChildren } = this.props;

        if (pChildren !== children) {
            this.setState({
                myHeight: this.sidePopupBody.getBoundingClientRect().height,
            });
        }
    }

    onScrollHandler(e) {
        const timer = setTimeout(() => {
            this.setState({
                scrollPosition: e.target.scrollTop,
            });
        }, 300);

        if (this.timer && this.timer !== timer) {
            clearTimeout(this.timer);
        }

        this.timer = timer;
    }

    removeHandler = (id) => {
        const { removeHandle } = this.props;

        removeHandle(id);
        this.ScrollWatcher.refresh();
    }

    voidHandler = () => {
        const { voidHandle } = this.props;

        voidHandle();
        this.ScrollWatcher.refresh();
    }

    callDetail = () => {
        const { callDetail } = this.props;

        callDetail();
        this.ScrollWatcher.refresh();
    }

    saveHandler = async (buttonType) => {
        const { saveHandle, beforePerformingValidation, loadingText } = this.props;

        beforePerformingValidation();

        const formValidatationResults = await this.form.validateForm();
        const formIsValid = this.form.isValid();

        if (formIsValid) {
            let button = 'btnSaveClicked';

            if (buttonType === 'draft') {
                button = 'btnSaveDraftClicked';
            }

            const updatedState = {
                btnSaveDisabled: true,
                [button]: isMobile.matches ? '...' : loadingText,
            };

            this.setState({
                ...updatedState,
            }, () => {
                saveHandle(buttonType, () => this.failedCallback({ [button]: null }));
                this.ScrollWatcher.refresh();
            });
        } else {
            try {
                formValidatationResults.forEach(({ name, validations }) => {
                    if (validations) {
                        validations.forEach(({ type, show }) => {
                            if (type === 'error' && show) {
                                throw name;
                            }
                        });
                    }
                });
            } catch (e) {
                const el = document.getElementsByName(e)[0];

                el.focus();
            }

            this.setState({ btnSaveDisabled: true, isSubmitted: true });
        }
    }

    validateSidebarForm = async (disableButtonAfterValid = false) => {
        const formValidate = await this.form.validateForm();
        const formIsValid = this.form.isValid();
        if (formIsValid) {
            this.setState({ btnSaveDisabled: true }, () => {
                if (!disableButtonAfterValid) this.failedCallback();
                this.ScrollWatcher.refresh();
            });
        } else {
            try {
                formValidate.forEach(({ name, validations }) => {
                    if (validations) {
                        validations.forEach(({ type, show }) => {
                            if (type === 'error' && show) {
                                throw name;
                            }
                        });
                    }
                });
            } catch (e) {
                const el = document.getElementsByName(e)[0];

                el.focus();
            }

            this.setState({ btnSaveDisabled: true, isSubmitted: true });
        }
        return formIsValid;
    }

    // saveDraft = async () => {
    //     const { saveHandle } = this.props;
    //     await this.form.validateForm();
    //     const formIsValid = this.form.isValid();

    //     if (formIsValid) {
    //         this.setState({
    //             btnSaveDisabled: true,
    //             btnSaveDraftClicked: 'Tunggu ...',
    //         }, () => {
    //             saveHandle('draft', () => this.failedCallback({ btnSaveDraftClicked: null }));
    //             this.ScrollWatcher.refresh();
    //         });
    //     } else {
    //         this.setState({ btnSaveDisabled: true });
    //     }
    // }

    failedCallback = (payload) => {
        this.setState({
            ...payload,
            btnSaveDisabled: false,
        });
    }

    cancelHandler = (event) => {
        const { withCancelConfirmation, cancelHandle } = this.props;

        event.stopPropagation();
        if (this.form) {
            this.form.reset(); // reset validation
        }

        this.ScrollWatcher.refresh();

        if (cancelHandle) {
            cancelHandle();
            return;
        }

        if (withCancelConfirmation) {
            this.popupCancelConfirmation.showPopup();
            return;
        }

        this.hidePopup();
    }

    showPopup = () => {
        document.body.style.overflow = 'hidden';
        this.setState({ show: true });
    }

    hidePopup = () => {
        document.body.style.overflow = 'auto';
        const { onHide, withCancelConfirmation } = this.props;

        if (withCancelConfirmation) this.popupCancelConfirmation.hidePopup();

        this.setState(({ btnSaveClicked, btnSaveDraftClicked }) => ({
            show: false,
            hiding: true,
            btnSaveClicked: btnSaveClicked ? 'Selesai' : null,
            btnSaveDraftClicked: btnSaveDraftClicked ? 'Selesai' : null,
        }));

        setTimeout(() => {
            this.setState({
                hiding: false,
                btnSaveDisabled: false,
                btnSaveClicked: null,
                btnSaveDraftClicked: null,
                isSubmitted: false,
            });
        }, 300);

        if (onHide) onHide(true);
    }

    validateInput = async (target, needSubmit = true) => {
        const { isSubmitted } = this.state;

        if (!needSubmit) await this.form.validateFields(target);

        if (isSubmitted) {
            if (needSubmit) await this.form.validateFields(target);
            this.setState({
                btnSaveDisabled: !this.form.isValid(),
            });
        }
    };

    resetValidateInput = async (inputsOrNames) => {
        await this.form.resetFields(...inputsOrNames);
        this.setState({
            btnSaveDisabled: !this.form.isValid(),
        });
    }

    setBtnSaveDisabled = (val) => {
        this.setState({
            btnSaveDisabled: val,
        });
    }

    recursiveCloneChildren(children) {
        const { myHeight, isScrolling, scrollPosition } = this.state;
        return React.Children.map(
            children,
            (child) => {
                let childProps = {};
                if (React.isValidElement(child)) {
                    if ('className' in child.props && (child.props.className.indexOf('date-time-picker-component') > -1)) {
                        childProps = {
                            parentHeight: myHeight,
                            isScrolling,
                            scrollPosition,
                        };
                    }
                }
                if (child) {
                    if (child.props) {
                        if (child.props.children) {
                            childProps.children = this.recursiveCloneChildren(child.props.children);

                            if (childProps.children.length && childProps.children.length === 1) {
                                const { children: [ch] } = childProps;
                                childProps.children = ch;
                            }
                        }
                        return React.cloneElement(child, childProps);
                    }
                    return child;
                }

                return (null);
            },
        );
    }

    _onSubmit(e) {
        const { submitHandle } = this.props;
        e.preventDefault();

        if (submitHandle) { submitHandle(); }
    }

    renderFooterButtons = () => {
        const {
            t, msg,
            type, voidHandle, removeHandle, btnCancelText,
            saveDraft,
            btnDraftText, callDetail, btnDetailDisabled, btnDetailText,
            saveHandle, confirmButtonClass, btnSaveText, customLeftButtons,
            btnSaveDisabledFromParent, showSaveButton, btnOtherDraftText,
        } = this.props;
        const { btnSaveDisabled, btnSaveDraftClicked, btnSaveClicked } = this.state;
        const leftButtons = [];
        const rightButtons = [];

        if ((type === 'edit' || type === 'delete') && removeHandle) {
            leftButtons.push(
                <button
                    key="1"
                    type="button"
                    className="btn btn-remove"
                    onClick={this.removeHandler}
                >
                    <i className="fa fa-trash" />
                </button>,
            );
        }

        if (voidHandle) {
            leftButtons.push(
                <button
                    key="2"
                    style={{ color: '#ef5350' }}
                    type="button"
                    className="btn"
                    onClick={this.voidHandler}
                >
                    Void
                </button>,
            );
        }

        if (customLeftButtons) {
            leftButtons.push(...customLeftButtons);
        }

        rightButtons.push(
            <button
                key="3"
                type="button"
                className="btn"
                onClick={this.cancelHandler}
            >
                {btnCancelText || t ? t('label.cancel', 'Batal', { ns: 'translation' }) : 'Batal'}
            </button>,
        );

        if (saveDraft) {
            rightButtons.push(
                <button
                    key="4"
                    type="button"
                    className="btn btn-warning"
                    onClick={() => this.saveHandler('draft')}
                    disabled={btnSaveDisabledFromParent || btnSaveDisabled}
                >
                    {btnSaveDisabled && btnSaveDraftClicked ? btnSaveDraftClicked : (btnDraftText || btnOtherDraftText)}
                </button>,
            );
        }

        if (callDetail) {
            rightButtons.push(
                <button
                    key="5"
                    type="button"
                    className="btn btn-warning"
                    onClick={this.callDetail}
                    disabled={btnDetailDisabled}
                >
                    {btnDetailText || 'Detail'}
                </button>,
            );
        }

        if ((saveHandle
            && (!((btnDraftText && type === 'create')))) || showSaveButton) {
                rightButtons.push(
                    <button
                        key="6"
                        type="button"
                        className={`${confirmButtonClass || ''} btn btn-primary`}
                        onClick={() => this.saveHandler('save')}
                        disabled={btnSaveDisabledFromParent || btnSaveDisabled}
                    >
                        {btnSaveDisabled && btnSaveClicked ? btnSaveClicked : (btnSaveText || t ? t('label.save', 'Simpan', { ns: 'translation' }) : 'Save')}
                    </button>,
                );
        }
        return (
            <div className="row">
                {leftButtons.length > 0 && <div className={`col-xs-${leftButtons.length >= 2 ? '6' : '3'}`}>{leftButtons}</div>}
                <div className={leftButtons.length > 0 ? `col-xs-${leftButtons.length >= 2 ? '6' : '9'} text-right` : 'col-xs-12 text-right'}>{rightButtons}</div>
                {msg
                    && (
                        <div className="col-xs-12">
                            <font color="red" style={{ fontSize: 12 }}>{msg}</font>
                        </div>
                    )
                }
            </div>
        );
    }

    render() {
        let { children } = this.props;
        const {
            width, render,
            titleWithBackground,
            actionsButtonRenderer,
        } = this.props;

        const {
            show, hiding, btnSaveDisabled,
        } = this.state;

        children = this.recursiveCloneChildren(children);

        return (
            <div className={`${(show ? 'active ' : '') + (hiding ? 'hiding ' : '')}side-popup-wrap`}>
                {/* onClick={this.cancelHandler} */}
                <FormWithConstraints
                    ref={(c) => { this.form = c; }}
                    onSubmit={(e) => { this._onSubmit(e); }}
                    noValidate
                >
                    <div
                        className="side-popup"
                        style={{ width: width || 400, paddingBottom: 74 }}
                        onClick={e => e.stopPropagation()}
                        onKeyPress={() => { }}
                        role="presentation"
                    >
                        <div className="side-popup-body" ref={(div) => { this.sidePopupBody = div; }}>
                            <div className="nano" style={{ height: '100%' }}>
                                <Scrollbars
                                    ref={(c) => { this.scrollBars = c; }}
                                    renderTrackHorizontal={props => <div {...props} className="track-horizontal" style={{ display: 'none' }} />}
                                    onScroll={val => this.onScrollHandler(val)}
                                >
                                    <ScrollWatcher ref={(watcher) => { this.ScrollWatcher = watcher; }} />
                                    <div className="nano-content" style={style.nanoContent}>
                                        {titleWithBackground && <div className="nano-content__title-with-background"><h2>{titleWithBackground}</h2></div>}
                                        <div className="side-popup-body-inner">
                                            {render && render({ show, validateInput: this.validateInput, resetValidateInput: this.resetValidateInput, setBtnSaveDisabled: this.setBtnSaveDisabled })}
                                            {!render && children}
                                        </div>
                                    </div>
                                </Scrollbars>
                            </div>
                        </div>

                        <div className="side-popup-footer">
                            {actionsButtonRenderer ? (
                                actionsButtonRenderer({
                                    validateForm: ({ disableButtonAfterValid = false } = {}) => this.validateSidebarForm(disableButtonAfterValid),
                                    disabled: btnSaveDisabled,
                                    closeSidebar: event => this.cancelHandler(event),
                                    callback: () => this.failedCallback(),
                                })
                            ) : this.renderFooterButtons()}
                        </div>

                        <PopupCancelConfirmation
                            ref={(c) => { this.popupCancelConfirmation = c; }}
                            onConfirm={this.hidePopup}
                        />
                    </div>
                </FormWithConstraints>
            </div>
        );
    }
}
