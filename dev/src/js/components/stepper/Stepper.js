import React from 'react';
import PropTypes from 'prop-types';

import './stepper.css';

function getCircleClass(activeIndex, index) {
    if (activeIndex === index) {
        return 'stepper-circle__active';
    }
    if (activeIndex > index) {
        return 'stepper-circle__complete';
    }
    return '';
}

function getLineClass(length, activeIndex, index) {
    if (index === -1 || index === length - 1) {
        return 'stepper-line__hidden';
    }
    if (activeIndex === index) {
        return 'stepper-line__active';
    }
    if (activeIndex > index) {
        return 'stepper-line__complete';
    }
    return '';
}

function Stepper({
    activeIndex,
    steps,
    width,
}) {
    return (
        <div className="stepper" style={{ width }}>
            {steps.map((step, index) => (
                <div key={step} className="stepper-step">
                    <div className="stepper-step-indicator">
                        <div className={`stepper-line ${getLineClass(steps.length, activeIndex, index - 1)}`} />
                        <div className={`stepper-circle ${getCircleClass(activeIndex, index)}`}>
                            {activeIndex > index && <div className="fa fa-check" />}
                        </div>
                        <div className={`stepper-line ${getLineClass(steps.length, activeIndex, index)}`} />
                    </div>
                    {step}
                </div>
            ))}
        </div>
    );
}

Stepper.propTypes = {
    activeIndex: PropTypes.number.isRequired,
    steps: PropTypes.arrayOf(PropTypes.string).isRequired,
    width: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
};

Stepper.defaultProps = {
    width: 500,
};

export default Stepper;
