import React from "react";
import PropTypes from "prop-types";
import { phonePreviewDeviceType } from "../../../config/enum";
import androidPhoneFrame from "../../../../assets/images/phone-android.png";
import iPhoneFrame from "../../../../assets/images/phone-iphone.png";
import logoTagline from '../../../../assets/images/logo_with_tagline.png';
import "./styles.less";

const PhonePreview = ({
    deviceType,
    showFooter,
    showContentSlider,
    onNextContent,
    onPrevContent,
    disabledPrevBtn,
    disabledNextBtn,
    children,
    size: { height, width },
    contentStyle,
    contentClassName,
    footerStyle,
    footerClassName,
    footerLogoWidth,
}) => {
    const deviceSrc = deviceType === phonePreviewDeviceType.ANDROID ? androidPhoneFrame : iPhoneFrame;

    return (
        <div className="phonepreview">
            {showContentSlider && (
                <button
                    type="button"
                    className="btn btn-default btn-sm btn--prev"
                    disabled={disabledPrevBtn}
                    onClick={onPrevContent}
                >
                    <i className="fa fa-chevron-left" aria-hidden="true" />
                </button>
            )}
            <div
                className="phonepreview__phoneholder"
                style={{
                    width,
                    height,
                }}
            >
                <img
                    src={deviceSrc}
                    alt="phonepreview"
                    className="phonepreview__frame"
                    width="100%"
                    height="100%"
                />

                <div className="phonepreview__body">
                    <div
                        className={`phonepreview__content ${contentClassName}`}
                        style={{ ...contentStyle }}
                    >
                        {children}
                    </div>
                    {showFooter && (
                        <div
                            className={`phonepreview__footer ${footerClassName}`}
                            style={{ ...footerStyle }}
                        >
                            <div>Powered by</div>
                            <img
                                src={logoTagline}
                                alt="majoo"
                                width={footerLogoWidth}
                            />
                        </div>
                    )}
                </div>
            </div>
            {showContentSlider && (
                <button
                    type="button"
                    className="btn btn-default btn-sm btn--next"
                    disabled={disabledNextBtn}
                    onClick={onNextContent}
                >
                    <i className="fa fa-chevron-right" aria-hidden="true" />
                </button>
            )}
        </div>
    );
};

PhonePreview.propTypes = {
    deviceType: PropTypes.string,
    showFooter: PropTypes.bool,
    showContentSlider: PropTypes.bool,
    onNextContent: PropTypes.func,
    onPrevContent: PropTypes.func,
    disabledNextBtn: PropTypes.bool,
    disabledPrevBtn: PropTypes.bool,
    children: PropTypes.node,
    size: PropTypes.shape({
        height: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
        width: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    }),
    contentStyle: PropTypes.shape({}),
    contentClassName: PropTypes.string,
    footerStyle: PropTypes.shape({}),
    footerClassName: PropTypes.string,
    footerLogoWidth: PropTypes.string,
};

PhonePreview.defaultProps = {
    deviceType: phonePreviewDeviceType.ANDROID,
    showFooter: true,
    showContentSlider: false,
    onNextContent: () => {},
    onPrevContent: () => {},
    disabledNextBtn: false,
    disabledPrevBtn: false,
    children: undefined,
    size: {
        height: 400,
        width: 180,
    },
    contentStyle: {},
    contentClassName: "",
    footerStyle: {},
    footerClassName: "",
    footerLogoWidth: "34px",
};

export default PhonePreview;
