.phonepreview {
  display: flex;
  align-items: center;

  &__phoneholder {
    position: relative;
  }

  &__frame {
    position: relative;
    z-index: 90;
  }

  &__body {
    position: absolute;
    inset: 0 1px 0 0;
    display: flex;
    flex-direction: column;
    z-index: 80;
  }

  &__content {
    flex: 1;
    background: #fff;
    border-radius: 26px 26px 0 0;
    overflow: hidden;
  }

  &__footer {
    background: #e6f8f9;
    font-size: 8px;
    height: 24px;
    border-radius: 0 0 26px 26px;
    margin: 0 0 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    
    img {
      margin-left: 8px;
    }
  }

  .btn--prev {
    margin-right: 6px;
  }

  .btn--next {
    margin-left: 6px;
  }
}
