/* eslint-disable react/prop-types */
import React from 'react';
import { Flex } from '@majoo-ui/react';
import { ChevronRightOutline, ChevronDownOutline } from '@majoo-ui/icons';

const RowExpanderColumn = props => ({
    id: 'expander',
    Header: () => null,
    Cell: ({ row, state, toggleAllRowsExpanded }) => (
        <Flex
            justify="center"
            {...row.getToggleRowExpandedProps()}
            onClick={() => {
                if (!Object.keys(state.expanded).includes(String(row.id))) {
                    toggleAllRowsExpanded(false);
                }
                row.getToggleRowExpandedProps().onClick();
            }}
        >
            {row.isExpanded ? <ChevronDownOutline /> : <ChevronRightOutline />}
        </Flex>
    ),
    unsortable: true,
    ...props,
});

export default RowExpanderColumn;
