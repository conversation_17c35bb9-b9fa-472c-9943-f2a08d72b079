import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import {
    Banner,
    Box,
    BannerDescription,
    Flex,
    BannerLink
} from '@majoo-ui/react';

const BannerAPK = ({ context, version, t }) => (
    <Banner
        bannerBlock
        variant="info"
    >
        <Box css={{ '& p': { whiteSpace: 'normal' } }}>
            <BannerDescription>
                {t('warning.apkVersion', 'hanya dapat digunakan pada aplikasi versi', { context, version })}
            </BannerDescription>
        </Box>
        <Flex justify="end" css={{ width: '100%' }}>
            <BannerLink link={`${process.env.PORTAL_BASE_URL}/hubungi-kami`}>{t('label.callUs', 'Hubungi Kami')}</BannerLink>
        </Flex>
    </Banner>
);

BannerAPK.propTypes = {
    version: PropTypes.string,
    context: PropTypes.string,
    t: PropTypes.func.isRequired,
};

BannerAPK.defaultProps = {
    version:  '3.1.5xxx',
    context: '',
};

export default withTranslation(['translation'])(BannerAPK);