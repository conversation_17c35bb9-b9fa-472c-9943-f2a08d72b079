import * as React from 'react';
import * as PropTypes from 'prop-types';
import * as _ from 'lodash';
import { connect } from 'react-redux';
import { useTranslation } from 'react-i18next';
import {
  Box, Button, Flex, Heading, Image, Paragraph,
} from '@majoo-ui/react';
import { ChevronRightOutline } from '@majoo-ui/icons';

import IconBanner from './icon-banner-text.svg';
import { ConditionalWrapper } from '../../wrapper/ConditionalWrapper';
import { isMobile as mobileQuery } from '../../../config/config';

import { flat } from '../../../data/layouts';
import { analyticLogEvent } from '../../../v2-utils/analytic';

const handleAnalyticLogEvent = ({
  userEmail, userStrap, pageInfo,
}) => {
  const bannerTitle = _.get(pageInfo, 'page_banner.name');
  const bannerText = _.get(pageInfo, 'page_banner.description_ind');
  const payload = {
    page_referrer: window.location.pathname,
    user_email: userEmail,
    outlet_id: userStrap.branchId,
    banner_title: bannerTitle,
    action_banner_text: bannerText,
  };
  analyticLogEvent('banner_text', payload);
};

const BannerText = (props) => {
  const { i18n: { language }, t } = useTranslation('translation');
  const { css, layouts: { menu, activeMenu } } = props;
  const [pageInfo, setPageInfo] = React.useState({});
  const isMobile = mobileQuery.matches;

  React.useEffect(() => {
    if (menu && activeMenu) {
      setPageInfo(flat(menu).find(x => String(x.id) === String(activeMenu)));
    }
  }, [menu, activeMenu]);

  if (_.get(pageInfo, 'page_banner.type') !== '1') return null;

  return (
    <Flex
      direction="row"
      align="center"
      gap={4}
      css={{
        userSelect: 'none', padding: '$spacing-05', my: '$spacing-05', borderRadius: '$lg', border: '1px solid $bgBorder', boxShadow: '0px 1px 4px 0px rgba(0, 0, 0, 0.04)', ...css,
      }}
    >
      <Image css={{ width: 24, height: 24, '@md': { width: 16, height: 16 } }} src={IconBanner} alt="icon-banner" />
      <Flex css={{ flex: 1 }} direction={isMobile ? 'column' : 'row'} {...!isMobile && { justify: 'between', align: 'center' }}>
        <ConditionalWrapper
          condition={!isMobile}
          wrapper={child => (
            <Box>
              {child}
              <Paragraph paragraph="longContentRegular">
                {language === 'id' ? _.get(pageInfo, 'page_banner.description_ind') : _.get(pageInfo, 'page_banner.description_en')}
              </Paragraph>
            </Box>
          )}
        >
          <Heading heading="sectionTitle">{language === 'id' ? _.get(pageInfo, 'page_banner.name') : _.get(pageInfo, 'page_banner.name_en')}</Heading>
        </ConditionalWrapper>
        <Button
          onClick={() => {
            handleAnalyticLogEvent({
              userEmail: props.userEmail, userStrap: props.userStrap, pageInfo,
            });
            window.open(`${_.get(pageInfo, 'page_banner.link')}`, '_blank');
          }}
          css={isMobile && {
            justifyContent: 'unset', paddingRight: 0, paddingLeft: 0, width: 'fit-content',
          }}
          buttonType="ghost"
          rightIcon={<ChevronRightOutline color="currentColor" />}
        >
          {t('label.learnMore', 'Pelajari selengkapnya')}
        </Button>
      </Flex>
    </Flex>
  );
};

BannerText.propTypes = {
  css: PropTypes.shape({}),
  layouts: PropTypes.shape({
    menu: PropTypes.arrayOf(PropTypes.shape({})),
    activeMenu: PropTypes.string,
  }),
};

BannerText.defaultProps = {
  css: {},
  layouts: { menu: {}, activeMenu: '' },
};

const mapStateToProps = store => ({
  layouts: {
    menu: store.layouts.menu,
    activeMenu: store.layouts.activeMenu,
  },
  userEmail: store.accountInfo.accountInfoResult.user_email,
  userStrap: store.users.strap,
});

export default connect(mapStateToProps)(BannerText);
