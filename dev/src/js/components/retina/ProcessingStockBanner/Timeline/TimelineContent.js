import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { Flex, Paragraph, Text } from '@majoo-ui/react';

const paragraphStyle = {
    overflow: 'hidden',
    whiteSpace: 'wrap',
    textOverflow: 'ellipsis',
};

export const timelineDate = tgl => `${moment(tgl, 'YYYY-MM-DD hh:mm:ss').format('DD MMM YYYY | HH:mm')} WIB`;

export function TimelineContent({ detail, translator }) {
    const { transaction_no: transactionNo, type, processed_at: processedAt } = detail;
    let transactionName = '';
    switch (type) {
        case 1:
            transactionName = 'Faktur Pembelian';
            break;
        case 2:
            transactionName = 'Faktur Pembelian';
            break;
        case 3:
            transactionName = 'Stok Opname';
            break;
        case 4:
            transactionName = 'Terima Mutasi Stok';
            break;
        case 5:
            transactionName = 'Transaksi Penjualan';
            break;
        case 6:
            transactionName = 'Retur Penjualan';
            break;
        case 7:
            transactionName = 'Produks<PERSON> Stok';
            break;
        case 9:
            transactionName = 'Invoice';
            break;
        case 11:
            transactionName = 'Stok Terbuang';
            break;
        case 12:
            transactionName = 'Retur Pembelian';
            break;
        default:
            break;
    }
    return transactionName ? (
        <Flex direction="column" gap={2}>
            <Paragraph variant="longContentRegular" color="primary" css={paragraphStyle}>
                <b>{transactionName}</b> nomor <b>{transactionNo}</b>
            </Paragraph>
            <Text variant="caption">{timelineDate(processedAt)}</Text>
        </Flex>
    ) : (
        <React.Fragment />
    );
}

TimelineContent.propTypes = {
    detail: PropTypes.shape({
        transaction_no: PropTypes.string.isRequired,
        type: PropTypes.number.isRequired,
        processed_at: PropTypes.string.isRequired,
    }).isRequired,
    translator: PropTypes.func.isRequired,
};
