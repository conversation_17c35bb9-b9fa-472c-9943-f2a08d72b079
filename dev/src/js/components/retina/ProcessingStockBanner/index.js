import React, { useContext, useEffect, useState, useRef } from 'react';
import PropTypes from 'prop-types';
import {
    Banner,
    Flex,
    Box,
    Text,
    Link,
    ModalDialog,
    ModalDialogTitle,
    ModalDialogContent,
    ToastContext,
    Separator,
} from '@majoo-ui/react';
import { CircleInfoFilled } from '@majoo-ui/icons';
import { useTranslation } from 'react-i18next';
import { useMediaQuery } from '~/utils/useMediaQuery';
import { getCalculationStatus } from '~/data/inventories';
import { catchError } from '~/utils/helper';
import { TimelineContent } from './Timeline/TimelineContent';

const ProcessingStockDetailModal = ({ open, onOpenChange, t, details }) => {
    const isMobile = useMediaQuery('(max-width: 1024px)');

    return (
        <ModalDialog
            open={open}
            isMobile={isMobile}
            onOpenChange={onOpenChange}
            modal
            allowPinchZoom={false}
            size="md"
            layer={5}
        >
            <ModalDialogTitle>{t('processingStockBanner.modalTitle')}</ModalDialogTitle>
            <ModalDialogContent css={{ padding: '16px 10px' }}>
                {details.length > 0 ? (
                    <div
                        className="timeline"
                        style={{
                            '--borderHeight': details.length > 0 ? `${72 * (details.length - 1)}px` : '0px',
                        }}
                    >
                        {details.map(log => (
                            <div className="timeline-container timeline-right" key={log.id}>
                                <Flex
                                    direction="column"
                                    gap={2}
                                    css={{ position: 'relative', top: '-6px', minHeight: '56px' }}
                                >
                                    <TimelineContent detail={log} translator={t} />
                                    <Separator css={{ marginTop: '4px', marginBottom: '4px' }} />
                                </Flex>
                            </div>
                        ))}
                    </div>
                ) : (
                    <Flex
                        justify="center"
                        align="center"
                        css={{
                            padding: '32px',
                            width: '100%',
                            '& > div': {
                                width: '100%',
                            },
                        }}
                    >
                        <Text variant="caption">
                            {t('label.noData', { defaultValue: 'Tidak ada data', ns: 'translation' })}
                        </Text>
                    </Flex>
                )}
            </ModalDialogContent>
        </ModalDialog>
    );
};

ProcessingStockDetailModal.propTypes = {
    open: PropTypes.bool.isRequired,
    onOpenChange: PropTypes.func.isRequired,
    t: PropTypes.func.isRequired,
    details: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
};

function ProcessingStockBanner() {
    const { t } = useTranslation(['Penjualan/inventory', 'translation']);
    const { addToast } = useContext(ToastContext);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [details, setDetails] = useState([]);
    const [showBanner, setShowBanner] = useState(false);
    const intervalRef = useRef(null);

    const fetchCalculationStatus = async () => {
        try {
            const res = await getCalculationStatus();

            if (res && res.data) {
                setDetails(res.data.details);
                if (showBanner && !res.data.status) {
                    window.location.reload();
                } else {
                    setShowBanner(res.data.status);
                }
            }
        } catch (error) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(error),
                variant: 'failed',
            });
        }
    };

    // Disabled due to server performance issue
    // useEffect(() => {
    //     // Initial fetch
    //     fetchCalculationStatus();

    //     // Set up polling interval (every 5 seconds)
    //     intervalRef.current = setInterval(() => {
    //         fetchCalculationStatus();
    //     }, 5000);

    //     // Clean up interval on component unmount
    //     return () => {
    //         if (intervalRef.current) {
    //             clearInterval(intervalRef.current);
    //         }
    //     };
    // }, []);

    return showBanner ? (
        <React.Fragment>
            <Box
                css={{
                    marginTop: '$spacing-05',
                    '> div > div': {
                        width: '100%',
                    },
                    '> div > div > div': {
                        width: '100%',
                    },
                    '> div > div > div > div:first-child': {
                        flex: 1,
                    },
                    '& p': {
                        maxWidth: 'unset',
                    },
                }}
            >
                <Banner
                    title={t('processingStockBanner.title')}
                    description={
                        <Flex align="center" justify="between">
                            <Text>{t('processingStockBanner.description')}</Text>
                            <Link onClick={() => setShowDetailModal(true)}>{t('processingStockBanner.linkText')}</Link>
                        </Flex>
                    }
                    variant="info"
                    icon={() => <CircleInfoFilled />}
                />
            </Box>
            {showDetailModal && (
                <ProcessingStockDetailModal
                    open={showDetailModal}
                    onOpenChange={setShowDetailModal}
                    t={t}
                    details={details}
                />
            )}
        </React.Fragment>
    ) : null;
}

export default ProcessingStockBanner;
