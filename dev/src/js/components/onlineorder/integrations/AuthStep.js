import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { useHistory } from 'react-router-dom';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import { isArray } from 'lodash';
import {
 Flex, Tag, Button, InputSelect, ModalDialog, ModalDialogContent, ModalDialogFooter, ModalDialogTitle, Text, Box,
} from '@majoo-ui/react';
import IconFailed from '~/images/icon-failed.png';
import {
    getImgProviderFile, providerName, style,
} from '../utils/utils';
import { checkParamsIsValid, getQueryParams } from '../../../utils/queryParams.util';
import * as marketplaceApi from '../../../data/marketplace';
import { catchError } from '../../../utils/helper';
import ModalPopup from '../../modalpopup/v.2/Container';
import warningImage from '../../../../assets/images/icon-warning.png';
import successImage from '../../../../assets/images/icons/icon_success_download.png';
import { PROVIDER_ID, STEP_INTEGRATION } from '../utils/enums';
import { deleteInfoIntegration, getInfoIntegration, saveInfoIntegration } from '../utils/storage';
import StepProgress from '../components/StepProgress';
import { isMobile } from '../../../config/config';
import FooterButtonPanel from '../components/FooterButtonPanel';
import '../styles/callback.less';
import { getOutletIntegrated } from '../../../data/marketplace/api';

@connect(state => ({
    email: state.accountInfo.accountInfoResult.user_email,
    outlet_name: state.accountInfo.accountInfoResult.cabang_name,
}))
class ClassComponent extends PureComponent {
    constructor(props) {
        super(props);

        this.state = {
            outletInfo: {},
            outletData: [],
            selectedOutlet: { id: '', mid: '' },
            isProcessIntegration: false,
            showPopup: false,
            submittedIntegration: false,
            successIntegration: false,
            errorMessage: '',
        };
    }

    componentDidMount() {
        const { hideProgress, assignButtons, assignCalendar } = this.props;

        assignCalendar(null, null, null);
        assignButtons([]);
        hideProgress();

        this.handleCheckParams();
    }

    handleFetchOutlet = async (code) => {
        const {
            showProgress, addNotification, hideProgress,
        } = this.props;
        showProgress();

        try {
            const response = await getOutletIntegrated({ code, provider_id: 9 });
            const dataOutlet = this.checkIntegrationStatus();

            this.setState({
                outletInfo: dataOutlet,
                isProcessIntegration: true,
                outletData: response.data.outlets.map(outlet => ({
                    name: outlet.provider_merchant_name,
                    value: outlet.provider_merchant_id,
                    isDisabled: outlet.is_integrated,
                    render: () => (outlet.is_integrated ? (
                            <Flex align="center" justify="between">
                                <Text>{outlet.provider_merchant_name}</Text>
                                <Tag readOnly css={{ span: { overflow: 'unset', color: '#A5ABAB' }, maxWidth: 'unset' }}>Telah Diintegrasikan</Tag>
                            </Flex>
                        ) : outlet.provider_merchant_name),
                })),
            });
        } catch (err) {
            addNotification({
                title: 'Terjadi Kesalahan',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    }

    handleCheckParams = () => {
        const { routeParams } = this.props;
        const params = getQueryParams();
        const hasCodeParam = checkParamsIsValid(params, ['code']);

        if (!checkParamsIsValid(routeParams, ['id']) && !hasCodeParam) {
            this.handleCancelIntegration();
        } else if (hasCodeParam) {
            this.handleFetchOutlet(params.code);
        }
    }

    doCreatingSubmission = async (params) => {
        const { selectedOutlet } = this.state;
        const { provider, onChangeStep, routeParams } = this.props;
        const payload = {
            code: params.code,
            provider_id: Number(provider),
            outlet_id: provider === PROVIDER_ID.GOBIZ ? Number(selectedOutlet.id) : Number(routeParams.id),
            mid: provider === PROVIDER_ID.GOBIZ ? selectedOutlet.mid : '',
        };

        this.setState({ submittedIntegration: true });

        await marketplaceApi.postGobizAuth(payload)
            .then(() => this.setState({
                isProcessIntegration: false,
                successIntegration: true,
            }, () => onChangeStep(STEP_INTEGRATION.SELESAI)))
            .catch(e => this.setState({
                errorMessage: catchError(e) === '' ? 'Something Wrong' : catchError(e),
                isProcessIntegration: false,
                successIntegration: false,
            }));

        deleteInfoIntegration();
    }

    tryAgain = () => {
        const params = getQueryParams();
        this.setState({ errorMessage: '', isProcessIntegration: true }, () => {
            this.doCreatingSubmission(params);
            this.bodyPopup.failedCallback();
        });
    }

    handleCancelIntegration = () => {
        const { router, provider } = this.props;
        const urlProvider = providerName(provider).toLowerCase();

        router.push(`/${urlProvider}`);
    }

    handleProcessIntegration = async () => {
        const {
            showProgress, addNotification, hideProgress, outletData,
        } = this.props;

        try {
            const authURL = await this.doFetchingAuthUrl();
            const dataOutlet = this.checkIntegrationStatus();

            if (dataOutlet !== null) {
                return this.setState({
                    outletInfo: dataOutlet,
                    showPopup: true,
                });
            }

            showProgress();
            saveInfoIntegration(outletData.outlets[0]);

            window.location.href = authURL;
            return null;
        } catch (e) {
            console.error(e);
            addNotification({
                title: 'Terjadi Kesalahan',
                message: catchError(e),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    }

    doFetchingAuthUrl = async () => {
        const { provider } = this.props;

        const res = await marketplaceApi.getGobizAuthURL({ provider });

        return res.data;
    }

    checkIntegrationStatus = () => {
        const result = getInfoIntegration();

        return result;
    }

    resetIntegration = async () => {
        deleteInfoIntegration();
        await this.handleProcessIntegration();
    }

    getEmailOutletName = () => {
        const { email, outlet_name } = this.props;
        const splittedEmail = email.split('@');
        const transformFirstLetter = [splittedEmail[0].slice(0, 1)];
        const nextLetter = splittedEmail[0].slice(1);

        for (let index = 0; index < nextLetter.length; index++) {
            transformFirstLetter.push('*');
        }

        return { email: `${transformFirstLetter.join('')}@${splittedEmail[1]}`, outlet_name };
    }

    render() {
        const {
            provider, onChangeStep, step, t: translation, browserHistory, routeParams, isFoldedMenu,
        } = this.props;
        const {
            outletInfo, isProcessIntegration, errorMessage, outletData, selectedOutlet,
            submittedIntegration, successIntegration,
        } = this.state;
        const { outlet_name: outletName, outlet_id: outletId } = outletInfo;
        const imageFile = getImgProviderFile(provider);
        const contextText = translation('integration.contextText', { returnObjects: true, provider: providerName(provider) });
        const { email, outlet_name: globalOutletname } = this.getEmailOutletName();

        return (
            <React.Fragment>
                <section className="panel">
                    <div className="panel-heading table-header" style={{ padding: submittedIntegration ? '24px 12px' : 'inherit' }}>
                        <div className={isMobile.matches ? '' : 'space-bettwen'}>
                            <h4 className="title">{translation('integration.titleInfo', 'Langkah Integrasi')}</h4>
                            {!submittedIntegration && (
                                <StepProgress
                                    title={translation('integration.stepTitle.validation', 'Validasi Data Outlet')}
                                    stepCount={2}
                                    percentBar="100%"
                                    finalStep={step}
                                />
                            )}
                        </div>
                    </div>
                    <div className="panel-body" style={style.panel}>
                        {submittedIntegration && !successIntegration && (
                            <Flex justify="start" align="center" direction="column" css={{ mt: '20px', gap: '16px' }}>
                                <Box>
                                    <img src={warningImage} alt="warning-reject" style={{ objectFit: 'cover', width: '64px' }} />
                                </Box>
                                <Box css={{ textAlign: 'center' }}>
                                    <Text color="primary" css={{ textAlign: 'center', fontSize: '20px', fontWeight: 600 }}>Proses Integrasi Gagal</Text>
                                </Box>
                                <Box css={{ textAlign: 'center', maxWidth: '512px' }}>
                                    <Text color="#585F5F" css={{ textAlign: 'center !important', fontSize: '14px', fontWeight: 400 }}>
                                        {errorMessage}
                                    </Text>
                                </Box>
                            </Flex>
                        )}
                        {!submittedIntegration && (
                            <div style={style.container} className="text-center mt-sm">
                                <h5 className="panel-body-title">{translation('integration.title', `Proses Integrasi Majoo x ${providerName(provider)}`, { provider: providerName(provider) })}</h5>
                                <div className="mb-sm">
                                    <img style={{ height: '80px' }} src={imageFile} alt="Majoo Integration" />
                                </div>
                                {isArray(contextText) && contextText.map(text => (<p key={text} className="mb-sm">{text}</p>))}
                            </div>
                        )}
                    </div>
                </section>
                <FooterButtonPanel isFoldedMenu={isFoldedMenu}>
                    <div className={isMobile.matches ? 'space-bettwen' : 'd-flex justify-content-end mr-md'}>
                        <button type="button" className="btn btn-default" onClick={this.handleCancelIntegration}>{translation('label.cancel', 'Batal', { ns: 'translation' })}</button>
                        <button type="button" className="btn btn-primary" onClick={() => (submittedIntegration ? this.resetIntegration() : this.handleProcessIntegration())}>
                            {submittedIntegration ? 'Ulangi' : translation('label.processIntegration', 'Proses Integrasi', { ns: 'translation' })}
                        </button>
                    </div>
                </FooterButtonPanel>
                <ModalPopup
                    confirmHandle={() => browserHistory.push('/support/buy')}
                    ref={(c) => { this.failedProgress = c; }}
                    confirmText={translation('integration.failed.confirmLabel', 'Upgrade Langganan Advance atau Prime')}
                    cancelText={translation('label.close', 'Tutup', { ns: 'translation' })}
                    fixedWidth={450}
                >
                    <div className="text-center" style={{ maxWidth: '700px' }}>
                        <div>
                            <img src={warningImage} alt="warning-reject" style={{ objectFit: 'cover', width: '64px' }} />
                        </div>
                        <h4 className="title-modal">{translation('integration.failed.title', 'Proses Gagal')}</h4>
                        <p className="text">
                            {translation('integration.failed.description', 'Outlet terkait harus memiliki Layanan Support Tambahan Ecommerce untuk mengaktifkan fitur ini. Lakukan pembelian Layanan Support Tambahan -  Ecommerce, dengan tap button “Beli Layanan Support Tambahan”')}
                        </p>
                    </div>
                </ModalPopup>
                <ModalPopup
                    confirmHandle={() => onChangeStep(STEP_INTEGRATION.PETAKAN_PRODUCT)}
                    ref={(c) => { this.successAuth = c; }}
                    confirmText={translation('label.done', 'Selesai', { ns: 'translation' })}
                    fixedWidth={450}
                >
                    <div className="text-center" style={{ maxWidth: '700px' }}>
                        <div>
                            <img src={successImage} alt="warning-reject" style={{ objectFit: 'cover', width: '64px' }} />
                        </div>
                        <h4 className="title-modal">{translation('integration.success.title', 'Integrasi Berhasil')}</h4>
                        <p className="text">
                            {translation('integration.success.description', 'Outlet telah terintegrasi dengan fitur GoBiz. Silakan lakukan pengaturan lebih lanjut untuk menggunakan fitur ini.')}
                        </p>
                    </div>
                </ModalPopup>
                <ModalPopup
                    confirmHandle={this.resetIntegration}
                    ref={(c) => { this.preventive = c; }}
                    show={this.state.showPopup}
                    confirmText="Reset"
                    cancelText={translation('label.cancel', 'Batal', { ns: 'translation' })}
                    fixedWidth={450}
                >
                    <Flex justify="center" direction="column" className="text-center">
                        <Flex justify="center">
                            <img src={warningImage} alt="warning-reject" style={{ objectFit: 'cover', width: '64px' }} />
                        </Flex>
                        <h4 className="title-modal">{translation('integration.prevent.title', 'Proses tidak bisa dilanjutkan')}</h4>
                        <p className="text">
                            {translation('integration.prevent.description', `Terdeteksi outlet ${outletName} sedang berproses integrasi, apakah anda akan membatalkan proses yang berjalan ?`, { outlet: outletName })}
                        </p>
                    </Flex>
                </ModalPopup>
                <ModalDialog size="lg" modal layer="$modal" open={isProcessIntegration}>
                    <ModalDialogTitle>Pilih Outlet</ModalDialogTitle>
                    <ModalDialogContent>
                        <Text color="#585F5F" css={{ fontWeight: 400, fontSize: '14px' }}>
                            Akun GoBiz (
                            {email}
                            ) berhasil diverifikasi. Pilih cabang yang akan diintegrasikan dengan pengaturan outlet majoo
                            {' '}
                            <b>{outletName || globalOutletname}</b>
                            .
                        </Text>
                        <Box css={{ mt: '16px' }}>
                            <Text color="primary" css={{ fontSize: '12px' }}>Cabang</Text>
                            <InputSelect
                                value={outletData.find(o => o.value === selectedOutlet.mid)}
                                option={outletData}
                                onChange={e => this.setState({
                                        selectedOutlet: {
                                            id: outletId || routeParams.id,
                                            mid: String(e.value),
                                        },
                                    })
                                }
                            />
                        </Box>
                    </ModalDialogContent>
                    <ModalDialogFooter css={{ display: 'flex', gap: '$compact' }}>
                        <Button buttonType="ghost" size="sm">
                            Batal
                        </Button>
                        <Button disabled={Object.values(selectedOutlet).every(val => !val)} onClick={() => this.doCreatingSubmission(getQueryParams())} variant="green" size="sm">
                            Simpan
                        </Button>
                    </ModalDialogFooter>
                </ModalDialog>
            </React.Fragment>
        );
    }
}

ClassComponent.propTypes = {
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    hideProgress: PropTypes.func,
    showProgress: PropTypes.func,
    addNotification: PropTypes.func,
    provider: PropTypes.number.isRequired,
    router: PropTypes.shape({
        push: PropTypes.func,
    }).isRequired,
    routeParams: PropTypes.shape({
        id: PropTypes.number,
    }),
    onChangeStep: PropTypes.func,
    outletData: PropTypes.shape({
        outlets: PropTypes.shape([]),
    }),
    step: PropTypes.number,
    t: PropTypes.func,
    isFoldedMenu: PropTypes.bool,
};

ClassComponent.defaultProps = {
    assignCalendar: () => { },
    assignButtons: () => { },
    hideProgress: () => { },
    showProgress: () => { },
    addNotification: () => { },
    routeParams: {},
    onChangeStep: () => { },
    outletData: {},
    step: 0,
    t: () => { },
    isFoldedMenu: false,
};

const AuthStep = (props) => {
    const history = useHistory();

    return <ClassComponent {...props} browserHistory={history} />;
};

export default withTranslation(['FoodOrder/main', 'translation'])(AuthStep);
