import React from 'react';
import PropTypes from 'prop-types';
import { Element } from 'react-scroll';

import CheckListItem from './CheckListItem';


export default class CheckList extends React.Component {
    static propTypes = {
        data: PropTypes.arrayOf(PropTypes.array),
        checkChangeHandle: PropTypes.func,
        type: PropTypes.string,
        message: PropTypes.string,
    }

    static defaultProps = {
        data: PropTypes.arrayOf(PropTypes.object),
        checkChangeHandle: () => { },
        type: '',
        message: '',
    }

    refreshCheckList() {
        document.getElementById('checklist-react-scroll').scrollTop = 0;
    }

    render() {
        const {
            data, checkChangeHandle, type, message,
        } = this.props;

        return (
            <div className="checklist-wrap">
                <div className="nano">
                    <div className="nano-content">
                        <Element
                            className="scroll-area-custom grey-scrollbar-track"
                            style={{
                                height: '300px',
                            }}
                            id="checklist-react-scroll"
                        >
                            <ul className="checklist">
                                {
                                    message !== '' &&
                                    <div className="empty-checklist-item" key="empty-checklist-item">{message}</div>
                                }

                                {
                                    data.map(item => (
                                        <CheckListItem
                                            key={`checklist-${item[0]}`}
                                            data={item}
                                            checkChangeHandle={checkChangeHandle}
                                            type={type}
                                        />
                                    ))
                                }
                            </ul>
                        </Element>
                    </div>
                </div>
            </div>
        );
    }
}
