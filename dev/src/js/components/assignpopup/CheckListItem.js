import React from 'react';
import PropTypes from 'prop-types';

const CheckListItem = ({ data, type, checkChangeHandle }) => {
    const disabled = !data[3] && type !== null;
    const arrInitial = data[2].split(' ').slice(0, 3);
    const initialName = arrInitial.map(item => item.substr(0, 1)).join('');
    const urlImage = data[1] === '' ? null : data[1];
    const image = (urlImage) ? (<img src={urlImage} alt="" />) : initialName;

    return (
        <li className={disabled ? 'checklist-item-disable' : 'checklist-item'}>
            <label>
                <input type="checkbox" disabled={disabled} checked={data[4]} onChange={() => checkChangeHandle(!data[4], data)} />
                <span className="content clearfix">
                    {urlImage !== '' && <span className="thumb">{image}</span>}
                    <span className="data" style={{ marginLeft: urlImage === '' ? '0px' : '70px' }}>
                        <span className="name" style={{ transform: 'translateY(70%)' }}>
                            {data[2]}{disabled && ' (Sudah dipilih)'}
                        </span>
                    </span>
                </span>
            </label>
        </li>
    );
};

CheckListItem.propTypes = {
    data: PropTypes.arrayOf(PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.bool,
    ])),
    type: PropTypes.string,
    checkChangeHandle: PropTypes.func,
};

CheckListItem.defaultProps = {
    data: [],
    type: '',
    checkChangeHandle: () => {},
};

export default CheckListItem;
