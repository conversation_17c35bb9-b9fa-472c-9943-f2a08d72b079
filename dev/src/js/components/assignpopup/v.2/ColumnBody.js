// TODO: garis bawahnya list ga ada
/**
 * default data: [{id, name}]
 *
 * how untuk custom column diluar default data:
 * 1. format data sesuai yang dibutuhkan
 * 2. gunakan customColumnMeta sebagai meta/konfigurasi custom column
 */
import React from 'react';
import PropTypes from 'prop-types';
import { List, CellMeasurerCache, CellMeasurer } from 'react-virtualized';
import './custom.css';

export default class ColumnBody extends React.Component {
    static propTypes = {
        data: PropTypes.arrayOf(PropTypes.shape({
            id: PropTypes.oneOfType([
                PropTypes.number,
                PropTypes.string,
            ]),
            name: PropTypes.string,
            imageUrl: PropTypes.string,
            available: PropTypes.bool,
            checked: PropTypes.bool,
        })),
        changeEvent: PropTypes.func,
        width: PropTypes.number.isRequired,
        height: PropTypes.number.isRequired,
        customColumnMeta: PropTypes.arrayOf(PropTypes.shape({
            field: PropTypes.string,
            header: PropTypes.string,
            width: PropTypes.string,
        })),
    }

    static defaultProps = {
        data: [],
        changeEvent: () => { },
        customColumnMeta: undefined,
    }

    state = {
        scrollIndex: undefined,
    }

    cache = new CellMeasurerCache({
        fixedWidth: true,
        defaultHeight: 70,
        keyMapper: (rowIndex, columnIndex) => {
            const { data } = this.props;
            const row = data[rowIndex];

            return `${row.id}:${columnIndex}`;
        },
    })

    componentWillReceiveProps = ({ data }) => {
        const { data: pData } = this.props;

        if (pData !== data) {
            this.checkList.forceUpdateGrid();
        }
    };

    componentDidUpdate = ({ data: prevData }) => {
        const { data } = this.props;

        if (prevData !== data) {
            this.checkList.recomputeRowHeights(0);
        }
    }

    shouldComponentUpdate = (nextProps, { scrollIndex }) => {
        const { scrollIndex: sScrollIndex } = this.state;

        if (sScrollIndex !== scrollIndex && scrollIndex !== 0) {
            return false;
        }

        return true;
    }

    refreshScrollPosition = () => {
        this.setState({
            scrollIndex: 0,
        });
    }

    _handleScroll = ({ target }) => {
        const { scrollTop, scrollLeft } = target;
        const { Grid: grid } = this.checkList;

        grid.handleScrollEvent({ scrollTop, scrollLeft });
    }

    renderRow = (paramData) => {
        const {
            index, style, parent,
        } = paramData;

        const { data, changeEvent, customColumnMeta } = this.props;
        const initialName = data[index].name.match(/\b(\w)/g) ? data[index].name.match(/\b(\w)/g).slice(0, 3).join('') : '-';

        const customStyle = {
            innerCheckList: {},
        };

        if (customColumnMeta) {
            Object.assign(customStyle, {
                innerCheckList: {
                    paddingLeft: 0,
                    paddingRight: 0,
                },
            });
        }

        return (
            <CellMeasurer
                key={data[index].id}
                cache={this.cache}
                parent={parent}
                columnIndex={0}
                rowIndex={index}
            >
                <div
                    className={!data[index].available ? 'checklist-item-disable' : 'checklist-item'}
                    style={style}
                >
                    <label style={customStyle.innerCheckList}>
                        <input
                            type="checkbox"
                            disabled={!data[index].available}
                            checked={data[index].checked}
                            onChange={() => changeEvent(data[index].id)}
                        />
                        {
                            customColumnMeta
                            && (
                                <div className="content">
                                    <div
                                        className="column-data-container"
                                        style={{ marginLeft: 0 }}
                                    >
                                        <div className="inner-column-wrapper">
                                        {
                                            customColumnMeta.map(({ field, header, width }) => (
                                                <div
                                                    key={header}
                                                    style={{ width: `${width}` }}
                                                    className="column-item"
                                                >
                                                    {data[index][field]}
                                                </div>
                                            ))
                                        }
                                        </div>
                                        <div className="checkbox-wrapper">
                                            <div style={{ padding: '16px 0 10px' }}>
                                                <div className="checkbox-ui" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )
                        }
                        {
                            !customColumnMeta
                            && (
                                <div className="content clearfix">
                                    <div className="thumb">
                                        {
                                            data[index].imageUrl
                                            && (
                                                <img
                                                    src={data[index].imageUrl}
                                                    alt=""
                                                />
                                            )
                                        }

                                        {
                                            !data[index].imageUrl
                                            && initialName
                                        }
                                    </div>
                                    <div
                                        className="data"
                                        style={{ marginLeft: data[index].imageUrl === '' ? '0px' : '70px' }}
                                    >
                                        <span className="name">
                                            {data[index].name}
                                            {!data[index].available && !data[index].checked && ' (Sudah dipilih)'}
                                        </span>
                                    </div>
                                </div>
                            )
                        }
                    </label>
                </div>
            </CellMeasurer>
        );
    }

    onScrollHandler = ({ clientHeight, scrollHeight, scrollTop }) => {
        if (scrollTop === (scrollHeight - clientHeight)) {
            const { onFetch } = this.props;
            onFetch();
        }
    }

    render() {
        const {
            data, width, height,
        } = this.props,
            { scrollIndex } = this.state;

        return (
            <div className="checklist-wrap" style={{ height: `${height}px` }}>
                <div className="checklist">
                    {/*
                    error di viewnya, TODO: coba nanti errornya diinfokan/diwrite di list aja
                    {
                        data.length <= 0
                        && <div className="empty-checklist-item" key="empty-checklist-item">Data kosong / tidak ditemukan.</div>
                    }
                    */}
                    <List
                        ref={(c) => { this.checkList = c; }}
                        width={width}
                        height={height}
                        rowCount={data.length}
                        rowRenderer={this.renderRow}
                        deferredMeasurementCache={this.cache}
                        rowHeight={this.cache.rowHeight}
                        className="checklist-root"
                        scrollToIndex={scrollIndex}
                        onScroll={this.onScrollHandler}
                        onRowsRendered={() => this.setState({ scrollIndex: undefined })}
                    />
                </div>
            </div>
        );
    }
}
