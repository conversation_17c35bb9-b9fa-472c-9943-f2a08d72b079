/**
 * default untuk title gunakan "titlePlaceholder",
 * jika antara title header, placeholder pada search, dan nama pada kolom sama, agar lebih mudah
 *
 * data.name is required untuk menentukan default sort
 *
 * filterCategories : array of [key, value, "field name to display"] pairs
 *
 *
 *
 * update BEFORE 27 Des 2018:
 * filterCategories : array of [key, value, fieldKeyToFilter] pairs
 * often key is same as fieldKeyToFilter
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import update from 'immutability-helper';
import _ from 'lodash';

import ModalPopup from '../../modalpopup/Container';
import AssignSearch from './Search';
import AssignSearchWithFilter from './SearchWithFilter';
import ColumnHeader from './ColumnHeader';
import ColumnBody from './ColumnBody';

import { ishasProperty } from '../../../utils/helper';

let allDataLoaded = false;

// only for server side
let checkedFilteredData = [];
const addToCheckedFilteredData = (val, data) => {
    // untuk handle uncheck
    // tampilkan checkedFilteredData yang hanya ada di state :value
    checkedFilteredData = checkedFilteredData.filter((cf) => {
        const found = val.find(v => (String(cf.id) === String(v)));

        return !!found;
    });
    val.forEach((x) => {
        const found = data.find(dt => (String(dt.id) === String(x)));

        if (found) {
            const payload = {
                id: found.id,
                name: found.name,
            };
            // cegah duplikasi data
            checkedFilteredData = checkedFilteredData.filter(cf => String(cf.id) !== String(payload.id));
            checkedFilteredData.push(payload);
        }
    });
};
export default class AssignPopup extends PureComponent {
    static propTypes = {
        title: PropTypes.string,
        isHideHeader: PropTypes.bool,
        titlePlaceholder: PropTypes.string,
        description: PropTypes.string,
        data: PropTypes.arrayOf(PropTypes.shape({
            id: PropTypes.oneOfType([
                PropTypes.number,
                PropTypes.string,
            ]),
            name: PropTypes.string,
            imageUrl: PropTypes.string,
        })),
        value: PropTypes.arrayOf(PropTypes.oneOfType([
            PropTypes.number,
            PropTypes.string,
        ])).isRequired,
        objValue: PropTypes.arrayOf(PropTypes.shape({
            id: PropTypes.oneOfType([
                PropTypes.number,
                PropTypes.string,
            ]),
            name: PropTypes.string,
        })),
        showCheckAll: PropTypes.bool,
        confirmHandle: PropTypes.func,
        filterCategories: PropTypes.arrayOf(PropTypes.array),
        customColumnMeta: PropTypes.arrayOf(PropTypes.shape({
            field: PropTypes.string,
            header: PropTypes.string,
            width: PropTypes.string, // in percentage
            customSort: PropTypes.func,
        })),
        renderCustomBody: PropTypes.func, // custom extra ordinary component body. Currently used in promo product, which additional checklist state
        innerBodyHeight: PropTypes.number,
        width: PropTypes.number,
        show: PropTypes.bool,
        searchExtension: PropTypes.node,
        onFetch: PropTypes.func,
        onHide: PropTypes.func,
        cancelText: PropTypes.string,
        confirmText: PropTypes.string,
        searchText: PropTypes.string,
    }

    static defaultProps = {
        title: undefined,
        titlePlaceholder: '',
        description: '',
        data: undefined,
        showCheckAll: undefined,
        objValue: [],
        confirmHandle: () => { },
        filterCategories: undefined,
        customColumnMeta: undefined,
        renderCustomBody: undefined,
        innerBodyHeight: 300,
        width: undefined,
        show: false,
        isHideHeader: false,
        searchExtension: null,
        onFetch: undefined,
        onHide: () => { },
        cancelText: 'Batal',
        confirmText: 'Simpan',
        searchText: 'Cari',
    }

    constructor(props) {
        super(props);

        this.state = {
            data: [],
            filteredData: [],
            value: props.value,
            page: 1,
            filterText: '',
            sortType: 'asc',
            fieldToSort: 'name',
            isCheckAll: ishasProperty(props, 'showCheckAll') && props.showCheckAll ? false : undefined,
            isSorted: false,
            filterOptionValues: [],
            categoryReferences: [],
            loading: false,
        };
    }

    componentWillMount = () => {
        const {
            data, value, onFetch, objValue,
        } = this.props;

        allDataLoaded = false;
        checkedFilteredData = objValue;

        if (onFetch) {
            this.fetchDataHandler();
        } else {
            this._initData(data, value);
        }
    };

    componentWillReceiveProps = ({ data, value }) => {
        const { data: pData, value: pValue } = this.props;

        if (pData !== data
            || pValue !== value
        ) {
            this._initData(data, value);
        }
    };

    fetchDataHandler = async () => {
        const {
            page, sortType, fieldToSort, filterText, filterOptionValues,
            value,
        } = this.state;
        const { onFetch } = this.props;

        this.setState({ loading: true });

        const res = await onFetch({
            page, filterText, sortType, fieldToSort, filterOptionValues,
        });

        this.setState({ loading: false });

        this._initData(res.data, value, true);

        allDataLoaded = (page >= res.totalPage);
    }

    fetchNextData = () => {
        const { onFetch } = this.props;

        if (!allDataLoaded && onFetch) {
            this.setState(prevState => ({ page: prevState.page + 1 }), () => {
                this.fetchDataHandler();
            });
        }
    }

    clearDataAfterSort = () => {
        const { onFetch } = this.props;

        if (onFetch) {
            this.setState({ page: 1, data: [], filteredData: [] }, () => {
                this.checkList.refreshScrollPosition();
                this.fetchDataHandler();
            });
        }
    }

    show = () => {
        allDataLoaded = false;
        checkedFilteredData = [];
        this.assignPopup.showPopup();
    }

    hide = () => {
        this.assignPopup.hidePopup();
    }

    /**
     * build categories
     * output: multiple categories
     */
    _buildCategoryReferences = (conf, data) => {
        if (conf) {
            const categoryOptions = [];

            conf.forEach((identifier) => {
                const _tempCategoryIds = [];
                let _tempCategories = [];

                data.forEach((item) => {
                    const id = item[identifier[0]];

                    if (!_tempCategoryIds.includes(id)) {
                        _tempCategoryIds.push(id);
                        _tempCategories.push({
                            id,
                            value: item[identifier[1]],
                        });
                    }
                });

                /* sort by value */
                _tempCategories = _.sortBy(_tempCategories, [o => o.value.toLowerCase()]);
                _tempCategories.unshift({ id: '__all__', value: `Semua ${identifier[2] || 'Kategori'}` });

                categoryOptions.push({
                    field: identifier[0],
                    data: _tempCategories,
                    fieldName: identifier[2],
                });
            });

            return categoryOptions;
        }

        return conf;
    }

    _initData = (newData, newValue, isServerSide = false) => {
        if (isServerSide) {
            this._initDataServerSide(newData, newValue);
        } else {
            this._initDataClientSide(newData, newValue);
        }
    }

    _initDataServerSide = (newData, newValue) => {
        let categoryReferences;
        const { data, filteredData } = this.state;
        const { filterCategories } = this.props;
        const preparedData = this._prepareData(newData, newValue);

        const newDataState = [...data, ...preparedData];
        const isCheckAll = this._isCheckAll(newDataState, newValue);

        if (filterCategories) {
            categoryReferences = this._buildCategoryReferences(filterCategories, newDataState);
        }

        this.setState({
            data: newDataState,
            filteredData: [...filteredData, ...preparedData],
            value: newValue,
            isCheckAll,
            categoryReferences,
        });
    }

    _initDataClientSide = (newData, newValue) => {
        let categoryReferences;
        const { filterCategories } = this.props;
        const data = this._prepareData(newData, newValue);
        const isCheckAll = this._isCheckAll(data, newValue);

        if (filterCategories) {
            categoryReferences = this._buildCategoryReferences(filterCategories, data);
        }

        this.setState({
            data,
            filteredData: data,
            value: newValue,
            isCheckAll,
            categoryReferences,
        });
    }

    _prepareData = (data, value) => {
        if (data) {
            const { sortType, fieldToSort } = this.state;
            const flaggedData = this._setDefaultFlag(data, value);
            const sortedData = this._sort(flaggedData, fieldToSort, sortType);

            if (value) {
                const dataChecked = sortedData.filter(item => item.checked);
                const dataUnchecked = sortedData.filter(item => !item.checked);

                return [...dataChecked, ...dataUnchecked];
            }
            return sortedData;
        }

        return data;
    }

    /** add flag: available, checked. Plain data terhadap value. */
    _setDefaultFlag = (data, value) => (
        data.map((item) => {
            let isAvailable = true;

            if (ishasProperty(item, 'available')) {
                if (!item.available && value.includes(item.id)) {
                    isAvailable = item.available;
                }
            }

            if (ishasProperty(item, 'disable_default_flag')) {
                if (item.disable_default_flag) {
                    isAvailable = item.available;
                }
            }

            return Object.assign({}, item, {
                available: isAvailable,
                checked: value.includes(item.id),
            });
        })
    )

    _changeChekboxHandler = (valId) => {
        const { value, filteredData } = this.state,
            idxVal = value.indexOf(valId),
            idxFilteredData = filteredData.findIndex(item => item.id === valId);

        let newValue;

        if (idxVal > -1) {
            newValue = update(value, {
                $splice: [[idxVal, 1]],
            });
        } else {
            newValue = [...value, valId];
        }

        const newFilteredData = update(filteredData, {
            [idxFilteredData]: { checked: { $set: !(idxVal > -1) } },
        });

        const isCheckAll = this._isCheckAll(newFilteredData, newValue);

        this.setState({
            value: newValue,
            filteredData: newFilteredData,
            isCheckAll,
        }, () => {
            addToCheckedFilteredData(newValue, newFilteredData);
        });
    };

    _filterData = (data, filterText, filterOptionValues) => {
        let filterBySelection;
        const keySearch = 'name';
        const isFilterByOptions = filterOptionValues.map(item => item).length > 0;

        const filteredBySearch = data.filter(item => (item[keySearch]).toLowerCase().includes(filterText.toLowerCase()));

        if (isFilterByOptions) {
            const { categoryReferences } = this.state;

            filterBySelection = filteredBySearch;

            filterOptionValues.forEach((filterOption, index) => {
                if (filterOption !== '__all__') {
                    const key = categoryReferences[index].field;

                    filterBySelection = filterBySelection.filter(item => String(item[key]) === String(filterOption));
                }
            });
        }

        return {
            filteredData: filterBySelection || filteredBySearch,
        };
    }

    _changeSearchHandler = (val) => {
        const { onFetch } = this.props;

        if (onFetch) {
            const isFilter = val.indexOf(':') > -1;
            const {
                categoryReferences, filterOptionValues: currentFilterOptionValues, filterText: currentFilterText,
            } = this.state;

            // update key search value
            let filterText = val;
            if (isFilter) {
                filterText = currentFilterText;
            }

            // define and or update filter option values array
            let filterOptionValues = currentFilterOptionValues;
            if (isFilter) {
                const [keyFilter, valueFilter] = val.split(':');
                const idxCategory = categoryReferences.findIndex(item => item.field === keyFilter);

                if (currentFilterOptionValues) {
                    // modify current filter option values array
                    filterOptionValues = [...currentFilterOptionValues];
                    filterOptionValues[idxCategory] = valueFilter;
                } else {
                    // build new filter option values array
                    filterOptionValues = categoryReferences.map((item) => {
                        if (item.field === keyFilter) {
                            return valueFilter;
                        }

                        return '';
                    });
                }
            }

            this.setState({ page: 1, filterText, filterOptionValues });

            clearTimeout(this.filterIdle);
            this.filterIdle = setTimeout(() => {
                this.setState({
                    page: 1, filterText, data: [], filteredData: [],
                }, () => {
                    this.checkList.refreshScrollPosition();
                    this.fetchDataHandler();
                });
            }, 500);
        } else {
            this.filterClientSide(val);
        }
    }

    filterClientSide = (val) => {
        const isFilter = val.indexOf(':') > -1;
        const {
            data, sortType, fieldToSort, isSorted, value, isCheckAll, categoryReferences, filterOptionValues: currentFilterOptionValues, filterText: currentFilterText,
        } = this.state;
        // update key search value
        let filterText = val;
        if (isFilter) {
            filterText = currentFilterText;
        }

        // define and or update filter option values array
        let filterOptionValues = currentFilterOptionValues;
        if (isFilter) {
            const [keyFilter, valueFilter] = val.split(':');
            const idxCategory = categoryReferences.findIndex(item => item.field === keyFilter);

            if (currentFilterOptionValues) {
                // modify current filter option values array
                filterOptionValues = [...currentFilterOptionValues];
                filterOptionValues[idxCategory] = valueFilter;
            } else {
                // build new filter option values array
                filterOptionValues = categoryReferences.map((item) => {
                    if (item.field === keyFilter) {
                        return valueFilter;
                    }

                    return '';
                });
            }
        }

        const { filteredData } = this._filterData(data, filterText, filterOptionValues);

        let dataFilterToSort = this._setDefaultFlag(filteredData, value);

        if (isSorted) {
            dataFilterToSort = this._sort(filteredData, fieldToSort, sortType);
        }

        const countSelected = dataFilterToSort.reduce((prev, curr) => (curr.checked ? prev + 1 : prev), 0);

        this.setState({
            filterText,
            filteredData: dataFilterToSort,
            isCheckAll: isCheckAll === undefined ? undefined : countSelected === dataFilterToSort.length && countSelected > 0,
            filterOptionValues,
        });
    }

    _sort = (data, fieldToSort, sortType) => {
        let fieldsHasCustomSort;
        let iteratee = fieldToSort;
        const { customColumnMeta } = this.props;

        if (customColumnMeta) {
            fieldsHasCustomSort = customColumnMeta
                .filter(item => ishasProperty(item, 'customSort'))
                .map(item => item.field);
        }

        if (customColumnMeta && fieldsHasCustomSort && fieldsHasCustomSort.includes(fieldToSort)) {
            ({ customSort: iteratee } = customColumnMeta.find(item => item.field === fieldToSort));
        }

        return _.orderBy(data, [iteratee], [sortType]);
    }

    _changeHeaderHandler = (type, updatedVal) => {
        /**
         * header change handler: sort by header column, check / uncheck all
         * important to make value valid
         * newFilteredData is presentation purpose only
         * @updatedVal: type = checkAll => bool
         */
        const { sortType: currentSortType, filteredData, value } = this.state;

        if (type === 'sort') {
            let sortType = 'asc';
            const fieldToSort = updatedVal;
            const { fieldToSort: currentFieldToSort } = this.state;

            if (currentFieldToSort === fieldToSort) {
                if (currentSortType === 'asc') {
                    sortType = 'desc';
                }
            }

            const sortedData = this._sort(filteredData, fieldToSort, sortType);

            this.setState({
                sortType,
                filteredData: sortedData,
                isSorted: true,
                fieldToSort,
            }, () => {
                this.clearDataAfterSort();
            });
        }

        if (type === 'checkAll') {
            const { newValue, newFilteredData } = filteredData.reduce((prev, curr) => {
                if (curr.available) {
                    let _updatedValue;

                    if (updatedVal) {
                        _updatedValue = [...prev.newValue, curr.id];
                    } else if (value.length > filteredData.length) {
                        _updatedValue = prev.newValue.filter(item => item !== curr.id);
                    } else {
                        _updatedValue = prev.newValue;
                    }

                    return {
                        newValue: _updatedValue,
                        newFilteredData: [...prev.newFilteredData, Object.assign({}, curr, { checked: updatedVal })],
                    };
                }

                let _updatedVal = prev.newValue;

                if (!curr.available && curr.checked) {
                    _updatedVal = [..._updatedVal, curr.id];
                }

                return {
                    newValue: _updatedVal,
                    newFilteredData: [...prev.newFilteredData, curr],
                };
            }, {
                newValue: !updatedVal && value.length > filteredData.length ? value : [],
                newFilteredData: [],
            });

            this.setState({
                isCheckAll: updatedVal,
                value: newValue,
                filteredData: newFilteredData,
            }, () => {
                addToCheckedFilteredData(newValue, newFilteredData);
            });
        }
    }

    _onHideEvent = () => {
        const { onHide } = this.props;
        const { data } = this.state,
            value = data.map(item => (item.checked ? item.id : null)).filter(item => item); // TODO: check lagi kalau ada scenario available not available
        const isCheckAll = this._isCheckAll(data, value);

        this.checkList.refreshScrollPosition();

        this.setState({
            filterText: '',
            sortType: 'asc',
            isCheckAll,
            isSorted: false,
            filteredData: data,
            value,
            filterOptionValues: [],
        });

        onHide();
    }

    _isCheckAll = (data, selected) => {
        const { isCheckAll } = this.state;

        if (isCheckAll !== undefined && data !== undefined) {
            return data.length > 0 && data.length === selected.length;
        }

        return isCheckAll;
    }

    _buildSearch = (categories, _props) => {
        if (categories) {
            return (
                <AssignSearchWithFilter
                    {..._props}
                    categories={categories}
                />
            );
        }

        return (<AssignSearch {..._props} />);
    }

    render() {
        const {
            title, titlePlaceholder, description, confirmHandle, filterCategories, customColumnMeta, renderCustomBody, innerBodyHeight, width,
            show, isHideHeader, searchExtension, cancelText, confirmText, searchText,
        } = this.props;

        const {
            filteredData, value, filterText, sortType, fieldToSort, isCheckAll, filterOptionValues, categoryReferences,
            loading,
        } = this.state;

        const defaultTitle = title || `Pilih ${titlePlaceholder}`;
        const modalWidth = width || (filterCategories ? 650 : 500);

        return (
            <ModalPopup
                width={modalWidth}
                type="assign"
                show={show}
                cancelText={cancelText}
                confirmText={confirmText}
                ref={(c) => { this.assignPopup = c; }}
                confirmHandle={() => confirmHandle(value, checkedFilteredData)}
                onHide={this._onHideEvent}
            >
                <div className="assign-container">
                    <div className="assign-header" style={!description ? { paddingBottom: '10px' } : {}}>
                        <h2 className="title" style={{ color: 'white', marginBottom: 0 }}>{defaultTitle}</h2>
                        {description && <div className="description">{description}</div>}
                    </div>
                    <div style={{ display: 'flex' }}>
                        <div style={{ flex: '1' }}>
                            {this._buildSearch(categoryReferences, {
                                value: filterText,
                                changeEvent: this._changeSearchHandler,
                                titlePlaceholder,
                                filterOptionValues,
                                searchText,
                            })}
                        </div>
                        {searchExtension}
                    </div>
                    <div style={{ position: 'relative' }}>
                        {!isHideHeader && (
                            <ColumnHeader
                                titlePlaceholder={titlePlaceholder}
                                sort={sortType}
                                sortField={fieldToSort}
                                isCheckAll={isCheckAll}
                                changeEvent={this._changeHeaderHandler}
                                customColumnMeta={customColumnMeta}
                            />
                        )}
                        <ColumnBody
                            data={filteredData}
                            changeEvent={this._changeChekboxHandler}
                            ref={(c) => { this.checkList = c; }}
                            width={modalWidth}
                            customColumnMeta={customColumnMeta}
                            titlePlaceholder={titlePlaceholder}
                            height={innerBodyHeight}
                            onFetch={this.fetchNextData}
                        />
                        {
                            renderCustomBody
                            && (
                                renderCustomBody()
                            )
                        }
                        <div className={`popup-server-side-loading ${loading ? 'popup-server-side-loading--show' : 'popup-server-side-loading--hide'}`}>
                            <div className="popup-server-side-loading__content">
                                <div className="popup-server-side-loading__caption">Loading...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </ModalPopup>
        );
    }
}
