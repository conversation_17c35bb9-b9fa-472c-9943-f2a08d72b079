import React from 'react';
import PropTypes from 'prop-types';
import update from 'immutability-helper';
import { disableBodyScroll, clearAllBodyScrollLocks } from 'body-scroll-lock';

import ModalPopup from '../modalpopup/Container';

import AssignSearch from './Search';
import AssignSort from './Sort';
import CheckList from './CheckList';

function descendingSort(a, b) {
    if (a[2].toLowerCase() > b[2].toLowerCase()) { return -1; }
    if (a[2].toLowerCase() < b[2].toLowerCase()) { return 1; }
    return 0;
}

function sortData(data, previousOrder) {
    if (previousOrder === 'asc') {
        return data.sort(descendingSort).reverse();
    }
    return data.sort(descendingSort);
}

export default class AturPilihan extends React.Component {
    static propTypes = {
        changeEvent: PropTypes.func,
        data: PropTypes.arrayOf(PropTypes.object),
        value: PropTypes.arrayOf(PropTypes.string),
        cancelText: PropTypes.string,
        title: PropTypes.string,
        description: PropTypes.string,
        confirmText: PropTypes.string,
        confirmHandle: PropTypes.func,
        tipePilihan: PropTypes.string,
        showCheckAll: PropTypes.bool,
        // componentFilter: PropTypes.element,
        componentFilter: PropTypes.func,
        type: PropTypes.string
    }

    static defaultProps = {
        changeEvent: () => {},
        data: [],
        value: [],
        cancelText: '',
        title: '',
        description: '',
        confirmText: '',
        confirmHandle: () => {},
        tipePilihan: '',
        showCheckAll: false,
        componentFilter: null,
        type: ''
    }

    static addCheckStatusToData = (data, value) => {
        const dataCheck = [];
        const dataUncheck = [];
        let allData = [];
        if (!!data && data.length > 0) {
            data.forEach((item) => {
                const status = !!(!!value && value.length > 0 && value.includes(item.id));
                if (status) {
                    dataCheck.push([item.id, item.photo, item.name, value !== undefined && value.includes(item.id) ? true : item.available, !!(!!value && value.length > 0 && value.includes(item.id)), item.item_bahan_jadi === '1']);
                } else {
                    dataUncheck.push([item.id, item.photo, item.name, value !== undefined && value.includes(item.id) ? true : item.available, !!(!!value && value.length > 0 && value.includes(item.id)), item.item_bahan_jadi === '1']);
                }
                // return [item.id, item.photo, item.name, value !== undefined && value.includes(item.id) ? true : item.available, !!value && value.length > 0 && value.includes(item.id) ? true : false]
            });
            allData = sortData(dataCheck, 'asc').concat(sortData(dataUncheck, 'asc'));
        }
        return allData;
    }

    constructor(props) {
        super(props);
        this.state = {
            filterText: '',
            sort: 'asc',
            dataCheck: [],
            filteredData: [],
            message: '',
        };
    }

    componentWillMount() {
        const { data, value } = this.props;
        const dataCheck = this.constructor.addCheckStatusToData(data, value);

        this.setState({
            dataCheck,
        }, () => {
            this.setFilterData();
        });
    }

    componentWillReceiveProps(nextProps) {
        if (this.props.data !== nextProps.data || this.props.value !== nextProps.value) {
            const dataCheck = this.constructor.addCheckStatusToData(nextProps.data, nextProps.value);

            this.setState({
                dataCheck,
                // sort: 'asc'
            }, () => {
                this.setFilterData();
            });
        }
    }

    setFilterData() {
        const filteredData = this.state.dataCheck.filter(item => item[2].toLowerCase().includes(this.state.filterText.toLowerCase()));

        this.setState({
            filteredData,
        }, () => {
            let message = '';
            if (this.state.dataCheck.length > 0 && this.state.filteredData.length <= 0) {
                message = `Tidak ada data yang sesuai dengan pencarian : ${this.state.filterText}`;
            } else if (this.state.dataCheck <= 0) {
                message = 'Tidak ada data';
            }

            this.setState({
                message,
            });
            // this.changeSortHandler(this.state.sort)
        });
    }

    changeFilterHandler(value) {
        this.setState({ filterText: value }, () => {
            this.setFilterData();
            this.scrollCheckList.refreshCheckList();
        });
    }

    changeSortHandler(value) {
        this.setState({ sort: value }, () => {
            const filteredData = sortData(this.state.filteredData, this.state.sort);
            this.setState({ filteredData });
        });
    }

    changeCheckHandle(value, oldValue) {
        const index = this.state.dataCheck.indexOf(oldValue);
        // Update Data for checkbox
        const newCheckbox = update(this.state.dataCheck[index], {
            $set: [oldValue[0], oldValue[1], oldValue[2], oldValue[3], value, oldValue[5]],
        });
        const newData = update(this.state.dataCheck, {
            $splice: [[index, 1, newCheckbox]],
        });
        // Update array of value
        const newValue = this.props.value;
        if (value) {
            newValue.push(oldValue[0]);
        } else {
            const i = this.props.value.indexOf(this.state.dataCheck[index][0]);
            if (index > -1) {
                newValue.splice(i, 1);
            } else {
                console.log('Something wrong');
            }
        }
        this.setState({
            dataCheck: newData,
        }, () => {
            this.props.changeEvent(newValue);
            this.setFilterData();
            /* this.setState({
                filteredData: this.state.dataCheck
            }, function(){
                this.changeSortHandler(this.state.sort)
            }.bind(this)) */
        });
    }

    selectAllEvent() {
        let checkAll = false;
        const mainDataChecked = [...this.state.dataCheck];
        const filteredData = [...this.state.filteredData];
        const checkedFiltered = filteredData.filter(x => x[4] === true);
        if (filteredData.length > checkedFiltered.length) checkAll = true;

        const newValue = mainDataChecked.map((x) => {
            const dataFound = filteredData.find(z => z[0] === x[0]);
            if (dataFound) {
                dataFound[4] = checkAll;
                return dataFound;
            }
            return x;
        });
        this.props.changeEvent(newValue.filter(x => x[4] === true).map(z => z[0]));
    }

    clearFilter() {
        const $this = this;

        this.setState({ filterText: '' }, () => {
            $this.setFilterData();
            $this.scrollCheckList.refreshCheckList();
        });
    }

    showPopup() {
        this.clearFilter();
        this.assignPopup.showPopup();
        disableBodyScroll(document.querySelector('#checklist-react-scroll'));
    }

    hidePopup = () => {
        this.assignPopup.hidePopup();
        clearAllBodyScrollLocks();
    }

    confirmHandler = () => {
        this.props.confirmHandle();
        clearAllBodyScrollLocks();
    }

    render() {
        let checkAllValue = false;
        const checkedValue = [...this.state.filteredData].filter(x => x[4] === true);
        if (checkedValue.length === this.state.filteredData.length && checkedValue.length > 0) checkAllValue = true;

        return (
            <ModalPopup
                type="assign"
                // show={this.props.show}
                classes="assign-Modal"
                confirmText={this.props.confirmText ? this.props.confirmText : 'Pilih'}
                cancelText={this.props.cancelText ? this.props.cancelText : 'Batal'}
                confirmHandle={this.confirmHandler}
                customCancelHandler={this.hidePopup}
                ref={(c) => { this.assignPopup = c; }}
                width={560}
            >
                <div className="assign-container">
                    <div className="assign-header">
                        <h2 className="title">{this.props.title}</h2>
                        {this.props.description && <div className="description">{this.props.description}</div>}
                    </div>
                    <AssignSearch
                        componentFilter={this.props.componentFilter}

                        value={this.state.filterText}
                        changeEvent={val => this.changeFilterHandler(val)}
                        tipePilihan={this.props.tipePilihan}
                    />
                    <AssignSort
                        sort={this.state.sort}
                        changeEvent={val => this.changeSortHandler(val)}
                        tipePilihan={this.props.tipePilihan}

                        showCheckAll={this.props.showCheckAll}
                        selectAllEvent={() => this.selectAllEvent()}
                        selectAllValue={checkAllValue}
                    />
                    <CheckList
                        data={this.state.filteredData}
                        filterText={this.state.filterText}
                        checkChangeHandle={(val, oldVal) => this.changeCheckHandle(val, oldVal)}
                        type={this.props.type}
                        message={this.state.message}
                        ref={(c) => { this.scrollCheckList = c; }}
                    />
                </div>
            </ModalPopup>
        );
    }
}
