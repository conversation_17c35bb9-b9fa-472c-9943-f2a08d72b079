import React from 'react';
import InputCheckbox from '../form/InputCheckbox';

export default class AssignSort extends React.Component {
  constructor(props) {
    super(props);

    this.changeHandle = this.changeHandle.bind(this);
  }

  changeHandle() {
    const value = this.props.sort == 'desc' ? 'asc' : 'desc';
    this.props.changeEvent(value);
  }

  render() {
    return (
      <div style={{ position: 'relative' }}>
        <div className="assign-sort" onClick={this.changeHandle}>
          Nama {this.props.tipePilihan}<i className={`fa fa-caret-${this.props.sort == 'desc' ? 'down' : 'up'}`} />
        </div>
        {this.props.showCheckAll &&
        <div style={{ right: '24px', top: '9px', position: 'absolute' }}>
          <InputCheckbox name="checkAllItem" id="checkAllItem" changeEvent={this.props.selectAllEvent} checked={this.props.selectAllValue} />
        </div>
        }
      </div>
    );
  }
}
