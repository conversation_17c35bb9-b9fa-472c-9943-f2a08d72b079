import React from 'react';
import InputSelect from '../form/Select';

export default class AssignSearch extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: '',
    };

    this.changeHandle = this.changeHandle.bind(this);
    this.changeFilterProduk = this.changeFilterProduk.bind(this);
  }

  componentDidMount() {
    this.setState({
      value: (this.props.value ? this.props.value : ''),
    });
  }

  changeHandle(event) {
    this.setState({ value: event.target.value });
    this.props.changeEvent(event.target.value);
  }

  changeFilterProduk(value) {
    this.props.changeFilterProduk(value);
  }

  render() {
    const CustomFilter = (this.props.componentFilter) ? this.props.componentFilter() : null;

    return (
        <div className="assign-search">
          {CustomFilter &&
            <div className="inline-form form-group">
              <div className="form-wrap" style={{ width: '70%' }}>
                <input type="text" onChange={this.changeHandle} placeholder={`Cari ${this.props.tipePilihan}`} value={this.props.value} />
              </div>
              <div className="form-wrap" style={{ width: '30%' }}>
                <CustomFilter />
              </div>
            </div>
          }
          {!CustomFilter &&
            <input type="text" onChange={this.changeHandle} placeholder={`Cari ${this.props.tipePilihan}`} value={this.props.value} />
          }
        </div>
    );
  }
}
