import React, { Component } from 'react';
import onClickOutside from "react-onclickoutside";
import InputCheckboxDark from '../form/InputCheckboxDark/';

class AnalysisFilter extends Component {

    constructor(props) {
        super(props);

        this.state = {
            isShow: false,
            isHideProductWithRecipe: false,
            isOnlyProductSales: false
        }
    }

    handleClickOutside = ()  => {
        this.setState({
            isShow: false
        });
    }

    showFilter = () => {
        this.setState((prevState) => ({
            isShow: !prevState.isShow
        }))
    }

    changeIsHideProductWithRecipe = (val) => {
        this.setState({
            isHideProductWithRecipe: val
        });
    }

    changeIsOnlyProductSales = (val) => {
        this.setState({
            isOnlyProductSales: val
        });
    }

    processHandler = () => {
        const value = {
            isHideProductWithRecipe: this.state.isHideProductWithRecipe,
            isOnlyProductSales: this.state.isOnlyProductSales
        }
    
        this.handleClickOutside();
        this.props.processEvent(value);
    }

    render() {
        const { isShow } = this.state;

        return (
            <div className="analysis-filter-container">
                <div className="analysis-filter-button" onClick={this.showFilter}><div className="text">Filter</div><div className="icon"><i className="fa fa-angle-down"></i></div></div>
                <div className="filter-wrap" style={{display: isShow ? 'block': 'none'}}>
                    <div className="filter-wrap-body">
                        <div className="row mb-lg">
                            <div className="col-sm-12">
                                <InputCheckboxDark
                                    id="hide-product-with-recipe"
                                    changeEvent={val =>
                                        this.changeIsHideProductWithRecipe(val)
                                    }
                                    name="hide_product_with_recipe"
                                    checked={this.state.isHideProductWithRecipe}
                                    disabled={false}
                                    label="Sembunyikan produk dengan resep"
                                />
                            </div>
                        </div>
                        <div className="row mb-lg">
                            <div className="col-sm-12">
                                <InputCheckboxDark
                                    id="only-product-sales"
                                    changeEvent={val =>
                                        this.changeIsOnlyProductSales(val)
                                    }
                                    name="only_product_sales"
                                    checked={this.state.isOnlyProductSales}
                                    disabled={false}
                                    label="Hanya yang terdapat penjualan"
                                />
                            </div>
                        </div>
                    </div>
                    <div className="filter-wrap-footer">
                        <div className="row">
                            <div className="col-sm-12 text-right">
                                <button
                                    className={`btn btn-primary`}
                                    style={{ height: '100%' }}
                                    onClick={this.processHandler}
                                >Proses</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}

export default onClickOutside(AnalysisFilter);