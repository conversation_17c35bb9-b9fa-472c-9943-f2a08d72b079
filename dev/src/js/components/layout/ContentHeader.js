import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Link } from 'react-router-dom';
import {
    Button,
    Box,
    Flex,
    Banner,
    BannerLink,
    BannerDescription,
} from '@majoo-ui/react';

import tokenUtils from '../../utils/token.util';
import userUtils from '../../utils/user.util';

import { layoutsActions } from '../../data/layouts/layouts.reducer';
import layoutSelectors from '../../data/layouts/layouts.selector';

import { resetDateRangeHelper } from '../../utils/helper';
import { isSupportExpired, getExpiredBranchId } from '../../services/session';
import { getExpiredCabangName } from '../../data/supports/selector';
import AdvertisementV2 from '../../pages/LayoutV2/Advertisement';

import { getAds, postAdsLogs } from '../../data/advertisement';
import DropDown from '../buttons/DropDown';
/* ENUM */
import { whiteListFromSupport, accountType as accountTypeLists } from '../../config/enum';
import { redirectToLogin } from '../../pages/LayoutV2/utils';


class ClassComponent extends React.Component {
    static createBreadcrumbLink(link, key, text, index, routes) {
        if (index === routes.length - 1 || (index === routes.length - 2 && (!routes[routes.length - 1].name || routes[index].name === routes[routes.length - 1].name))) {
            return (
                <li key={key}>
                    <span className="current">{text}</span>
                </li>
            );
        }
        return (
            <li key={key}>
                <Link to={link}>{text}</Link>
            </li>
        );
    }

    static redirectortal(param) {
        if (param === 0) {
            window.open(
                'https://play.google.com/store/apps/details?id=com.klopos',
                '_blank',
            );
        } else if (param === 1) {
            window.open(`${process.env.PORTAL_BASE_URL}/faq?tab=install`, '_blank');
        } else {
            window.open(`${process.env.PORTAL_BASE_URL}/perangkat`, '_blank');
        }
    }

    static propTypes = {
        buttons: PropTypes.arrayOf(PropTypes.shape()),
        calendar: PropTypes.shape({
            start: PropTypes.string,
            end: PropTypes.string,
            onchange: PropTypes.func,
            rangeLimit: PropTypes.number,
        }),
        cabang: PropTypes.arrayOf(PropTypes.shape()),
        filterCalendar: PropTypes.shape({
            data: PropTypes.arrayOf(PropTypes.shape),
            value: PropTypes.string,
            onchange: PropTypes.func,
        }),
        setGlobalMessage: PropTypes.func.isRequired,
        router: PropTypes.shape({
            push: PropTypes.func,
        }),
        onMenuClick: PropTypes.func,
        updateActiveGroupMenu: PropTypes.func,
        warningMessageData: PropTypes.arrayOf(
            PropTypes.shape({}),
        ),
        style: PropTypes.shape({}),
        isLoaderShow: PropTypes.bool,
        isFoldedMenu: PropTypes.bool,
        isMobile: PropTypes.bool,
        hasMenuBanner: PropTypes.bool,
        t: PropTypes.func.isRequired,
    }

    static defaultProps = {
        buttons: [],
        calendar: {
            start: '',
            end: '',
            onchange: () => { },
            rangeLimit: null,
        },
        filterCalendar: {
            data: [],
            value: '',
            onchange: () => { },
        },
        cabang: undefined,
        router: {
            push: () => { },
        },
        onMenuClick: null,
        updateActiveGroupMenu: null,
        warningMessageData: undefined,
        style: {},
        isLoaderShow: true,
        isFoldedMenu: false,
        isMobile: false,
        hasMenuBanner: false,
    }

    constructor(props) {
        super(props);

        this.state = {
            adsOpen: false,
            adsData: [],
            noAds: '',
            adsEndDate: '',
            adsTotalHit: 0,
            localAdsHit: 0,
            isWarningDismissed: false,
        };
    }

    componentWillMount() {
        this._fetchAds();
    }

    _postAdsLog = async (id, action) => {
        const payload = {
            type: 'spanduk', // spanduk
            source: 'spanduk', // spanduk / pesan
            action, // read / dismiss / hook
            id, // id dari message / spanduk
        };

        postAdsLogs(payload);
    }

    _fetchAds = async () => {
        const today = moment();
        const payload = {
            start_date: today.format('YYYY-MM-DD'),
            end_date: today.format('YYYY-MM-DD'),
        };

        try {
            const fetGetAds = await getAds(payload);
            if (fetGetAds.status && fetGetAds.data.length > 0) {
                this.setState({
                    adsData: fetGetAds.data[0],
                    noAds: fetGetAds.data[0].banner_advert_no,
                    adsTotalHit: fetGetAds.data[0].total_hit,
                    adsEndDate: fetGetAds.data[0].end_date,
                    adsOpen: true,
                }, () => {
                    this.setupLocalStorage(fetGetAds.data[0].banner_advert_no);
                });
            } else {
                this.setupLocalStorage('-');
            }
        } catch (e) {
            this.setupLocalStorage('-');
        }
    }

    setupLocalStorage = (noAds) => {
        const idUser = tokenUtils.getTokenPayload('id');
        const { adsEndDate } = this.state;
        const cookies = JSON.parse(this.getCookie('advertisement'));
        let localAdsHit = 0;

        if (cookies && cookies.noAds === noAds && cookies.idUser === idUser) {
            localAdsHit = parseInt(cookies.localAdsHit, 10);
        }

        const localAdsArray = {
            noAds,
            localAdsHit,
            idUser,
        };

        this.setCookie('advertisement', JSON.stringify(localAdsArray), adsEndDate);
        this.setState({
            localAdsHit,
        });
    }

    dateRangeController = (startDate, endDate) => {
        const {
            calendar: {
                onchange, rangeLimit,
            },
            setGlobalMessage,
        } = this.props;

        const { newStartDate, newEndDate, isForceReset } = resetDateRangeHelper(startDate, endDate, rangeLimit);

        if (isForceReset) {
            setGlobalMessage({
                title: `Maksimal rentang waktu yang diperbolehkan: ${rangeLimit / 30} bulan`,
                message: 'Rentang waktu akan kami sesuaikan dengan pengaturan standar (bulan ini)',
                level: 'warning',
                autoDismiss: 10,
            });
        }

        onchange(newStartDate, newEndDate);
    }

    /* ACTION OPEN SIDEBAR SUPPORT PAGE -- REDIRECTING */
    openSupportMenu = (supportType) => {
        let sType = supportType;

        switch (supportType) {
            case 'BUSINESS':
                sType = 'BUSINESS';
                break;
            case 'REGULER':
                sType = 'REGULER';
                break;
            default:
                sType = supportType;
                break;
        }

        this.forceOpenSupportSidebar(sType);
    }

    forceOpenSupportSidebar = (sType) => {
        const { router, updateActiveGroupMenu, onMenuClick } = this.props;
        /* FIXME: REALLY NEED TO REMOVE THIS ONE */
        const supp = [whiteListFromSupport.BELI_LANGGANAN_SUPPORT, whiteListFromSupport.TIKET_SUPPORT];
        updateActiveGroupMenu(whiteListFromSupport.ROOT_SUPPORT);
        onMenuClick(1, whiteListFromSupport.SUPPORT_PARENT, supp);
        onMenuClick(2, whiteListFromSupport.BELI_LANGGANAN_SUPPORT, []);
        router.push(`/support/buy?support=${sType}`);
    }

    onHideAds = () => {
        const idUser = tokenUtils.getTokenPayload('id');
        const { noAds, adsEndDate } = this.state;
        this.setState({
            adsOpen: false,
        }, () => {
            const getlocalAds = JSON.parse(this.getCookie('advertisement'));

            let localAdsHit = parseInt(1, 10);
            if (getlocalAds) {
                localAdsHit = parseInt(getlocalAds.localAdsHit, 10) + 1;
            }
            const localAdsArray = { noAds, localAdsHit, idUser };
            this.setState({
                localAdsHit,
            }, () => {
                this.setCookie('advertisement', JSON.stringify(localAdsArray), adsEndDate);
            });
        });

        // save to LOG
        this._postAdsLog(noAds, 'dismiss');
    }

    setCookie = (name, value, date) => {
        const expires = `expires=${moment(date).toDate()}`;
        document.cookie = `${name}=${value};${expires};path=/`;
    }

    getCookie = (name) => {
        const fullname = `${name}=`;
        const decodedCookie = decodeURIComponent(document.cookie);
        const ca = decodedCookie.split(';');
        for (let i = 0; i < ca.length; i += 1) {
            let c = ca[i];
            while (c.charAt(0) === ' ') {
                c = c.substring(1);
            }
            if (c.indexOf(fullname) === 0) {
                return c.substring(fullname.length, c.length);
            }
        }
        return null;
    }

    dismissWarning = () => {
        this.setState({
            isWarningDismissed: true,
        });
    }

    noContentDetection = (isBreadcrumb, isBreadcrumbAndButton) => {
        const { isMobile } = this.props;
        const { isWarningDismissed, adsData, shouldShowWarningMessage } = this.state;
        const isAdsBreadcrumbButton = adsData.length || isBreadcrumb || isBreadcrumbAndButton;
        if (isMobile) {
            return (shouldShowWarningMessage && isWarningDismissed === false) || isAdsBreadcrumbButton ? '' : 'no-content';
        }

        return isAdsBreadcrumbButton ? '' : 'no-content';
    }

    render() {
        const {
            adsData, adsOpen, localAdsHit, adsTotalHit,
            noAds,
        } = this.state;

        const {
            buttons: btns,
            cabang,
            warningMessageData,
            style,
            isLoaderShow,
            isFoldedMenu,
            isMobile,
            hasMenuBanner,
            t,
            router,
        } = this.props;

        const buttons = btns
            .map((button, index) => {
                const key = `btn-${index}`;
                if (Object.prototype.hasOwnProperty.call(button, 'dropdown')) {
                    return (
                        <DropDown
                            key={key}
                            position="right"
                            type={button.type}
                            content={button.content}
                            items={button.dropdown}
                            showIcon={!button.hideIcon}
                        />
                    );
                }
                if (Object.prototype.hasOwnProperty.call(button, 'custom')) {
                    return button.custom;
                }
                return (
                    <Button
                        key={key}
                        size="sm"
                        onClick={button.action}
                        css={{
                            '& i': {
                                marginRight: '8px',
                            },
                        }}
                        disabled={button.isDisabled}
                    >
                        {button.content}
                    </Button>
                );
            });
        // TODO: temporary harusnya ambil dari redux yang di ambil dari new local storage
        // const isLoginBefore = userUtils.getLocalConfigByKey('isDeviceLogin');
        // TODO: cheker ini harusnya ga disini, better via click
        const isAuthed = tokenUtils.validate();
        if (!isAuthed) {
            redirectToLogin(router);
        }
        /* GET ACCOUNT TYPE */
        const accountType = userUtils.getLocalConfigByKey('accountType');
        const listExpiredOutletId = getExpiredBranchId();
        let outlets = '';

        let accountTypeLabel = accountType;
        if (accountType === accountTypeLists.ENTERPRISE) accountTypeLabel = accountTypeLists.PRIME;

        if (cabang) {
            outlets = getExpiredCabangName(cabang, listExpiredOutletId);
        }

        const warnedOutlets = [];
        warningMessageData.forEach((x) => {
            if (x) {
                warnedOutlets.push(x.name);
            }
        });
        /* END */
        // TODO: remove isBreadcrumb jika memang tidak dipake, sementara di set false dulu
        // const isBreadcrumb = currentRoutes.reduce((prev, curr) => prev || !curr.breadcrumbIgnore, false);
        const isBreadcrumb = false;
        const isBreadcrumbAndButton = isBreadcrumb && buttons.length > 0;
        const noContent = this.noContentDetection(isBreadcrumb, isBreadcrumbAndButton);

        const wrapperContent = {
            position: 'relative',
            margin: '0 auto',
            width: '100%',
            padding: '0 $spacing-05',
            '@lg': { maxWidth: !isFoldedMenu ? '1006px' : '1206px', padding: 0 },
            '@3xl': { maxWidth: '1206px', margin: '0 auto', padding: 0 },
        };
        return (
            <div
                className={`content-header clearfix ${noContent}`}
                ref={(c) => { this.container = c; }}
                style={{
                    padding: 0,
                    marginTop: 0,
                    ...(!hasMenuBanner && !isSupportExpired()) && { marginBottom: isMobile ? '0' : '40px' },
                    ...(isSupportExpired() && !isMobile) && { padding: '24px 0 0' },
                    ...(btns.length) && {
                            display: 'flex',
                            justifyContent: 'center',
                            marginBottom: 0,
                            '@media (min-width: 1023px)': {
                                marginLeft: !isFoldedMenu ? 280 : 80,
                            }
                    },
                    ...style,
                }}
            >
                <Box css={wrapperContent}>
                    {(adsOpen && (parseInt(localAdsHit, 10) < parseInt(adsTotalHit, 10)) && !isLoaderShow) && (
                        // RETINA VERSION
                        <AdvertisementV2
                            isAdsData={adsData}
                            onHideAdsHandle={this.onHideAds}
                            isOpen={adsOpen}
                            // save lo LOG
                            onRedirectEvent={
                                () => {
                                    this.setState({ adsOpen: false });
                                    this._postAdsLog(noAds, 'hook');
                                }
                            }
                        />
                    )}
                    {isSupportExpired() && (
                        <Banner variant="failed" css={{ mb: '$spacing-06', padding: '16px' }}>
                            <Flex
                                justify="between"
                                css={{
                                    '& p': { whiteSpace: 'normal' }, width: '100%', flexDirection: 'column', '@md': { whiteSpace: 'nowrap', flexDirection: 'row' },
                                }}
                            >
                                <BannerDescription>
                                    {t('subscription.expiredSupport.description', `Paket layanan berlangganan ${accountTypeLabel} pada cabang ${outlets} telah berakhir. `, { accountType: accountTypeLabel, outlets })}
                                </BannerDescription>
                                <Flex
                                    justify="end"
                                    onClick={this.openSupportMenu.bind(this, accountTypeLabel)}
                                    css={{
                                        cursor: 'pointer', ml: 'auto', '& a': { color: '$textRed' }, '& a:hover': { color: '$textRed' },
                                    }}
                                >
                                    <BannerLink>
                                        {t('subscription.expiredSupport.updateButton', 'Perbarui Layanan')}
                                    </BannerLink>
                                </Flex>
                            </Flex>
                        </Banner>
                    )}
                    {!!btns.length && (
                        <Box
                            className="content-header-action pull-right"
                            css={{
                                display: 'flex',
                                gap: '8px',
                                padding: 0,
                                maxWidth: !isFoldedMenu ? '1006px' : '1206px',
                                flexDirection: 'column',
                                width: '$full',
                                '@lg': {
                                    padding: '24px 0 0',
                                    flexDirection: 'row',
                                    alignItems: 'center',                                
                                    justifyContent: 'end',
                                }
                            }}
                        >
                            {buttons}
                        </Box>
                    )}
                </Box>
            </div>
        );
    }
}

const ContentHeader = (props) => <ClassComponent {...props} />;

const mapStateToProps = (store) => {
    const {
        layouts: {
            menu,
            activeMenu,
            activeMenuGroup,
            expandedMenu,
        },
    } = store;
    return {
        calendar: {
            start: store.layout.calendar.start,
            end: store.layout.calendar.end,
            onchange: store.layout.calendar.onchange, // set null untuk menghapus onchange, set tanpa parameter(undefined) jika tidak ingin merubah value existing
            rangeLimit: store.layout.calendar.rangeLimit,
        },
        filterCalendar: {
            data: store.layout.filterCalendar.data,
            value: store.layout.filterCalendar.value,
            onchange: store.layout.filterCalendar.onchange,
        },
        buttons: store.layout.buttons,
        cabang: store.branch.list,
        supports: store.support,
        customBreadcrumbs: store.layout.customBreadcrumbs,
        menuList: layoutSelectors.getVisibleNav({
            menu, activeMenu, activeMenuGroup, expandedMenu,
        }),
        mobileMenuList: layoutSelectors.getVisibleNav({
            menu: layoutSelectors.getAllowedMobileMenu(layoutSelectors.getModifiedMobileMenuList(menu)),
            activeMenu,
            activeMenuGroup,
            expandedMenu,
        }),
    };
};

const mapDispatchToProps = dispatch => bindActionCreators({
    onMenuClick: layoutsActions.onMenuChange,
    updateActiveGroupMenu: layoutsActions.updateActiveMenuGroup,
}, dispatch);

export default connect(mapStateToProps, mapDispatchToProps)(ContentHeader);
