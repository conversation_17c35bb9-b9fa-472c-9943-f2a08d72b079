/* @import (css) url('https://fonts.googleapis.com/css2?family=Inter:wght@500;700&display=swap'); */
.warning_expired {
    position: fixed;
    bottom: 64px;
    left: 0;
    background: #99341D;
    width: 280px;
    height: 64px;
    padding: 10.5px 10px 11.5px 10px;
    color: #FFF;
    font-family: "Inter", sans-serif;
}

.warning_expired_toggle {
    position: fixed;
    bottom: 64px;
    left: 0;
    background: #99341D;
    width: 80px;
    height: 64px;
    padding: 10.5px 10px 11.5px 10px;
    color: #FFF;
    font-family: "Inter", sans-serif;
}

.warning_expired_main_toggle {
    display: flex;
    justify-content: center;
    flex-basis: 125px;
    margin-top: 5px;
}

.warning_expired .warning_expired_expand {
    position: absolute;
    width: 280px;
    height: 0;
    bottom: 64px;
    background: #fff;
    border: none;
    overflow: hidden;
    color: #1d1d1d;
    left: 0;
    opacity: 0;

    transition: all 0.24s ease-out;
}

.warning_expired .warning_expired_expand.expanded {
    border: 1px solid #DCE9F2;
    bottom: 64px;
    opacity: 1;
}

.warning_expired_expand--header {
    padding: 19px 16px 16px 16px;
    color: #263238;
    font-size: 14px;
    line-height: 17px;
    letter-spacing: -0.18px;
    font-weight: 600;
    font-family: "Inter", sans-serif;
    border-bottom: 1px solid #D8E7F0;
}

.warning_expired_expand--header button{
    width: 20px;
    height: 20px;
    background: transparent;
    border: none;
    font-size: 17px;
    color: #78909c;
    position: absolute;
    top: 16px;
    right: 16px;
}

.warning_expired_expand--body {
    padding: 9px;
}

.warning_expired_expand--body table {
    width: 100%;
    table-layout: fixed;
}

.warning_expired_expand--body table thead{
    background: #edf0f7;
}

.warning_expired_expand--body table tbody tr {
    height: 46px;
}

.warning_expired_expand--body table thead tr th{
    font-weight: 500;
    font-style: normal;
    font-size: 11.5px;
    line-height: 15px;
    padding: 8px;
    color: #78909c;
    width: 50%;
}

.warning_expired_expand--body table tbody tr td{
    font-weight: 600;
    font-size: 11px;
    line-height: 15px;
    padding: 8px;
    color: #263238;
    width: 50%;
    overflow: hidden;
}

.warning_expired_expand--body table tbody tr td.spanned{
    background: #edf0f7;
    color: #78909c;
}

.warning_expired_expand--body table tbody tr td > span{
    width: 100%;
    white-space: pre;
    text-overflow: ellipsis;
    display: block;
    overflow: hidden;
}

.warning_expired_expand--body table tbody tr td.right-side {
    text-align: right;
}

.warning_expired .warning_expired_main {
    display: flex;
}

.warning_expired .warning_expired_main label {
    display: block;
    flex-basis: 125px;
    font-weight: 500;
    font-size: 13px;
    line-height: 18px;
    letter-spacing: -0.18px;
    margin: 0;
    padding: 0;
}

.warning_expired .warning_expired_main button.naked {
    height: 32px;
    font-size: 11px;
    flex: 1;
    text-align: center;
    line-height: 32px;
    border: none;
    background: transparent;
    color: #fff;
    outline: none;
    visibility: visible;
}

.warning_expired .warning_expired_main button.naked.hidden {
    visibility: hidden;
}

.warning_expired .warning_expired_main button {
    flex-basis: 100px;
    border: none;
    background: #ff5630;
    border-radius: 3px;
    color: #fff;
    font-size: 13px;
}

.chat_panel {
    position: fixed;
    bottom: 0;
    left: 0;
    background: #10AF8C;
    width: 280px;
    transition: width 0.5s;
    height: 64px;
    padding: 0px 16px 0px 24px;
    color: #FFF;
    font-family: "Inter", sans-serif;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    cursor: pointer;
}

.chat_panel_toggle {
    position: fixed;
    bottom: 0;
    left: 0;
    background: transparent;
    width: 80px;
    transition: width 0.5s;
    height: 64px;
    padding: 10.5px 10px 11.5px 10px;
    color: #FFF;
    font-family: "Inter", sans-serif;
    display: flex;
    flex-direction: row;
    cursor: pointer;
}

.chat_panel.disabled {
    background: #636363;
}

.chat_panel.disabled button {
    background: #808080;
}

.chat_panel .chat_panel--main {
    display: flex;
    flex-basis: 125px;
}

.chat_panel_toggle .chat_panel_toggle--main {
    display: flex;
    flex-basis: 125px;
    justify-content: center;
}

.chat_panel.unavailable {
    height: 96px;
}

.chat_panel.unavailable .chat_panel--main {
    display: block;
    flex-basis: 100%;
}

/* .chat_panel .chat_panel--main img {
    margin-right: 10px;
} */

.chat_panel .chat_panel--main div {
    padding-right: 8px;
}

.chat_panel .chat_panel--main div > span {
    display: block;
    padding: 0;
    margin: 0;
    font-weight: 500;
    font-size: 11px;
    line-height: 14px;
}

.chat_panel .chat_panel--main div > label {
    padding: 0;
    margin: -2px 0 0 0;
    display: block;
    font-weight: bold;
    font-size: 13px;
    line-height: 17px;
    font-family: "Quicksand", cursive;
}

.chat_panel .chat_panel--main label {
    font-size: 13px;
    line-height: 17px;
}

/* iframe#launcher {
    left: 150px !important;
    bottom: 2px !important;
    margin: 0 !important;
} */
