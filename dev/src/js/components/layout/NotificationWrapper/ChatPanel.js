import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { <PERSON>ton, Image } from '@majoo-ui/react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Translation } from 'react-i18next';
import { layoutsActions } from '../../../data/layouts/layouts.reducer';
import layoutSelectors from '../../../data/layouts/layouts.selector';
import './wrapper.css';
/* ENUM */
import { whiteListFromSupport } from '../../../config/enum';
import MajooCareImg from '../../../../assets/images/majoo-care-logo.png';

export default
@connect(
    ({
        layouts: {
            menu,
            activeMenu,
            activeMenuGroup,
            expandedMenu,
        },
    }) => ({
        menuList: layoutSelectors.getVisibleNav({
            menu, activeMenu, activeMenuGroup, expandedMenu,
        }),
        mobileMenuList: layoutSelectors.getVisibleNav({
            menu: layoutSelectors.getAllowedMobileMenu(layoutSelectors.getModifiedMobileMenuList(menu)),
            activeMenu,
            activeMenuGroup,
            expandedMenu,
        }),
    }),
    dispatch => bindActionCreators({
        onMenuClick: layoutsActions.onMenuChange,
        updateActiveGroupMenu: layoutsActions.updateActiveMenuGroup,
    }, dispatch),
)
class ChatPanel extends Component {
    static propTypes = {
        clickHandler: PropTypes.func.isRequired,
        accountType: PropTypes.string.isRequired,
        router: PropTypes.shape({
            push: PropTypes.func,
        }).isRequired,
        onMenuClick: PropTypes.func,
        updateActiveGroupMenu: PropTypes.func,
        isToggleMenu: PropTypes.bool,
        isHover: PropTypes.bool,
        onHoverHandler: PropTypes.func,
        isMobile: PropTypes.bool,
    };

    static defaultProps = {
        onMenuClick: null,
        updateActiveGroupMenu: null,
        isToggleMenu: false,
        isHover: false,
        onHoverHandler: () => { },
        isMobile: false,
    }

    constructor(props) {
        super(props);
        this.state = {
        };
    }

    onMouseEnterHandler = () => {
        const { onHoverHandler } = this.props;

        const hoverStatus = true;
        onHoverHandler(hoverStatus);
    }

    onMouseLeaveHandler = () => {
        const { onHoverHandler } = this.props;

        const hoverStatus = false;
        onHoverHandler(hoverStatus);
    }

    onClickHandler = () => {
        const { clickHandler } = this.props;
        // const isEmergency = false;

        clickHandler();
    }

    renderChat = () => {
        const { isToggleMenu, isMobile } = this.props;
        return (
            <div
                className="chat_panel"
                role="button"
                style={{
                    zIndex: '1300',
                    width: !isMobile ? '280px' : '360px',
                }}
                onClick={this.onClickHandler}
                onKeyUp={() => { }}
                tabIndex="-1"
                onMouseEnter={isToggleMenu ? this.onMouseEnterHandler : () => { }}
                onMouseLeave={isToggleMenu ? this.onMouseLeaveHandler : () => { }}
            >
                <Image width={80} height={22} src={MajooCareImg} alt="Majoo Care" />
                <Translation ns="translation">
                    {
                        t => (
                            <Button
                                type="button"
                                buttonType="secondary"
                                tabIndex="-1"
                                onClick={this.onClickHandler}
                                css={{
                                    width: '135px',
                                    padding: 0,
                                }}
                            >
                                {t('label.chatPanelButton', 'Chat 24 Jam')}
                            </Button>
                        )
                    }
                </Translation>
            </div>
        );
    }

    renderChatToggle = () => (
        <div
            className="chat_panel_toggle"
            role="button"
            style={{
                zIndex: '1011',
            }}
            onClick={this.onClickHandler}
            onKeyUp={() => { }}
            tabIndex="-1"
            onMouseEnter={this.onMouseEnterHandler}
            onMouseLeave={this.onMouseLeaveHandler}
        />
    )

    forceSetActiveMenu = () => {
        const {
            onMenuClick,
            updateActiveGroupMenu,
            router,
        } = this.props;

        const supp = [whiteListFromSupport.BELI_LANGGANAN_SUPPORT, whiteListFromSupport.TIKET_SUPPORT];
        updateActiveGroupMenu(whiteListFromSupport.ROOT_SUPPORT);
        onMenuClick(1, whiteListFromSupport.SUPPORT_PARENT, supp);
        onMenuClick(2, whiteListFromSupport.BELI_LANGGANAN_SUPPORT, []);
        router.push('/support/buy?support=ADVANCE');
    }

    render() {
        const { isToggleMenu, isHover } = this.props;

        if (isToggleMenu && !isHover) {
            return this.renderChatToggle();
        }

        return this.renderChat();
    }
}
