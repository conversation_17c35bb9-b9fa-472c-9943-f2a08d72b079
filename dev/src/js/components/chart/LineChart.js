/* eslint-disable no-nested-ternary */
import React, { useEffect, useState, useRef } from 'react';
import {
  CartesianGrid,
  Line,
  LineChart as LC,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import {
  Box,
  Heading,
  Paragraph,
  InputCheckbox,
  Text,
  Paper,
  Button,
  InputSelect,
  Image,
  Flex,
} from '@majoo-ui/react';
import { CloseOutline, PlusOutline } from '@majoo-ui/icons';
import PropTypes from 'prop-types';
import { foundations } from '@majoo-ui/core';
import moment from 'moment';
import { useTranslation } from 'react-i18next';
import { currency, formatThousandSeparator, numSeparator } from '../../utils/helper';
import { ProductList } from '../retina';

import doodle from './data_kosong.png';
import {
  compactCurrency, handleXaxis, roundDown, roundUp,
} from './helper';
import { styled } from '../../stitches.config';
import { isValidDate } from '../../utils/date.util';

const { colors } = foundations;

const COMPARISON_LIMIT = 5;

const ChartTitleLegend = ({
  title,
  legendData,
  filteredData,
  setFilteredData,
  isComparator,
  titleOnly,
}) => filteredData.length > 0 && (
  <Box
    css={{
      display: 'flex',
      marginBottom: '$spacing-06',
      '@sm': {
        flexDirection: 'column',
        gap: '$compact',
        alignItems: 'start',
      },
      '@md': { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
    }}
  >
    <Box>
      <Heading
        heading="sectionTitle"
        css={{ lineHeight: '24px', marginBottom: '$spacing-03' }}
      >
        {title}
      </Heading>
    </Box>
    {!titleOnly && !isComparator && (
      <Box
        css={{
          '@sm': {
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '$compact'
          },
          '@md': {
            display: 'flex',
            flexWrap: 'nowrap',
            gap: '$compact',
          },
        }}
      >
        {legendData.map(legend => (
          <Box
            key={legend.name}
            css={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'start',
            }}
          >
            {legend.isHideable && (
              <InputCheckbox
                id={legend.name}
                checked={
                  filteredData.find(data => data.name === legend.name) ? !filteredData.find(data => data.name === legend.name).hide : false
                }
                onCheckedChange={status => setFilteredData(
                  filteredData.map(data => (data.name === legend.name
                    ? { ...data, hide: !status }
                    : { ...data })),
                )
                }
              />
            )}
            <Box
              css={{
                backgroundColor: legend.color,
                borderRadius: '50%',
                display: 'inline-block',
                margin: legend.isHideable ? '0px 6px 0px 10px' : '0px 4px 0px 0px',
                '@sm': {
                  height: legend.isHideable ? 4 : 10,
                  width: legend.isHideable ? 4 : 10,
                },
                '@md': {
                  height: 6,
                  width: 6,
                },
              }}
            />
            <Text variant="label" color="primary">{legend.name}</Text>
          </Box>
        ))}
      </Box>
    )}
  </Box>
);

ChartTitleLegend.propTypes = {
  title: PropTypes.string.isRequired,
  legendData: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  filteredData: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  setFilteredData: PropTypes.func.isRequired,
  isComparator: PropTypes.bool,
  titleOnly: PropTypes.bool,
};

ChartTitleLegend.defaultProps = {
  isComparator: undefined,
  titleOnly: false,
};

const StyledIconLeft = styled('div', {
  margin: '5px 3px 5px 0px',
  display: 'inline-flex',
  verticalAlign: 'middle',
  width: '8px',
  height: '8px',
  borderRadius: '50%',
});

const ChartTooltip = (props) => {
  const { customTooltip, ...rest } = props;
  const {
    hover, payload, active, legendData, maxValue, period, chartTypeLabel, dateKey, isDateFormat,
  } = rest;
  if (legendData.some(l => l.dataKey === 'novalue')) {
    return (<div />);
  }
  const formatValue = (legendDataKey, hoverKey, hoverValue) => {
    let finalValue = '';
    const selectedKey = legendDataKey.find(l => l.dataKey === hoverKey);
    if (selectedKey === undefined) return finalValue;
    const formattedValue = Number.isNaN(Number(hoverValue)) ? '0' : `${selectedKey.isCurrency
      ? currency({ value: hoverValue, convertMinus: hoverValue < 0, decimal: hoverValue % 1 !== 0 })
      : numSeparator(hoverValue)
      }`;
    finalValue = `${formattedValue} ${chartTypeLabel || selectedKey.tooltipLabel || selectedKey.name}`;
    return finalValue;
  };

  const HOVER_RANGE_VALUE = maxValue > 100000 ? 100000 : 0;

  if (active && hover) {
    let hoverDate;
    let dateFormat = 'DD MMMM YYYY';
    let mapValue = [];
    if (payload && payload[0]) {
      const firstPayload = payload[0].payload;
      hoverDate = firstPayload[dateKey];
      if (!firstPayload[dateKey] || !isValidDate(firstPayload[dateKey])) {
        Object.keys(firstPayload).some((key) => {
          if (isValidDate(firstPayload[key])) {
            hoverDate = firstPayload[key];
            return true;
          }
          return false;
        });
      }
      if (period === 'jam' || period === 'hour') {
        dateFormat = 'HH:mm';
      }
      if (period === 'bulan' || period === 'month') {
        dateFormat = 'MMMM YYYY';
      }
      if (period === 'tahun' || period === 'year') {
        dateFormat = 'YYYY';
      }
      const value = firstPayload[hover];
      const inRangeValue = HOVER_RANGE_VALUE === 0
        ? Object.keys(firstPayload).filter(
          key => firstPayload[key] === value,
        )
        : Object.keys(firstPayload).filter(
          key => firstPayload[key] > value - HOVER_RANGE_VALUE
            && firstPayload[key] < value + HOVER_RANGE_VALUE,
        );
      const visibleValue = legendData
        .filter(v => v.hide === false);
      mapValue = visibleValue.map(val => ({
        ...val,
        name: val.dataKey,
        hoverValue: firstPayload[val.dataKey],
      }));
    }
    return customTooltip ? customTooltip(rest) : (
      <Box
        css={{
          backgroundColor: '$textContent',
          color: '$white',
          borderRadius: '$sm',
          padding: '$spacing-03',
        }}
      >
        {hoverDate
          && <Paragraph paragraph="shortContentRegular">{isDateFormat && moment(hoverDate).isValid() ? moment(hoverDate).format(dateFormat) : hoverDate}</Paragraph>}
        {mapValue.map(s => (
          <Flex key={s.name} align="center" gap={2}>
            <StyledIconLeft css={{ backgroundColor: s.color }} />
            <Paragraph
              key={s.name}
              paragraph="shortContentRegular"
            >
              {formatValue(legendData, s.name, s.hoverValue)}
            </Paragraph>
          </Flex>
        ))}
      </Box>
    );
  }
  return null;
};

ChartTooltip.propTypes = {
  hover: PropTypes.string,
  payload: PropTypes.arrayOf(PropTypes.shape({})),
  active: PropTypes.oneOfType([PropTypes.bool]),
  legendData: PropTypes.arrayOf(PropTypes.shape({})),
  maxValue: PropTypes.number.isRequired,
  period: PropTypes.string,
  chartTypeLabel: PropTypes.string,
  dateKey: PropTypes.string,
};

ChartTooltip.defaultProps = {
  hover: null,
  payload: undefined,
  active: undefined,
  legendData: [],
  period: '',
  chartTypeLabel: undefined,
  dateKey: 'date',
};

const ComparatorItem = ({
  dataKey, color, onCloseClick, disableClose, variants,
}) => (
  <Box
    css={{
      display: 'flex',
      flexDirection: 'column',
      '@sm': {
        padding: '$spacing-02',
        gap: 8,
        backgroundColor: '$white',
        boxShadow: '$small',
        borderRadius: '$md',
      },
      '@md': {
        padding: '$spacing-03 $spacing-04',
        gap: '$compact',
        backgroundColor: 'none',
        boxShadow: 'none',
        borderRadius: 'none',
      },
    }}
  >
    <Box
      css={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        position: 'relative',
      }}
    >
      <Box css={{ display: 'flex', alignItems: 'center', height: 24 }}>
        <Box
          css={{
            position: 'absolute',
            left: 4,
            backgroundColor: color,
            width: 12,
            height: 12,
            borderRadius: '50%',
          }}
        />
        <Box
          css={{
            width: 80,
            height: 4,
            backgroundColor: color,
          }}
        />
      </Box>
      <Box
        onClick={() => onCloseClick()}
        css={{ cursor: 'pointer', display: disableClose ? 'none' : 'block' }}
      >
        <CloseOutline color={colors.iconSecondary} size={24} />
      </Box>
    </Box>
    <ProductList name={dataKey} variants={variants} direction="column" css={{ '& > div': { fontWeight: 600 } }} />
  </Box>
);

ComparatorItem.propTypes = {
  dataKey: PropTypes.string.isRequired,
  color: PropTypes.string.isRequired,
  onCloseClick: PropTypes.func.isRequired,
  disableClose: PropTypes.bool.isRequired,
  variants: PropTypes.arrayOf(PropTypes.string),
};

ComparatorItem.defaultProps = {
  variants: [],
};

const ComparatorController = ({
  option, onComparatorAdd, onComparatorSearch, placeholder, emptyDataMessage, t,
}) => {
  const [toggle, setToggle] = useState(true);

  const handleToggle = () => {
    setToggle(!toggle);
  };

  const handleSelected = (selected) => {
    onComparatorAdd(selected);
    setToggle(true);
  };

  const handleSearch = (keyword) => {
    onComparatorSearch(keyword);
    return keyword;
  };

  return (
    <React.Fragment>
      <Box
        css={{
          display: toggle ? 'flex' : 'none',
          justifyContent: 'center',
          alignItems: 'center',
          '@sm': {
            padding: 0,
          },
          '@md': {
            padding: '$spacing-03 $spacing-04',
          },
        }}
      >
        <Button
          buttonType="ghost"
          css={{
            fontWeight: 600,
            '@sm': {
              height: 68,
              width: '100%',
              backgroundColor: '$white',
              boxShadow: '$small',
              borderRadius: '$md',
            },
            '@md': {
              height: 'auto',
              width: 'auto',
              backgroundColor: 'none',
              boxShadow: 'none',
              borderRadius: 'none',
            },
          }}
          leftIcon={<PlusOutline size={24} color={colors.primary500} />}
          onClick={() => handleToggle()}
        >
          {t('label.comparison', 'Perbandingan')}
        </Button>
      </Box>
      <Box
        css={{
          display: toggle ? 'none' : 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          padding: '$spacing-03 $spacing-04',
          '@sm': {
            height: 68,
            width: '100%',
            backgroundColor: '$white',
            boxShadow: '$small',
            borderRadius: '$md',
          },
          '@md': {
            height: 'unset',
            width: 'unset',
            backgroundColor: 'unset',
            boxShadow: 'unset',
            borderRadius: 'unset',
          },
        }}
      >
        <InputSelect
          key={`${toggle}-toggle-chart-comparator`}
          showLeadingIcon
          hideArrowicon
          search
          size="sm"
          placeholder={placeholder || t('placeholder.search', 'Cari ...')}
          option={option}
          onChange={x => handleSelected(option.find(opt => opt.value === x.value))}
          onSearch={y => handleSearch(y)}
          emptyDataMessage={emptyDataMessage && emptyDataMessage}
        />
      </Box>
    </React.Fragment>
  );
};

ComparatorController.propTypes = {
  option: PropTypes.arrayOf(PropTypes.shape({})),
  onComparatorAdd: PropTypes.func.isRequired,
  placeholder: PropTypes.string.isRequired,
  onComparatorSearch: PropTypes.func,
  emptyDataMessage: PropTypes.string,
  t: PropTypes.func,
};

ComparatorController.defaultProps = {
  option: [{}],
  onComparatorSearch: () => { },
  emptyDataMessage: undefined,
  t: () => { },
};

const Comparator = ({
  option, onCloseClick, onComparatorAdd, legendData, placeholder, onComparatorSearch, disableClose, disableController, emptyDataMessage, t,
}) => {
  const [filteredOption, setFilteredOption] = useState(option);
  const [isDisableClose, setIsDisableClose] = useState(false);

  const visibleOptions = option.filter(opt => !opt.hide);

  useEffect(() => {
    setFilteredOption(
      legendData.filter(f => !option.some((l) => {
        if (typeof l.value === 'number') {
          return +l.value === +f.value;
        }
        if (typeof l.value === 'string') {
          return String(l.value) === String(f.value);
        }
        return false;
      })),
    );
    if (option.length === 1 || disableClose) {
      setIsDisableClose(true);
    } else {
      setIsDisableClose(false);
    }
  }, [option, legendData]);

  return (
    <Paper
      css={{
        padding: 0,
        margin: '$spacing-03',
        display: 'grid',
        '@sm': {
          gap: 8,
          gridTemplateColumns: 'repeat(2,1fr)',
          boxShadow: 'none',
          backgroundColor: 'transparent',
        },
        '@md': {
          width: `calc((20% - 4px) * ${visibleOptions.length === 5 || disableController ? visibleOptions.length : visibleOptions.length + 1})`,
          rowGap: '$compact',
          gridTemplateColumns: `repeat(${visibleOptions.length === 5 || disableController ? visibleOptions.length : visibleOptions.length + 1},1fr)`,
          boxShadow: '$small',
          backgroundColor: '$white',
          '& > div': {
            borderRight: '2px solid black',
            borderImage:
              'linear-gradient(0deg, rgba(255,255,255,0) 0%, rgba(238,240,240,1) 50%, rgba(255,255,255,0) 100%)',
            borderImageSlice: 1,
          },
          '& > div:nth-child(5n)': {
            borderRight: 'none',
          },
        },
      }}
    >
      {visibleOptions.map(opt => (
        <ComparatorItem
          key={opt.value}
          dataKey={opt.name || opt.dataKey}
          color={opt.color}
          onCloseClick={() => onCloseClick(opt)}
          variants={opt.variants}
          disableClose={isDisableClose}
        />
      ))}
      {(option.length < COMPARISON_LIMIT && !disableController) && (
        <ComparatorController
          option={filteredOption}
          onComparatorAdd={d => onComparatorAdd(d)}
          onComparatorSearch={keyword => onComparatorSearch(keyword)}
          placeholder={placeholder}
          emptyDataMessage={emptyDataMessage}
          t={t}
        />
      )}
    </Paper>
  );
};

Comparator.propTypes = {
  option: PropTypes.arrayOf(PropTypes.shape({})),
  legendData: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  onCloseClick: PropTypes.func.isRequired,
  onComparatorAdd: PropTypes.func.isRequired,
  placeholder: PropTypes.string.isRequired,
  onComparatorSearch: PropTypes.func,
  disableClose: PropTypes.bool,
  disableController: PropTypes.bool,
  emptyDataMessage: PropTypes.string,
  t: PropTypes.func,
};

Comparator.defaultProps = {
  option: [{}],
  onComparatorSearch: () => { },
  disableClose: false,
  disableController: false,
  emptyDataMessage: undefined,
  t: () => { },
};

export const LineChart = ({
  data,
  legendData,
  xAxisKey,
  customXAxisFormat,
  customYAxisFormat,
  title,
  onComparatorChange,
  comparatorList,
  placeholder,
  roundUpYAxis,
  period,
  chartTypeLabel,
  intervalXAxis,
  tickCountYAXis,
  isEmptyData,
  disableComparatorClose,
  disableComparatorController,
  width,
  height,
  customTooltip,
  emptyDataMessage,
  isDateFormat = true,
  hideLegendTitle,
  containerWidth,
  hideRightYAxis,
}) => {
  const [hover, setHover] = useState(null);
  const [filteredLegendData, setFilteredLegendData] = useState([]);
  const [isComparator, setIsComparator] = useState(false);
  const [maxData, setMaxData] = useState(0);
  const [isDividedByThousand, setIsDividedByThousand] = useState(false);

  const { t, i18n } = useTranslation('translation');
  const lang = i18n.language;

  useEffect(() => {
    if (maxData > 0) {
      setIsDividedByThousand(String(Math.round(maxData)).length > 3);
    }
  }, [maxData]);

  useEffect(() => {
    setFilteredLegendData(
      legendData.map(value => ({
        ...value,
        hide: false,
        isCurrency: !!value.isCurrency,
      })),
    );
    if (legendData.length === 0) {
      setFilteredLegendData([{
        dataKey: 'novalue', name: 'novalue', color: colors.chartYellow, hide: true,
      }]);
      setIsComparator(false);
    }
    // if (legendData.length === 0) {
    //   setFilteredData([{
    //     dataKey: 'novalue', name: 'novalue', color: colors.chartYellow, hide: false,
    //   }]);
    // }
    if (legendData.some(l => l.isComparator === true)) {
      setIsComparator(true);
      setFilteredLegendData(
        legendData
          .map(value => ({ ...value, hide: false }))
          .slice(0, COMPARISON_LIMIT),
      );
    }
  }, [legendData]);

  return (
    <React.Fragment>
      {!hideLegendTitle && (
        <ChartTitleLegend
          title={title}
          legendData={legendData}
          filteredData={filteredLegendData}
          setFilteredData={setFilteredLegendData}
          isComparator={isComparator}
          isDividedByThousand={isDividedByThousand}
          titleOnly={data.length === 0}
        />
      )}
      <Box
        css={{
          '@sm': {
            overflowY: 'hidden',
            whiteSpace: 'nowrap',
            overflowX: 'scroll',
            scrollbarWidth: 0,
            '&::-webkit-scrollbar': { width: 0, height: 0 },
          },
          '@4xl': {
            overflowX: 'hidden',
            overflowY: 'auto',
            whiteSpace: 'wrap',
          },
        }}
      >
        <Box
          css={{
            '@sm': {
              width: data.length === 0 ? 'auto' : 1006,
              height: 250,
              display: data.length === 0 ? 'flex' : 'inline-block',
              justifyContent: data.length === 0 && 'center',
            },
            '@md': { width: width || legendData.some(l => l.yAxisId === 'right') ? '100%' : 1006, height: height || 300 },
            '@3xl': { width: width || legendData.some(l => l.yAxisId === 'right') ? '100%' : 1206, height: height || 300 },
            '& circle': {
              r: 4,
            },
            'g.yAxis text': {
              fontSize: '$label',
              lineHeight: '$label',
              fontWeight: '500',
              fill: '$textSecondary',
            },
            'g.xAxis text': {
              fontSize: '$label',
              lineHeight: '$label',
              fontWeight: '500',
              fill: '$textPrimary',
            },
            ...(containerWidth && { width: `${containerWidth} !important` }),
          }}
        >
          <ResponsiveContainer>
            <LC
              data={legendData.length === 0 ? data.map(d => ({ ...d, novalue: 0 })) : data}
              margin={{
                left: -40,
                top: 20,
                bottom: 5,
                right: 20,
              }}
            >
              <XAxis
                padding={{ left: 48 }}
                width={2000}
                dataKey={xAxisKey}
                tickFormatter={(dateValue) => {
                  if (dateValue === 'auto') return 0;
                  if (customXAxisFormat) {
                    return customXAxisFormat(dateValue);
                  }

                  if (!isValidDate(dateValue)) return dateValue;

                  if (period) {
                    return handleXaxis(dateValue, period);
                  }

                  return dateValue;
                }}
                interval={intervalXAxis}
                tickLine={false}
                axisLine={false}
                hide={data.length === 0}
                dy={16}
              />
              <YAxis
                yAxisId="left"
                width={72}
                textAnchor="start"
                tickLine={false}
                axisLine={false}
                dy={-15}
                dx={-10}
                type="number"
                scale="linear"
                domain={!isEmptyData ? [
                  dataMin => (dataMin < 0 ? roundDown(dataMin) : 0),
                  (dataMax) => {
                    setMaxData(dataMax);
                    return roundUpYAxis ? roundUp(dataMax) : dataMax;
                  },
                ] : [0, 200]}
                tickCount={tickCountYAXis}
                allowDecimals={roundUpYAxis}
                tickFormatter={val => (customYAxisFormat ? customYAxisFormat(val) : compactCurrency(val, lang))}
              />
              {!hideRightYAxis && (
                <YAxis
                  yAxisId="right"
                  textAnchor="start"
                  orientation="right"
                  tickLine={false}
                  axisLine={false}
                  dy={-10}
                  dx={40}
                  type="number"
                  domain={['dataMin', 'auto']}
                  allowDecimals={roundUpYAxis}
                  tickCount={maxData === 0 ? 3 : tickCountYAXis}
                  tickFormatter={val => formatThousandSeparator(val, { notation: 'compact', compactDisplay: 'short' })}
                />
              )}
              <CartesianGrid vertical={false} x={8} width={1200} />
              <Tooltip
                cursor={false}
                content={(
                  <ChartTooltip
                    hover={hover}
                    legendData={filteredLegendData}
                    maxValue={maxData}
                    period={period}
                    chartTypeLabel={chartTypeLabel}
                    dateKey={xAxisKey}
                    customTooltip={customTooltip}
                    isDateFormat={isDateFormat}
                  />
                )}
              />
              {filteredLegendData.map(dataFiltered => (
                <Line
                  yAxisId={dataFiltered.yAxisId || 'left'}
                  key={dataFiltered.dataKey}
                  type="monotone"
                  dot={false}
                  dataKey={dataFiltered.dataKey}
                  stroke={dataFiltered.color}
                  hide={dataFiltered.hide}
                  strokeWidth={4}
                  onMouseOver={() => setHover(dataFiltered.dataKey)}
                  activeDot={
                    data.length === 1
                      ? {
                        onMouseOver: (_, b) => {
                          const { cx, cy, fill } = b;
                          setHover(dataFiltered.dataKey);
                          return (
                            <circle
                              cx={cx}
                              cy={cy}
                              r={10}
                              fill={colors.white}
                              stroke={fill}
                              strokeWidth={3}
                            />
                          );
                        },
                        onMouseLeave: () => setHover(null),
                      }
                      : (e) => {
                        const { cx, cy, fill } = e;
                        return (
                          e.dataKey === hover && (
                            <circle
                              cx={cx}
                              cy={cy}
                              r={10}
                              fill={colors.white}
                              stroke={fill}
                              strokeWidth={3}
                            />
                          )
                        );
                      }
                  }
                />
              ))}
            </LC>
          </ResponsiveContainer>
          {/* <Box css={{
                display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', flexDirection: 'column',
              }}
              >
                <Image src={doodle} />
                <Text variant="status">Tidak ada data yang dapat ditampilkan</Text>
              </Box> */}
        </Box>
      </Box>
      {
        isComparator && (
          <Comparator
            legendData={comparatorList}
            placeholder={placeholder}
            option={filteredLegendData}
            onCloseClick={x => onComparatorChange({ type: 'DELETE', payload: x })
            }
            onComparatorAdd={x => onComparatorChange({ type: 'ADD', payload: x })
            }
            onComparatorSearch={x => onComparatorChange({ type: 'SEARCH', payload: x })
            }
            disableClose={disableComparatorClose}
            disableController={disableComparatorController}
            emptyDataMessage={emptyDataMessage}
            t={t}
          />
        )
      }
    </React.Fragment>
  );
};

LineChart.propTypes = {
  data: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  legendData: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  xAxisKey: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
  customXAxisFormat: PropTypes.func,
  customYAxisFormat: PropTypes.func,
  onComparatorChange: PropTypes.func,
  comparatorList: PropTypes.arrayOf(PropTypes.shape({})),
  placeholder: PropTypes.string,
  roundUpYAxis: PropTypes.bool,
  period: PropTypes.string,
  chartTypeLabel: PropTypes.string,
  intervalXAxis: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  tickCountYAXis: PropTypes.number,
  isEmptyData: PropTypes.bool,
  disableComparatorClose: PropTypes.bool,
  disableComparatorController: PropTypes.bool,
  width: PropTypes.number,
  height: PropTypes.number,
  customTooltip: PropTypes.elementType,
  emptyDataMessage: PropTypes.string,
  isDateFormat: PropTypes.bool,
  hideLegendTitle: PropTypes.bool,
  containerWidth: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  hideRightYAxis: PropTypes.bool,
};

LineChart.defaultProps = {
  customXAxisFormat: undefined,
  customYAxisFormat: undefined,
  onComparatorChange: undefined,
  comparatorList: [],
  placeholder: undefined,
  roundUpYAxis: true,
  period: undefined,
  chartTypeLabel: undefined,
  intervalXAxis: 'preserveStart',
  tickCountYAXis: 5,
  isEmptyData: false,
  disableComparatorClose: false,
  disableComparatorController: false,
  width: undefined,
  height: undefined,
  customTooltip: undefined,
  emptyDataMessage: undefined,
  isDateFormat: true,
  hideLegendTitle: false,
  containerWidth: undefined,
  hideRightYAxis: false,
};
