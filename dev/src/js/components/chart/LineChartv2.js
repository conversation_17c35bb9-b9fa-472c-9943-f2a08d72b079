/* eslint-disable no-nested-ternary */
/**
 * summary enhancement from chart v1:
 * 1. separate components into multiple files
 * 2. separate style config
 * 3. Add dynamic filter component
 * 4. collapsible grafik
 * TODO: update summary change
 */
import React, { useEffect, useState } from 'react';
import { CartesianGrid, Line, LineChart as LC, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import { Heading, Box, Accordion, AccordionItem, AccordionTrigger, AccordionContent, Text, Separator } from '@majoo-ui/react';
import PropTypes from 'prop-types';
import { foundations } from '@majoo-ui/core';
import { useTranslation } from 'react-i18next';
import { get } from 'lodash';
import { useMediaQuery } from '../../utils/useMediaQuery';

import ChartTitleLegend from './components/ChartTitleLegend';
import ChartTooltip from './components/ChartTooltip';
import Comparator from './components/Comparator';

import { formatThousandSeparator } from '../../utils/helper';

// import doodle from './data_kosong.png'; TODO: should remove this assets if not used?
import { compactCurrency, handleXaxis, roundDown, roundUp } from './helper';
import { isValidDate } from '../../utils/date.util';
import { COMPARISON_LIMIT } from './config';
import { lineChartStyle } from './styles';
import './customStyles.css';

const { colors } = foundations;

export const LineChartv2 = ({
    data,
    legendData,
    xAxisKey,
    customXAxisFormat,
    customYAxisFormat,
    title,
    onComparatorChange,
    comparatorList,
    placeholder,
    roundUpYAxis,
    period,
    chartTypeLabel,
    intervalXAxis,
    tickCountYAXis,
    isEmptyData, // TODO: review this
    disableComparatorClose,
    disableComparatorController,
    width,
    height,
    customTooltip,
    emptyDataMessage,
    children,
    isDateFormat = true,
    showSeparator = true,
    hideCollapsibleButton = false,
    fullWidth = false,
}) => {
    const [hover, setHover] = useState(null);
    const [filteredLegendData, setFilteredLegendData] = useState([]);
    const [isComparator, setIsComparator] = useState(false);
    const [maxData, setMaxData] = useState(0);
    const [isDividedByThousand, setIsDividedByThousand] = useState(false);
    const [isAccordionOpened, updateAccordionState] = useState(true);
    const [keyChart, setKeyChart] = useState(0);

    const { t, i18n } = useTranslation('translation');
    const lang = i18n.language;
    const isMobile = useMediaQuery('(max-width: 767px)');

    useEffect(() => {
        if (maxData > 0) {
            setIsDividedByThousand(String(Math.round(maxData)).length > 3);
        }
    }, [maxData]);

    useEffect(() => {
        setFilteredLegendData(
            legendData.map(value => ({
                ...value,
                hide: false,
                isCurrency: !!value.isCurrency,
            })),
        );
        if (legendData.length === 0) {
            setFilteredLegendData([
                {
                    dataKey: 'novalue',
                    name: 'novalue',
                    color: colors.chartYellow,
                    hide: true,
                },
            ]);
            setIsComparator(false);
        }
        if (legendData.some(l => l.isComparator === true)) {
            setIsComparator(true);
            setFilteredLegendData(legendData.map(value => ({ ...value, hide: false })).slice(0, COMPARISON_LIMIT));
        }
    }, [legendData]);

    useEffect(() => {
        // for trigger replay animation chart every data changed
        setKeyChart(prev => prev + 1);
    }, [JSON.stringify(data)]);
    
    const hasRightLegendData = !!filteredLegendData.find(x => get(x, 'yAxisId') && x.yAxisId === 'right');

    return (
        <React.Fragment>
            <Accordion
                defaultValue="item-1"
                type="single"
                onValueChange={() => { updateAccordionState(!isAccordionOpened); }}
            >
                <AccordionItem value="item-1">
                    {!hideCollapsibleButton && (
                        <AccordionTrigger arrowColor={isMobile ? colors.iconPrimary : colors.iconGreen} hasContent style={{ margin: '0 8px' }}>
                            <Box css={{ display: 'flex', flex: 1, justifyContent: 'space-between', alignItems: 'center' }}>
                                <Heading heading="sectionTitle" css={{ lineHeight: '24px' }}>
                                    {title}
                                </Heading>
                                {!isMobile && (
                                    <Text color="green">{isAccordionOpened ? t('label.hide', 'Sembunyikan') : t('label.unhide', 'Tampilkan')}</Text>
                                )}
                            </Box>
                        </AccordionTrigger>
                    )}
                    {hideCollapsibleButton && (
                        <Heading heading="sectionTitle" css={{ lineHeight: '24px', marginLeft: 8 }}>
                            {title}
                        </Heading>
                    )}
                    <AccordionContent>
                        <Box css={{ marginTop: '$spacing-06' }}>
                            <ChartTitleLegend
                                legendData={legendData}
                                filteredData={filteredLegendData}
                                setFilteredData={setFilteredLegendData}
                                isComparator={isComparator}
                                isDividedByThousand={isDividedByThousand}
                                hideLegend={data.length === 0}
                            />
                            {/* Dynamic Chart Filter
                                NOTES: if in future need more dynamic child make it as array of component
                                    example: [<ComponentFilter>, <AnotherComponent>, ...]
                            */}
                            {children}
                            <Box
                                css={lineChartStyle.wrapper}
                            >
                                <Box
                                    css={lineChartStyle.innerWrapper(data, width, height, legendData)}
                                    onScroll={(e) => {
                                        const axis = document.querySelector(".recharts-yAxis");
                                        axis.style = `transform: translateX(${e.target.scrollLeft}px);`;
                                   }}
                                >
                                    <ResponsiveContainer width={isMobile && !fullWidth ? 800 : '100%'} height="100%">
                                        <LC
                                            key={keyChart}
                                            data={legendData.length === 0 ? data.map(d => ({ ...d, novalue: 0 })) : data}
                                            margin={lineChartStyle.lineChart.margin}
                                        >
                                            <XAxis
                                                padding={lineChartStyle.lineChart.xAxis}
                                                width={2000}
                                                dataKey={xAxisKey}
                                                tickFormatter={dateValue => {
                                                    if (dateValue === 'auto') return 0;
                                                    if (customXAxisFormat) {
                                                        return customXAxisFormat(dateValue);
                                                    }

                                                    if (!isValidDate(dateValue)) return dateValue;

                                                    if (period) {
                                                        return handleXaxis(dateValue, period);
                                                    }

                                                    return dateValue;
                                                }}
                                                interval={intervalXAxis}
                                                tickLine={false}
                                                axisLine={false}
                                                hide={data.length === 0}
                                                dy={16}
                                            />
                                            <YAxis
                                                yAxisId="left"
                                                width={72}
                                                textAnchor="start"
                                                tickLine={false}
                                                axisLine={false}
                                                dy={-15}
                                                dx={-10}
                                                type="number"
                                                domain={
                                                    !isEmptyData
                                                        ? [
                                                            dataMin => (dataMin < 0 ? roundDown(dataMin) : 0),
                                                            dataMax => {
                                                                setMaxData(dataMax);
                                                                return roundUpYAxis ? roundUp(dataMax) : dataMax;
                                                            },
                                                        ]
                                                        : [0, 200]
                                                }
                                                tickCount={maxData === 0 && !isEmptyData ? 3 : tickCountYAXis}
                                                allowDecimals={roundUpYAxis}
                                                tickFormatter={val => (customYAxisFormat ? customYAxisFormat(val) : compactCurrency(val, lang))}
                                            />
                                            {hasRightLegendData && (
                                                <YAxis
                                                    yAxisId="right"
                                                    textAnchor="start"
                                                    orientation="right"
                                                    tickLine={false}
                                                    axisLine={false}
                                                    dy={-10}
                                                    dx={40}
                                                    type="number"
                                                    domain={['dataMin', 'auto']}
                                                    allowDecimals={roundUpYAxis}
                                                    tickCount={maxData === 0 ? 3 : tickCountYAXis}
                                                    tickFormatter={val =>
                                                        formatThousandSeparator(val, {
                                                            notation: 'compact',
                                                            compactDisplay: 'short',
                                                        })
                                                    }
                                                />
                                            )}
                                            <CartesianGrid vertical={false} x={8} width={1200} />
                                            <Tooltip
                                                cursor={{ stroke: '#404545', strokeWidth: 1, strokeDasharray: "3 3" }}
                                                content={
                                                    <ChartTooltip
                                                        hover={hover}
                                                        legendData={filteredLegendData}
                                                        maxValue={maxData}
                                                        period={period}
                                                        chartTypeLabel={chartTypeLabel}
                                                        dateKey={xAxisKey}
                                                        customTooltip={customTooltip}
                                                        isDateFormat={isDateFormat}
                                                    />
                                                }
                                            />
                                            {filteredLegendData.map(dataFiltered => (
                                                <Line
                                                    yAxisId={dataFiltered.yAxisId || 'left'}
                                                    key={dataFiltered.dataKey}
                                                    type="monotone"
                                                    dot={false}
                                                    dataKey={dataFiltered.dataKey}
                                                    stroke={dataFiltered.color}
                                                    hide={dataFiltered.hide}
                                                    strokeWidth={4}
                                                    onMouseOver={() => setHover(dataFiltered.dataKey)}
                                                    activeDot={
                                                        data.length === 1
                                                            ? {
                                                                onMouseOver: (_, b) => {
                                                                    const { cx, cy, fill } = b;
                                                                    setHover(dataFiltered.dataKey);
                                                                    return <circle cx={cx} cy={cy} r={10} fill={colors.white} stroke={fill} strokeWidth={3} />;
                                                                },
                                                                onMouseLeave: () => setHover(null),
                                                            }
                                                            : e => {
                                                                const { cx, cy, fill } = e;
                                                                return (
                                                                    e.dataKey === hover && (
                                                                        <circle cx={cx} cy={cy} r={10} fill={colors.white} stroke={fill} strokeWidth={3} />
                                                                    )
                                                                );
                                                            }
                                                    }
                                                    isAnimationActive
                                                />
                                            ))}
                                        </LC>
                                    </ResponsiveContainer>
                                </Box>
                            </Box>
                            {isComparator && (
                                <Comparator
                                    legendData={comparatorList}
                                    placeholder={placeholder}
                                    option={filteredLegendData}
                                    onCloseClick={x => onComparatorChange({ type: 'DELETE', payload: x })}
                                    onComparatorAdd={x => onComparatorChange({ type: 'ADD', payload: x })}
                                    onComparatorSearch={x => onComparatorChange({ type: 'SEARCH', payload: x })}
                                    disableClose={disableComparatorClose}
                                    disableController={disableComparatorController}
                                    emptyDataMessage={emptyDataMessage}
                                    t={t}
                                />
                            )}
                        </Box>
                    </AccordionContent>
                </AccordionItem>
            </Accordion>
            {showSeparator && (
                <Separator css={{ display: 'block', margin: '20px 0 20px' }} />
            )}
        </React.Fragment>
    );
};

LineChartv2.propTypes = {
    data: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
    legendData: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
    xAxisKey: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    customXAxisFormat: PropTypes.func,
    customYAxisFormat: PropTypes.func,
    onComparatorChange: PropTypes.func,
    comparatorList: PropTypes.arrayOf(PropTypes.shape({})),
    placeholder: PropTypes.string,
    roundUpYAxis: PropTypes.bool,
    period: PropTypes.string,
    chartTypeLabel: PropTypes.string,
    intervalXAxis: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    tickCountYAXis: PropTypes.number,
    isEmptyData: PropTypes.bool,
    disableComparatorClose: PropTypes.bool,
    disableComparatorController: PropTypes.bool,
    width: PropTypes.number,
    height: PropTypes.number,
    customTooltip: PropTypes.elementType,
    emptyDataMessage: PropTypes.string,
    children: PropTypes.oneOfType([
        PropTypes.arrayOf(PropTypes.node), PropTypes.node
    ]),
    isDateFormat: PropTypes.bool,
    showSeparator: PropTypes.bool,
    hideCollapsibleButton: PropTypes.bool,
    fullWidth: PropTypes.bool,
};

LineChartv2.defaultProps = {
    customXAxisFormat: undefined,
    customYAxisFormat: undefined,
    onComparatorChange: undefined,
    comparatorList: [],
    placeholder: '',
    roundUpYAxis: true,
    period: undefined,
    chartTypeLabel: undefined,
    intervalXAxis: 'preserveStartEnd',
    tickCountYAXis: 5,
    isEmptyData: false,
    disableComparatorClose: false,
    disableComparatorController: false,
    width: undefined,
    height: undefined,
    customTooltip: undefined,
    emptyDataMessage: undefined,
    children: undefined,
    isDateFormat: true,
    showSeparator: true,
    hideCollapsibleButton: false,
    fullWidth: false,
};
