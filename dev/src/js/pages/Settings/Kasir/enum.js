export const CASHIER_TYPE = {
    TABLE: '1',
    VOID: '2',
    REFUND: '3',
    COMPLIMENT: '4',
};

export const AUTH_PROPS = {
    PARENT: 'value',
    CHILDREN: 'children',
};

export const PAYMENT_TYPE_KEY = { 
    CASH: 'Tunai',
    NON_CASH: 'Non Tunai',
    TRANSFER: 'Transfer',
    QRIS_MAJOO: 'Qris majoo',
    COMPLIMENT: 'Compliment',
    DEPOSIT: 'Deposit'
}

export const PAYMENT_TYPE_IDS = { 
    [PAYMENT_TYPE_KEY.CASH]: [1],
    [PAYMENT_TYPE_KEY.NON_CASH]: [2,8,14,18],
    [PAYMENT_TYPE_KEY.TRANSFER]: [4],
    [PAYMENT_TYPE_KEY.QRIS_MAJOO]: [6],
    [PAYMENT_TYPE_KEY.COMPLIMENT]: [5],
    [PAYMENT_TYPE_KEY.DEPOSIT]: [9],
}

export const PAYMENT_TYPE_CHECKBOX = t => [
    {
        value: PAYMENT_TYPE_KEY.CASH,
        label: t('dialog.infoSection.paymentMethod.cash', 'Tunai')
    },
    {
        value: PAYMENT_TYPE_KEY.NON_CASH,
        label: t('dialog.infoSection.paymentMethod.nonCash', 'Nontunai')
    },
    {
        value: PAYMENT_TYPE_KEY.TRANSFER,
        label: t('dialog.infoSection.paymentMethod.transfer', 'Transfer')
    },
    {
        value: PAYMENT_TYPE_KEY.QRIS_MAJOO,
        label: t('dialog.infoSection.paymentMethod.qrisMajoo', 'QRIS Majoo')
    },
    {
        value: PAYMENT_TYPE_KEY.COMPLIMENT,
        label: t('dialog.infoSection.paymentMethod.compliment', 'Komplimen')
    },
    {
        value: PAYMENT_TYPE_KEY.DEPOSIT,
        label: t('dialog.infoSection.paymentMethod.deposit', 'Deposit')
    }
]

export const TRANSACTION_TYPE = t => [
    {
        value: 1,
        label: t('dialog.infoSection.moneyIn', 'Uang Masuk')
    },
    {
        value: 2,
        label: t('dialog.infoSection.moneyOut', 'Uang Keluar')
    }
]