export const styleFormLabel = {
  '& > span': {
    color: '$textPrimary',
    fontSize: '12px',
    fontWeight: 600,
    lineHeight: '$section-sub-title',
    letterSpacings: '$section-sub-title',
  },
};

export const STATUS_TYPE = {
  APPROVE: 8,
  REJECTED: 112,
  ON_PROCESS: 5,
};

export const OWNER_APP_DEEPLINK_APPSTORE = 'https://apps.apple.com/us/app/majoo-owner/id6446137792';
export const OWNER_APP_DEEPLINK_PLAYSTORE = 'https://play.google.com/store/apps/details?id=id.majoo.owner';

export const dataURItoBlob = (dataURI) => {
  // convert base64/URLEncoded data component to raw binary data held in a string
  let byteString;
  if (dataURI.split(',')[0].indexOf('base64') >= 0) {
    byteString = atob(dataURI.split(',')[1]);
  } else {
    byteString = unescape(dataURI.split(',')[1]);
  }

  // separate out the mime component
  const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];

  // write the bytes of the string to a typed array
  const ia = new Uint8Array(byteString.length);
  for (let i = 0; i < byteString.length; i += 1) {
    ia[i] = byteString.charCodeAt(i);
  }

  return new Blob([ia], { type: mimeString });
};
