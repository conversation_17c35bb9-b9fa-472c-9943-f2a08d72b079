import React, { useState } from 'react';
import { AlertDialog, Paragraph, InputRadioGroup, InputRadio, InputSelect, FormHelper, Text, Flex, InputSwitch, InputSelectCheck, InputSelectTag } from '@majoo-ui/react';
import _ from 'lodash';
import { useMediaQuery } from '../../../../../utils/useMediaQuery';
import { integratedBanks } from '../constants';
import { Trans } from 'react-i18next';

export const ConfirmCancel = ({
    open, onOpenChange, onConfirm, isEditMode, t,
}) => {
    const isMobile = useMediaQuery('(max-width: 767px)');
    return (
        <AlertDialog
            onCancel={() => {
                onOpenChange(false);
            }}
            isMobile={isMobile}
            onConfirm={onConfirm}
            open={open}
            description={isEditMode ? (
                <Paragraph>
                   <Trans t={t} i18nKey="modal.cancel.descEdit" />
                </Paragraph>
            ) : (
                <Paragraph>
                   <Trans t={t} i18nKey="modal.cancel.descAdd" />
                </Paragraph>
            )}
            title={isEditMode ? t('modal.cancel.titleEdit') : t('modal.cancel.titleAdd')}
            labelConfirm={t('label.continue', { ns: 'translation' })}
            labelCancel={t('label.cancel', { ns: 'translation' })}
            dialogType="negative"
            css={{
                width: isMobile ? 'unset' : '422px',
            }}
        />
    );
};

export const ConfirmAdd = ({
    open, onOpenChange, onConfirm, isEditMode, t,
}) => {
    const isMobile = useMediaQuery('(max-width: 767px)');
    return (
        <AlertDialog
            onCancel={() => {
                onOpenChange(false);
            }}
            isMobile={isMobile}
            onConfirm={onConfirm}
            open={open}
            description={isEditMode ? (
                <Paragraph>
                   <Trans t={t} i18nKey="modal.confirm.descEdit" />
                </Paragraph>
            ) : (
                <Paragraph>
                   <Trans t={t} i18nKey="modal.confirm.descAdd" />
                </Paragraph>
            )}
            title={isEditMode ? t('modal.confirm.titleEdit') : t('modal.confirm.titleAdd')}
            labelConfirm={t('label.continue', { ns: 'translation' })}
            labelCancel={t('label.cancel', { ns: 'translation' })}
            dialogType="primary"
            css={{
                width: isMobile ? 'unset' : '422px',
            }}
        />
    );
};

export const ConfirmDelete = ({
    open, onOpenChange, onConfirm, detail, t,
}) => {
    const isMobile = useMediaQuery('(max-width: 767px)');
    return (
        <AlertDialog
            onCancel={() => {
                onOpenChange(false);
            }}
            isMobile={isMobile}
            onConfirm={onConfirm}
            open={open}
            description={(
                <Trans t={t} i18nKey="modal.delete.desc">
                    {{ name: _.get(detail, 'bank_name') }}
                </Trans>
            )}
            title={t('modal.delete.title')}
            labelConfirm={t('label.continue', { ns: 'translation' })}
            labelCancel={t('label.cancel', { ns: 'translation' })}
            dialogType="negative"
            css={{
                width: isMobile ? 'unset' : '422px',
            }}
        />
    );
};

export const AccountOptionModal = ({
    open, onOpenChange, onConfirm, accOption, setAccOption, selectedBank, setSelectedBank, t,
}) => {
    const isMobile = useMediaQuery('(max-width: 767px)');
    const [bankError, setBankError] = useState('');

    return (
        <AlertDialog
            singleButton
            onCancel={() => {
                onOpenChange(false);
                setAccOption(null);
                setBankError('');
            }}
            open={open}
            isMobile={isMobile}
            disabledButton={!accOption || bankError}
            onConfirm={!selectedBank && accOption === '2' ? () => setBankError(t('option.error')) : onConfirm}
            description={(
                <InputRadioGroup
                    gap={5}
                    outlined
                    direction="column"
                    css={{ '& > label': { display: 'contents' } }}
                    onValueChange={val => {
                        setAccOption(val);
                        if (val === '1') {
                            setBankError('')
                            setSelectedBank('');
                        }
                    }}
                >
                    <InputRadio
                        outlined
                        id="1"
                        value="1"
                        direction="column"
                        inputType="default"
                        css={{ '& > div': { alignItems: 'baseline' }, padding: '20px $compact $compact 20px', ...accOption === '1' && { borderColor: '#04C99E' } }}
                        label={(
                            <Flex direction="column" css={{ gap: 6 }}>
                                <Text variant="contentButton">
                                    {t('option.registered')}
                                </Text>
                                <Text variant="caption">
                                    {t('option.detail')}
                                </Text>
                            </Flex>
                        )}
                    />
                    <InputRadio
                        outlined
                        id="2"
                        value="2"
                        direction="column"
                        inputType="default"
                        css={{ '& > div': { alignItems: 'baseline' }, padding: '20px $compact $compact 20px', ...accOption === '2' && { borderColor: '#04C99E' } }}
                        label={(
                            <Flex direction="column" css={{ gap: 6 }}>
                                <Text variant="contentButton">
                                    {t('option.new')}
                                </Text>
                                <Text variant="caption">
                                    {t('option.newDesc')}
                                </Text>
                                <InputSelect
                                    size="sm"
                                    id="new-bank-account"
                                    placeholder={t('option.placeholder')}
                                    isInvalid={false}
                                    option={integratedBanks}
                                    value={integratedBanks.find(i => i.value === selectedBank.value)}
                                    onChange={(value) => {
                                        setSelectedBank(value);
                                        setBankError('');
                                    }}
                                    css={{ marginTop: '2px' }}
                                    disabled={accOption !== '2'}
                                />
                                {bankError && (
                                    <FormHelper error>
                                        {t('option.error')}
                                    </FormHelper>
                                )}
                            </Flex>
                        )}
                    />
                </InputRadioGroup>
            )}
            title={t('option.title')}
            labelConfirm={t('label.continue2', { ns: 'translation' })}
            dialogType="primary"
            css={{
                width: isMobile ? 'unset' : '422px',
            }}
        />
    );
};

export const VerificationFailed = ({
    open, onOpenChange, onConfirm, t,
}) => {
    const isMobile = useMediaQuery('(max-width: 767px)');
    return (
        <AlertDialog
            singleButton
            onCancel={() => {
                onOpenChange(false);
            }}
            isMobile={isMobile}
            onConfirm={onConfirm}
            open={open}
            description={(
                <Paragraph>
                   {t('modal.failed.desc')}
                    <ul>
                        <li>{t('modal.failed.li1')}</li>
                        <li>{t('modal.failed.li2')}</li>
                        <li>{t('modal.failed.li3')}</li>
                    </ul>
                </Paragraph>
            )}
            title={t('modal.failed.title')}
            labelConfirm={t('modal.failed.agree')}
            dialogType="primary"
            css={{
                width: isMobile ? 'unset' : '422px',
            }}
        />
    );
};

export const VerificationInformation = ({
    open, onOpenChange, onConfirm, t,
}) => {
    const isMobile = useMediaQuery('(max-width: 767px)');
    return (
        <AlertDialog
            singleButton
            onCancel={() => {
                onOpenChange(false);
            }}
            isMobile={isMobile}
            onConfirm={onConfirm}
            open={open}
            description={(
                <Paragraph>
                   {t('modal.info.desc')}
                    <ol>
                        <li>{t('modal.info.li1')}</li>
                        <li>{t('modal.info.li2')}</li>
                        <li>{t('modal.info.li3')}</li>
                    </ol>
                    {t('modal.info.desc2')}
                </Paragraph>
            )}
            title={t('modal.info.title')}
            labelConfirm={t('modal.info.agree')}
            dialogType="primary"
            css={{
                width: isMobile ? 'unset' : '422px',
            }}
        />
    );
};

export const ConfirmAddNewAcc = ({ open, onCancel, onConfirm, onClose, t }) => {
    const isMobile = useMediaQuery('(max-width: 767px)');
    return (
        <AlertDialog
            open={open}
            isMobile={isMobile}
            onConfirm={onConfirm}
            onCancel={onCancel}
            onClose={onClose}
            description={(
                <Paragraph>
                    {t('new.subTitle')}
                    <ul style={{ marginTop: '8px' }}>
                        <li><Trans t={t} i18nKey="new.li1" /></li>
                        <li><Trans t={t} i18nKey="new.li2" /></li>
                    </ul>
                </Paragraph>
            )}
            title={t('new.title')}
            actionButtonProps={{ css: { flex: isMobile && 2 } }}
            labelConfirm={t('new.confirm')}
            labelCancel={t('new.cancel')}
            dialogType="primary"
            css={{
                width: isMobile ? 'unset' : '422px',
            }}
        />
    );
};

export const AlreadyIntegratedModal = ({ open, onCancel, onConfirm, t }) => {
    const isMobile = useMediaQuery('(max-width: 767px)');
    return (
        <AlertDialog
            singleButton
            open={open}
            isMobile={isMobile}
            onConfirm={onConfirm}
            onCancel={onCancel}
            description={(
                <Paragraph>
                   {t('modal.hasIntegerated.desc')}
                </Paragraph>
            )}
            title={t('modal.hasIntegerated.title')}
            labelConfirm={t('modal.hasIntegerated.agree')}
            dialogType="primary"
            css={{
                width: isMobile ? 'unset' : '422px',
            }}
        />
    );
};

export const AddSettlementAccountModal = ({
    open, onOpenChange, onConfirm, selectedAccountOption, setSelectedAccountOption, 
    selectedOutletOptions, setSelectedOutletOptions, selectedMarketplaceOptions, 
    setSelectedMarketplaceOptions, outletOptions, marketplaceOptions, 
    accountOptions, fetchData, fetchOutlet, isLoading, 
    t, isIntegratedWebstore, isIntegratedWallet, setIsIntegratedWebstore, setIsIntegratedWallet
}) => {
    const isMobile = useMediaQuery('(max-width: 767px)');

    return (
        <AlertDialog
            singleButton
            onCancel={() => {
                onOpenChange(false);
                setSelectedAccountOption('');
                setSelectedOutletOptions([]);
                setSelectedMarketplaceOptions([]);
                setIsIntegratedWebstore(false);
                setIsIntegratedWallet(false);
            }}
            open={open}
            isMobile={isMobile}
            disabledButton={selectedAccountOption === '' && selectedOutletOptions === ''}
            onConfirm={!selectedOutletOptions && selectedAccountOption === '2' ? () => setBankError(t('option.error')) : onConfirm}
            description={(
                <Flex
                    direction="column"
                    gap={5}
                    style={{
                        maxHeight: 450,
                        overflowY: 'auto',
                    }}
                >
                    <Flex
                        outlined
                        direction="column"
                        gap={3}
                        style={{
                            border: '1px solid #DADDDD',
                            padding: '16px',
                            borderRadius: '8px',
                        }}
                    >
                        <Text variant="contentButton">
                            {t('settlement.account')}
                        </Text>
                        <Text variant="caption">
                            {t('settlement.accountDesc')}
                        </Text>
                        <InputSelect
                            size="sm"
                            id="bank-account"
                            placeholder={t('settlement.accountPlaceholder')}
                            isInvalid={false}
                            option={accountOptions}
                            value={selectedAccountOption}
                            fetchData={fetchData}
                            onChange={(val) => {
                                setSelectedAccountOption(val);
                                fetchOutlet(val.value);
                                setSelectedOutletOptions([]);
                                setSelectedMarketplaceOptions([]);
                            }}
                            css={{ marginTop: '2px', width: 280, '@md': { width: '$full' } }}
                            isLoading={isLoading}
                        />
                    </Flex>
                    {isIntegratedWallet && (
                        <Flex
                            outlined
                            direction="column"
                            gap={3}
                            style={{
                                border: '1px solid #DADDDD',
                                padding: '16px',
                                borderRadius: '8px',
                            }}
                        >
                            <Text variant="contentButton">
                                {t('settlement.outlet')}
                            </Text>
                            <Text variant="caption">
                                {t('settlement.outletDesc')}
                            </Text>
                            <InputSelectTag
                                size="sm"
                                id="settlement-outlet"
                                placeholder={t('settlement.outletPlaceholder')}
                                onChange={(value) => {
                                    setSelectedOutletOptions(value);
                                }}
                                option={outletOptions}
                                showSelectAll={false}
                                css={{ marginTop: '2px', width: 280, '@md': { width: '$full' } }}
                                value={selectedOutletOptions}
                                placement="top"
                            />

                        </Flex>
                    )}
                    {isIntegratedWebstore && (
                        <Flex
                            outlined
                            direction="column"
                            gap={3}
                            style={{
                                border: '1px solid #DADDDD',
                                padding: '16px',
                                borderRadius: '8px',
                            }}
                        >
                            <Text variant="contentButton">
                                {t('settlement.activate')}
                            </Text>
                            <Text variant="caption">
                                {t('settlement.activateDesc')}
                            </Text>
                            <InputSelectTag
                                size="sm"
                                id="settlement-outlet"
                                placeholder={t('settlement.outletPlaceholder')}
                                onChange={(value) => {
                                    setSelectedMarketplaceOptions(value);
                                }}
                                option={marketplaceOptions}
                                showSelectAll={false}
                                css={{ marginTop: '2px', width: 280, '@md': { width: '$full' } }}
                                value={selectedMarketplaceOptions}
                                placement="top"
                            />
                        </Flex>
                    )}
                </Flex>
            )}
            title={t('settlement.title')}
            labelConfirm={t('label.save', { ns: 'translation' })}
            dialogType="primary"
            css={{
                width: isMobile ? 'unset' : '422px',
            }}
        />
    );
};