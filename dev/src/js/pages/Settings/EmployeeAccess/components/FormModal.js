import React, {
    useState, useCallback, useMemo, useContext, useEffect, useRef,
} from 'react';
import {
    PageDialog,
    PageDialogTitle,
    PageDialogContent,
    PageDialogFooter,
    DialogClose,
    Button,
    Paper,
    Box,
    Flex,
    FormGroup,
    FormLabel,
    Upload,
    InputText,
    FormHelper,
    Separator,
    InputNumber,
    InputSelect,
    InputGroup,
    InputRightElement,
    Paragraph,
    InputSwitch,
    Tooltip,
} from '@majoo-ui/react';
import {
    EyeSlashOutline,
    EyeOutline,
    RemoveOutline,
    CircleInfoOutline,
} from '@majoo-ui/icons';
import { foundations } from '@majoo-ui/core';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { useMediaQuery } from '../../../../utils/useMediaQuery';
import {
    confirmationModalType,
    errorMessage,
    modeEnum,
    roleEnum,
    DEFAULT_ROLES,
} from '../settings/constant';
import { getIdUser } from '../../../../utils/passport';
import userUtils from '../../../../utils/user.util';
import { EmployeeAccessContext } from '../context/EmployeeAccessContext';
import ImagePreviewModal from './ImagePreviewModal';
import VerificationContent from './VerificationContent';
import ChangePIN from './ChangePIN';
import ModalMultiOutlet from './ModalMultiOutlet';
import OutletTag from './OutletTag';
import {
    editEmployee,
    fetchEmployeeDetail,
    fetchEmployeeNIK,
    fetchOutlet,
    fetchRole,
    postEmployee,
    removeEmployee,
    uploadImage,
} from '../utils/api';
import {
 CoachMarkEmployee, employeeSteps, ACTIONS, CoachMarkFragment,
} from './CoachMark';
import { updateOnboardingTask } from '../../../../services/session';
import { isStarterSubscription } from '../../../../v2-utils';

const FormModal = ({
    open,
    onOpenChange,
    mode,
    detailForm,
    handleFetchTable,
}) => {
    const contextData = useContext(EmployeeAccessContext);
    const isMobile = useMediaQuery('(max-width: 1024px)');
    const detailFormRaw = useRef({});
    const [showPassword, setShowPassword] = useState(false);
    const [outletList, setOutletList] = useState([]);
    const [roleList, setRoleList] = useState([]);
    const [rolesNeedVerification, setRolesNeedVerification] = useState([]);
    const [rolesAllowedPinToMaintained, setRolesAllowedPinToMaintained] = useState([]);
    const [rolesAllowedRoleToMaintained, setRolesAllowedRoleToMaintained] = useState([]);
    const [isShowModalMOutlet, setIsShowModalMOutlet] = useState(false);
    const [isEditable, setIsEditable] = useState(true);
    const imagePreviewRef = useRef(null);
    const [previewImage, setPreviewImage] = useState('');
    const [titlePreviewImage, setTitlePreviewImage] = useState('');
    const [tempNIK, setTempNIK] = useState('');
    const verificationRef = useRef();
    const changePINRef = useRef();
    const [isNeedVerification, setIsNeedVerification] = useState(false);
    const [isMultipleOutlet, setIsMultipleOutlet] = useState(false);
    const [disableMultiOutlet, setDisableMultiOutlet] = useState(false);
    const { t, idCabang } = contextData;
    const isStarter = isStarterSubscription();

    const schema = yup.object({
        nama: yup.string().required(t('f.name.error')),
        nik: yup.string().required(t('f.id.error')),
        hakAkses: yup.string().required(t('f.access.error')),
        pin: yup.string().min(6, t('f.pinError')).max(6, t('f.pinError')),
        email: yup.string().email(t('f.email.format')),
        ...(!disableMultiOutlet) && {
            idCabang: yup.array().min(1, t('f.outletError')),
        },
        ...(isNeedVerification) && {
            email: yup.string().email(t('f.email.format')).required(t('f.email.required')),
        },
    });

    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
        getValues,
        watch,
        reset,
        control,
        setError,
        clearErrors,
    } = useForm({
        resolver: yupResolver(schema),
    });

    const setOutletValue = useCallback(val => outletList.find(data => Number(data.value) === Number(val)), [watch('idCabang'), outletList]);
    const setRoleValue = useCallback(val => roleList.find(data => String(data.value) === String(val)), [watch('hakAkses'), roleList]);

    const modalTitle = useMemo(() => {
        let prefix = '';
        switch (mode) {
            case modeEnum.ADD:
                prefix = 'add';
                break;
            case modeEnum.EDIT:
                prefix = 'edit';
                break;
            default:
                break;
        }
        return t(`f.${prefix}`);
    }, [mode]);

    const handlePhotoPreview = (file) => {
        imagePreviewRef.current.click();
        setPreviewImage(file.url);
        setTitlePreviewImage(file.name);
    };

    const showModalMOutletHandler = (event) => {
        event.preventDefault();
        setIsShowModalMOutlet(true);
    };

    const onRemoveSelectedOutlet = (item) => {
        const list = getValues('idCabang');
        const payload = list && list.length > 0 && list.filter(x => x.id !== item.id);

        setValue('idCabang', payload);
    };

    const verification = () => {
        const onSuccessfully = () => {
            contextData.handleCloseModal();
            setValue('isActivated', 1);
            handleFetchTable();
        };
        verificationRef.current.handleVerification(onSuccessfully);
    };

    const handleVerificationModal = () => {
        const role = setRoleValue(detailFormRaw.current.hakAkses);
        contextData.handleOpenModal({
            type: 'primary',
            title: t('v.mTitle'),
            description: <VerificationContent ref={verificationRef} payload={{ ...detailFormRaw.current, roleName: role.name }} />,
            buttons: {
                confirmButton: {
                    label: t('v.confirm'),
                    action: () => verification(),
                },
                cancelButton: {
                    label: t('v.pending'),
                    action: contextData.handleCloseModal,
                },
            },
        });
    };

    const changePIN = () => {
        changePINRef.current.handleChangePIN();
    };

    const handlePinModal = () => {
        const refetch = () => {
            contextData.handleCloseModal();
        };

        contextData.handleOpenModal({
            type: 'primary',
            title: t('pin.mTitle'),
            description: <ChangePIN ref={changePINRef} payload={detailFormRaw.current} refetch={refetch} />,
            buttons: {
                confirmButton: {
                    label: t('label.save', { ns: 'translation' }),
                    action: () => changePIN(),
                },
                cancelButton: {
                    label: t('label.cancel', { ns: 'translation' }),
                    action: contextData.handleCloseModal,
                },
            },
        });
    };

    const handleFetchOutlet = async () => {
        const onSuccessfully = (res) => {
            const remap = res.map(item => ({
                name: item.cabang_name,
                value: item.id_cabang,
                id: item.id_cabang,
                id_outlet: item.id_cabang,
            })).sort((a, b) => a.name.localeCompare(b.name));
            setOutletList(remap);
        };
        await fetchOutlet(contextData, onSuccessfully);
    };

    const validateImageSize = (file, error) => {
        const MAX_FILE_SIZE = 5;
        const fileSize = (file.size / 1024 / 1024).toFixed(1);

        if (error) return Promise.reject((new Error(error)));
        if (fileSize > MAX_FILE_SIZE) return Promise.reject((new Error(t('maxUpload'))));

        return Promise.resolve();
    };

    const getUserLoginPermission = () => String(userUtils.getLocalConfigByKey('permissionId'));

    const getUserLoginId = () => String(getIdUser());

    const filterRolesNeedVerification = roles => roles
        .filter(role => (
            (role.is_disabled === 1 && [roleEnum.ADMIN, roleEnum.MANAGER, roleEnum.WAREHOUSE].includes(String(role.permission_id)))
            || role.is_disabled !== 1
        ))
        .map(role => role.id);

    const filterRolesWithPin = (roles) => {
        const filteredRole = roles.filter((role) => {
            // first condition to filter available roles who can perform pin update
            if (
                (role.is_disabled === 1 && [roleEnum.ADMIN, roleEnum.MANAGER, roleEnum.CASHIER, roleEnum.WAITERS, roleEnum.KITCHEN].includes(String(role.permission_id)))
                || (role.is_disabled !== 1 && role.is_allowed_login_mobile === 1)
            ) {
                const permissionId = getUserLoginPermission();
                // second condition to filter all available roles which the pin is eligible to update
                if (String(permissionId) === roleEnum.ADMIN
                    || (String(permissionId) === roleEnum.MANAGER && String(role.permission_id) !== String(permissionId))
                ) {
                    return true;
                }
            }

            return false;
        });

        return filteredRole.map(role => role.id);
    };

    const filterRolesAllowedByUserLoggedIn = (roles) => {
        const permissionId = getUserLoginPermission();
        const idUser = getIdUser();
        const merchantId = userUtils.getLocalConfigByKey('parentId');
        if (permissionId === roleEnum.ADMIN) {
            if (!merchantId || String(idUser) === String(merchantId)) return roles.map(role => role.id);
            return roles.filter(role => role.is_majoo_owner === '0').map(role => role.id);
        }
        return roles.filter(role => (![roleEnum.ADMIN].includes(String(role.permission_id)) && role.is_majoo_owner === '0')).map(role => role.id);
    };

    const handleFetchRole = async () => {
        const onSuccessfully = (roles) => {
            const needVerification = filterRolesNeedVerification(roles);
            const allowedPinToMaintained = filterRolesWithPin(roles);
            const allowedRoleToMaintained = filterRolesAllowedByUserLoggedIn(roles);
            let remap = roles.map(item => ({ ...item, name: item.name, value: item.id })).sort((a, b) => a.name.localeCompare(b.name));

            // filter master roles to gain access restriction
            if (mode === modeEnum.ADD || mode === modeEnum.EDIT) {
                remap = remap.filter(role => allowedRoleToMaintained.includes(role.value));
            }

            setRoleList(remap);
            setRolesNeedVerification(needVerification);
            setRolesAllowedPinToMaintained(allowedPinToMaintained);
            setRolesAllowedRoleToMaintained(allowedRoleToMaintained);
        };
        await fetchRole(contextData, onSuccessfully);
    };

    const handleRemove = async () => {
        const onSuccessfully = () => {
            contextData.addToast({
                title: t('toast.success', { ns: 'translation' }),
                description: (
                    <Box css={{ maxWidth: 268 }}><Trans t={t} i18nKey="t.deleteSuccess" values={{ name: detailFormRaw.current.nama }} /></Box>
                ),
                variant: 'success',
            });
            onOpenChange(false);
            contextData.handleCloseModal();
            handleFetchTable();
        };
        const { id, outlet_default_id }  = detailForm
        await removeEmployee(contextData, onSuccessfully, { id, outlet_default_id });
    };

    const handleUploadImage = async (file, callback) => {
        const onSuccessfully = (filePath) => {
            if (callback) callback(filePath);
        };
        await uploadImage(contextData, onSuccessfully, file);
    };

    const onSubmit = async (formData) => {
        const {
            id, nik, nama, email, pin, telepon, posisi, outlet, hakAkses, fotoEmployee, status, idCabang,
        } = formData;

        let formatedOutlets;
        if (isMultipleOutlet) {
            formatedOutlets = idCabang && idCabang.length ? idCabang.map(x => ({ id_outlet: x.id_outlet, is_default: x.is_default })) : [{ id_outlet: '', is_default: '' }];
        } else {
            formatedOutlets = idCabang && idCabang.length ? [{ id_outlet: idCabang[0].id_outlet, is_default: true }] : [{ id_outlet: '', is_default: '' }];
        }

        let payload = {
            id_employee: id || '',
            employee_nik: nik || tempNIK,
            employee_name: nama,
            employee_email: email,
            employee_notlp: telepon,
            employee_posisi: posisi,
            id_outlet: outlet,
            status: status ? 1 : 0,
            id_permission: hakAkses,
            employee_foto_path: fotoEmployee && fotoEmployee.length > 0 ? fotoEmployee[0].url : '',
            employee_pin: pin || '123456',
            id_cabang: formatedOutlets,
            outlets: formatedOutlets,
        };

        contextData.handleCloseModal();
        contextData.showProgress();

        if (fotoEmployee && fotoEmployee.length > 0 && !!fotoEmployee[0].uid) {
            await handleUploadImage(fotoEmployee[0], (filePath) => {
                payload = { ...payload, employee_foto_path: filePath };
            });
        }

        const onFailed = (res) => {
            if (res === errorMessage.NAMA) setError('nama', { type: 'name already exist', message: `*${res}` });
            else if (res === errorMessage.EMAIL) setError('email', { type: 'email already exist', message: `*${res}` });
            else if (res === errorMessage.NIK) setError('nik', { type: 'nik already exist', message: `*${res}` });
        };

        switch (mode) {
            case modeEnum.ADD: {
                const onSuccessfully = async (data) => {
                    detailFormRaw.current = { ...getValues(), id: data.id_employee };
                    // logic ada di API untuk menentukan 0 dan 1: misal ketika usernya admin, manager, dan warehouse, atau custom
                    if ('user_is_active' in data && parseInt(data.user_is_active, 10) === 0) {
                        handleVerificationModal();
                    }
                    contextData.addToast({
                        title: t('toast.success', { ns: 'translation' }),
                        description: (
                            <Box css={{ maxWidth: 268 }}>
                                <Trans t={t} i18nKey="t.addSuccess" values={{ name: nama }} />
                            </Box>
                        ),
                        variant: 'success',
                    });
                    onOpenChange(false);
                    await handleFetchTable();
                };
                await postEmployee(contextData, onSuccessfully, onFailed, payload);
                contextData.hideProgress();
                break;
            }
            case modeEnum.EDIT: {
                const onSuccessfully = async (data) => {
                    detailFormRaw.current = getValues();
                    if ('user_is_active' in data && parseInt(data.user_is_active, 10) === 0) {
                        handleVerificationModal();
                    }
                    contextData.addToast({
                        title: t('toast.success', { ns: 'translation' }),
                        description: (
                            <Box css={{ maxWidth: 268 }}>
                                <Trans t={t} i18nKey="t.editSuccess" values={{ name: nama }} />
                            </Box>
                        ),
                        variant: 'success',
                    });
                    onOpenChange(false);
                    await handleFetchTable();
                };
                await editEmployee(contextData, onSuccessfully, onFailed, payload);
                contextData.hideProgress();
                break;
            }
            default: break;
        }
    };

    const handleConfirmationModal = (type) => {
        const modeForm = mode === modeEnum.ADD ? t('mode.add') : t('mode.edit');
        const isAdd = mode === modeEnum.ADD;
        switch (type) {
            case confirmationModalType.CLOSE:
                contextData.handleOpenModal({
                    type: 'negative',
                    title: isAdd ? t('m.cancelAdd') : t('m.cancelEdit'),
                    description: (
                        <Paragraph
                            paragraph="longContentRegular"
                            color="primary"
                            css={{ overflowWrap: 'break-word' }}
                        >
                            {isAdd ? (
                                <Trans t={t} i18nKey="m.descCancelAdd" />
                            ) : (

                                    <Trans t={t} i18nKey="m.descCancelEdit" />
                                )}
                        </Paragraph>
                    ),
                    buttons: {
                        confirmButton: {
                            label: t('label.continue', { ns: 'translation' }),
                            action: () => {
                                onOpenChange(false);
                                contextData.handleCloseModal();
                            },
                        },
                        cancelButton: {
                            label: t('label.back', { ns: 'translation' }),
                            action: contextData.handleCloseModal,
                        },
                    },
                });
                break;
            case confirmationModalType.DELETE:
                contextData.handleOpenModal({
                    type: 'negative',
                    title: t('m.deleteTitle'),
                    description: (
                        <Paragraph
                            paragraph="longContentRegular"
                            color="primary"
                            css={{ overflowWrap: 'break-word' }}
                        >
                            <Trans t={t} i18nKey="m.deleteDesc" values={{ permission: detailFormRaw.current.permission_name, name: detailFormRaw.current.nama }} />
                        </Paragraph>
                    ),
                    buttons: {
                        confirmButton: {
                            label: t('label.continue', { ns: 'translation' }),
                            action: () => handleRemove(),
                        },
                        cancelButton: {
                            label: t('label.cancel', { ns: 'translation' }),
                            action: contextData.handleCloseModal,
                        },
                    },
                });
                break;
            case confirmationModalType.SELESAI:
                switch (mode) {
                    case modeEnum.ADD:
                        contextData.handleOpenModal({
                            type: 'primary',
                            title: t('m.saveTitle'),
                            description: (
                                <Paragraph
                                    paragraph="longContentRegular"
                                    color="primary"
                                    css={{ overflowWrap: 'break-word' }}
                                >
                                    <Trans t={t} i18nKey="m.saveAdd" values={{ name: getValues('nama') }} />
                                </Paragraph>
                            ),
                            buttons: {
                                confirmButton: {
                                    label: t('label.continue', { ns: 'translation' }),
                                    action: () => onSubmit(getValues()),
                                },
                                cancelButton: {
                                    label: t('label.cancel', { ns: 'translation' }),
                                    action: contextData.handleCloseModal,
                                },
                            },
                        });
                        break;
                    case modeEnum.EDIT:
                        contextData.handleOpenModal({
                            type: 'primary',
                            title: t('m.editTitle'),
                            description: (
                                <Paragraph
                                    paragraph="longContentRegular"
                                    color="primary"
                                    css={{ overflowWrap: 'break-word' }}
                                >
                                    <Trans t={t} i18nKey="m.saveEdit" values={{ name: getValues('nama') }} />
                                </Paragraph>
                            ),
                            buttons: {
                                confirmButton: {
                                    label: t('label.continue', { ns: 'translation' }),
                                    action: () => onSubmit(getValues()),
                                },
                                cancelButton: {
                                    label: t('label.cancel', { ns: 'translation' }),
                                    action: contextData.handleCloseModal,
                                },
                            },
                        });
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
    };

    const handleFetchDetail = async () => {
        await fetchEmployeeDetail(contextData, ({ detail }) => {
            const { idPermission } = contextData;
            const isAllowedToEdit = true;
            const reasonForNotAllowed = '';

            if (!isAllowedToEdit) {
                onOpenChange(false);
                contextData.addToast({
                    title: t('toast.error', { ns: 'translation' }),
                    description: reasonForNotAllowed,
                    variant: 'failed',
                });
            } else {
                const {
                    name: nama,
                    phone: telepon,
                    job_title: posisi,
                    outlet_id: outlet,
                    role_id: hakAkses,
                    photo,
                    active_status: status,
                    is_email_verified: isActivated,
                    is_editable: isEditableDetail,
                    outlets: outletList,
                    is_owner: isOwner,
                    ...restDetailUtama
                } = detail;

                const formatedIdCabangList = outletList.map(x => (
                    {
                        ...x,
                        id: `${x.id_outlet}`,
                        value: `${x.id_outlet}`,
                        id_outlet: `${x.id_outlet}`,
                        is_default: Boolean(+x.is_default),
                    }
                ));

                const form = {
                    ...restDetailUtama,
                    nama,
                    telepon,
                    posisi,
                    outlet,
                    hakAkses,
                    status: !!status,
                    pin: '123456',
                    isActivated,
                    fotoEmployee: photo ? [{
                        name: photo.replace(/^.*[\\/]/, ''),
                        url: photo,
                    }] : [],
                    idCabang: formatedIdCabangList,
                    isOwner,
                };
                setIsEditable(!!isEditableDetail);
                reset(form);
                detailFormRaw.current = form;
            }
        }, detailForm.id, onOpenChange);
    };

    const setFormDetail = useCallback(async () => {
        contextData.showProgress();
        await handleFetchDetail();
        handleFetchOutlet();
        handleFetchRole();
        contextData.hideProgress();
    }, [detailForm]);

    useEffect(() => {
        if (rolesNeedVerification.includes(String(getValues('hakAkses')))) {
            setIsNeedVerification(true);
        } else {
            setIsNeedVerification(false);
            clearErrors('email');
        }

        const userLoginRole = String(contextData.dataUserLogin.data.permission_name).toUpperCase();
        const idUserLoginRole = String(contextData.dataUserLogin.data.permission_id);
        const valueHakAkses = getValues('hakAkses');
        let chossenPermission = roleList.find(item => item.value === valueHakAkses);
        let idChossenPermission = roleList.find(item => item.id === valueHakAkses);
        let isMajooOwner = false;
        let idPermission;

        if (chossenPermission) {
            idPermission = String(chossenPermission.value);
            isMajooOwner = chossenPermission.is_majoo_owner === '1';
            chossenPermission = chossenPermission.name.toUpperCase();
        }

        if (idChossenPermission) {
            setIsMultipleOutlet(idChossenPermission.is_can_multioutlet);
            idChossenPermission = String(idChossenPermission.permission_id)
        };

        if (getUserLoginId() === String(getValues().id)) {
            setDisableMultiOutlet(true);
        } else if (idChossenPermission === roleEnum.ADMIN) {
            setDisableMultiOutlet(true);
        } else {
            setDisableMultiOutlet(false);
        }

        // switch (userLoginRole) {
        //     case DEFAULT_ROLES.OWNER:
        //     case DEFAULT_ROLES.ADMIN:
        //         if (idChossenPermission === roleEnum.STAFF && valueHakAkses === idPermission && chossenPermission === DEFAULT_ROLES.STAFF) {
        //             setIsMultipleOutlet(false);
        //         } else if (chossenPermission === DEFAULT_ROLES.ADMIN || chossenPermission === DEFAULT_ROLES.MANAGER || idChossenPermission === roleEnum.CUSTOM_PRIVILAGE || isMajooOwner) {
        //             setIsMultipleOutlet(chossenPermission === DEFAULT_ROLES.ADMIN || !isStarter);
        //         } else {
        //             setIsMultipleOutlet(false);
        //         }
        //         break;
        //     case DEFAULT_ROLES.MANAGER:
        //         if (idChossenPermission === roleEnum.STAFF && valueHakAkses === idPermission && chossenPermission === DEFAULT_ROLES.STAFF) {
        //             setIsMultipleOutlet(false);
        //         } else if (idChossenPermission === roleEnum.CUSTOM_PRIVILAGE) {
        //             setIsMultipleOutlet(!isStarter);
        //         } else if (chossenPermission === DEFAULT_ROLES.MANAGER) {
        //             setIsMultipleOutlet(!isStarter);
        //             setDisableMultiOutlet(true);
        //         } else {
        //             setIsMultipleOutlet(false);
        //         }
        //         break;
        //     default:
        //         if (idChossenPermission === roleEnum.STAFF && valueHakAkses === idPermission && chossenPermission === DEFAULT_ROLES.STAFF) {
        //             setIsMultipleOutlet(!isStarter);
        //         } else if (idUserLoginRole === roleEnum.CUSTOM_PRIVILAGE) {
        //             if (idChossenPermission === roleEnum.CUSTOM_PRIVILAGE) {
        //                 setIsMultipleOutlet(!isStarter);
        //             }
        //         } else {
        //             setIsMultipleOutlet(false);
        //         }
        //         break;
        // }
    }, [watch('hakAkses'), rolesNeedVerification]);

    useEffect(() => {
        if (mode === modeEnum.ADD) {
            contextData.showProgress();
            handleFetchOutlet();
            handleFetchRole();
            fetchEmployeeNIK(contextData, (nik) => {
                reset({
                    pin: '123456',
                    nik,
                    status: 1,
                    nama: '',
                    telepon: '',
                    email: '',
                    posisi: '',
                    hakAkses: '',
                    fotoEmployee: [],
                    idCabang: [],
                    outlets: [],
                });
                setTempNIK(nik);
                setIsEditable(true);
            });
            contextData.hideProgress();
        } else if (mode === modeEnum.EDIT) {
            setFormDetail();
        }
    }, [mode, detailForm]);

    const listOutlet = getValues('idCabang') && getValues('idCabang').length > 0 ? getValues('idCabang') : [];

    const handleChangeCoachMark = ({ action }) => {
        const resetedAction = [ACTIONS.RESET];
        if (resetedAction.includes(action)) {
            updateOnboardingTask({
                employeePage: 1,
            });
        }
    };

    return (
        <React.Fragment>
            <PageDialog
                open={open}
                onOpenChange={() => handleConfirmationModal(confirmationModalType.CLOSE)}
                css={{ animation: 'unset' }}
                isMobile={isMobile}
            >
                <PageDialogTitle>
                    {modalTitle}
                </PageDialogTitle>
                <PageDialogContent wrapperSize="md">
                    <CoachMarkEmployee t={t} steps={employeeSteps} onChange={handleChangeCoachMark} />
                    <Box
                        as="form"
                        id="karyawan-form"
                    >
                        <Paper
                            responsive
                            css={{
                                padding: '20px 16px 0px 16px',
                                width: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '$compact',
                                '@md': {
                                    padding: '$cozy',
                                    paddingBottom: 44,
                                    gap: '$cozy',
                                },
                            }}
                        >
                            <FormGroup responsive="input">
                                <FormLabel htmlFor="foto">
                                    {t('f.photo')}
                                </FormLabel>
                                <Controller
                                    control={control}
                                    name="fotoEmployee"
                                    render={({ field: { onChange, value, ...restField } }) => (
                                        <Upload
                                            {...restField}
                                            fileList={value}
                                            onChange={({ file }) => onChange([file])}
                                            onPreview={handlePhotoPreview}
                                            onRemove={() => onChange([])}
                                            multiple={false}
                                            listType="picture"
                                            accept=".jpg,.jpeg,.png"
                                            width={150}
                                            height={150}
                                            max={1}
                                            showCropper
                                            disabled={!isEditable}
                                            onBeforeCrop={validateImageSize}
                                        />
                                    )}
                                />
                            </FormGroup>
                            <CoachMarkFragment className="employee-information">
                                <Flex css={{ gap: '$compact', '@md': { gap: '$cozy' } }} direction="column">
                                    <FormGroup responsive="input">
                                        <FormLabel htmlFor="nama" variant="required">
                                            {t('f.name.label')}
                                        </FormLabel>
                                        <InputText
                                            {...register('nama')}
                                            id="nama"
                                            placeholder={t('f.name.placeholder')}
                                            isInvalid={!!errors.nama}
                                            disabled={!isEditable}
                                            maxLength={60}
                                        />
                                        {errors.nama && <FormHelper error>{errors.nama.message}</FormHelper>}
                                    </FormGroup>
                                    <FormGroup responsive="input">
                                        <FormLabel htmlFor="nik" variant="required">
                                            {t('f.id.label')}
                                        </FormLabel>
                                        <InputText
                                            {...register('nik')}
                                            id="nik"
                                            maxLength={20}
                                            isInvalid={!!errors.nik}
                                            disabled={!isEditable}
                                        />
                                        {errors.nik && <FormHelper error>{errors.nik.message}</FormHelper>}
                                    </FormGroup>
                                    <CoachMarkFragment className="access-right">
                                        <FormGroup responsive="input">
                                            <FormLabel htmlFor="hakAkses" variant="required">
                                                {t('f.access.label')}
                                            </FormLabel>
                                            <Controller
                                                control={control}
                                                name="hakAkses"
                                                render={({ field: { onChange, value: val, ...restField }, fieldState: { invalid } }) => (
                                                        <InputSelect
                                                            {...restField}
                                                            id="hakAkses"
                                                            placeholder={t('placeholder.select', { ns: 'translation' })}
                                                            search
                                                            onSearch={() => { }}
                                                            option={roleList}
                                                            onChange={({ value }) => {
                                                                onChange(value);
                                                                const findSelectedRole = roleList.find(x => x.id === value);
                                                                if (findSelectedRole) {
                                                                    setIsMultipleOutlet(String(findSelectedRole.is_can_multioutlet) === '1');
                                                                }

                                                                if (String(findSelectedRole.permission_id) === roleEnum.ADMIN) {
                                                                    const mappingSelectAllOutlet = outletList.map(x => ({ ...x, is_default: x.id === idCabang }));
                                                                    setValue('idCabang', mappingSelectAllOutlet)
                                                                }
                                                            }}
                                                            value={setRoleValue(val)}
                                                            isInvalid={invalid}
                                                            disabled={
                                                                (mode === modeEnum.EDIT
                                                                && (!isEditable
                                                                    || String(getUserLoginId()) === String(detailFormRaw.current.id)
                                                                    || (String(getUserLoginId()) !== String(detailFormRaw.current.id)
                                                                        && String(detailFormRaw.current.id) !== ''
                                                                        && !rolesAllowedRoleToMaintained.includes(detailFormRaw.current.hakAkses)
                                                                    )
                                                                ))
                                                                || watch('isOwner')
                                                            }
                                                        />
                                                    )}
                                            />
                                            {!errors.hakAkses && isNeedVerification && <FormHelper css={{ color: '$textPrimary' }}>{t('emailRequired')}</FormHelper>}
                                            {errors.hakAkses && <FormHelper error>{errors.hakAkses.message}</FormHelper>}
                                        </FormGroup>
                                    </CoachMarkFragment>
                                    <FormGroup responsive="input">
                                        <FormLabel htmlFor="status">
                                            <Flex align="center" onClick={e => e.preventDefault()}>
                                                <Box css={{ marginRight: 10 }}>
                                                    {t('f.employeeAccess.label')}
                                                </Box>
                                                <Tooltip
                                                    side="top"
                                                    align="start"
                                                    label={(<Trans t={t} i18nKey="f.employeeAccess.tooltip" />)}
                                                >
                                                    <Box css={{ cursor: 'pointer' }}>
                                                        <CircleInfoOutline color="#A5ABAB" />
                                                    </Box>
                                                </Tooltip>
                                            </Flex>
                                        </FormLabel>
                                        <Controller
                                            control={control}
                                            name="status"
                                            render={({ field: { onChange, value, ...restField } }) => (
                                                <Flex align="center" gap={5}>
                                                    <InputSwitch
                                                        {...restField}
                                                        id="status"
                                                        dataOffLabel="OFF"
                                                        dataOnLabel="ON"
                                                        onCheckedChange={val => onChange(val)}
                                                        checked={value}
                                                        disabled={!isEditable}
                                                    />
                                                    <FormHelper>
                                                        {watch('status') ? t('f.status.active') : t('f.status.inactive')}
                                                    </FormHelper>
                                                </Flex>
                                            )}
                                        />
                                    </FormGroup>
                                </Flex>
                            </CoachMarkFragment>
                            <Separator css={{ margin: '$spacing-03 0' }} />
                            <FormGroup responsive="input">
                                <FormLabel htmlFor="telepon">
                                    {t('f.phone.label')}
                                </FormLabel>
                                <Controller
                                    control={control}
                                    name="telepon"
                                    render={({ field: { onChange, ...restField } }) => (
                                        <InputNumber
                                            {...restField}
                                            id="telepon"
                                            placeholder={t('f.phone.placeholder')}
                                            allowEmptyFormatting
                                            allowLeadingZeros
                                            thousandSeparator={false}
                                            onValueChange={({ value }) => onChange(value)}
                                            disabled={!isEditable || watch('isOwner')}
                                            maxLength={13}
                                        />
                                    )}
                                />
                            </FormGroup>
                            <FormGroup responsive="input">
                                <FormLabel
                                    htmlFor="email"
                                    {...(isNeedVerification) && { variant: 'required' }}
                                >
                                    Email
                                </FormLabel>
                                <Flex justify="between" gap={3}>
                                    <InputText
                                        {...register('email')}
                                        id="email"
                                        placeholder={t('f.email.placeholder')}
                                        isInvalid={!!errors.email}
                                        disabled={!isEditable || watch('isOwner')}
                                    />
                                    {mode === modeEnum.EDIT && !watch('isActivated') && (
                                        <Box>
                                            <Button buttonType="ghost" type="button" onClick={handleVerificationModal}>{t('f.email.verification')}</Button>
                                        </Box>
                                    )}
                                </Flex>
                                {errors.email && <FormHelper error>{errors.email.message}</FormHelper>}
                                {!errors.email && mode === modeEnum.EDIT && !watch('isActivated') && <FormHelper error>{t('f.email.unverified')}</FormHelper>}
                            </FormGroup>
                            <FormGroup responsive="input">
                                <FormLabel htmlFor="posisi">
                                    {t('f.position.label')}
                                </FormLabel>
                                <InputText
                                    {...register('posisi')}
                                    id="posisi"
                                    placeholder={t('f.position.placeholder')}
                                    disabled={!isEditable}
                                />
                            </FormGroup>
                            {isMultipleOutlet ? (
                                <FormGroup responsive="input" css={{ rowGap: '$spacing-02' }}>
                                    <FormLabel htmlFor="multiOutlet" variant="required">
                                        Outlet
                                    </FormLabel>
                                    <Button onClick={showModalMOutletHandler} buttonType="secondary" disabled={disableMultiOutlet}>
                                        {t('f.adjustOutlet')}
                                    </Button>
                                    <OutletTag list={listOutlet} onRemove={onRemoveSelectedOutlet} disabled={disableMultiOutlet} t={t} />

                                    <Controller
                                        control={control}
                                        name="idCabang"
                                        render={({ field: { onChange } }) => (
                                                <React.Fragment>
                                                    <ModalMultiOutlet
                                                        open={isShowModalMOutlet}
                                                        onOpenChange={setIsShowModalMOutlet}
                                                        dataOutlet={outletList}
                                                        control={control}
                                                        setOutletValue={setOutletValue}
                                                        getValues={getValues}
                                                        setValue={setValue}
                                                        watch={watch}
                                                        onChange={onChange}
                                                        t={t}
                                                    />
                                                </React.Fragment>
                                            )}
                                    />
                                    {errors.idCabang && <FormHelper error>{errors.idCabang.message}</FormHelper>}
                                </FormGroup>
                            ) : (
                                <FormGroup responsive="input">
                                    <FormLabel htmlFor="idCabang" variant="required">
                                        Outlet
                                    </FormLabel>
                                    <Controller
                                        control={control}
                                        name="idCabang"
                                        render={({ field: { onChange, value: val, ...restField }, fieldState: { invalid } }) => {
                                            const tagValue = val && val.length > 0 && val[0].id || '';
                                            return (
                                                <InputSelect
                                                    {...restField}
                                                    id="idCabang"
                                                    placeholder={t('placeholder.select', { ns: 'translation' })}
                                                    search
                                                    onSearch={() => { }}
                                                    option={outletList}
                                                    onChange={({ name, value }) => onChange([{
                                                        name, value, id: value, id_outlet: value, is_default: true,
                                                    }])}
                                                    value={setOutletValue(tagValue)}
                                                    isInvalid={invalid}
                                                    disabled={!isEditable}
                                                    placement="top"
                                                />
                                            );
                                        }}
                                    />
                                    {errors.idCabang && <FormHelper error>{errors.idCabang.message}</FormHelper>}
                                </FormGroup>
                            )}
                            {/*
                                yang bisa merubah pin:
                                    - admin
                                    - manager
                                    - custom yang punya login ke android
                            */}
                            {
                                (mode === modeEnum.ADD
                                    || (mode === modeEnum.EDIT && (String(getUserLoginId()) === String(detailFormRaw.current.id)
                                        || (String(getUserLoginId()) !== String(detailFormRaw.current.id) && rolesAllowedPinToMaintained.includes(detailFormRaw.current.hakAkses)))))
                                && (
                                    <CoachMarkFragment className="pin-section">
                                        <FormGroup responsive="input">
                                            <FormLabel htmlFor="pin" variant="required">
                                                PIN
                                            </FormLabel>
                                            <Flex justify="between" gap={3}>
                                                <InputGroup
                                                    isInvalid={!!errors.pin}
                                                    readOnly={mode === modeEnum.EDIT}
                                                >
                                                    <Controller
                                                        control={control}
                                                        name="pin"
                                                        render={({ field: { onChange, ...restField } }) => (
                                                            <InputNumber
                                                                {...restField}
                                                                id="pin"
                                                                allowEmptyFormatting
                                                                allowLeadingZeros
                                                                thousandSeparator={false}
                                                                type={showPassword ? 'text' : 'password'}
                                                                onValueChange={({ value }) => onChange(value)}
                                                                autocomplete="new-password"
                                                                maxLength={6}
                                                                readOnly={mode === modeEnum.EDIT}
                                                            />
                                                        )}
                                                    />
                                                    <InputRightElement css={{ cursor: mode === modeEnum.ADD ? 'pointer' : 'text' }}>
                                                        {
                                                            mode === modeEnum.ADD && (
                                                                showPassword
                                                                ? (
                                                                    <EyeSlashOutline
                                                                        color="#A5ABAB"
                                                                        onClick={() => setShowPassword(false)}
                                                                    />
                                                                ) : (
                                                                    <EyeOutline
                                                                        color="#A5ABAB"
                                                                        onClick={() => setShowPassword(true)}
                                                                    />
                                                                )
                                                            )
                                                        }
                                                    </InputRightElement>
                                                </InputGroup>
                                                {mode === modeEnum.EDIT && (
                                                    <Box>
                                                        <Button buttonType="ghost" type="button" onClick={handlePinModal}>{t('f.changePIN')}</Button>
                                                    </Box>
                                                )}
                                            </Flex>
                                            {errors.pin
                                                ? <FormHelper error>{errors.pin.message}</FormHelper>
                                                : <FormHelper css={{ color: '$textPrimary' }}>{t('f.defaultPIN')}</FormHelper>
                                            }
                                        </FormGroup>
                                    </CoachMarkFragment>
                                )
                            }
                        </Paper>
                    </Box>
                </PageDialogContent>
                <PageDialogFooter
                    css={{
                        justifyContent: 'center',
                        height: 'auto',
                        padding: '28px 16px 24px 16px',
                        borderTop: '1px solid $gray150',
                        '@md': { height: 64, padding: '12px 24px', borderTop: 'none' },
                    }}
                >
                    <Box
                        css={{
                            maxWidth: 982,
                            width: '100%',
                            display: 'flex',
                            justifyContent: mode === modeEnum.EDIT && !watch('isOwner') ? 'space-between' : 'flex-end',
                        }}
                    >
                        {(mode === modeEnum.EDIT && !watch('isOwner')) && (
                            <Box>
                                <Button
                                    buttonType="negative-secondary"
                                    onClick={() => handleConfirmationModal(confirmationModalType.DELETE)}
                                    css={{ minWidth: 40, padding: 0 }}
                                >
                                    <RemoveOutline
                                        color={foundations.colors.btnNegative}
                                    />
                                </Button>
                            </Box>
                        )}
                        <Box
                            css={{
                                display: 'flex',
                                gap: '$compact',
                            }}
                        >
                            <DialogClose asChild>
                                <Button buttonType="ghost">
                                    {t('label.cancel', { ns: 'translation' })}
                                </Button>
                            </DialogClose>
                            <Button
                                type="button"
                                form="karyawan-form"
                                onClick={handleSubmit(() => handleConfirmationModal(confirmationModalType.SELESAI))}
                                disabled={Object.keys(errors).length > 0}
                            >
                                {t('label.save', { ns: 'translation' })}
                            </Button>
                        </Box>
                    </Box>
                </PageDialogFooter>
            </PageDialog>
            <ImagePreviewModal ref={imagePreviewRef} title={`File: ${titlePreviewImage}`} image={previewImage} />
        </React.Fragment>
    );
};

FormModal.propTypes = {
    open: PropTypes.bool.isRequired,
    onOpenChange: PropTypes.func.isRequired,
    mode: PropTypes.string.isRequired,
    detailForm: PropTypes.shape({
        id: PropTypes.oneOfType([
            PropTypes.string,
            PropTypes.number,
        ]),
    }),
    handleFetchTable: PropTypes.func.isRequired,
};

FormModal.defaultProps = {
    detailForm: {},
};

export default FormModal;
