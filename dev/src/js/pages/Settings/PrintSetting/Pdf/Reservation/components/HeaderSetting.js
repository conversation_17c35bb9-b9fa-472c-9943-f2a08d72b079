import React, { useContext } from 'react';
import {
    Flex,
    Heading,
    InputSwitch,
    Text,
    Button,
    IconButton,
} from '@majoo-ui/react';
import { Controller, useFieldArray, useFormContext } from 'react-hook-form';
import { CaretUpOutline, CaretDownOutline, RemoveOutline } from '@majoo-ui/icons';
import { colors, styled } from '../../../../../../stitches.config';
import { headerFields } from '../config/constants';
import ReservationPdfContext from '../context/ReservationPdfContext';
import AdditionalInfoModal from './modal/AdditionalInfoModal';
import { sortObj } from '../../../../../../utils/helper';
import OwnershipInfoModal from './modal/OwnershipInfoModal';

const FieldGroupContainer = styled(Flex, {
    padding: '$spacing-05',
    flexDirection: 'column',
    gap: '$spacing-05',
    borderRadius: '$lg',
    border: '1px solid $bgBorder',
});

const FieldContainer = styled(Flex, {
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    variants: {
        noGroup: {
            true: {
                paddingRight: '$spacing-05',
            },
        },
    },
});

function HeaderSetting() {
    const { t, modalState, setModalState } = useContext(ReservationPdfContext);
    const { control, watch, setValue } = useFormContext();
    const { fields: additionalFields, ...additionalFieldsArray } = useFieldArray({
        control,
        name: 'show_header_custom.customer_additional_info',
        keyName: '_id',
    });
    const { fields: ownershipFields, ...ownershipFieldsArray } = useFieldArray({
        control,
        name: 'show_header_custom.ownership_info',
        keyName: '_id',
    });
    const customHeaderWatch = watch('show_header_custom');
    return (
        <React.Fragment>
            <Flex direction="column" gap={6}>
                <FieldGroupContainer mt={6}>
                    {headerFields.default.map(field => (
                        <FieldContainer key={field.name}>
                            <Text variant="label" color="primary">
                                {t(`reservation.tab.headerField.${field.name}`, field.defaultName)}
                            </Text>
                            <Controller
                                control={control}
                                render={({ field: { onChange, value } }) => (
                                    <InputSwitch
                                        id={field.name}
                                        onCheckedChange={onChange}
                                        dataOnLabel="ON"
                                        dataOffLabel="OFF"
                                        checked={value}
                                    />
                                )}
                                name={`show_header_default.${field.name}`}
                            />
                        </FieldContainer>
                    ))}
                </FieldGroupContainer>
                <Flex direction="column" gap={3}>
                    <Heading heading="sectionTitle">
                        {t('reservation.tab.headerField.outletInfo.title', 'Informasi Outlet')}
                    </Heading>
                    <FieldGroupContainer>
                        {headerFields.outletInfo.map(field => (
                            <FieldContainer key={field.name}>
                                <Text variant="label" color="primary">
                                    {t(`reservation.tab.headerField.outletInfo.${field.name}`, field.defaultName)}
                                </Text>
                                <Controller
                                    control={control}
                                    render={({ field: { onChange, value } }) => (
                                        <InputSwitch
                                            id={field.name}
                                            onCheckedChange={onChange}
                                            dataOnLabel="ON"
                                            dataOffLabel="OFF"
                                            checked={value}
                                        />
                                    )}
                                    name={`show_header_default.${field.name}`}
                                />
                            </FieldContainer>
                        ))}
                    </FieldGroupContainer>
                </Flex>
                <Flex direction="column" gap={3}>
                    <Heading heading="sectionTitle">
                        {t('reservation.tab.headerField.customerInfo.title', 'Informasi Pelanggan')}
                    </Heading>
                    <FieldGroupContainer>
                        {headerFields.customerInfo.map(field => (
                            <FieldContainer key={field.name}>
                                <Text variant="label" color="primary">
                                    {t(`reservation.tab.headerField.customerInfo.${field.name}`, field.defaultName)}
                                </Text>
                                <Controller
                                    control={control}
                                    render={({ field: { onChange, value } }) => (
                                        <InputSwitch
                                            id={field.name}
                                            onCheckedChange={onChange}
                                            dataOnLabel="ON"
                                            dataOffLabel="OFF"
                                            checked={value}
                                        />
                                    )}
                                    name={`show_header_default.${field.name}`}
                                />
                            </FieldContainer>
                        ))}
                    </FieldGroupContainer>
                </Flex>
                <Flex direction="column" gap={3}>
                    <FieldContainer noGroup>
                        <Text variant="label" color="primary">
                            {t(`reservation.tab.headerField.${headerFields.additionalInfo.name}`, headerFields.additionalInfo.defaultName)}
                        </Text>
                        <Controller
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <InputSwitch
                                    id={headerFields.additionalInfo.name}
                                    onCheckedChange={(val) => {
                                        onChange(val);
                                        if (!val) {
                                            setValue('show_header_custom.customer_additional_info', []);
                                        }
                                    }}
                                    dataOnLabel="ON"
                                    dataOffLabel="OFF"
                                    checked={value}
                                />
                            )}
                            name="show_header_custom.showAdditionalInfo"
                        />
                    </FieldContainer>
                    {customHeaderWatch.showAdditionalInfo && (
                        <FieldGroupContainer>
                            {additionalFields.map((field, index) => (
                                <FieldContainer key={field.id}>
                                    <Text variant="label" color="primary">
                                        {field.name}
                                    </Text>
                                    <Flex align="center" gap={3}>
                                        <Flex align="center">
                                            <IconButton
                                                disabled={index < 1}
                                                onClick={() => additionalFieldsArray.swap(index, index - 1)}
                                            >
                                                <CaretUpOutline color={index < 1 ? colors.iconDisable : colors.iconPrimary} />
                                            </IconButton>
                                            <IconButton
                                                disabled={index >= additionalFields.length - 1}
                                                onClick={() => additionalFieldsArray.swap(index, index + 1)}
                                            >
                                                <CaretDownOutline color={index >= additionalFields.length - 1 ? colors.iconDisable : colors.iconPrimary} />
                                            </IconButton>
                                        </Flex>
                                        <IconButton
                                            onClick={() => additionalFieldsArray.remove(index)}
                                        >
                                            <RemoveOutline />
                                        </IconButton>
                                    </Flex>
                                </FieldContainer>
                            ))}
                            <Button
                                size="sm"
                                buttonType="secondary"
                                onClick={() => setModalState(current => ({ ...current, openAdditionalModal: true }))}
                            >
                                {t('label.selectData', 'Pilih Data')}
                            </Button>
                        </FieldGroupContainer>
                    )}
                </Flex>
                <Flex direction="column" gap={3}>
                    <FieldContainer noGroup>
                        <Text variant="label" color="primary">
                            {t(`reservation.tab.headerField.${headerFields.ownershipInfo.name}`, headerFields.ownershipInfo.defaultName)}
                        </Text>
                        <Controller
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <InputSwitch
                                    id={headerFields.customerInfo.name}
                                    onCheckedChange={(val) => {
                                        onChange(val);
                                        if (!val) {
                                            setValue('show_header_custom.ownership_info', []);
                                        }
                                    }}
                                    dataOnLabel="ON"
                                    dataOffLabel="OFF"
                                    checked={value}
                                />
                            )}
                            name="show_header_custom.showOwnerInfo"
                        />
                    </FieldContainer>
                    {customHeaderWatch.showOwnerInfo && (
                        <FieldGroupContainer>
                            {ownershipFields.map((field, index) => (
                                <FieldContainer key={field.id}>
                                    <Text variant="label" color="primary">
                                        {field.name}
                                    </Text>
                                    <Flex align="center" gap={3}>
                                        <Flex align="center">
                                            <IconButton
                                                disabled={index < 1}
                                                onClick={() => ownershipFieldsArray.swap(index, index - 1)}
                                            >
                                                <CaretUpOutline color={index < 1 ? colors.iconDisable : colors.iconPrimary} />
                                            </IconButton>
                                            <IconButton
                                                disabled={index >= ownershipFields.length - 1}
                                                onClick={() => ownershipFieldsArray.swap(index, index + 1)}
                                            >
                                                <CaretDownOutline color={index >= ownershipFields.length - 1 ? colors.iconDisable : colors.iconPrimary} />
                                            </IconButton>
                                        </Flex>
                                        <IconButton
                                            onClick={() => ownershipFieldsArray.remove(index)}
                                        >
                                            <RemoveOutline />
                                        </IconButton>
                                    </Flex>
                                </FieldContainer>
                            ))}
                            <Button
                                size="sm"
                                buttonType="secondary"
                                onClick={() => setModalState(current => ({ ...current, openOwnershipModal: true }))}
                            >
                                {t('label.selectData', 'Pilih Data')}
                            </Button>
                        </FieldGroupContainer>
                    )}
                </Flex>
            </Flex>
            {modalState.openAdditionalModal && (
                <AdditionalInfoModal />
            )}
            {modalState.openOwnershipModal && (
                <OwnershipInfoModal />
            )}
        </React.Fragment>
    );
}

export default HeaderSetting;
