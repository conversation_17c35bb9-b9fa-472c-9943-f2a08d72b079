import React from 'react';
import moment from 'moment';
import { Button, RowMenuColumn, RowExpanderColumn } from '@majoo-ui/react';
import StatusColumn from '../components/Columns/Status';
import ENUM from "./enum";
import cForm from "./form";
import PriceColumn from '../../../../../components/retina/table/PriceColumn';

export const listColumns = ({
  setConfirmationPayment,
  callbackDetail,
  callbackLogActivity,
  t
}) => [
    RowExpanderColumn,
    {
      id: 'payment_date',
      Header: t('payment_date', 'Tanggal Pembayaran'),
      accessor: 'payment_date',
      Cell: ({ value }) =>
        moment(value).isValid()
          ? `${moment(value).format('DD MMMM YYYY')} WIB`
          : '-',
    },
    {
      id: 'name',
      Header: t('trx_name', '<PERSON><PERSON>'),
      accessor: 'name',
    },
    {
      id: 'outlet_name',
      Header: 'Outlet',
      accessor: 'outlet_name',
    },
    {
      id: 'status',
      Header: 'Status',
      accessor: 'status',
      Cell: StatusColumn,
    },
    {
      id: 'eye',
      Header: t('paid_at', 'Dibayar Pada'),
      accessor: 'paid_at',
      // unsortable: true,
      Cell: props => {
        const { status, paid_at, payment_date } = props.row.original;

        if ([ENUM.STATUS_KEY.PAID, ENUM.STATUS_KEY.FAILED].includes(status))
          return `${moment(
            status === ENUM.STATUS_KEY.FAILED ? payment_date : paid_at,
          ).format('DD MMMM YYYY, HH:mm')} WIB`;

        if (status === ENUM.STATUS_KEY.WAITING_CONFIRMATION)
          return (
            <Button
              size="sm"
              buttonType="secondary"
              onClick={() =>
                setConfirmationPayment({ popup: true, data: props.row.original })
              }
            >
              {(ENUM.WORDING_PAID_AT(t).waiting_confirmation)}
            </Button>
          );

        return ENUM.WORDING_PAID_AT(t)[status] || '-';
      },
    },
    {
      id: 'total_amount',
      Header: t('total_paid', 'Total Dibayar'),
      accessor: 'total_amount',
      isSubRow: true,
      Cell: props =>
        props && !props.value ? '-' : PriceColumn({ ...props, decimal: false }),
    },
    {
      id: 'employees',
      Header: t('receiver', 'Penerima'),
      accessor: 'employees',
      isSubRow: true,
      Cell: ({ value }) => `${Array.isArray(value) ? value.length : 0} ${t('employee', 'Karyawan')}`,
    },
    {
      id: 'menu',
      Header: () => null,
      Cell: props => {
        const {
          row: { original },
        } = props;

        return RowMenuColumn([
          {
            title: t('see_detail', 'Lihat Detail'),
            onClick: () => callbackDetail(original, cForm.FORM_MODE.DETAIL),
          },
          {
            title: t('activity_log', 'Log Aktivitas'),
            onClick: props => {
              const {
                row: { original },
              } = props;

              return callbackLogActivity({
                id: original._id,
                outlet_id: original.outlet_id,
              });
            },
          },
        ]).Cell(props);
      },
      options: [
        {
          title: t('see_detail', 'Lihat Detail'),
          onClick: props => {
            const {
              row: { original },
            } = props;

            return callbackDetail(original, cForm.FORM_MODE.DETAIL);
          },
        },
        {
          title: t('activity_log', 'Log Aktivitas'),
          onClick: props => {
            const {
              row: { original },
            } = props;

            return callbackLogActivity({
              id: original._id,
              outlet_id: original.outlet_id,
            });
          },
        },
      ],
      unsortable: true,
    },
  ];

export const COLUMNS_ORDER_MOBILE = [
  'expander',
  'payment_date',
  'name',
  'outlet_name',
  'total_amount',
  'employees',
  'status',
  'eye',
  'menu',
];

const DATA = [
  {
    id: 1,
    schedule_name: moment(new Date()).format('DD MMMM YYYY'),
    name: 'Gaji September 2022 ',
    outlet_name: 'Bara Kopi',
    status: 0,
    paid_at: 0,
    paid_sum: 20500000,
    receiver: 4,
  },
  {
    id: 2,
    schedule_name: moment(new Date()).format('DD MMMM YYYY'),
    name: 'Gaji September 2022 ',
    outlet_name: 'Bara Kopi Jakarta',
    status: 1,
    paid_at: 2,
    paid_sum: 10500000,
    receiver: 4,
  },
  {
    id: 3,
    schedule_name: moment(new Date()).format('DD MMMM YYYY'),
    name: 'Gaji September 2022 ',
    outlet_name: 'Bara Kopi',
    status: 2,
    paid_at: 1,
    paid_sum: 20500000,
    receiver: 4,
  },
  {
    id: 4,
    schedule_name: moment(new Date()).format('DD MMMM YYYY'),
    name: 'Gaji September 2022 ',
    outlet_name: 'Bara Kopi',
    status: 3,
    paid_at: 2,
    paid_sum: 20500000,
    receiver: 4,
  },
  {
    id: 5,
    schedule_name: moment(new Date()).format('DD MMMM YYYY'),
    name: 'Gaji September 2022 ',
    outlet_name: 'Bara Kopi',
    status: 4,
    paid_at: 2,
    paid_sum: 20500000,
    receiver: 4,
  },
];

const BE_DATA = [
  {
    id: '6eabhjgui8950hgh0h0',
    payment_schedule_name: 'Gaji September 2022',
    outlet_id: 34566,
    outlet_name: 'Barakopi Jakarta',
    status: 'paid',
    payment_type: 1,
    payment_schedule: {
      payment_calculation_start: '01/12/2022',
      payment_calculation_end: '30/12/2022',
      payment_date: '01/12/2022',
      paid_at: '10/12/2022',
    },
    payment_employee: [],
    payment_employee_total: '********',
  },
  {
    id: '6eabhjgui8950hgh0h1',
    payment_schedule_name: 'Gaji September 2022',
    outlet_id: 34566,
    outlet_name: 'Barakopi Jakarta',
    status: 'failed',
    payment_type: 1,
    payment_schedule: {
      payment_calculation_start: '01/12/2022',
      payment_calculation_end: '30/12/2022',
      payment_date: '01/12/2022',
      paid_at: '10/12/2022',
    },
    payment_employee: [],
    payment_employee_total: '********',
  },
  {
    id: '6eabhjgui8950hgh0h2',
    payment_schedule_name: 'Gaji September 2022',
    outlet_id: 34567,
    outlet_name: 'Barakopi Bandung',
    status: 'on_schedule',
    payment_type: 1,
    payment_schedule: {
      payment_calculation_start: '01/12/2022',
      payment_calculation_end: '30/12/2022',
      payment_date: '01/12/2022',
      paid_at: '10/12/2022',
    },
    payment_employee: [],
    payment_employee_total: '********',
  },
  {
    id: '6eabhjgui8950hgh0h3',
    payment_schedule_name: 'Gaji September 2022',
    outlet_id: 34567,
    outlet_name: 'Barakopi Bandung',
    status: 'draft',
    payment_type: 1,
    payment_schedule: {
      payment_calculation_start: '01/12/2022',
      payment_calculation_end: '30/12/2022',
      payment_date: '01/12/2022',
      paid_at: '10/12/2022',
    },
    payment_employee: [{}, {}],
    payment_employee_total: '********',
  },
  {
    id: '6eabhjgui8950hgh0h4',
    payment_schedule_name: 'Gaji September 2022',
    outlet_id: 34567,
    outlet_name: 'Barakopi Bandung',
    status: 'waiting_confirmation',
    payment_type: 1,
    payment_schedule: {
      payment_calculation_start: '01/12/2022',
      payment_calculation_end: '30/12/2022',
      payment_date: '01/12/2022',
      paid_at: '10/12/2022',
    },
    payment_employee: [{}, {}],
    payment_employee_total: '********',
  },
];

const NEW_BE_DATA = [
  {
    _id: '63b59c277c66f54ad578f624',
    bank_account_name: 'Barakopi Bank Raya 1',
    bank_account_number: '************',
    bank_name: 'Raya',
    created_at: null,
    deleted_at: null,
    employees: [
      {
        merchant_id: 'string',
        outlet_id: 'string',
        outlet_name: 'string',
        user_id: 'string',
        user_name: 'string',
        role_name: 'string',
        nik: 'string',
        bank_name: 'string',
        bank_account_number: 'string',
        bank_account_name: 'string',
        status: 'string',
        earnings: 1000000, // float
        deduction: 10000, // float
        net_earnings: 'string',
        present: 22,
        absent: 1,
        components: [
          {
            name: 'Tidak Masuk Kerja',
            type: 'pengurang',
            value: 10000,
            description: 'tidak masuk kerja 1 hari tanpa kabar',
          },
        ],
      },
    ],
    is_deleted: false,
    method: 'scheduled_transfer', // scheduled_transfer, manual_transfer
    name: 'Transfer Gaji November 2022',
    outlet_id: 353176,
    outlet_name: 'Barakopi',
    paid_at: '2022-11-03T00:00:00.000Z', // date juga
    recapitulation_end: '0001-01-03T00:00:00.000Z',
    recapitulation_start: '0001-01-03T00:00:00.000Z',
    payment_date: '2022-11-03T00:00:00.000Z', // diisi apabila method = scheduled_transfer
    status: 'waiting_confirmation', // draft, scheduled, on_process, waiting_confirmation, paid, failed
    total_amount: 1000000,
    type: 'payroll', // payroll, benefit, ulang_tahun
    updated_at: null,
  },
];

export default { COLUMNS: listColumns, DATA, BE_DATA, NEW_BE_DATA, COLUMNS_ORDER_MOBILE };
