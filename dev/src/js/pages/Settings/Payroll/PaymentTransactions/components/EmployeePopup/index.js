import React, { useState } from 'react';
import {
    Box,
    Button,
    Flex,
    InputSearchbox,
    InputSelect,
    ModalDialog,
    ModalDialogContent,
    ModalDialogFooter,
    ModalDialogTitle,
    Table,
    FormHelper,
} from '@majoo-ui/react';
import debounce from 'lodash/debounce';
import { Trans } from 'react-i18next';
import useEmployees from '../../hooks/useEmployees';
import { usePayrollPaymentContext } from '../../context/PayrollPaymentContext';
import { useMediaQuery } from '../../../../../../utils/useMediaQuery';
import tableForm from '../../constants/tableForm';
import { TRANSFER_METHOD } from '../../constants/form';
import formatEmployees from '../../utils/formatEmployees';

const Index = () => {
    const [localError, setLocalError] = useState(true);
    const isMobile = useMediaQuery('(max-width: 1024px)');
    const {
        employeePopupState,
        setEmployeePopupState,
        t,
        form: { setValue, clearErrors, setError, getValues, watch },
        errorsExist,
    } = usePayrollPaymentContext();
    const {
        employeeList,
        setEmployeeList,
        employeeListMeta: {
            loading,
            page,
            filter: filterStatus,
            search: searchQuery,
            hasMoreItems,
            isClientSide,
            totalData,
        },
        setEmployeeListMeta,
        setEmployee,
        fetchEmployeeData,
    } = useEmployees();
    const forecastedEmployees = formatEmployees(getValues('selected_forecasted_employees'));
    const [selectedEmployeeIds, setSelectedEmployeeIds] = useState(getValues('employees_ids') || []);

    const handleSearch = debounce(search => {
        if (isClientSide) {
            setEmployeeList(
                search
                    ? forecastedEmployees.filter(d => `${d.user_name}`.toLowerCase().includes(search))
                    : forecastedEmployees,
            );
        } else {
            setEmployeeListMeta({
                search,
            });
        }
    }, 1000);

    const selectedIds = selectedEmployeeIds.reduce((prev, curr) => {
        // eslint-disable-next-line no-param-reassign
        prev[curr] = true;

        return prev;
    }, {});

    const handleFilterStatus = debounce(
        ({ value }) =>
            setEmployee(prev => ({
                ...prev,
                data: value
                    ? forecastedEmployees.filter(d => `${d.status}`.toLowerCase() === value.split('_').join(' '))
                    : forecastedEmployees,
            })),
        500,
    );

    return (
        <ModalDialog
            layer="$modal"
            open={employeePopupState.show}
            size="auto"
            isMobile={isMobile}
            onOpenChange={() => {
                setLocalError(false);

                clearErrors('employees');

                setEmployeePopupState(prev => ({ ...prev, show: false }));
            }}
        >
            <ModalDialogTitle>{t('select_employee', 'Pilih Karyawan')}</ModalDialogTitle>
            <ModalDialogContent
                css={{
                    width: 'auto',
                    maxHeight: '80vh',
                    padding: '16px 16px 36px 16px',
                    overflow: 'auto',
                    '@md': {
                        height: '400px',
                        maxHeight: 'calc(100vh - 150px)',
                        overflow: 'auto',
                        padding: '16px 16px 36px 16px',
                    },
                    '@lg': {
                        height: '520px',
                        padding: '16px 16px 36px 16px',
                    },
                }}
            >
                {getValues('method') === TRANSFER_METHOD.SCHEDULED && (
                    <Box
                        css={{
                            backgroundColor: '#CCEAFD',
                            border: '1px solid #0197F4',
                            padding: '8px 16px',
                            width: '100%',
                            borderRadius: '8px',
                            mb: '16px',
                        }}
                    >
                        <Trans
                            t={t}
                            i18nKey="auto_info"
                            defaults="Saat ini <strong>Transfer Otomatis</strong> hanya dapat dilakukan ke sesama
              rekening Bank Raya. Data karyawan yang ditampilkan hanya karyawan
              dengan rekening <strong>Bank Raya</strong> saja"
                            components={{ bold: <strong /> }}
                        />
                    </Box>
                )}
                {getValues('method') === TRANSFER_METHOD.MANUAL && (
                    <Box
                        css={{
                            backgroundColor: '#CCEAFD',
                            border: '1px solid #0197F4',
                            padding: '8px 16px',
                            width: '100%',
                            borderRadius: '8px',
                            mb: '16px',
                        }}
                    >
                        <Trans
                            t={t}
                            i18nKey="manual_info"
                            defaults="Karyawan yang ditampilkan hanya karyawan dengan informasi bank
              lengkap meliputi <strong>Nomor Rekening,</strong> <strong>Nama Bank,</strong> dan{' '}
              <strong>Nama Pemilik Rekening.</strong> Silakan lengkapi informasi bank
              karyawan pada halaman <strong>Pengaturan Karyawan</strong>"
                            components={{ bold: <strong /> }}
                        />
                    </Box>
                )}
                <Flex direction={isMobile ? 'column' : 'row'} gap={3}>
                    <InputSearchbox
                        onChange={handleSearch}
                        css={{
                            '@sm': {
                                width: '100%',
                            },
                            '@lg': {
                                width: '240px',
                            },
                        }}
                        placeholder={`${t('s', 'Cari')} ...`}
                    />
                    <InputSelect
                        option={tableForm.STATUS(t)}
                        value={tableForm.STATUS(t).find(status => status.value === filterStatus)}
                        onChange={
                            isClientSide ? handleFilterStatus : ({ value }) => setEmployeeListMeta({ filter: value })
                        }
                        css={{
                            '@sm': {
                                width: '100%',
                            },
                            '@lg': {
                                width: '280px',
                                ml: '16px',
                            },
                        }}
                        size="sm"
                        textSelector="name"
                        valueSelector="value"
                        placeholder={t('select_status', 'Pilih status')}
                    />
                </Flex>

                <Box
                    css={{
                        width: 'auto',
                        '@lg': {
                            width: 1022,
                            padding: '$spacing-03',
                        },
                    }}
                >
                    <Table
                        keyId="id_employee"
                        columns={tableForm.CHOOSE_EMPLOYEES_COLUMNS(t)}
                        data={employeeList}
                        isLoading={loading}
                        pageIndex={page - 1}
                        hasMoreItems={hasMoreItems}
                        totalData={
                            getValues('isDetail')
                                ? totalData + getValues('selected_forecasted_employees').length
                                : totalData
                        }
                        {...(isClientSide ? {} : { fetchData: fetchEmployeeData })}
                        emptyDataMessage={t('empty_table', 'Tidak ada data untuk ditampilkan')}
                        isInfiniteScroll={employeeList.length > 0}
                        onSelectedChange={ids => {
                            setSelectedEmployeeIds(ids);

                            if (ids.length < 1) {
                                setError('employees');
                                return setLocalError(true);
                            }

                            setLocalError(false);
                            return clearErrors('employees');
                        }}
                        selectedIds={selectedIds}
                        searchQuery={searchQuery}
                        css={{
                            mt: '16px',
                            alignSelf: 'stretch',
                            height: '240px',
                            '& table > tbody[role=rowgroup] > tr[role=row] > td > div > div > p:last-child': {
                                textAlign: 'left',
                            },
                            '& table > tbody[role=rowgroup] > tr > td > div > div > p:last-child': {
                                textAlign: 'right',
                            },
                            ...(employeeList.length > 0
                                ? {
                                      '&::-webkit-scrollbar': {
                                          width: '8px',
                                      },
                                      '&::-webkit-scrollbar-thumb': {
                                          width: '8px',
                                          backgroundColor: 'rgba(39, 42, 42, 0.6)',
                                          borderRadius: '59px',
                                      },
                                      '&::-webkit-scrollbar-track': {
                                          borderRadius: '59px',
                                          width: '8px',
                                          backgroundColor: '#EEF0F0',
                                      },
                                      '@lg': {
                                          height: '350px',
                                      },
                                  }
                                : {
                                      '@lg': {
                                          height: '60px',
                                          overflowY: 'hidden',
                                      },
                                  }),
                        }}
                    />
                    {localError && errorsExist && (
                        <FormHelper error>{t('v.emp_req', 'Mohon pilih karyawan terlebih dahulu')}</FormHelper>
                    )}
                </Box>
            </ModalDialogContent>
            <ModalDialogFooter
                css={{
                    '@sm': { padding: '$spacing-05' },
                    '@md': {
                        pr: '$spacing-06',
                    },
                }}
            >
                <Flex gap={5}>
                    <Button
                        buttonType="ghost"
                        onClick={() => {
                            clearErrors('employees');

                            setEmployeePopupState(prev => ({ ...prev, show: false }));
                        }}
                        block={isMobile}
                    >
                        {t('btn.cancel', 'Batal')}
                    </Button>
                    <Button
                        disabled={localError || errorsExist}
                        onClick={async () => {
                            const unselectedIds = employeeList.reduce((prev, curr) => {
                                if (!selectedEmployeeIds.includes(curr.id_employee || curr.user_id)) {
                                    prev.push(curr.id_employee || curr.user_id);
                                }

                                return prev;
                            }, []);
                            const employees = getValues('employees') || [];
                            const filteredEmployees = [
                                ...employees.filter(d => !unselectedIds.includes(d.id_employee || d.user_id)),
                                ...employeeList.filter(d => selectedEmployeeIds.includes(d.id_employee || d.user_id)),
                            ];

                            setValue(
                                'employees_ids',
                                filteredEmployees.map(d => d.id_employee || d.user_id),
                            );
                            setValue(
                                'employees',
                                filteredEmployees.sort((a, b) => `${a.user_name}`.localeCompare(`${b.user_name}`)),
                            );
                            // setValue('forecasted_employees', employees);
                            setValue(
                                'selected_forecasted_employees',
                                filteredEmployees.sort((a, b) => `${a.user_name}`.localeCompare(`${b.user_name}`)),
                            );

                            return setEmployeePopupState(prev => ({ ...prev, show: false }));
                        }}
                        block={isMobile}
                    >
                        {t('select', 'Pilih')}
                    </Button>
                </Flex>
            </ModalDialogFooter>
        </ModalDialog>
    );
};

export default Index;
