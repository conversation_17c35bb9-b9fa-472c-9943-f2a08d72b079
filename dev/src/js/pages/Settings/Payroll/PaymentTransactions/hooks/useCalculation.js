import { useEffect, useState } from 'react';
import omit from 'lodash/omit';
import moment from 'moment';
import { getCalculationPayroll, postPaymentTransaction } from '../../../../../data/payment';
import { catchError } from '../../../../../utils/helper';
import { usePayrollPaymentContext } from '../context/PayrollPaymentContext';
import createPayload from '../utils/createPayload';
import cForm from '../constants/form';

const useCalculation = ({ autoUpdate } = { autoUpdate: false }) => {
  const {
    showProgress,
    hideProgress,
    setRecapData,
    toast,
    t,
    form: { getValues, reset, setValue, watch },
  } = usePayrollPaymentContext();
  const isDetailForecast = getValues('isDetail') && getValues('is_forecasted');

  const [calculation, setCalculation] = useState({
    loading: false,
    others: {},
    salaries: [],
    error: false,
  });

  const filteredChangedValue = isDetailForecast
    ? getValues('employees')
    : getValues('employees').filter(
      employee => !(Array.isArray(employee.components) && employee.components.length > 0),
    );

  const fetchCalculationPayroll = async () => {
    showProgress();

    setCalculation(prev => ({ ...prev, loading: true }));
    setRecapData(prev => ({ ...prev, loading: true }));

    try {
      const response = await getCalculationPayroll(
        `outlet_id=${getValues('outlet_id')}&user_id=${filteredChangedValue
          .map(employee => employee.id_employee || employee.user_id)
          .join(',')}&payment_date=${moment(getValues('payment_date')).format('YYYY-MM-DD')}`,
      );

      setCalculation(prev => ({
        ...prev,
        salaries: response.data.list_salary,
        others: { ...omit(response.data, 'list_salary') },
        error: false,
      }));

      reset({
        ...getValues(),
        recapitulation_end: response.data.recapitulation_end_date,
        recapitulation_start: response.data.recapitulation_start_date,
        // request BE to set to recapitulated when this fetch is success
        employees: [
          ...(isDetailForecast ? [] : getValues('employees')).filter(
            employee => Array.isArray(employee.components) && employee.components.length > 0,
          ),
          ...filteredChangedValue.map(employee => {
            const findById = response.data.list_salary.find(
              salary => `${salary.employee_id}` === (employee.id_employee || `${employee.user_id}`),
            );

            if (Object.keys(findById || {}).length > 0 && !employee.isChanged) {
              const components =
                Array.isArray(findById.components) && findById.components.length > 0
                  ? findById.components.map((component, idx) => ({
                    ...component,
                    idx,
                  }))
                  : [];

              const deduction = components
                .filter(
                  component =>
                    component.type === cForm.COMPONENT_TYPES.DEDUCTION && component.active,
                )
                .reduce((prev, curr) => prev + curr.value, 0);
              const earnings = components
                .filter(
                  component =>
                    component.type === cForm.COMPONENT_TYPES.INCOME && component.active,
                )
                .reduce((prev, curr) => prev + curr.value, 0);

              return {
                ...employee,
                ...findById,
                // from employee api bank_account_name:
                bank_account_name: employee.account_name || employee.bank_account_name,
                bank_account_number: employee.account_number || employee.bank_account_number,
                nik: employee.user_nik || employee.nik,
                role_name: employee.user_posisi || employee.role_name,
                outlet_name: employee.cabang_name || employee.outlet_name,
                user_id: +employee.id_employee || employee.user_id,
                user_name: employee.user_name,
                components,
                temp_components: components,
                deduction,
                earnings,
                net_earnings: earnings - deduction,
              };
            }

            return employee;
          }),
        ],
      });

      setRecapData(prev => ({ ...prev, error: false }));
      setValue('is_forecasted', false);

      if (autoUpdate) {
        try {
          const payload = createPayload({
            ...getValues(),
            paymentFailedStatusExist: false,
            recapitulation_status: cForm.RECAPITULATION_STATUS.RECAPITULATED,
          });

          await postPaymentTransaction(payload);

          setValue('force_recapitulated', false);

          // set the form value to recapitulated because the process of auto update was successfully
          setValue('recapitulation_status', cForm.RECAPITULATION_STATUS.RECAPITULATED);

          setValue('is_error_autoupdate', false);
          return setValue('refetch_detail', !getValues('refetch_detail'));
        } catch (err) {
          setValue('is_error_autoupdate', true);
          setValue('recapitulation_status', cForm.RECAPITULATION_STATUS.NOT_RECAPITULATED);
          setValue('force_recapitulated', false);

          return toast.addToast({
            title: t('failed_update_payment', 'Gagal memperbarui data pembayaran'),
            description: catchError(err),
            variant: 'failed',
            position: 'top-right',
          });
        }
      }

      return setValue('force_recapitulated', true);
    } catch (err) {
      // request BE to set to recapitulated when this fetch is success
      setValue('force_recapitulated', false);
      setValue('recapitulation_status', cForm.RECAPITULATION_STATUS.NOT_RECAPITULATED);

      if (catchError(err) === cForm.ERRORS.CUTOFF) {
        setValue('is_forecasted', true);
        setValue('is_error_cutoff', true);
        setValue('refetch_forecasted', !getValues('refetch_forecasted'));
      }
      setRecapData(prev => ({ ...prev, error: true }));
      setCalculation(prev => ({
        ...prev,
        salaries: [],
        others: {},
        error: true,
      }));

      // toast.addToast({
      //   title: 'Gagal mengambil data',
      //   description: catchError(err),
      //   variant: 'failed',
      //   position: 'top-right',
      //   dismissAfter: 3000,
      // });
    } finally {
      setCalculation(prev => ({ ...prev, loading: false }));
      setRecapData(prev => ({ ...prev, loading: false }));

      hideProgress();
    }
    return false;
  };

  useEffect(() => {
    const shouldFetch =
      (getValues('recapitulation_status') === cForm.RECAPITULATION_STATUS.NOT_RECAPITULATED &&
        Array.isArray(filteredChangedValue) &&
        filteredChangedValue.length > 0) ||
      isDetailForecast;

    if (shouldFetch) fetchCalculationPayroll();
  }, []);

  return {
    loading: calculation.loading,
    data: calculation.salaries,
    others: calculation.others,
    error: calculation.error,
    isForecast: watch('is_forecasted'),
    enableForecast: watch('refetch_forecasted'),
  };
};

export default useCalculation;
