import React from 'react';
import { RowMenuColumn, TagStatus } from '@majoo-ui/react';
import { normalizePhoneNumber } from '~/utils/helper';
import { TABLE_ACTION } from './enum';

export const columnTable = (LANG_DATA, listBranch, quota, onRowAction, whatsAppFeatures) => [
    {
        Header: LANG_DATA.COLUMN.NAME,
        accessor: 'label',
        colMinWidth: 150,
        isMobileHeader: true,
    },
    {
        Header: LANG_DATA.COLUMN.WHATSAPP_NUMBER,
        accessor: 'phone_number',
        colMinWidth: 170,
        Cell: ({ value }) => (value ? normalizePhoneNumber(value) : '-'),
    },
    {
        Header: 'Outlet',
        accessor: 'outlet_id',
        Cell: ({ value }) => {
            const outlet = listBranch.find(x => x.id_cabang === String(value));
            return outlet && outlet.cabang_name ? outlet.cabang_name : '-';
        },
    },
    {
        Header: 'Status',
        accessor: 'status_client',
        colMinWidth: 150,
        Cell: ({ value }) => (
            <TagStatus type={value ? 'success' : 'default'}>
                {value ? LANG_DATA.COLUMN.ACTIVE : LANG_DATA.COLUMN.INACTIVE}
            </TagStatus>
        ),
    },
    {
        Header: LANG_DATA.COLUMN.AUTH,
        accessor: 'status',
        Cell: ({ value }) => {
            let tagValue = {
                type: 'new',
                label: LANG_DATA.COLUMN.NOT_CONNECTED_YET,
            };
            if (value === 'login') {
                tagValue = {
                    type: 'success',
                    label: LANG_DATA.COLUMN.LOGIN,
                };
            }
            if (value === 'logout') {
                tagValue = {
                    type: 'error',
                    label: LANG_DATA.COLUMN.LOGOUT,
                };
            }
            return (
                <TagStatus type={tagValue.type} css={{ maxWidth: 'unset' }}>
                    {tagValue.label}
                </TagStatus>
            );
        },
    },
    {
        Header: LANG_DATA.COLUMN.FUNCTION,
        accessor: 'category',
        colMinWidth: 150,
        Cell: ({ value }) =>
            value
                .map(x => {
                    const whatsappFeature = whatsAppFeatures.find(({ feature_code }) => feature_code === x);
                    return whatsappFeature ? whatsappFeature.feature_name : x;
                })
                .join(','),
    },
    {
        id: 'menu',
        Header: '',
        Cell: propsCell => {
            const {
                row: {
                    original: { status_client: status, category },
                },
            } = propsCell;
            const usedFor = category.length ? category[0] : '';
            return RowMenuColumn([
                {
                    title: LANG_DATA.TABLE_ACTION.EDIT,
                    onClick: props => {
                        const {
                            row: { original },
                        } = props;
                        onRowAction(TABLE_ACTION.EDIT, original);
                    },
                },
                {
                    title: LANG_DATA.TABLE_ACTION.RE_AUTH,
                    onClick: props => {
                        const {
                            row: { original },
                        } = props;
                        onRowAction(TABLE_ACTION.RE_AUTH, original);
                    },
                },
                {
                    title: LANG_DATA.TABLE_ACTION.DELETE,
                    onClick: props => {
                        const {
                            row: { original },
                        } = props;
                        onRowAction(TABLE_ACTION.DELETE, original);
                    },
                },
            ]).Cell(propsCell);
        },
        unsortable: true,
    },
];

export const MenuIcon = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="20" height="20" fill="#DBDBDB" />
        <g clipPath="url(#clip0_3501_104231)">
            <rect width="1366" height="871" transform="translate(-502 -454)" fill="#FAFAFA" />
            <g filter="url(#filter0_d_3501_104231)">
                <rect x="-142" y="-358" width="982" height="672" rx="8" fill="white" shapeRendering="crispEdges" />
                <rect x="-117.5" y="-149.5" width="933" height="371" rx="7.5" stroke="#EEF0F0" />
                <rect width="20" height="20" rx="4" fill="url(#pattern0_3501_104231)" />
            </g>
        </g>
        <defs>
            <filter
                id="filter0_d_3501_104231"
                x="-146"
                y="-361"
                width="990"
                height="680"
                filterUnits="userSpaceOnUse"
                colorInterpolationFilters="sRGB"
            >
                <feFlood floodOpacity="0" result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                />
                <feOffset dy="1" />
                <feGaussianBlur stdDeviation="2" />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0" />
                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3501_104231" />
                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3501_104231" result="shape" />
            </filter>
            <pattern id="pattern0_3501_104231" patternContentUnits="objectBoundingBox" width="1" height="1">
                <use xlinkHref="#image0_3501_104231" transform="translate(-0.0151515) scale(0.0151515)" />
            </pattern>
            <clipPath id="clip0_3501_104231">
                <rect width="1366" height="871" fill="white" transform="translate(-502 -454)" />
            </clipPath>
            <image
                id="image0_3501_104231"
                width="68"
                height="66"
                xlinkHref="data:image/png;base64,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"
            />
        </defs>
    </svg>
);

export const GearIcon = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="20" height="20" rx="4" fill="url(#pattern0_4090_2480)" />
        <defs>
            <pattern id="pattern0_4090_2480" patternContentUnits="objectBoundingBox" width="1" height="1">
                <use xlinkHref="#image0_4090_2480" transform="translate(-0.0326087) scale(0.0108696)" />
            </pattern>
            <image
                id="image0_4090_2480"
                width="98"
                height="92"
                xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGIAAABcCAYAAACV1WDTAAAMP2lDQ1BJQ0MgUHJvZmlsZQAASImVVwdYU8kWnluSkEBooUsJvQnSCSAlhBZAehFshCRAKDEGgoodXVRw7WIBG7oqotgBsSOKhUWw98WCgLIuFuzKmxTQdV/53nzf3PnvP2f+c+bcmXvvAKB2miMS5aLqAOQJC8RxoYH0sSmpdFI3wAAJaAIDYMLh5ouYMTGRAJah9u/l3U2ASNtrDlKtf/b/16LB4+dzAUBiIE7n5XPzID4MAF7JFYkLACBKefOpBSIphhVoiWGAEC+S4kw5rpTidDneL7NJiGNB3AyAkgqHI84EQLUd8vRCbibUUO2H2EnIEwgBUKND7JeXN5kHcRrENtBGBLFUn5H+g07m3zTThzU5nMxhLJ+LrCgFCfJFuZzp/2c6/nfJy5UM+bCCVSVLHBYnnTPM2+2cyRFSrAJxnzA9KhpiTYg/CHgye4hRSpYkLFFujxpy81kwZ0AHYiceJygCYkOIQ4S5UZEKPj1DEMKGGK4QdJqggJ0AsR7Ei/j5wfEKmy3iyXEKX2hdhpjFVPAXOGKZX6mvh5KcRKZC/3UWn63Qx1SLshKSIaZAbFEoSIqCWBVix/yc+AiFzeiiLFbUkI1YEieN3wLiOL4wNFCujxVmiEPiFPaleflD88W2ZAnYUQp8sCArIUyeH6yZy5HFD+eCtfOFzMQhHX7+2MihufD4QcHyuWM9fGFivELng6ggME4+FqeIcmMU9rgZPzdUyptB7JZfGK8YiycVwAUp18czRAUxCfI48aJsTniMPB58OYgELBAE6EACazqYDLKBoK2vvg/eyXtCAAeIQSbgAwcFMzQiWdYjhNd4UAT+hIgP8ofHBcp6+aAQ8l+HWfnVAWTIegtlI3LAM4jzQATIhfcS2SjhsLck8BQygn9458DKhfHmwirt//f8EPudYUImUsFIhjzS1YYsicHEIGIYMYRoixvgfrgPHgmvAbC64Azca2ge3+0JzwgdhMeEG4ROwp1JgmLxT1GOAZ1QP0SRi/Qfc4FbQU13PBD3hepQGdfBDYAD7gb9MHF/6NkdsixF3NKs0H/S/tsMfngaCjuyExkl65IDyDY/j1S1U3UfVpHm+sf8yGNNH843a7jnZ/+sH7LPg23Ez5bYIuwQ1oKdwS5ix7F6QMdOYQ1YK3ZCiodX11PZ6hryFieLJwfqCP7hb+jJSjOZ71Tj1Ov0Rd5XwJ8mfUcD1mTRdLEgM6uAzoRfBD6dLeQ6jqS7OLm4AiD9vshfX29iZd8NRKf1Ozf/DwB8Tw0ODh77zoWfAuCAJ9z+R79zNgz46VAG4MJRrkRcKOdw6YUA3xJqcKfpA2NgDmzgfFyAB/ABASAYhINokABSwEQYfRZc52IwFcwE80AJKAPLwRqwAWwG28AusBccBPXgODgDzoPLoB3cAPfg6ukCL0A/eAc+IwhCQqgIDdFHTBBLxB5xQRiIHxKMRCJxSAqShmQiQkSCzETmI2XISmQDshWpRg4gR5EzyEWkA7mDPEJ6kdfIJxRDVVAt1Ai1QkehDJSJRqAJ6AQ0E52CFqEL0KXoOrQK3YPWoWfQy+gNtBN9gQ5gAFPGdDBTzAFjYCwsGkvFMjAxNhsrxcqxKqwWa4TP+RrWifVhH3EiTsPpuANcwWF4Is7Fp+Cz8SX4BnwXXoc349fwR3g//o1AJRgS7AneBDZhLCGTMJVQQign7CAcIZyDe6mL8I5IJOoQrYmecC+mELOJM4hLiBuJ+4iniR3EJ8QBEomkT7In+ZKiSRxSAamEtJ60h3SKdJXURfqgpKxkouSiFKKUqiRUKlYqV9qtdFLpqlK30meyOtmS7E2OJvPI08nLyNvJjeQr5C7yZ4oGxZriS0mgZFPmUdZRainnKPcpb5SVlc2UvZRjlQXKc5XXKe9XvqD8SPmjiqaKnQpLZbyKRGWpyk6V0yp3VN5QqVQragA1lVpAXUqtpp6lPqR+UKWpOqqyVXmqc1QrVOtUr6q+VCOrWaox1SaqFamVqx1Su6LWp05Wt1JnqXPUZ6tXqB9Vv6U+oEHTcNaI1sjTWKKxW+OiRo8mSdNKM1iTp7lAc5vmWc0nNIxmTmPRuLT5tO20c7QuLaKWtRZbK1urTGuvVptWv7amtpt2kvY07QrtE9qdOpiOlQ5bJ1dnmc5BnZs6n3SNdJm6fN3FurW6V3Xf643QC9Dj65Xq7dO7ofdJn64frJ+jv0K/Xv+BAW5gZxBrMNVgk8E5g74RWiN8RnBHlI44OOKuIWpoZxhnOMNwm2Gr4YCRsVGokchovdFZoz5jHeMA42zj1cYnjXtNaCZ+JgKT1SanTJ7TtelMei59Hb2Z3m9qaBpmKjHdatpm+tnM2izRrNhsn9kDc4o5wzzDfLV5k3m/hYnFGIuZFjUWdy3JlgzLLMu1li2W762srZKtFlrVW/VY61mzrYusa6zv21Bt/G2m2FTZXLcl2jJsc2w32rbboXbudll2FXZX7FF7D3uB/Ub7jpGEkV4jhSOrRt5yUHFgOhQ61Dg8ctRxjHQsdqx3fDnKYlTqqBWjWkZ9c3J3ynXa7nTPWdM53LnYudH5tYudC9elwuW6K9U1xHWOa4PrKzd7N77bJrfb7jT3Me4L3Zvcv3p4eog9aj16PS080zwrPW8xtBgxjCWMC14Er0CvOV7HvT56e3gXeB/0/svHwSfHZ7dPz2jr0fzR20c/8TXz5fhu9e30o/ul+W3x6/Q39ef4V/k/DjAP4AXsCOhm2jKzmXuYLwOdAsWBRwLfs7xZs1ing7Cg0KDSoLZgzeDE4A3BD0PMQjJDakL6Q91DZ4SeDiOERYStCLvFNmJz2dXs/nDP8FnhzREqEfERGyIeR9pFiiMbx6BjwsesGnM/yjJKGFUfDaLZ0auiH8RYx0yJORZLjI2JrYh9FuccNzOuJZ4WPyl+d/y7hMCEZQn3Em0SJYlNSWpJ45Oqk94nByWvTO4cO2rsrLGXUwxSBCkNqaTUpNQdqQPjgsetGdc13n18yfibE6wnTJtwcaLBxNyJJyapTeJMOpRGSEtO2532hRPNqeIMpLPTK9P7uSzuWu4LXgBvNa+X78tfye/O8M1YmdGT6Zu5KrM3yz+rPKtPwBJsELzKDsvenP0+JzpnZ85gbnLuvjylvLS8o0JNYY6webLx5GmTO0T2ohJR5xTvKWum9IsjxDvykfwJ+Q0FWvBHvlViI/lF8qjQr7Ci8MPUpKmHpmlME05rnW43ffH07qKQot9m4DO4M5pmms6cN/PRLOasrbOR2emzm+aYz1kwp2tu6Nxd8yjzcub9XuxUvLL47fzk+Y0LjBbMXfDkl9BfakpUS8Qltxb6LNy8CF8kWNS22HXx+sXfSnmll8qcysrLvizhLrn0q/Ov634dXJqxtG2Zx7JNy4nLhctvrvBfsWulxsqilU9WjVlVt5q+unT12zWT1lwsdyvfvJayVrK2c13kuob1FuuXr/+yIWvDjYrAin2VhpWLK99v5G28uilgU+1mo81lmz9tEWy5vTV0a12VVVX5NuK2wm3Ptidtb/mN8Vv1DoMdZTu+7hTu7NwVt6u52rO6erfh7mU1aI2kpnfP+D3te4P2NtQ61G7dp7OvbD/YL9n//EDagZsHIw42HWIcqj1sebjyCO1IaR1SN72uvz6rvrMhpaHjaPjRpkafxiPHHI/tPG56vOKE9ollJyknF5wcPFV0auC06HTfmcwzT5omNd07O/bs9ebY5rZzEecunA85f7aF2XLqgu+F4xe9Lx69xLhUf9njcl2re+uR391/P9Lm0VZ3xfNKQ7tXe2PH6I6TV/2vnrkWdO38dfb1yzeibnTcTLx5+9b4W523ebd77uTeeXW38O7ne3PvE+6XPlB/UP7Q8GHVH7Z/7Ov06DzxKOhR6+P4x/eecJ+8eJr/9EvXgmfUZ+XdJt3VPS49x3tDetufj3ve9UL04nNfyZ8af1a+tHl5+K+Av1r7x/Z3vRK/Gny95I3+m51v3d42DcQMPHyX9+7z+9IP+h92fWR8bPmU/Kn789QvpC/rvtp+bfwW8e3+YN7goIgj5sh+BTBY0YwMAF7vBICaAgANns8o4+TnP1lB5GdWGQL/CcvPiLLiAUAt/H+P7YN/N7cA2L8dHr+gvtp4AGKoACR4AdTVdbgOndVk50ppIcJzwJaor+l56eDfFPmZ84e4f26BVNUN/Nz+C6M2fF0HjsaGAAAAimVYSWZNTQAqAAAACAAEARoABQAAAAEAAAA+ARsABQAAAAEAAABGASgAAwAAAAEAAgAAh2kABAAAAAEAAABOAAAAAAAAAJAAAAABAAAAkAAAAAEAA5KGAAcAAAASAAAAeKACAAQAAAABAAAAYqADAAQAAAABAAAAXAAAAABBU0NJSQAAAFNjcmVlbnNob3TmEhNRAAAACXBIWXMAABYlAAAWJQFJUiTwAAAB1GlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iWE1QIENvcmUgNi4wLjAiPgogICA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPgogICAgICA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIgogICAgICAgICAgICB4bWxuczpleGlmPSJodHRwOi8vbnMuYWRvYmUuY29tL2V4aWYvMS4wLyI+CiAgICAgICAgIDxleGlmOlBpeGVsWURpbWVuc2lvbj45MjwvZXhpZjpQaXhlbFlEaW1lbnNpb24+CiAgICAgICAgIDxleGlmOlBpeGVsWERpbWVuc2lvbj45ODwvZXhpZjpQaXhlbFhEaW1lbnNpb24+CiAgICAgICAgIDxleGlmOlVzZXJDb21tZW50PlNjcmVlbnNob3Q8L2V4aWY6VXNlckNvbW1lbnQ+CiAgICAgIDwvcmRmOkRlc2NyaXB0aW9uPgogICA8L3JkZjpSREY+CjwveDp4bXBtZXRhPgod9OxjAAAAHGlET1QAAAACAAAAAAAAAC4AAAAoAAAALgAAAC4AAAlbTTOGGAAACSdJREFUeAHsW1lPG1cUPoABQ8IS9jUQICakQCDQEqVtVqVtVFV96C/rY6VKfav60KhVo6SobZoGZYEEAgESAoGwOew7BrM6Pd819zIee7DxFiP5SKO5nuXOnfPds313HPOehaLywTUQEwXig2MgBhAFIjJwIK9AOBwOwgYPhg3tqBhrIDY2VpzEPiYmhuLi4owv1pzxCsTu7i5hk4BIUDR9RJsaDUgATCaTACI+Pl5z1rjpBoR25u/s7LhYA7rB+agYawBWAJF7WATAkcDI4/oeDIEACFog9DdGf/umAQlEQkKCAMdnIOCGtre3lSuKWoBvCje6CorHBkCwGbkqN4uAFWxtbSmXZPSA6HHfNQAgpHuCZXgSBQSCsAzMACNqCZ7U5f8xCQYsQrorbW8uQAAAgIF9VIKvAViFBAJgaMUFiI2NjWitoNVOkNtaq9DHCgUELAFAQKJFW5AR2OsOQGADCPpYoYCAO7Lb7aEZQbRXFw0AhMTERJdjEQ8EkgZkcTu7Dt52yJyQyC/hOfPAm21ubooX3OB7THEmOpac5PLCkfDjSAKxtr5Oi0vLtLJqE1v5qZOUnZnpUZ877F6npmcYMOxnKTXlOJ2tPO3x2g958EgCMTs/T/2Dw2TfsHMM2xSKzcvJpuSkJEUjSKXaOcZ1vOil95yKL62sCMAuNNbL0xGzP5JAvOp/Q3//90ApsaHuHJUWFwkl610UrOann39R1xYV5NN333ytfkdKI+KAmGQ3gmwNCjMSPRBlpaUMQgZlZ2VSoqZKBQiwiIetbaorb0DA2jY3tygvN4fjiWterzoJQSPigOjseSkUcZD70AORkZ5OycnJlMVxQgvEu8nJvdgwrVTnDQj0DQDraz46MAFQHQapETFArKyu0jZbwoOHT0TtAqXGmeKopKhIVPU225p65dmFeRoYHFK/EzlrQlVq5sxJW52urdvJwRnWBscSKbi2rLSEg3YKA5fBccVMieZEmptboFUGwMrgrXMycOWzi+J4JoMcDokYIKRL+PPefaEIvDyUVldTLSxkjpUvBYpaWFqSP/3aI8uCS0MWhW1oZJTm5hdodo5d09YmfXX9mkhzD3KRfj3Y4KaIBgI5/4n0NEGxrNudFT7eAzEEypICt2TWFEOY7eBwlpaX5SWCGdD+BsgAABVtPK+crdpstMGxwVmf7ESB0FqE0qKXhowR8rLcHGegRYyQ4qwj9mOFPG60j1qExjVJJSXEJ1BaWqqoE0o4TZWCChm1Q3y8SViAPI7ZDtFaDbiy7e0dQtwYHX8nzsEKUBzC1emlqaFBBGsE7XBIRLsmqQAoFqlpynHXqjiFXUsau6HDyDInBX39g0L5i+y6kCRg00tddY0A4qDsTX9PIL/DBgSCK2aikfS/GaTVtTWPsxOB9ZOG8yIrymO3IyU2LpZimbk8rKBOQDblcOzSxNQMTUxO06jVyrTJoupKEnBN/FxIfm62SBpeDgwyt5UgYhfiFwrJYEjYgEBWNDQ8ZjjmvoEBjzMTNwCISxcviBlqxCkZduzlhHVikqwTU/R2ZIQwRr3ARUGKCvLYnW1RF9c5UBrGgfS34lSJ/ha/focNiNFxK7V3dYsZf1DqmZebKyrapoZ64c+b7/2rXsxbMaYuDKAhC7qunl6XGKPtUrpK1DiN9bXaU363wwYECqXO7pciTZydm3MbsAywhUxtxHE6eb7mrBOIf+4rN5LPtMO3N7/kos3E3wSFhn4YHB4VY+zs7hWprDbgy0EnmZMoJzuLThYV0PnaGnk4oH3YgJDU9QhbRkfXC7dBV1kqRV6vpRaQ4TRzFgVloNBKOZ7CTKslqC7BbSB7ByTV0tbR4XZJKMYRNiDsXJDZ1tdoeHSMnjzbfzkUbaAlaqvPUnpqKlkqyhTZBiAetT2jNV4lHBsf51Q1mUqKiyk3J4vO4Dq2HC2l4aaxAA4MDL1lamWXWh63il60loG6BYxvxol0Av0eDAkbEHKwRoTdpYtNIgDK67CXizqzTD20PH6iTjl98znyJ31VnfjYuHX7rrjSOjGh7ghFrAobEEgZV2yrgtNpa3+uXspSXi4AqCgvFRahTnADRdgiL+aAA2p51KqoDaSwlopykVJq01ntvcFqt3d2i64ePX2qukTGdOPqJcGFgSYJhoQNCJm+Yo9UUcqNK5e9Ll3inpbHbSpWgFuCe6iynPZ6r3xOoPvvf/hRdXFkYgRyc4fjPdm4QJOCZcqJySnxVQjSVxBzqJQ/rj/Hfv/goggxYnQMBdcSp8AvCJSHmalrAFHF68/JZrMg7+SzQrH/9Y87wiKnZmaEJaCGyM7MIqyXo76AxSeJcZiEhetXCr2NKSQW0dreKfz7NA9aip66LszP5yKpQLyIr0UaAL51+47skoGwCItAhXuMrSSUgndyFnQ96jEYN6h0UCTgrU6kpYkFKnyccFiXFRIg/rrf4gbEFhNu2gWafSWm+/x5C1zUA44V+NYKVgUgC/PzGMwSt0CvtBWkBiaBM53eLzDhIqF8UOcAKT8vT8S5iAECmYY32hkczoVGJ4/jq64kYSfjDNwbtsa6Wq/uzddnHHSd/kME/bXOtfNMYaURYRGgM7b4/xSv3wwJSgN+VUpqSqooyCq5DrCUl8nDPu0BLr5nwuzs7XstllCR62MGYg2imKvyw/pmnx68dxFi3m93mtkCtjn+2dStZq60seRaXXVGfPSQyrHvsOMIiWvCCBG8UJ1iFvUN9KtBS78KEs3fZUjJW0kKO5CZqAbmQ0Nf6ctbgmGZIQMC/zAatU7QwuIiV9LtcszCl0NxgQCB+ICv9rDOjFQYFTe4JwRsbaWNrAofIFQe0vLUYHUNxIEh5qJk9iZP433K+YOEQp5ch10fkX2EDAijqjgYFiF5K1TpoM+NJNirbEbvtJ94+J+9hQwIuKYe9uPLKzb256+UrlBJX/v8UzFz/WVQxaIOf4D8rLOLnj7vVH3rG1jcgYUUcnZ1jJdULzONEqggJmEt/Pe7zaqriAfCU4zAoL9geiAY0spUSVvHPl3iqU+QivhqL5nXt29ev+rpkkMfC0U9ExKLwECdZjzrFqyDAQTSV6z2gYjD7JRfcSAmaNPGtg5nYTk1Pa2+kcL5QL8G1wMRqmD9PwAAAP//Sd8hagAACkxJREFU7VvpbxvHFX+SqIMUD92iZFm3qUq+IzlyLMdRkjpNUrRA0Q8F+q1/Uj8WCNAPBYKiQIC2OVsnceVCiQ07tivHli1Zty2Jou77oOi+39BvTK/I5c1KtR6wnOHu7Mzs/Pbds1nPmIjJ7/fTxsYGqjHR1taWajc68ZR2d3fJNztPaxvrNDg0RNnZOZSdlUUtnmb66aWLMfUXqdGUd4YGHo/Q9IyXjxmqLC8np8tFTXW15HLa9W037/SRn+cxPjFB+Xn51OrxkM1mpZbmBt0mkYpvbp4++8cVfavdbid7oY1OH2+janclj5WnruXn5+s20Sp5fI+xfVaiQNy5d5+2trbpxg8/7BnXZrNRSVER1dXUUMfZU3uux3PiwaNBuvKvHn3L5e63qK3lmP4vlSeTU7S2vkFfffOtnMpI2eppIafDTmdPHufFDYISbeD/CyDeebOLPE2NhIfJYq4T8s3NKSD+9sVXcioj5SsLRPuZ01R/tIbKS0v3vIHLK6v0x4//nBEAZJD/ORBjE09ox79Lw6OjXPppcsqrdMXW9paSf4Usnxvr6qir85zMOaFyYGiYenqvKx22vbPN3NBE7soKxRUYY2NjkwLPArTO+g2iCRxhybFQkcup5gGxkQytrK4SxJ6QzWojm7VA9W3JyaEGfsZC1hn1tTU8bo40My1TKppCRwrK5qsEEHyzc/oSFOZ7b1/S/xOpAPBbd/toeWVFHdA90EGXLnQqroAyh7HwZHKax9+mu/fuKWV95uQJJbvD6ZN45gEQPvn0c33LkaoqqqmupuIiF+FFCMeZunGEStqAwAJcv3mblvntAYcIpQIIWEqPBodp2uejaa9XgVDASrG5oYGtJictLi9TIPCMFhYXtdVUyEB1X+yiAtYjNUeqZDoJlZGAqKosJztzm7OwkHJzc+PqO21AwGyc5jcTpt61777Xk0oFEGvr67zISwTrqX9gQPdtVnE6HPS73/7GrEnM1yIB0dRQq7gh5o5CGmYciObGRrp4/hy/mfl7FGvIvEyrsQKBxYeMrqyoIGtBAb35xuum/Ua7GAgEaGVtjbwzPvry6xcmcWN9PTXV11EV+xDFrIcSoYwDEfQjTpODWdjFC5UIxQoE5DYeEPZ8DgNSxQo9GTLjcuidoI6wJTRE2oBQOuLWbWW1wLMWgmKtZVOzjg+YnInQPMt+KOLRsXEaGR9X4sDBoB6trlILL31CcQKAYh4zOztLcYVcS6Tc2dmh/sEhWlpaptt9fbqLhtpaqmevvqbKTSXFRfp8PJW0ARHJahIPu9VzLKw3HMvk4agNjYwzGJP0dGqKIBpgqeCtTNY0NRsfUQNED+Cb9A880k3Fatp3OgJKdGNzk27evksIW8HWF4I9j7e048wpDnWcltMxlRBJYxzHgjk8PDamwikwjy+8fo7jR01sy1vJYonNbo9pQEMjvFx/+evflSW2znMRQhwLoYwKjnnZ2WI6xTGnYld8YjctHHH91h1tv8tkjWVn+2t0vuM142nT/0vsN/Q/esyWGAMRYhJHijWZdpbAxWheutGfiWeItAEBxQaxIbTJ3u7i8pL8ZR1xVOmJuqNHVDBQXzCpwGz87J9f0y577/5dP7nZGnJXVJKHo6nJKmKTYdWlcAFNa4GVXCFWUjFHgCF6j7e2ULFzn3AEgPCy4yUEVoaSFUpErqbDfpf5RCvDcbnoO7lXgEhEV6WFI2RioaWEJYyAmAXs5H4RSaIkoZihoGuq3Xwk5yXLGNHK3//hI93EYXewYeChstIS9ubr9PlkKhkDYnLaS3fv99MSc8XM7KyeM+I/4A6YnuFi90g2IUzy4OFjnWSCSPKwcoY4cleU677SUYGBAProTx/r7qELTrS1BvMrLFpTQRkDQiZrTOqYKbhI5mIqwiQyn2jlJ59+oZrAVBYCF/76Fz+XvykpMwaEnxVsMG06TL03burJIwwBTnijo4NKSoo4emnToWMAAdm8srbKfsOICmVb2UQFN1zs7NB9pKMCToCe+5yNA1BoBLna7aZfvn+ZncTsuIN7keaaMSDECTOanjKxcMkUo1OYDtks4xvLcFaStIGOunThPFnZcy9lMZUKyhgQXt8sPRwYormFeZp4+nTP3Gs5l11oK6QTrR5+yywqVIHEztV/99LOtl+ZvhBjUO4II6RLNyCEjlDGj/0DXPpf8qBl0giZdPA84MWnyljIGBCRrCZ5OCndlZVKNEGBS1JHrqVDNkvfUvZ8d4Nm2WFECB++SjiCJ11eVpqSjRDSf8aAgHP3n/sP1fac+YVFlclCSAKLjW07ixxIw4MXOV2UnZPFac0iJaNlK4zDXkjl5WX0Xndy2T15cCnx9m/zActsm3VS34OHPJdFPlY4ubRLZSUl0lSXFouFo8fBIOPJtp/o88lUMgaE6Aj4EQtLS2yyBn2AeU7wrHMMBwkepD7DkegGiIJk05zG/uGjrHAQb2h0jDlhXillxK+EOtvbpbqnPJB+BBYbnjXkLgKCWFTkJDY3t5RMhlkLIJB0MYoEiAI8tNPhZCCaVRihhPPD8ZAkdeQezAdZvmDee1W9HHhJcF7CJwhOmsXDgp51fPOQ8Y1lxjjCOLDxfxCIVU70/8ji6sUbGdpOOCORN1GSOtJf0Hobfw7EXk48c+KkMqvPd5yVW9Ja7hsgEEeCudrT+z1zTPhtngihIwuGScP/EEICCFxjRkEgvLoJdBNCJtBPoWF6adDdxRsNCvI5vN4op9Ja7hsg8IbCgfvym6ssHl7E+mN5evHOzdoagTBri2vvv/uOMihSZZ5GG29fA5Gbyx73uXalQ2DFwDOHFWWkPG4XutXSeB3/jQkqbPgFV8FywwYxbNFZWFzQtx4CEcIRUIYfvPs2bTKnwKKR6KterSQqEsJ28E5uAIJEE7hS6JUFIsAp1cBuQO3gg4g6e6pNrQneVhB27oGgyIdHx6knZK+UixMy2C5TUlxMeZYXG7umfcEdf76QaC9EzWX2RbI4ToTPBORzAVhKGGNgaITW1jbUjnVsr09n6lU90POffSOaZFIS4zGzViJFcMuwCZkXTwhOpFE3RPPOxXqLZ0u9jJdMue+AiOVhjEBEylmLJRb6fUQ0IGIZPx1tDiQQ2A3+7bVeFYbO4v1K3V0X6Fhj/Z71gQKGSXzl6jV9rZKTSb/68Gf6/36pHEggJFwC7xyH2Q47SS7JgqcjTCJ9J1MeSCAQn0I0F9+tIRjo4kChlZ2vcAQgHgwM6kswVzPlpOlBY6gcSCBieK4D18QUCJhz8lXp8w9ND9wD7vcJiyMKIHCEkv6qFBHLTY6UAgTUDyn1KwAgJPdt/LjlJSCwnQVAiEOV+qm82j0CCITbAQISTqH0EhCITgIElIeU+hUQbgAYOEJJAyEiCUBsc9gYdKgrQpcq8broBiw+dIOIqNAeNRByUsXsGQjoiUMgZFWSK2XhIY6MSlp63gMEAABXyAEwDgGR5Yq/hDgCENALqBtFkvS4BwhZeHAGjkPOkKVKrMTCAwARSSKmjL39F01L96HNf1ZAAAAAAElFTkSuQmCC"
            />
        </defs>
    </svg>
);
