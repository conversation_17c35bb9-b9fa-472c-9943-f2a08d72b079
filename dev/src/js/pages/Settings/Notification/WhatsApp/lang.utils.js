import React from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { MenuIcon, GearIcon } from './utils';

export const useTranslationHook = () => {
    const { t, ready, i18n } = useTranslation(['Pengaturan/whatsAppSetting', 'translation']);

    const TransComponent = ({ i18nKey, children, components }) => (
        <Trans t={t} i18nKey={i18nKey} components={components}>
            {children}
        </Trans>
    );

    const LANG_KEY = {
        ALERT_DELETE_DESC: 'alert.delete.desc',
        ALERT_DELETE_DESC2: 'alert.delete.desc2',
        ALERT_CLOSE_DESC: 'alert.closeEditDialog.desc',
        ALERT_SAVE_DESC: 'alert.confirmSave.desc',
        ALERT_ACTIVATE_DESC: 'alert.activate.desc',
        ALERT_DEACTIVATE_DESC: 'alert.deactivate.desc',
        CARD_DESC: 'card.desc',
        LAST_SEND: 'field.status.lastTest',
        TOAST_ADD_SUCCESS: 'toast.registerSuccess',
        TOAST_ADD_FAILED: 'toast.registerFailed',
        TOAST_DELETE_SUCCESS: 'toast.deleteSuccess',
        TOAST_DELETE_FAILED: 'toast.deleteFailed',
        TOAST_ACTIVATE_SUCCESS: 'toast.activateSuccess',
        TOAST_DEACTIVATE_SUCCESS: 'toast.deactivateSuccess',
        TOAST_DEACTIVATE_FAILED: 'toast.deactivateFailed',
    };

    const retval = {
        LANG_KEY,
        LANG_DATA: {
            TITLE: t('title', 'Pengaturan Nomor WhatsApp'),
            BUTTON: {
                CHECK_AUTH: t('button.checkAuth', 'Cek Autentikasi'),
                ADD_WHATSAPP: t('button.addWhatsApp', 'Tambah Nomor'),
                SEND_MESSAGE: t('button.sendMessage', 'Kirim Pesan Testing'),
            },
            PLACEHOLDER: {
                SEARCH: t('translation:placeholder.search', 'Cari...'),
            },
            FILTER: {
                ALL_CATEGORY: t('filter.allFunctions', 'Semua Fungsi'),
                RESERVATION: t('filter.reservation', 'Reservasi'),
                LOYALTI: t('filter.loyalti', 'Loyalti'),
                ALL_STATUS: t('translation:select.showOnMenuOption.semua', 'Semua'),
                ACTIVE: t('translation:status.active', 'Aktif'),
                INACTIVE: t('translation:status.inactive', 'Tidak Aktif'),
                REPORT: t('filter.report', 'Laporan'),
                MERCHANT: t('filter.merchant', 'Notifikasi Merchant'),
                CUSTOMER: t('filter.customer', 'Notifikasi Pelanggan'),
            },
            CARD: {
                LOYALTI: t('card.loyalti.title', 'Kuota Nomor Loyalti'),
                RESERVATION: t('card.reservation.title', 'Kuota Nomor Reservasi'),
                REPORT: t('card.report.title', 'Kuota Nomor Laporan'),
                MERCHANT: t('card.merchant.title', 'Kuota Nomor Notifikasi Merchant'),
                CUSTOMER: t('card.customer.title', 'Kuota Nomor Notifikasi Pelanggan'),
                LOYALTI_TOOLTIP: t(
                    'card.loyalti.tooltip',
                    'Kuota nomor didapatkan karena berlangganan addon Loyalti, terhitung per Outlet',
                ),
                RESERVATION_TOOLTIP: t(
                    'card.reservation.tooltip',
                    'Kuota nomor didapatkan karena berlangganan paket Prime, terhitung per Outlet',
                ),
            },
            COLUMN: {
                NAME: t('column.name', 'Nama'),
                WHATSAPP_NUMBER: t('column.whatsAppNumber', 'Nomor Whatsapp'),
                AUTH: t('column.auth', 'AUTENTIKASI'),
                FUNCTION: t('column.func'),
                DESCRIPTION: t('column.desc', 'Deskripsi'),
                ACTIVE: t('column.active', 'Active'),
                INACTIVE: t('column.inactive', 'Inactive'),
                LOGIN: t('column.login', 'Connected'),
                LOGOUT: t('column.logout', 'Disconnected'),
                NOT_CONNECTED_YET: t('column.notConnectedYet', 'Not Connected Yet'),
            },
            TABLE_ACTION: {
                EDIT: t('action.edit', 'Ubah'),
                DEACTIVATE: t('action.deactivate', 'Nonaktifkan'),
                ACTIVATE: t('action.activate', 'Aktifkan'),
                RE_AUTH: t('action.reauth', 'Lakukan Autentikasi'),
                DELETE: t('action.delete', 'Hapus'),
            },
            TOAST: {
                ERROR: t('translation:toast.error', 'Gagal!'),
                SUCCESS: t('translation:toast.success', 'Berhasil!'),
                EDIT_ERROR: t('toast.editFailed', 'Informasi nomor Whatsapp gagal diperbarui'),
                EDIT_SUCCESS: t('toast.editSuccess', 'Informasi nomor WhatsApp berhasil diperbarui'),
                ACTIVATE_FAILED: t('toast.activateFailed', 'Hanya dapat mengaktifkan 2 nomor dalam 1 fungsi'),
                LOGIN_SUCCESS: t('toast.loginSuccess', 'Autentikasi berhasil dilakukan'),
            },
            DIALOG: {
                ADD_TITLE: t('dialog.title.add', 'Tambah Nomor WhatsApp'),
                EDIT_TITLE: t('dialog.title.edit', 'Ubah Informasi Nomor WhatsApp'),
                AUTH_TITLE: t('dialog.title.auth', 'Autentikasi Nomor WhatsApp'),
            },
            LABEL: {
                CANCEL: t('translation:label.cancel', 'Batal'),
                BACK: t('translation:label.back', 'Kembali'),
                SAVE: t('translation:label.save', 'Simpan'),
                NEXT: t('translation:label.nextThen', 'Selanjutnya'),
                CONTINUE: t('translation:label.continue'),
            },
            ALERT: {
                TITLE: {
                    DELETE: t('alert.delete.title', 'Hapus Nomor'),
                    CLOSE: t('alert.closeEditDialog.title', 'Batal Ubah'),
                    CONFIRM: t('alert.confirmSave.title', 'Simpan Perubahan'),
                    ACTIVATE: t('alert.activate.title', 'Aktifkan Nomor'),
                    DEACTIVATE: t('alert.deactivate.title', 'Nonaktifkan Nomor'),
                },
                DESC: {
                    ACTIVATE: t(
                        'alert.activate.desc',
                        'Nomor yang dinonaktifkan akan mempengaruhi pengaturan yang terintegrasi sebelumnya dan menjadi tidak aktif. Lanjutkan?',
                    ),
                    DEACTIVATE: t(
                        'alert.deactivate.desc',
                        'Nomor yang aktif dapat digunakan sebagai pengirim pesan Whatssapp. Lanjutkan?',
                    ),
                },
            },
            FORM: {
                TITLE: {
                    REGISTER: t('form.register.title'),
                    AUTH: t('form.auth.title'),
                    COMPLETE: t('form.complete.title'),
                },
                DESC: {
                    REGISTER: t('form.register.desc'),
                },
                BANNER: {
                    AUTH: t('form.auth.banner'),
                    COMPLETE: t('form.complete.banner'),
                },
            },
            AUTH: {
                TITLE: t('form.auth.title2'),
                INSTRUCTION: [
                    () => <Trans t={t} i18nKey="form.auth.instruction.1" />,
                    () => (
                        <Trans
                            t={t}
                            i18nKey="form.auth.instruction.2"
                            components={{ icon1: <MenuIcon />, icon2: <GearIcon /> }}
                        />
                    ),
                    () => <Trans t={t} i18nKey="form.auth.instruction.3" />,
                    () => <Trans t={t} i18nKey="form.auth.instruction.4" />,
                ],
                NEED_HELP: t('form.auth.needHelp'),
                SEE_TUTORIAL: t('form.auth.seeTutorial'),
                VERIFIED: t('form.auth.verificationSuccess'),
            },
            FIELD: {
                USED_FOR: {
                    LABEL: t('field.usedFor.label', 'Fungsi Nomor'),
                    DESC: t('field.usedFor.desc', 'Fungsi nomor WhatsApp yang didaftarkan'),
                    ERROR: t('field.usedFor.error', 'Fungsi nomor wajib diisi'),
                },
                OUTLET: {
                    LABEL: t('field.outlet.label', 'Outlet'),
                    DESC: t('field.outlet.desc', 'Pilih outlet yang dapat menggunakan nomor ini'),
                    ERROR: t('field.outlet.error', 'Outlet wajib diisi'),
                },
                LABEL: {
                    LABEL: t('field.label.label', 'Label Nomor'),
                    ERROR: t('field.label.error', 'Label Nomor wajib diisi'),
                    ERROR_USED: outlet =>
                        t('field.label.errorUsed', 'Label nomor telah digunakan pada Outlet {{outlet}}', {
                            outlet: outlet || t('label.otherMenu', 'Lainnya', { ns: 'translation' }),
                        }),
                },
                PHONE: {
                    LABEL: t('field.phone.label', 'Nomor Broadcast'),
                    LABEL2: t('field.phone.label2', 'Kode Negara'),
                    LABEL3: t('field.phone.label3', 'Nomor Ponsel'),
                    DESC: t(
                        'field.phone.desc',
                        'Untuk menghindari nomor diblokir, gunakan nomor WhatsApp yang aktif lebih dari 3 bulan',
                    ),
                    ERROR: t('field.phone.error', 'Nomor ponsel wajib diisi'),
                    ERROR_MIN: t('field.phone.errorMin', 'Wajib mengisi 9 digit nomor ponsel'),
                    ERROR_USED: outlet =>
                        t('field.phone.errorUsed', 'Nomor ponsel telah digunakan pada Outlet {{outlet}}', {
                            outlet: outlet || t('label.otherMenu', 'Lainnya', { ns: 'translation' }),
                        }),
                    ERROR_USED_OTHER_MERCHANT: t(
                        'field.phone.errorUsedOtherMerchant',
                        'Nomor ponsel telah digunakan pada Merchant lain',
                    ),
                    HELPER: t('field.phone.helper', 'Nomor ponsel tidak bisa diubah setelah disimpan.'),
                },
                DESCRIPTION: {
                    LABEL: t('field.description.label', 'Deskripsi'),
                },
                STATUS: {
                    LABEL: t('field.status.label', 'Status'),
                    DESC: t('field.status.desc', 'Aktifkan untuk dapat menggunakan nomor'),
                    LIMIT_BANNER: limit =>
                        t(
                            'field.status.limitBanner',
                            '{{limit}} nomor aktif pada fungsi terpilih di outlet. Nomor ini akan disimpan dengan status "Tidak Aktif"',
                            { limit },
                        ),
                    LIMIT_UPDATE_BANNER: t(
                        'field.status.limitUpdateBanner',
                        'Perubahan tidak dapat dilakukan karena kuota nomor aktif untuk fungsi ini di outlet sudah tercapai.',
                    ),
                    NOT_SEND: t('field.status.notYet', 'Belum Dilakukan Testing'),
                    SEND: t('field.status.send', 'Tersimpan'),
                    FAIL_SEND: t('field.status.failSend', 'Gagal Terkirim'),
                    CAN_SAVE: t('field.status.canSave', 'Nomor dapat disimpan'),
                },
                SENDER: {
                    LABEL: t('field.sender.label', 'Nomor Ponsel Pengirimr'),
                    DESC: t('field.sender.desc', 'Nomor ponsel telah diverifikasi'),
                },
                RECIPIENT: {
                    LABEL: t('field.recipient.label', 'Nomor Ponsel Penerima'),
                    DESC: t('field.recipient.desc', 'Nomor ponsel yang dituju untuk melakukan testing'),
                    ERROR: t('field.recipient.error', 'Nomor ponsel wajib diisi'),
                },
                TEXT_EXAMPLE: {
                    LABEL: t('field.textExample.label'),
                    ERROR: t('field.textExample.error', 'Pesan test wajib diisi'),
                },
            },
            PLACEHOLDER_EXAMPLE: example => t('translation:placeholder.example') + example,
        },
        t,
        TransComponent,
        ready,
        currentLang: i18n.language,
    };

    return retval;
};
