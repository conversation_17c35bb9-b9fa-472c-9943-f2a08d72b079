import React, { useContext } from 'react';
import { Table } from '@majoo-ui/react';
import { normalizePhoneNumber, removeCountryCodePrefix } from '~/utils/helper';
import { useTranslationHook } from '../lang.utils';
import { columnTable } from '../utils';
import WhatsAppSettingContext from '../context/WhatsAppSettingContext';
import { STATUS_SEND, TABLE_ACTION } from '../enum';

const List = () => {
    const {
        whatsAppList,
        fetchData,
        totalItem,
        totalItemPerPage,
        isLoading,
        alert,
        deleteWhatsApp,
        listBranch,
        summaryQuota,
        setFormData,
        setOpenDialog,
        setAuthDialog,
        whatsAppFeatures,
        addToast,
    } = useContext(WhatsAppSettingContext);
    const { LANG_DATA, LANG_KEY, TransComponent } = useTranslationHook();

    const hanldleRowAction = (act, data) => {
        const tempFormData = {
            accountId: data.account_id,
            usedFor: data.category[0],
            idOutlet: data.outlet_id,
            labelNo: data.label,
            phoneNumber: removeCountryCodePrefix(data.phone_number),
            description: data.deskripsi,
            status: data.status_client,
            isLogin: data.status === 'login',
            sendTest: STATUS_SEND.NOT_SEND,
            timeTest: '',
        };
        setFormData(tempFormData);
        switch (act) {
            case TABLE_ACTION.EDIT:
                setOpenDialog(true);
                break;
            case TABLE_ACTION.RE_AUTH:
                setAuthDialog(true);
                break;
            case TABLE_ACTION.DELETE: {
                if (data.is_have_active_add_on === 1) {
                    addToast({
                        title: LANG_DATA.TOAST.ERROR,
                        description: `Nomor ${normalizePhoneNumber(
                            data.phone_number,
                        )} tidak dapat dihapus karena sedang digunakan sebagai nomor pengirim notifikasi`,
                        variant: 'failed',
                    });
                    return;
                }
                alert({
                    title: LANG_DATA.ALERT.TITLE.DELETE,
                    description: (
                        <TransComponent
                            i18nKey={tempFormData.status ? LANG_KEY.ALERT_DELETE_DESC2 : LANG_KEY.ALERT_DELETE_DESC}
                        >
                            {{ name: tempFormData.labelNo }}
                        </TransComponent>
                    ),
                    type: 'negative',
                    onConfirm: () => deleteWhatsApp(tempFormData),
                });
                break;
            }
            default:
                break;
        }
    };

    return (
        <Table
            id="table-whatsapp-setting"
            data={whatsAppList}
            columns={columnTable(LANG_DATA, listBranch, summaryQuota, hanldleRowAction, whatsAppFeatures)}
            fetchData={fetchData}
            totalData={totalItem}
            isLoading={isLoading}
            rowLimit={totalItemPerPage}
        />
    );
};

export default List;
