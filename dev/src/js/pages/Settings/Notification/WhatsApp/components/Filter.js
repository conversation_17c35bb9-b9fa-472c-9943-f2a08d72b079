import React, { useContext } from 'react';
import { Flex, InputSearchbox, InputSelect, ButtonGroupItem, ButtonGroup, Separator } from '@majoo-ui/react';
import { debounce } from 'lodash';
import WhatsAppSettingContext from '../context/WhatsAppSettingContext';

const Filter = () => {
    const { filter, statusOptions, categoryOptions, setFilter, alert, router } = useContext(WhatsAppSettingContext);
    return (
        <React.Fragment>
            <Separator css={{ margin: '$cozy 0px' }} />
            <Flex
                css={{ flexDirection: 'column', '@lg': { flexDirection: 'row', alignItems: 'center' } }}
                justify="between"
                gap={4}
            >
                <Flex align="center" justify="start" gap={4}>
                    <InputSearchbox
                        onChange={debounce(value => setFilter(prev => ({ ...prev, keyword: value })), 800)}
                        css={{ width: 260 }}
                    />
                    <InputSelect
                        size="sm"
                        value={categoryOptions.find(x => x.value === filter.category)}
                        option={categoryOptions}
                        onChange={option => {
                            if (option.render) {
                                setFilter(prev => ({ ...prev, category: String(prev.category) }));
                                alert({
                                    title: 'Beli Add-On',
                                    description: `Anda akan dialihkan ke halaman pembelian Notifikasi ${option.name}. Lanjutkan?`,
                                    type: 'primary',
                                    onConfirm: () => {
                                        router.push('/support/buy?tab=support');
                                    },
                                });
                            } else {
                                setFilter(prev => ({ ...prev, category: option.value }));
                            }
                        }}
                        css={{
                            width: 320,
                        }}
                    />
                </Flex>
                <ButtonGroup
                    type="single"
                    value={filter.status}
                    onValueChange={val => {
                        if (val) {
                            setFilter(prev => ({ ...prev, status: val }));
                        }
                    }}
                    css={{ border: '1px solid $bgBorder' }}
                >
                    {statusOptions.map(status => (
                        <ButtonGroupItem key={status.value} value={status.value}>
                            {status.name}
                        </ButtonGroupItem>
                    ))}
                </ButtonGroup>
            </Flex>
        </React.Fragment>
    );
};

export default Filter;
