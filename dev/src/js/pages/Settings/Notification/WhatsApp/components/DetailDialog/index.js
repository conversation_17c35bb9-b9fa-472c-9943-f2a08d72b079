import React, { useContext, useMemo } from 'react';
import { PageDialog, <PERSON>lex, Button, DialogClose, Box } from '@majoo-ui/react';
import { TrashOutline } from '@majoo-ui/icons';
import { normalizePhoneNumber } from '~/utils/helper';
import WhatsAppSettingContext from '../../context/WhatsAppSettingContext';
import StepOne from './StepOne';
import { useTranslationHook } from '../../lang.utils';

const DetailDialog = () => {
    const {
        isMobile,
        openDialog,
        setOpenDialog,
        addToast,
        alert,
        formData,
        setFormData,
        handleUpdateData,
        handleRegisterData,
        deleteWhatsApp,
        whatsAppList,
    } = useContext(WhatsAppSettingContext);
    const { LANG_DATA, LANG_KEY, TransComponent } = useTranslationHook();
    const isEdit = useMemo(() => !!formData.accountId, [formData.accountId]);

    const originalFormData = formData.accountId
        ? whatsAppList.find(x => x.account_id === formData.accountId) || {}
        : {};

    const handleCloseDialog = () => {
        alert({
            title: LANG_DATA.ALERT.TITLE.CLOSE,
            description: (
                <TransComponent i18nKey={LANG_KEY.ALERT_CLOSE_DESC}>
                    {{ name: isEdit ? LANG_DATA.DIALOG.EDIT_TITLE : LANG_DATA.DIALOG.ADD_TITLE }}
                </TransComponent>
            ),
            type: 'negative',
            onConfirm: () => setOpenDialog(false),
        });
    };

    const handleDelete = () => {
        if (originalFormData.is_have_active_add_on === 1) {
            addToast({
                title: LANG_DATA.TOAST.ERROR,
                description: `Nomor ${normalizePhoneNumber(
                    originalFormData.phone_number,
                )} tidak dapat dihapus karena sedang digunakan sebagai nomor pengirim notifikasi`,
                variant: 'failed',
            });
            return;
        }
        alert({
            title: LANG_DATA.ALERT.TITLE.DELETE,
            description: (
                <TransComponent i18nKey={formData.status ? LANG_KEY.ALERT_DELETE_DESC2 : LANG_KEY.ALERT_DELETE_DESC}>
                    {{ name: formData.labelNo }}
                </TransComponent>
            ),
            type: 'negative',
            onConfirm: () => deleteWhatsApp(formData),
        });
    };

    const handleSave = async (data, onError) => {
        if (isEdit) {
            await handleUpdateData(data, '', onError);
            return;
        }
        await handleRegisterData(data, onError);
    };

    return (
        <PageDialog open={openDialog} onOpenChange={handleCloseDialog} isMobile={isMobile}>
            <PageDialog.Title>{isEdit ? LANG_DATA.DIALOG.EDIT_TITLE : LANG_DATA.DIALOG.ADD_TITLE}</PageDialog.Title>
            <PageDialog.Content
                css={{
                    '@sm': { padding: 0 },
                    '@md': { padding: 22 },
                    display: 'flex',
                    justifyContent: 'center',
                    gap: 24,
                }}
                wrapperSize="lg"
            >
                <StepOne
                    formData={formData}
                    onSubmit={(data, onError) => {
                        setFormData(prev => ({ ...prev, ...data }));
                        alert({
                            title: LANG_DATA.ALERT.TITLE.CONFIRM,
                            description: (
                                <TransComponent i18nKey={LANG_KEY.ALERT_SAVE_DESC}>
                                    {{ phone: normalizePhoneNumber(data.phoneNumber) }}
                                </TransComponent>
                            ),
                            type: 'primary',
                            onConfirm: async () => {
                                await handleSave(data, onError);
                            },
                        });
                    }}
                    isEdit={isEdit}
                    originalFormData={originalFormData}
                />
            </PageDialog.Content>
            <PageDialog.Footer
                css={{
                    display: 'flex',
                    gap: '$compact',
                    justifyContent: 'center',
                    padding: '12px 24px',
                }}
            >
                <Box
                    css={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        '@sm': {
                            gap: 16,
                            width: '100%',
                        },
                        '@md': {
                            width: '100%',
                            maxWidth: 970,
                        },
                    }}
                >
                    <Box>
                        {isEdit && (
                            <Button buttonType="negative-secondary" onClick={handleDelete}>
                                <TrashOutline color="currentColor" />
                            </Button>
                        )}
                    </Box>
                    <Flex gap={5}>
                        <DialogClose asChild>
                            <Button buttonType="ghost" size="md">
                                {LANG_DATA.LABEL.CANCEL}
                            </Button>
                        </DialogClose>
                        <Button size="md" type="submit" form="informationForm">
                            {LANG_DATA.LABEL.SAVE}
                        </Button>
                    </Flex>
                </Box>
            </PageDialog.Footer>
        </PageDialog>
    );
};

export default DetailDialog;
