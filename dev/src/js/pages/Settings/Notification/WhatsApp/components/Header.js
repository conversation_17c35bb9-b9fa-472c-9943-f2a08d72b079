import React, { useContext } from 'react';
import { Flex, Heading, Button } from '@majoo-ui/react';
import { RefreshFilled, PlusOutline } from '@majoo-ui/icons';
import { TooltipGuidance, FavoriteWrapper } from '../../../../../components/retina';
import WhatsAppSettingContext from '../context/WhatsAppSettingContext';

const Header = () => {
    const { LANG_DATA, fetchData, setOpenDialog, setFormData } =
        useContext(WhatsAppSettingContext);
    const handleAdd = () => {
        setFormData({
            accountId: '',
            usedFor: '',
            labelNo: '',
            phoneNumber: '',
            descripstion: '',
            status: false,
        });
        setOpenDialog(true);
    };

    return (
        <Flex justify="between" css={{ flexDirection: 'column', '@lg': { flexDirection: 'row' } }} gap={4}>
            <FavoriteWrapper>
                <Heading heading="pageTitle">{LANG_DATA.TITLE}</Heading>
                <TooltipGuidance />
            </FavoriteWrapper>
            <Flex gap={4}>
                <Button
                    leftIcon={<RefreshFilled color="currentColor" />}
                    onClick={() => fetchData()}
                    buttonType="secondary"
                    size="sm"
                >
                    {LANG_DATA.BUTTON.CHECK_AUTH}
                </Button>
                <Button
                    leftIcon={<PlusOutline color="currentColor" />}
                    onClick={handleAdd}
                    size="sm"
                >
                    {LANG_DATA.BUTTON.ADD_WHATSAPP}
                </Button>
            </Flex>
        </Flex>
    );
};

export default Header;
