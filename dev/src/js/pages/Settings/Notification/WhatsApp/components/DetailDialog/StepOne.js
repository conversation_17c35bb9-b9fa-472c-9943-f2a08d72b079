import React, { useContext, useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import {
    Heading,
    Paper,
    Box,
    Text,
    FormGroup,
    FormLabel,
    FormHelper,
    InputSelect,
    InputSwitch,
    InputTextArea,
    Flex,
    InputNumber,
    InputText,
    TagStatus,
} from '@majoo-ui/react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { get } from 'lodash';
import WhatsAppSettingContext from '../../context/WhatsAppSettingContext';
import CustomBanner from '../CustomBanner';
import { countryOptions, formLabelStyle } from '../../enum';
import * as whatsAppService from '../../../../../../data/whatsAppSetting';
import { catchError } from '../../../../../../utils/helper';

const StepOne = ({ formData, onSubmit, isEdit, originalFormData }) => {
    const {
        listBranch,
        LANG_DATA,
        addToast,
        showProgress,
        hideProgress,
        fetchWhatsAppFeatures,
        whatsAppFeatures: waFeatures,
        outletId,
        alert,
        router,
    } = useContext(WhatsAppSettingContext);
    const [whatsAppFeatures, setWhatsAppFeatures] = useState(waFeatures);

    const outletOptions = useMemo(
        () => listBranch.filter(x => x.id_cabang).map(({ id_cabang: value, cabang_name: name }) => ({ value, name })),
        [listBranch],
    );
    const usedForOptions = useMemo(
        () =>
            whatsAppFeatures.map(feature => ({
                value: feature.feature_code,
                name: feature.feature_name,
                render: !feature.expired_add_on
                    ? () => (
                          <Flex align="center" justify="between" gap={3}>
                              <Text>{feature.feature_name}</Text>
                              <TagStatus type="new">{LANG_DATA.TAG.BUY_ADDON}</TagStatus>
                          </Flex>
                      )
                    : undefined,
            })),
        [whatsAppFeatures],
    );
    const [summaryQuota, setSummaryQuota] = useState({
        notif_laporan: {
            total: 0,
            limit: 2,
        },
        notif_merchant: {
            total: 0,
            limit: 2,
        },
        notif_customer: {
            total: 0,
            limit: 2,
        },
    });

    const { control, setValue, watch, formState, handleSubmit, reset, setError } = useForm({
        defaultValues: {
            ...formData,
            idOutlet: formData.idOutlet || outletId,
        },
        resolver: yupResolver(
            yup.object({
                usedFor: yup.string().required(LANG_DATA.FIELD.USED_FOR.ERROR),
                idOutlet: yup.string().required(LANG_DATA.FIELD.OUTLET.ERROR),
                labelNo: yup.string().required(LANG_DATA.FIELD.LABEL.ERROR),
                phoneNumber: yup.string().min(9, LANG_DATA.FIELD.PHONE.ERROR_MIN).required(LANG_DATA.FIELD.PHONE.ERROR),
                description: yup.string(),
                status: yup.bool(),
            }),
        ),
    });

    const { errors } = formState;
    const { usedFor, idOutlet } = watch();

    const isDisableStatus = useMemo(() => {
        if (!usedFor) return true;
        if (!summaryQuota[usedFor]) return true;
        return summaryQuota[usedFor].total >= summaryQuota[usedFor].limit;
    }, [usedFor, JSON.stringify(summaryQuota), formData.accountId]);

    const fetchSummary = async () => {
        try {
            showProgress();
            const res = await whatsAppService.getSummaryWhatsApp(idOutlet);
            if (!res.data) throw new Error(res.message);
            setSummaryQuota(
                whatsAppFeatures.reduce((acc, item) => {
                    acc[item.feature_code] = {
                        total: res.data[item.feature_code],
                        limit: res.data[`kuota_${item.feature_code}`],
                    };
                    return acc;
                }, {}),
            );
        } catch (error) {
            addToast({
                title: LANG_DATA.TOAST.ERROR,
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    useEffect(() => {
        if (idOutlet) {
            fetchSummary();
            fetchWhatsAppFeatures(idOutlet, setWhatsAppFeatures, () => setWhatsAppFeatures([]));
            setValue('usedFor', null);
        }
    }, [String(idOutlet)]);

    useEffect(() => {
        reset(formData);
    }, [JSON.stringify(formData)]);

    return (
        <Paper
            css={{
                height: 'max-content',
                '@sm': {
                    width: '100%',
                    padding: '$compact',
                },
                '@md': {
                    width: 982,
                },
            }}
            responsive
        >
            <Box>
                <Heading heading="sectionTitle">{LANG_DATA.FORM.TITLE.REGISTER}</Heading>
                <Text color="secondary">{LANG_DATA.FORM.DESC.REGISTER}</Text>
            </Box>
            <Box
                as="form"
                id="informationForm"
                onSubmit={handleSubmit(data =>
                    onSubmit(data, (key, message) => setError(key, { type: 'custom', message })),
                )}
                css={{
                    marginTop: '$cozy',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '$cozy',
                }}
            >
                <FormGroup responsive="input">
                    <Flex direction="column" gap={2}>
                        <FormLabel variant="required" css={formLabelStyle}>
                            {LANG_DATA.FIELD.OUTLET.LABEL}
                        </FormLabel>
                        <Text color="secondary" variant="helper">
                            {LANG_DATA.FIELD.OUTLET.DESC}
                        </Text>
                    </Flex>
                    <Controller
                        name="idOutlet"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                            <InputSelect
                                isInvalid={!!get(errors, 'idOutlet')}
                                value={outletOptions.find(x => String(x.value) === String(value))}
                                option={outletOptions}
                                onChange={x => onChange(x.value)}
                            />
                        )}
                    />
                    {get(errors, 'idOutlet') && <FormHelper error>{get(errors, 'idOutlet.message')}</FormHelper>}
                </FormGroup>
                <FormGroup responsive="input">
                    <Flex direction="column" gap={2}>
                        <FormLabel variant="required" css={formLabelStyle}>
                            {LANG_DATA.FIELD.USED_FOR.LABEL}
                        </FormLabel>
                        <Text color="secondary" variant="helper">
                            {LANG_DATA.FIELD.USED_FOR.DESC}
                        </Text>
                    </Flex>
                    <Controller
                        name="usedFor"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                            <InputSelect
                                key={`${value}_${idOutlet}`}
                                isInvalid={!!get(errors, 'usedFor')}
                                value={usedForOptions.find(x => String(x.value) === String(value))}
                                option={usedForOptions}
                                onChange={option => {
                                    if (option.render) {
                                        onChange(option.value);
                                        alert({
                                            title: LANG_DATA.ALERT.TITLE.BUY_ADDON,
                                            description: LANG_DATA.ALERT.DESC.BUY_ADDON(option.name),
                                            type: 'primary',
                                            onConfirm: () => {
                                                router.push('/support/buy?tab=support');
                                            },
                                        });
                                        setTimeout(() => {
                                            onChange(null);
                                        }, 50);
                                    } else {
                                        onChange(option.value);
                                        if (
                                            summaryQuota[option.value] &&
                                            summaryQuota[option.value].total >= summaryQuota[option.value].limit
                                        )
                                            setValue('status', false);
                                    }
                                }}
                            />
                        )}
                    />
                    {get(errors, 'usedFor') && <FormHelper error>{get(errors, 'usedFor.message')}</FormHelper>}
                </FormGroup>
                <FormGroup responsive="input">
                    <FormLabel>{LANG_DATA.FIELD.STATUS.LABEL}</FormLabel>
                    <Controller
                        name="status"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                            <Flex gap={4} align="center">
                                <InputSwitch
                                    checked={value}
                                    dataOnLabel="ON"
                                    dataOffLabel="OFF"
                                    onCheckedChange={val => onChange(val)}
                                    disabled={
                                        !summaryQuota[usedFor] ||
                                        (isDisableStatus &&
                                            (!originalFormData.status_client ||
                                                usedFor !== originalFormData.category[0])) ||
                                        originalFormData.is_have_active_add_on
                                    }
                                />
                                <FormHelper>
                                    {!originalFormData.is_have_active_add_on
                                        ? LANG_DATA.FIELD.STATUS.DESC
                                        : LANG_DATA.FIELD.STATUS.ACTIVE_NOTIFICATION_DESC}
                                </FormHelper>
                            </Flex>
                        )}
                    />
                    {get(errors, 'status') && <FormHelper error>{get(errors, 'status.message')}</FormHelper>}
                    {usedFor && isDisableStatus && (
                        <React.Fragment>
                            {!isEdit && (
                                <CustomBanner>
                                    {LANG_DATA.FIELD.STATUS.LIMIT_BANNER(
                                        summaryQuota[usedFor] ? summaryQuota[usedFor].limit : 6,
                                    )}
                                </CustomBanner>
                            )}
                            {isEdit &&
                                (!originalFormData.status_client || usedFor !== originalFormData.category[0]) && (
                                    <CustomBanner>{LANG_DATA.FIELD.STATUS.LIMIT_UPDATE_BANNER}</CustomBanner>
                                )}
                        </React.Fragment>
                    )}
                </FormGroup>
                <FormGroup responsive="input">
                    <FormLabel variant="required">{LANG_DATA.FIELD.LABEL.LABEL}</FormLabel>
                    <Controller
                        name="labelNo"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                            <InputText
                                isInvalid={!!get(errors, 'labelNo')}
                                value={value}
                                onChange={e => onChange(e.target.value)}
                                placeholder={LANG_DATA.PLACEHOLDER_EXAMPLE('Admin')}
                            />
                        )}
                    />
                    {get(errors, 'labelNo') && <FormHelper error>{get(errors, 'labelNo.message')}</FormHelper>}
                </FormGroup>
                <FormGroup responsive="input">
                    <Flex direction="column" gap={2}>
                        <FormLabel variant="required" css={formLabelStyle}>
                            {LANG_DATA.FIELD.PHONE.LABEL}
                        </FormLabel>
                        <Text color="secondary" variant="helper">
                            {LANG_DATA.FIELD.PHONE.DESC}
                        </Text>
                    </Flex>
                    <Controller
                        name="phoneNumber"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                            <Flex gap={4} css={{ width: '$full' }}>
                                <FormGroup>
                                    <FormLabel css={{ fontWeight: 600, color: '$textPrimary' }}>
                                        {LANG_DATA.FIELD.PHONE.LABEL2}
                                    </FormLabel>
                                    <InputSelect value={countryOptions[0]} option={countryOptions} disabled />
                                    <FormHelper>&nbsp;</FormHelper>
                                </FormGroup>
                                <FormGroup css={{ width: '$full' }}>
                                    <FormLabel css={{ fontWeight: 600, color: '$textPrimary' }}>
                                        {LANG_DATA.FIELD.PHONE.LABEL3}
                                    </FormLabel>
                                    <InputNumber
                                        isInvalid={!!get(errors, 'phoneNumber')}
                                        value={value}
                                        thousandSeparator={false}
                                        format="### #### #### ####"
                                        mask=""
                                        allowEmptyFormatting
                                        onValueChange={e => onChange(e.value)}
                                        readOnly={isEdit}
                                    />
                                    <FormHelper>{LANG_DATA.FIELD.PHONE.HELPER}</FormHelper>
                                </FormGroup>
                            </Flex>
                        )}
                    />
                    {get(errors, 'phoneNumber') && <FormHelper error>{get(errors, 'phoneNumber.message')}</FormHelper>}
                </FormGroup>
                <FormGroup responsive="input">
                    <FormLabel>{LANG_DATA.FIELD.DESCRIPTION.LABEL}</FormLabel>
                    <Controller
                        name="description"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                            <InputTextArea
                                isInvalid={!!get(errors, 'description')}
                                value={value}
                                placeholder={LANG_DATA.PLACEHOLDER_EXAMPLE(
                                    'Nomor ini adalah nomor admin utama untuk reservasi',
                                )}
                                onChange={val => onChange(val)}
                            />
                        )}
                    />
                    {get(errors, 'description') && <FormHelper error>{get(errors, 'description.message')}</FormHelper>}
                </FormGroup>
            </Box>
        </Paper>
    );
};

StepOne.propTypes = {
    formData: PropTypes.shape().isRequired,
    onSubmit: PropTypes.func.isRequired,
    isEdit: PropTypes.bool.isRequired,
    originalFormData: PropTypes.shape().isRequired,
};

export default StepOne;
