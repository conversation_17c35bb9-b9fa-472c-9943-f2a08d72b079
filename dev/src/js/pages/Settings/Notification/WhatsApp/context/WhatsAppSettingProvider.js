import React, { useContext, useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { ToastContext, AlertDialogFunction } from '@majoo-ui/react';
import WhatsAppSettingContext from './WhatsAppSettingContext';
import { useTranslationHook } from '../lang.utils';
import useFetchList from '../hooks/useFetchList';
import useFilter from '../hooks/useFilter';
import { useMediaQuery } from '../../../../../utils/useMediaQuery';
import * as whatsAppService from '../../../../../data/whatsAppSetting';
import { catchError, normalizePhoneNumber } from '../../../../../utils/helper';
import { TABLE_ACTION } from '../enum';

const WhatsAppSettingProvider = ({ children, parentProps }) => {
    const { listBranch, filterBranch, showProgress, hideProgress, idCabang } = parentProps;
    const { addToast } = useContext(ToastContext);
    const idOutletDef = useMemo(() => filterBranch || 0, [filterBranch]);
    const [openDialog, setOpenDialog] = useState(false);
    const [authDialog, setAuthDialog] = useState(false);
    const { LANG_DATA, LANG_KEY, TransComponent, currentLang } = useTranslationHook();
    const isMobile = useMediaQuery('(max-width: 766px)');
    const [formData, setFormData] = useState({
        accountId: '',
        usedFor: '',
        idOutlet: '',
        labelNo: '',
        phoneNumber: '',
        descripstion: '',
        status: false,
        isLogin: false,
    });

    const { filter, setFilter, statusOptions } = useFilter({ LANG_DATA });

    const {
        fetchData,
        whatsAppList,
        summaryQuota,
        totalItem,
        totalItemPerPage,
        isLoading,
        fetchWhatsAppFeatures,
        whatsAppFeatures,
        categoryOptions,
    } = useFetchList({
        ...parentProps,
        idOutlet: idOutletDef,
        addToast,
        LANG_DATA,
        filter,
        currentLang,
    });

    const alert = option => {
        const dialog = new AlertDialogFunction({
            title: option.title,
            description: option.description,
            dialogType: option.type,
            labelConfirm: LANG_DATA.LABEL.CONTINUE,
            labelCancel: LANG_DATA.LABEL.CANCEL,
            onConfirm: option.onConfirm,
        });
        dialog.show();
    };

    const handleFailedProcessDataError = (data, errorMessage, cabangId, onError) => {
        if (errorMessage && onError) {
            const outlet = listBranch.find(x => x.id_cabang === String(cabangId));
            if (String(errorMessage).includes('Phone Number already use')) {
                let msg = LANG_DATA.FIELD.PHONE.ERROR_USED(outlet ? outlet.cabang_name : null);
                if (String(errorMessage) === 'Phone Number already use on other merchant') {
                    msg = LANG_DATA.FIELD.PHONE.ERROR_USED_OTHER_MERCHANT;
                }
                onError('phoneNumber', msg);
            } else if (String(errorMessage).includes('Label already use')) {
                onError('labelNo', LANG_DATA.FIELD.LABEL.ERROR_USED(outlet ? outlet.cabang_name : null));
            }
        } else {
            addToast({
                title: LANG_DATA.TOAST.ERROR,
                description: (
                    <TransComponent i18nKey={LANG_KEY.TOAST_ADD_FAILED}>
                        {{ number: normalizePhoneNumber(data.phoneNumber) }}
                    </TransComponent>
                ),
                variant: 'failed',
            });
        }
    };

    const handleUpdateData = async (data, type, onError) => {
        showProgress();
        let message, errorMessage;
        switch (type) {
            case TABLE_ACTION.ACTIVATE:
                message = (
                    <TransComponent i18nKey={LANG_KEY.TOAST_ACTIVATE_SUCCESS}>
                        {{ number: normalizePhoneNumber(data.phoneNumber) }}
                    </TransComponent>
                );
                errorMessage = LANG_DATA.TOAST.ACTIVATE_FAILED;
                break;
            case TABLE_ACTION.DEACTIVATE:
                message = (
                    <TransComponent i18nKey={LANG_KEY.TOAST_DEACTIVATE_SUCCESS}>
                        {{ number: normalizePhoneNumber(data.phoneNumber) }}
                    </TransComponent>
                );
                errorMessage = (
                    <TransComponent i18nKey={LANG_KEY.TOAST_DEACTIVATE_FAILED}>
                        {{ number: normalizePhoneNumber(data.phoneNumber) }}
                    </TransComponent>
                );
                break;
            default:
                message = LANG_DATA.TOAST.EDIT_SUCCESS;
                errorMessage = LANG_DATA.TOAST.EDIT_ERROR;
                break;
        }
        try {
            const payload = {
                id_account: data.accountId,
                phone_number: normalizePhoneNumber(data.phoneNumber),
                category: [data.usedFor],
                outlet_id: +data.idOutlet,
                label: data.labelNo,
                deskripsi: data.description,
                status_client: data.status,
            };
            const res = await whatsAppService.updateWhatsApp(payload);
            if (!res.data) throw new Error(res.error);
            addToast({
                title: LANG_DATA.TOAST.SUCCESS,
                description: message || res.message,
                variant: 'success',
            });
            setOpenDialog(false);
            fetchData();
        } catch (err) {
            let msg = catchError(err);
            if (Object.prototype.hasOwnProperty.call(err, 'message') && typeof err.message === 'string')
                ({ message: msg } = err);
            if (
                (String(msg).toLowerCase().includes('label already use') ||
                    String(msg).toLowerCase().includes('phone number already use')) &&
                onError
            ) {
                handleFailedProcessDataError(
                    data,
                    String(msg),
                    err.cause && err.cause.data ? err.cause.data.cabang_id : null,
                    onError,
                );
            } else {
                msg = errorMessage || msg;
                addToast({
                    title: LANG_DATA.TOAST.ERROR,
                    description: msg,
                    variant: 'failed',
                });
            }
        } finally {
            hideProgress();
        }
    };

    const handleRegisterData = async (data, onError) => {
        showProgress();
        try {
            const payload = [
                {
                    id_account: formData.accountId,
                    phone_number: normalizePhoneNumber(data.phoneNumber),
                    category: [data.usedFor],
                    outlet_id: +data.idOutlet,
                    label: data.labelNo,
                    deskripsi: data.description,
                    status_client: data.status,
                },
            ];
            const { meta } = await whatsAppService.registerWhatsApp(payload);
            if (
                meta &&
                meta.failed_processed_data &&
                Array.isArray(meta.failed_processed_data) &&
                meta.failed_processed_data.length > 0
            ) {
                const failedData = meta.failed_processed_data[0];
                handleFailedProcessDataError(data, failedData.error, failedData.cabang_id, onError);
            } else {
                addToast({
                    title: LANG_DATA.TOAST.SUCCESS,
                    description: (
                        <TransComponent i18nKey={LANG_KEY.TOAST_ADD_SUCCESS}>
                            {{ number: normalizePhoneNumber(data.phoneNumber) }}
                        </TransComponent>
                    ),
                    variant: 'success',
                });
                setFormData({ ...data, accountId: meta.success_processed_data[0].id });
                setOpenDialog(false);
                setAuthDialog(true);
                fetchData();
            }
        } catch (error) {
            addToast({
                title: LANG_DATA.TOAST.ERROR,
                description: (
                    <TransComponent i18nKey={LANG_KEY.TOAST_ADD_FAILED}>
                        {{ number: normalizePhoneNumber(data.phoneNumber) }}
                    </TransComponent>
                ),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    const deleteWhatsApp = async ({ idOutlet, accountId, phoneNumber }) => {
        try {
            await whatsAppService.deleteWhatsApp(idOutlet, accountId);
            fetchData();
            addToast({
                title: LANG_DATA.TOAST.SUCCESS,
                description: (
                    <TransComponent i18nKey={LANG_KEY.TOAST_DELETE_SUCCESS}>
                        {{ number: normalizePhoneNumber(phoneNumber) }}
                    </TransComponent>
                ),
                variant: 'success',
            });
            setOpenDialog(false);
        } catch (error) {
            addToast({
                title: LANG_DATA.TOAST.ERROR,
                description: catchError(error),
                variant: 'failed',
            });
        }
    };

    useEffect(() => {
        setFilter(prev => ({ ...prev, category: '' }));
        fetchWhatsAppFeatures();
    }, [currentLang, filterBranch, idCabang]);

    useEffect(() => {
        if (whatsAppFeatures && whatsAppFeatures.length > 0) {
            fetchData();
        }
    }, [filter.keyword, String(filter.category), filter.status, whatsAppFeatures]);

    return (
        <WhatsAppSettingContext.Provider
            value={{
                addToast,
                fetchData,
                whatsAppList,
                summaryQuota,
                totalItem,
                totalItemPerPage,
                filter,
                setFilter,
                statusOptions,
                categoryOptions,
                LANG_DATA,
                isLoading,
                isMobile,
                openDialog,
                setOpenDialog,
                authDialog,
                setAuthDialog,
                alert,
                deleteWhatsApp,
                formData,
                setFormData,
                handleRegisterData,
                handleUpdateData,
                whatsAppFeatures,
                outletId: filterBranch || idCabang,
                fetchWhatsAppFeatures,
                ...parentProps,
            }}
        >
            <div id="a-dialog" />
            {children}
        </WhatsAppSettingContext.Provider>
    );
};

WhatsAppSettingProvider.propTypes = {
    children: PropTypes.node.isRequired,
    parentProps: PropTypes.shape({
        listBranch: PropTypes.arrayOf(PropTypes.shape({})),
        filterBranch: PropTypes.string,
        showProgress: PropTypes.func,
        hideProgress: PropTypes.func,
        idCabang: PropTypes.string,
    }).isRequired,
};

export default WhatsAppSettingProvider;
