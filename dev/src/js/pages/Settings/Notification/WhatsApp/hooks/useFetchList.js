import React, { useCallback, useMemo, useState } from 'react';
import { catchError } from '~/utils/helper';
import { Flex, Text, TagStatus } from '@majoo-ui/react';
import * as whatsAppService from '../../../../../data/whatsAppSetting';

const useFetchList = ({ idOutlet, addToast, hideProgress, filter, LANG_DATA, currentLang, idCabang }) => {
    const [whatsAppList, setWhatsAppList] = useState([]);
    const [summaryQuota, setSummaryQuota] = useState({
        notif_laporan: {
            total: 0,
            limit: 2,
        },
        notif_merchant: {
            total: 0,
            limit: 2,
        },
        notif_customer: {
            total: 0,
            limit: 2,
        },
    });
    const [whatsAppFeatures, setWhatsAppFeatures] = useState([]);
    const [totalItem, setTotalItem] = useState(0);
    const [totalItemPerPage, setTotalItemPerPage] = useState(10);
    const [isLoading, setIsLoading] = useState(true);

    const fetchWhatsAppList = async params => {
        const currParams = { ...params };
        setIsLoading(true);
        try {
            const { keyword, status, category } = filter;
            const payload = {
                per_page: currParams.pageSize || totalItemPerPage,
                page: currParams.pageIndex ? currParams.pageIndex + 1 : 1,
                ...(keyword && { search: keyword }),
                ...(status !== '1' && { status }),
                ...(category && { category }),
            };
            const res = await whatsAppService.getListWhatsApp(idOutlet, payload);
            if (!res.data) throw new Error(res.message);
            const { data: list, meta } = res;
            setWhatsAppList(list);
            setTotalItem(meta.total);
            setTotalItemPerPage(meta.per_page);
        } catch (error) {
            addToast({
                title: LANG_DATA.TOAST.ERROR,
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            setIsLoading(false);
            hideProgress();
        }
    };

    const fetchSummary = async () => {
        try {
            const res = await whatsAppService.getSummaryWhatsApp(idOutlet);
            if (res.data) {
                setSummaryQuota(
                    whatsAppFeatures.reduce((acc, item) => {
                        acc[item.feature_code] = {
                            total: res.data[item.feature_code],
                            limit: res.data[`kuota_${item.feature_code}`],
                        };
                        return acc;
                    }, {}),
                );
            }
            if (!res.data) throw new Error(res.message);
        } catch (error) {
            addToast({
                title: LANG_DATA.TOAST.ERROR,
                description: catchError(error),
                variant: 'failed',
            });
        }
    };

    const fetchData = useCallback(
        params => {
            Promise.all([fetchWhatsAppList(params), fetchSummary()]);
        },
        [idOutlet, filter, whatsAppFeatures],
    );

    const fetchWhatsAppFeatures = async (outletId, onSuccess, onError) => {
        try {
            const res = await whatsAppService.getWhatsAppFeatures({
                outlet_id: outletId || idOutlet || idCabang,
                language: currentLang,
            });
            if (!res.data) throw new Error(res.message);
            if (onSuccess){
                onSuccess(res.data);
            }else{
                setWhatsAppFeatures(res.data);
            }
        } catch (error) {
            addToast({
                title: LANG_DATA.TOAST.ERROR,
                description: catchError(error),
                variant: 'failed',
            });
            if (onError) {
                onError(error);
            } else {
                setWhatsAppFeatures([]);
            }
        }
    };

    const categoryOptions = useMemo(
        () => [
            { value: '', name: LANG_DATA.FILTER.ALL_CATEGORY },
            ...(whatsAppFeatures && whatsAppFeatures.length > 0
                ? whatsAppFeatures.map(feature => ({
                      value: feature.feature_code,
                      name: feature.feature_name,
                      render: !feature.expired_add_on ? () => (
                          <Flex align="center" justify="between" gap={3}>
                              <Text>{feature.feature_name}</Text>
                              <TagStatus type="new">Beli Add-On</TagStatus>
                          </Flex>
                      ) : undefined,
                  }))
                : [
                      { value: 'notif_laporan', name: LANG_DATA.FILTER.REPORT },
                      { value: 'notif_merchant', name: LANG_DATA.FILTER.MERCHANT },
                      { value: 'notif_customer', name: LANG_DATA.FILTER.CUSTOMER },
                  ]),
        ],
        [LANG_DATA, whatsAppFeatures],
    );

    return {
        fetchData,
        summaryQuota,
        setSummaryQuota,
        totalItem,
        totalItemPerPage,
        setTotalItemPerPage,
        whatsAppList,
        setWhatsAppList,
        isLoading,
        setTotalItem,
        fetchWhatsAppFeatures,
        whatsAppFeatures,
        categoryOptions,
    };
};

export default useFetchList;
