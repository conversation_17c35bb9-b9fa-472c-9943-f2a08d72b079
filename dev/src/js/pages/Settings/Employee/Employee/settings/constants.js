import i18n from 'i18next';

const { t } = i18n;

export const TRANSLATION_NAMESPACES = {
  GLOBAL: 'translation',
  EMPLOYEELIST: '<PERSON><PERSON><PERSON>/PengaturanKaryawan/employeeList',
  EMPLOYEE_SETTING: 'Pengaturan/employeeListSetting',
};

export const ROLES = {
  ADMIN: '1',
  MANAGER: '2',
  CASHIER: '3',
  STAFF: '4',
  CUSTOM_PRIVILAGE: '4',
  WAREHOUSE: '5',
  WAITERS: '6',
  KITCHEN: '7',
};

// manager, admin, warehouse
export const roleIdWithRequiredEmail = ['2020DAMXD46XKI6', '2020DE53KG7D16L', '2020DNHWK0GNN0J'];

export const modeEnum = {
  ADD: 'ADD',
  EDIT: 'EDIT',
  VIEW: 'VIEW',
  MOVE: 'MOVE',
  DELETE: 'DELETE',
  TERMINATE: 'TERMINATE',
  VIEW_ACCESS_RIGHTS: 'VIEW_ACCESS_RIGHTS',
};

export const confirmationModalType = {
  DELETE: 'DELETE',
  SELESAI: 'SELESAI',
  SATUSEHAT_NIK_CHANGED: 'SATUSEHAT_NIK_CHANGED',
};

export const DEFAULT_ROLES = {
  OWNER: 'OWNER',
  ADMIN: 'ADMIN',
  MANAGER: 'MANAGER',
  CASHIER: 'CASHIER',
  STAFF: 'STAFF',
  WAREHOUSE: 'WAREHOUSE',
  WAITERS: 'WAITERS',
  KITCHEN: 'KITCHEN',
};

export const errorMessage = {
  NAMA: () => t('display.toast.addEmployeError.name', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }),
  TELEPON: () => t('display.toast.addEmployeError.phone', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }),
  EMAIL: () => t('display.toast.addEmployeError.email', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }),
  NIP: () => t('display.toast.addEmployeError.nip', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }),
};

export const responsivePaper = {
  display: 'flex',
  flexDirection: 'column',
  padding: '24px 16px',
  width: '100% !important',
  margin: 0,
  borderRadius: 0,
  backgroundColor: '$white',
  gap: '$compact',
  boxShadow: 'none',
  '@md': {
    padding: '26px 24px 24px 24px',
    // maxWidth: 647,
    width: '100% !important',
    borderRadius: 8,
    gap: '$cozy',
    boxShadow: 'rgb(0 0 0 / 8%) 0px 2px 12px',
  },
};

export const genderOptions = (translate) => [
  { id: 1, label: translate('display.label.gender.male', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }) },
  { id: 2, label: translate('display.label.gender.female', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }) },
];

export const maritalOptions = (translate) => [
  { value: 1, name: translate('display.label.maritalStatus.single', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }) },
  { value: 2, name: translate('display.label.maritalStatus.married', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }) },
  { value: 3, name: translate('display.label.maritalStatus.divorced', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }) },
  { value: 4, name: translate('display.label.maritalStatus.widowed', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }) },
];

export const religionOptions = [
  { value: 'Islam', name: () => 'Islam' },
  { value: 'Protestan', name: () => t('display.label.religion.protestant', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }) },
  { value: 'Katolik', name: () => t('display.label.religion.catholic', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }) },
  { value: 'Hindu', name: () => 'Hindu' },
  { value: 'Buddha', name: () => 'Buddha' },
  { value: 'Khonghucu', name: () => 'Khonghucu' },
];

export const jobStatusEnum = {
  KARYAWAN_TETAP: 1,
  KARYAWAN_KONTRAK: 2,
  PEKERJA_LEPAS: 3,
};

export const employeeType = {
  [jobStatusEnum.KARYAWAN_TETAP]: () => t('display.label.employmentStatus.fullTime', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }),
  [jobStatusEnum.KARYAWAN_KONTRAK]: () => t('display.label.employmentStatus.contract', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }),
  [jobStatusEnum.PEKERJA_LEPAS]: () => t('display.label.employmentStatus.freelance', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }),
}

export const jobStatusOptions = translate => [
  { value: jobStatusEnum.KARYAWAN_TETAP, name: translate('display.label.employmentStatus.fullTime', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }) },
  { value: jobStatusEnum.KARYAWAN_KONTRAK, name: translate('display.label.employmentStatus.contract', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }) },
  { value: jobStatusEnum.PEKERJA_LEPAS, name: translate('display.label.employmentStatus.freelance', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }) },
];

export const FIRST_STEP = 1;

export const LAST_STEP = 3;

export const stepFormID = {
  '#data-utama': FIRST_STEP,
  '#data-personal': FIRST_STEP + 1,
  '#data-karyawan': LAST_STEP,
};

export const cssMoveForm = {
  width: '$full',
  flexDirection: 'column',
  alignItems: 'unset',
  '@lg': {
      flexDirection: 'row',
      alignItems: 'center',
  }
};

export const MOVE_TYPE = {
  EMPLOYEE: 'employee',
  MEMBER: 'member',
};

export const  formLabelStyle = {
  '& > span': {
      color: '$textPrimary',
      fontSize: 14,
      fontWeight: 600,
  },
};

export const educationList = [
  { value: 'SD', name: 'SD' },
  { value: 'SMP', name: 'SMP' },
  { value: 'SMA', name: 'SMA' },
  { value: 'SMK', name: 'SMK' },
  { value: 'D3', name: 'D3' },
  { value: 'D4', name: 'D4' },
  { value: 'S1', name: 'S1' },
  { value: 'S2', name: 'S2' },
  { value: 'S3', name: 'S3' },
];

export const optionTerminateList = (translate) => [
  { value: 1, name: translate('dataInputSelect.optionTerminate.resign', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }) },
  { value: 2, name: translate('dataInputSelect.optionTerminate.workTermination', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }) },
];

export const filterStatusOptions = (translate) => [
  { value: '', name: translate('status.all', { ns: TRANSLATION_NAMESPACES.GLOBAL }) },
  { value: '2', name: translate('status.active', { ns: TRANSLATION_NAMESPACES.GLOBAL }) },
  { value: '1', name: translate('status.inactive', { ns: TRANSLATION_NAMESPACES.GLOBAL }) },
];

export const roleEnum = {
    ADMIN: '1',
    MANAGER: '2',
    CASHIER: '3',
    STAFF: '4',
    CUSTOM_PRIVILAGE: '4',
    WAREHOUSE: '5',
    WAITERS: '6',
    KITCHEN: '7',
};

export const IMPORT_TYPE =  {
  EMPLOYEE: 'employee',
  DOCUMENT: 'document',
};

export const IMPORT_TEMPLATE =  {
  DONT_HAVE_TEMPLATE: '1',
  HAVE_TEMPLATE: '2',
};

export const docTypeOptions = (trans) => ([
  { value: 'document_ktp', name: trans('form.content.personalData.label.uploadIdCard') },
  { value: 'document_kk', name: trans('form.content.personalData.label.kk') },
  { value: 'saving_book_photo', name: trans('form.content.employeeData.label.accountBook') },
  { value: 'attachment_cv', name: 'CV' },
  { value: 'attachment_portfolio', name: 'Portfolio' },
  { value: 'document_npwp', name: 'NPWP' },
  { value: 'document_paklaring', name: trans('form.content.personalDocs.label.clearanceLetter') },
  { value: 'document_bpjs_health', name: trans('form.content.employeeData.label.bpjsPhoto') },
  { value: 'document_bpjs_employment', name: trans('form.content.employeeData.label.employmentBpjs') },
  { value: 'document_diploma', name: trans('form.content.personalDocs.label.diploma') },
  { value: 'attachment_job_description', name: trans('form.content.HRLetter.label.workingDescription') },
  { value: 'attachment_current_sk', name: trans('form.content.HRLetter.label.letterOfAssignment') },
  { value: 'employee_photo', name: trans('form.content.primaryData.label.photo') },
]);

export const stepImport = (trans) => ([
  {
    title: trans('translation:label.exportTemplate', 'Ekspor Template'),
    withTolltip: false,
  },
  {
    title: trans('translation:label.importData', 'Impor Data'),
    withTolltip: false,
  },
]);

export const templateOptions = (trans) => [
  {
    id: '1',
    label: trans('modal.import.field.template.label'),
    description: trans('modal.import.field.template.helper'),
  },
  {
    id: '2',
    label: trans('modal.import.field.template.label', { context: 'has' }),
    description: trans('modal.import.field.template.helper', { context: 'has' }),
  },
];
