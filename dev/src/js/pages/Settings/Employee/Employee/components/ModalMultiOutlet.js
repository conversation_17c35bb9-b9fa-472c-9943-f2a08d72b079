/* eslint-disable react/prop-types */
import React, { useEffect, useState, useMemo, useRef } from 'react';
import {
    Button,
    ModalDialog,
    ModalDialogTitle,
    ModalDialogContent,
    ModalDialogFooter,
    InputSearchbox,
    Table,
    InputRadioGroup,
    Flex,
    FormHelper,
    RowSelectionColumn,
    InputRadio,
} from '@majoo-ui/react';

import { useMediaQuery } from '~/utils/useMediaQuery';
import { Controller } from 'react-hook-form';
import _ from 'lodash';
import isIn from 'validator/lib/isIn';

import { TRANSLATION_NAMESPACES } from '../settings/constants';

export const tableMultiOutlet = (getIsDisabledRadiuButton, t) => [
    RowSelectionColumn,
    {
        Header: t('header.outletName2', { ns: TRANSLATION_NAMESPACES.EMPLOYEE_SETTING }),
        accessor: 'name',
    },
    {
        Header: t('header.outletName', { ns: TRANSLATION_NAMESPACES.EMPLOYEE_SETTING }),
        accessor: 'value',
        unsortable: true,
        headerCss: {
            '& div': {
                display: 'flex',
                justifyContent: 'end',
            },
        },
        Cell: ({ cell: { value } }) => (
            <InputRadio
                id={value}
                inputType="default"
                disabled={getIsDisabledRadiuButton(value)}
                value={value}
                css={{
                    alignItems: 'flex-end',
                    '& > div': {
                        gap: '$spacing-06'
                    }
                }}
            />
        )
    },
];

const ModalMultiOutlet = ({ open, onOpenChange, dataOutlet, control, getValues, setValue, onChange, t }) => {
    const isMobile = useMediaQuery('(max-width: 767px)');
    const tableData = useRef([]);
    const [selectedOutlet, setSelectedOutlet] = useState([]);
    const [filteredTable, setFilteredTable] = useState([]);
    const [isNoDefaultOutlet, setIsNoDefaultOutlet] = useState(false);

    const onSelectedChangeHandler = (list) => {
        const cloneState = [...selectedOutlet];

        let defaultOtletId = '';
        const findDefaultOutlet = _.filter(cloneState, 'is_default');
        if (findDefaultOutlet.length > 0) {
            defaultOtletId = findDefaultOutlet[0].value;
        } else {
            defaultOtletId = '';
        }

        const payload = list && list.map((v) => ({
            value: v,
            is_default: defaultOtletId === v,
        }));
        setSelectedOutlet(payload);
    };

    const onSelectDefaultCabang = (value) => {
        const resetIdCabang = selectedOutlet.map((v) => ({
            ...v,
            is_default: false,
        }))
        const index = resetIdCabang.findIndex((v) => v.value === value);
        if (index >= 0) {
            resetIdCabang[index].is_default = true;

            setSelectedOutlet(resetIdCabang);
            setIsNoDefaultOutlet(false);
        }
    };

    const getDefaultCabangValue = () => {
        const list = [...selectedOutlet];
        const selected = list && list.length > 0 && list.filter((v) => v.is_default === true);
        if (selected && selected.length > 0) {
            return selected[0].value;
        } 
        return "";
    };

    const getIsDisabledRadiuButton = (value) => {
        const list = selectedOutlet && selectedOutlet.length > 0 ? selectedOutlet.map(x => x.value) : [];
        const isDisabled = isIn(value, list);
        return !isDisabled;
    };

    const saveMOutletHandler = () => {
        const cloneDataOutlet = [...dataOutlet];
        const listSelected = selectedOutlet.map(x => x.value);
        const filtered = cloneDataOutlet.filter((e) => listSelected.indexOf(e.value) > -1)
        const mergeArr = filtered.map((ob) => {
            const idx = selectedOutlet.findIndex((el) => el.value === ob.value);
            const { is_default: isDefault } = idx !== -1 ? selectedOutlet[idx] : {};
            return {
                ...ob,
                is_default: isDefault,
            };
        });

        const isAnyDefaultOutlet = isIn('true', mergeArr.map(x => `${x.is_default}`));
        if (isAnyDefaultOutlet) {
            onChange(mergeArr);
            onOpenChange(false);
        } else {
            setIsNoDefaultOutlet(true);
        }
    };

    const closeMOutletHandler = () => {
        setValue('multiOutlet', getValues('multiOutlet'));
        onOpenChange(false);
        setSelectedOutlet([]);
    };

    const handleTableSearch = _.debounce((e) => {
        if (e === '' || e === undefined) {
            setFilteredTable(tableData.current);
        } else if (e !== undefined) {
            setFilteredTable(
                tableData.current.filter(row => Object.values(row).some(value => String(value).toLowerCase().includes(e.toLowerCase()))),
            );
        }
    }, [tableData]);

    useEffect(() => {
        setSelectedOutlet(getValues('multiOutlet'));
        tableData.current = dataOutlet;
        setFilteredTable(dataOutlet);
        setIsNoDefaultOutlet(false);
    }, [open]);

    const selectedIds = useMemo(() => selectedOutlet && selectedOutlet.map(val => val.value).reduce((prev, curr) => {
        // eslint-disable-next-line no-param-reassign
        prev[curr] = true;

        return prev;
    }, {}), [selectedOutlet]);

    return (
        <ModalDialog open={open} isMobile={isMobile} width={isMobile ? '100%': '496px'} onOpenChange={onOpenChange} modal>
            <ModalDialogTitle>
                {t('outletSettings', 'Atur Outlet')}
            </ModalDialogTitle>
            <ModalDialogContent>
                <Flex direction="column" gap={5}>
                    <InputSearchbox
                        css={{ maxWidth: 258 }}
                        placeholder={t('placeholder.search', { ns: 'translation' })}
                        onChange={(e) => handleTableSearch(e)}
                    />
                    <Controller
                        control={control}
                        name="multiOutlet"
                        render={() => (
                            <InputRadioGroup
                                value={getDefaultCabangValue()}
                                onValueChange={onSelectDefaultCabang}
                            >
                                <Table
                                    keyId="value"
                                    selectedIds={selectedIds}
                                    columns={tableMultiOutlet(getIsDisabledRadiuButton, t)}
                                    data={filteredTable}
                                    totalData={filteredTable.length}
                                    rowLimit={dataOutlet.length}
                                    onSelectedChange={onSelectedChangeHandler}
                                    hideDataInfo
                                    hidePagination
                                    css={{
                                        height: filteredTable && filteredTable.length > 0 ? 'calc(60vh - 45px)' : 'auto',
                                    }}
                                    headerCss={{
                                        zIndex: '$modal',
                                        top: '-1px',
                                    }}
                                    customRowsStyle={() => [
                                        {
                                            predicate: true,
                                            css: {
                                                '& td:last-child': {
                                                    '& label': {
                                                        width: '$full',
                                                    }
                                                },
                                            },
                                        },
                                    ]}
                                />
                            </InputRadioGroup>
                        )}
                    />
                    {isNoDefaultOutlet && <FormHelper error css={{ paddingTop: '$compact' }}>Mohon pilih default outlet terlebih dahulu</FormHelper>}
                </Flex>
            </ModalDialogContent>
            <ModalDialogFooter
                css={{
                    display: 'flex',
                    gap: '$compact',
                }}
            >
                <Button
                    size="md"
                    buttonType="ghost"
                    onClick={closeMOutletHandler}
                >
                    {t('label.cancel', { ns: 'translation' })}
                </Button>
                <Button
                    size="md"
                    disabled={isNoDefaultOutlet}
                    onClick={saveMOutletHandler}
                >
                    {t('label.save', { ns: 'translation' })}
                </Button>
            </ModalDialogFooter>
        </ModalDialog>
    )
};

export default ModalMultiOutlet;
