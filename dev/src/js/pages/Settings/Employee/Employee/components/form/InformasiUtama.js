/* eslint-disable react/prop-types */
import React, { useContext, useRef, useState, useMemo } from 'react';
import { EyeSlashOutline, EyeOutline, CircleInfoOutline } from '@majoo-ui/icons';
import {
    Button,
    Flex,
    Box,
    Heading,
    FormGroup,
    FormLabel,
    Upload,
    InputText,
    FormHelper,
    Separator,
    InputNumber,
    InputSelect,
    InputGroup,
    InputRightElement,
    Paper,
    InputSwitch,
    Text,
    Tooltip,
} from '@majoo-ui/react';
import { Controller } from 'react-hook-form';
import PropTypes from 'prop-types';
import { roleIdWithRequiredEmail, modeEnum, responsivePaper, TRANSLATION_NAMESPACES, roleEnum } from '../../settings/constants';
import { EmployeeContext } from '../../context/EmployeeContext';
import ChangePIN from '../ChangePIN';
import ModalMultiOutlet from '../ModalMultiOutlet';
import OutletTag from '../OutletTag';

const InformasiUtama = ({
    register,
    errors,
    control,
    clearErrors,
    watch,
    mode,
    isEditable,
    handleVerificationModal,
    outletList,
    setOutletValue,
    getUserLoginId,
    detailFormRaw,
    roleList,
    setRoleValue,
    rolesAllowedPinToMaintained,
    rolesAllowedRoleToMaintained,
    rolesNeedVerification,
    handlePhotoPreview,
    validateImageSize,
    isMobile,
    setValue,
    getValues,
}) => {
    const contextData = useContext(EmployeeContext);
    const [showPassword, setShowPassword] = useState(false);
    const [isShowModalMOutlet, setIsShowModalMOutlet] = useState(false);
    const changePINRef = useRef();

    const { t, idCabang } = contextData;

    const changePIN = () => {
        changePINRef.current.handleChangePIN();
    };

    const handlePinModal = () => {
        const refetch = () => {
            contextData.handleCloseConfirmationModal();
        };
        contextData.handleOpenConfirmationModal({
            title: t('modal.editPIN.title'),
            description: <ChangePIN ref={changePINRef} payload={detailFormRaw} refetch={refetch} />,
            buttons: [
                {
                    type: 'primary',
                    title: t('label.save', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
                    action: () => changePIN(),
                },
            ],
        });
    };

    const multipleOutlet = useMemo(() => {
        let isMultipleOutlet = false;
        let isDisableMultiOutlet = false;

        if (roleList.length > 0 && outletList.length > 0) {
            const valueHakAkses = watch('hakAkses');
            const chossenPermission = roleList.find(item => item.value === valueHakAkses);

            if (chossenPermission) {
                isMultipleOutlet = chossenPermission.is_can_multioutlet === 1;
            }
            
            if (getUserLoginId() === String(watch('id'))) {
                isDisableMultiOutlet = true;
                setValue('multiOutlet', outletList.map(x => ({ ...x, is_default: x.value === idCabang })));
                setValue('outlet', idCabang);
            } else if (chossenPermission && String(chossenPermission.permission_id) === roleEnum.ADMIN) {
                isDisableMultiOutlet = true;
                setValue('multiOutlet', outletList.map(x => ({ ...x, is_default: x.value === idCabang })));
                setValue('outlet', idCabang);
            } else if (isMultipleOutlet) {
                isDisableMultiOutlet = false;
                const valueMultiOutlet = watch('multiOutlet');
                let newValueMultiOutlet = [];
                let defaultOutlet = idCabang;
                if (valueMultiOutlet && valueMultiOutlet.length > 0) {
                    const mappingDetailOutlet = valueMultiOutlet.map(x => {
                        if (x.is_default) defaultOutlet = x.value;

                        const selectedOutlet = outletList.find(y => String(y.value) === String(x.value));
                        return { ...x, name: selectedOutlet ? selectedOutlet.name : '' };
                    });
                    newValueMultiOutlet = mappingDetailOutlet;
                }
                setValue('multiOutlet', newValueMultiOutlet)
                setValue('outlet', defaultOutlet);
            } else {
                isDisableMultiOutlet = false;
                setValue('multiOutlet', []);
                setValue('outlet', idCabang);
            }
        }

        return {
            isMultipleOutlet,
            isDisableMultiOutlet,
        };
    }, [JSON.stringify(watch('hakAkses')), JSON.stringify(watch('multiOutlet')), JSON.stringify(roleList), JSON.stringify(outletList)]);

    const showModalMOutletHandler = (event) => {
        event.preventDefault();
        setIsShowModalMOutlet(true);
    };

    const listOutlet = getValues('multiOutlet') && getValues('multiOutlet').length > 0 ? getValues('multiOutlet') : [];

    const onRemoveSelectedOutlet = (item) => {
        const list = getValues('multiOutlet');
        const payload = list && list.length > 0 && list.filter(x => x.value !== item.value);

        setValue('multiOutlet', payload);
    };
    
    return (
        <Paper id="data-utama" css={responsivePaper}>
            <Box css={{ '@md': { marginBottom: '$spacing-03' } }}>
                <Heading
                    as="h3"
                    heading={{
                        '@initial': 'sectionSubTitle',
                        '@md': 'pageTitle',
                    }}
                >
                    {t('form.stepLabel.primaryData')}
                </Heading>
            </Box>
            <FormGroup responsive="input">
                <FormLabel htmlFor="foto">
                    {t('form.content.primaryData.label.photo')}
                </FormLabel>
                <Controller
                    control={control}
                    name="fotoEmployee"
                    render={({ field: { onChange, value, ...restField } }) => (
                        <Upload
                            {...restField}
                            fileList={value}
                            onChange={({ file }) => onChange([file])}
                            onPreview={handlePhotoPreview}
                            onRemove={() => onChange([])}
                            multiple={false}
                            listType="picture"
                            accept=".jpg,.jpeg,.png"
                            width={150}
                            height={150}
                            max={1}
                            showCropper
                            disabled={!isEditable}
                            onBeforeCrop={validateImageSize}
                        />
                    )}
                />
            </FormGroup>
            <FormGroup responsive="input">
                <FormLabel htmlFor="nama" variant="required">
                    {t('form.content.primaryData.label.name')}
                </FormLabel>
                <InputText
                    {...register('nama')}
                    id="nama"
                    placeholder={t('form.content.primaryData.placeholder.name')}
                    isInvalid={!!errors.nama}
                    disabled={!isEditable}
                    maxLength={60}
                />
                {errors.nama && <FormHelper error>{errors.nama.message}</FormHelper>}
            </FormGroup>
            <FormGroup responsive="input">
                <FormLabel htmlFor="nik" variant="required">
                    {t('form.content.primaryData.label.id')}
                </FormLabel>
                <InputText
                    {...register('nik')}
                    id="nik"
                    maxLength={20}
                    isInvalid={!!errors.nik}
                    disabled={!isEditable}
                />
                {errors.nik && <FormHelper error>{errors.nik.message}</FormHelper>}
            </FormGroup>
            <FormGroup responsive="input">
                <Flex align="start" gap={4}>
                    <Text color="primary" css={{ fontWeight: 600 }}>{t('form.content.primaryData.label.employeeAccess')}</Text>
                    <Tooltip css={{ maxWidth: '336px' }} align="start" side="top" label={t('form.content.primaryData.tooltips.employeeAccess')} withClick={isMobile}>
                        <Box css={{ cursor: 'pointer' }}>
                            <CircleInfoOutline color='#A5ABAB' />
                        </Box>
                    </Tooltip>
                </Flex>
                <Controller
                    control={control}
                    defaultValue
                    name="employeeAccess"
                    render={({ field: { onChange, value, ...restField } }) => (
                        <Flex align="center" gap={5}>
                            <InputSwitch
                                {...restField}
                                id="employeeAccess"
                                dataOffLabel="OFF"
                                dataOnLabel="ON"
                                onCheckedChange={val => onChange(val)}
                                checked={value}
                                defaultChecked
                                disabled={!isEditable}
                            />
                            <FormHelper>
                                {watch('employeeAccess') ? t('form.content.primaryData.caption.employeeAccess.active') : t('form.content.primaryData.caption.employeeAccess.inactive')}
                            </FormHelper>
                        </Flex>
                    )}
                />
            </FormGroup>
            <Separator css={{ margin: '$spacing-03 0' }} />
            <FormGroup responsive="input">
                <FormLabel htmlFor="telepon" variant="required">
                    {t('form.content.primaryData.label.phone')}
                </FormLabel>
                <Controller
                    control={control}
                    name="telepon"
                    render={({ field: { onChange, ...restField }, fieldState: { invalid } }) => (
                        <InputNumber
                            {...restField}
                            id="telepon"
                            placeholder={t('form.content.primaryData.placeholder.phone')}
                            allowEmptyFormatting
                            allowLeadingZeros
                            thousandSeparator={false}
                            isInvalid={invalid}
                            onValueChange={({ value }) => onChange(value)}
                            disabled={!isEditable || watch('isOwner')}
                            maxLength={13}
                        />
                    )}
                />
                {errors.telepon && <FormHelper error>{errors.telepon.message}</FormHelper>}
            </FormGroup>
            <FormGroup responsive="input">
                <FormLabel htmlFor="email">
                    Email
                </FormLabel>
                <Flex justify="between" gap={3}>
                    <InputText
                        {...register('email')}
                        id="email"
                        placeholder={t('form.content.primaryData.placeholder.email')}
                        isInvalid={!!errors.email}
                        disabled={!isEditable || watch('isOwner')}
                        onChange={val => { if (val) clearErrors('email') }}
                    />
                    {mode === modeEnum.EDIT && !watch('isVerified') && (
                        <Box>
                            <Button buttonType="ghost" type="button" onClick={() => handleVerificationModal(watch())}>{t('display.label.verify')}</Button>
                        </Box>
                    )}
                </Flex>
                {errors.email && <FormHelper error>{errors.email.message}</FormHelper>}
                {!errors.email && mode === modeEnum.EDIT && !watch('isVerified') && <FormHelper error>{t('display.inputErrorMessage.unverifiedEmail')}</FormHelper>}
            </FormGroup>
            <FormGroup responsive="input">
                <FormLabel htmlFor="posisi">
                    {t('form.content.primaryData.label.position')}
                </FormLabel>
                <InputText
                    {...register('posisi')}
                    id="posisi"
                    placeholder={t('form.content.primaryData.placeholder.position')}
                    isInvalid={!!errors.posisi}
                    disabled={!isEditable}
                />
                {errors.posisi && <FormHelper error>{errors.posisi.message}</FormHelper>}
            </FormGroup>
            <FormGroup responsive="input">
                <FormLabel htmlFor="hakAkses" variant="required">
                    {t('form.content.primaryData.label.access')}
                </FormLabel>
                <Controller
                    control={control}
                    name="hakAkses"
                    render={({ field: { onChange, value: val, ...restField }, fieldState: { invalid } }) => (
                        <InputSelect
                            {...restField}
                            search
                            id="hakAkses"
                            placeholder={t('placeholder.select', { ns: TRANSLATION_NAMESPACES.GLOBAL })}
                            onSearch={() => { }}
                            option={roleList}
                            onChange={({ value }) => {
                                onChange(value);
                                clearErrors('email');
                            }}
                            value={setRoleValue(val)}
                            isInvalid={invalid}
                            disabled={
                                (mode === modeEnum.EDIT && (!isEditable
                                    || String(getUserLoginId()) === String(detailFormRaw.id)
                                    || (String(getUserLoginId()) !== String(detailFormRaw.id)
                                        && String(detailFormRaw.id) !== ''
                                        && !rolesAllowedRoleToMaintained.includes(detailFormRaw.hakAkses))))
                                || watch('isOwner')
                            }
                            placement="top"
                        />
                    )}
                />
                {!errors.hakAkses && rolesNeedVerification.includes(String(watch('hakAkses'))) && <FormHelper css={{ color: '$textPrimary' }}>{t('display.inputErrorMessage.emailVerification')}</FormHelper>}
                {errors.hakAkses && <FormHelper error>{errors.hakAkses.message}</FormHelper>}
            </FormGroup>

            {multipleOutlet.isMultipleOutlet ? (
                <FormGroup responsive="input" css={{ rowGap: '$spacing-02' }}>
                    <FormLabel htmlFor="multiOutlet" variant="required">
                        Outlet
                    </FormLabel>
                    <Button onClick={showModalMOutletHandler} buttonType="secondary" disabled={multipleOutlet.isDisableMultiOutlet}>
                        {t('f.adjustOutlet', { ns: TRANSLATION_NAMESPACES.EMPLOYEE_SETTING })}
                    </Button>
                    <OutletTag list={listOutlet} onRemove={onRemoveSelectedOutlet} disabled={multipleOutlet.isDisableMultiOutlet} t={t} />

                    <Controller
                        control={control}
                        name="multiOutlet"
                        render={({ field: { onChange } }) => (
                                <React.Fragment>
                                    <ModalMultiOutlet
                                        id="multiOutlet"
                                        open={isShowModalMOutlet}
                                        onOpenChange={setIsShowModalMOutlet}
                                        dataOutlet={outletList}
                                        control={control}
                                        setOutletValue={setOutletValue}
                                        getValues={getValues}
                                        setValue={setValue}
                                        watch={watch}
                                        onChange={(value) => {
                                            const outletDefault = value.find(x => x.is_default);
                                            onChange(value);
                                            setValue('outlet', outletDefault.value);
                                            clearErrors('telepon');
                                        }}
                                        t={t}
                                    />
                                </React.Fragment>
                            )}
                    />
                    {errors.multiOutlet && <FormHelper error>{errors.multiOutlet.message}</FormHelper>}
                </FormGroup>
            ) : (
                <FormGroup responsive="input">
                    <FormLabel htmlFor="outlet" variant="required">
                        Outlet
                    </FormLabel>
                    <Controller
                        control={control}
                        name="outlet"
                        render={({ field: { onChange, value: val, ...restField } }) => (
                            <InputSelect
                                {...restField}
                                id="outlet"
                                placeholder={t('placeholder.select', { ns: TRANSLATION_NAMESPACES.GLOBAL })}
                                search
                                onSearch={() => { }}
                                option={outletList}
                                onChange={({ value }) => {
                                    onChange(value);
                                    clearErrors('telepon');
                                }}
                                value={setOutletValue(val)}
                                disabled={!isEditable || (mode === modeEnum.EDIT && String(watch('hakAkses')) === roleIdWithRequiredEmail[1])}
                                placement="top"
                            />
                        )}
                    />
                    {errors.outlet && <FormHelper error>{errors.outlet.message}</FormHelper>}
                </FormGroup>
            )}
            {/* component outlet single dibawah ini akan dihapus ketika multioutlet sudah aman */}
            {/* <FormGroup responsive="input">
                <FormLabel htmlFor="outlet" variant="required">
                    Outlet
                </FormLabel>
                <Controller
                    control={control}
                    name="outlet"
                    render={({ field: { onChange, value: val, ...restField } }) => (
                        <InputSelect
                            {...restField}
                            id="outlet"
                            placeholder={t('placeholder.select', { ns: TRANSLATION_NAMESPACES.GLOBAL })}
                            search
                            onSearch={() => { }}
                            option={outletList}
                            onChange={({ value }) => {
                                onChange(value);
                                clearErrors('telepon');
                            }}
                            value={setOutletValue(val)}
                            disabled={!isEditable || (mode === modeEnum.EDIT && String(watch('hakAkses')) === roleIdWithRequiredEmail[1])}
                            placement="top"
                        />
                    )}
                />
            </FormGroup> */}
            {/*
                yang bisa merubah pin:
                    - admin
                    - manager
                    - custom yang punya login ke android
            */}
            {
                (mode === modeEnum.ADD
                    || (mode === modeEnum.EDIT && (String(getUserLoginId()) === String(detailFormRaw.id)
                        || (String(getUserLoginId()) !== String(detailFormRaw.id) && rolesAllowedPinToMaintained.includes(detailFormRaw.hakAkses)))))
                && (
                    <FormGroup responsive="input">
                        <FormLabel htmlFor="pin" variant="required">
                            PIN
                        </FormLabel>
                        <Flex justify="between" gap={3}>
                            <InputGroup
                                isInvalid={!!errors.pin}
                                readOnly={mode === modeEnum.EDIT}
                            >
                                <Controller
                                    control={control}
                                    name="pin"
                                    render={({ field: { onChange, ...restField } }) => (
                                        <InputNumber
                                            {...restField}
                                            id="pin"
                                            allowEmptyFormatting
                                            allowLeadingZeros
                                            thousandSeparator={false}
                                            type={showPassword ? 'text' : 'password'}
                                            onValueChange={({ value }) => onChange(value)}
                                            autocomplete="new-password"
                                            maxLength={6}
                                            readOnly={mode === modeEnum.EDIT}
                                        />
                                    )}
                                />
                                <InputRightElement css={{ cursor: mode === modeEnum.ADD ? 'pointer' : 'text', display: mode === modeEnum.ADD ? 'block' : 'none' }}>
                                    {
                                        showPassword
                                            ? (
                                                <EyeSlashOutline
                                                    color={mode === modeEnum.ADD ? '#A5ABAB' : '#404545'}
                                                    onClick={() => mode === modeEnum.ADD && setShowPassword(false)}
                                                />
                                            ) : (
                                                <EyeOutline
                                                    color={mode === modeEnum.ADD ? '#A5ABAB' : '#404545'}
                                                    onClick={() => mode === modeEnum.ADD && setShowPassword(true)}
                                                />
                                            )
                                    }
                                </InputRightElement>
                            </InputGroup>
                            {mode === modeEnum.EDIT && (
                                <Box>
                                    <Button buttonType="ghost" type="button" onClick={handlePinModal}>{t('form.content.primaryData.label.changePIN')}</Button>
                                </Box>
                            )}
                        </Flex>
                        {errors.pin
                            ? <FormHelper error>{errors.pin.message}</FormHelper>
                            : <FormHelper css={{ color: '$textPrimary' }}>{t('form.content.primaryData.caption.defaultPIN')}</FormHelper>
                        }
                    </FormGroup>
                )
            }
        </Paper>
    );
};

InformasiUtama.propTypes = {
    register: PropTypes.func.isRequired,
    errors: PropTypes.shape({
        email: PropTypes.shape({ message: PropTypes.string }),
        hakAkses: PropTypes.shape({ message: PropTypes.string }),
        nama: PropTypes.shape({ message: PropTypes.string }),
        pin: PropTypes.shape({ message: PropTypes.string }),
        nik: PropTypes.shape({ message: PropTypes.string }),
        telepon: PropTypes.shape({ message: PropTypes.string }),
        posisi: PropTypes.shape({ message: PropTypes.string }),
        fotoEmployee: PropTypes.shape({ message: PropTypes.string }),
    }),
    control: PropTypes.shape({}).isRequired,
    watch: PropTypes.func.isRequired,
    mode: PropTypes.string.isRequired,
    isEditable: PropTypes.bool.isRequired,
    handleVerificationModal: PropTypes.func.isRequired,
    outletList: PropTypes.PropTypes.arrayOf(PropTypes.shape({
        name: PropTypes.string.isRequired,
        value: PropTypes.string.isRequired,
    })).isRequired,
    setOutletValue: PropTypes.func.isRequired,
    getUserLoginId: PropTypes.func.isRequired,
    detailFormRaw: PropTypes.shape({
        id: PropTypes.oneOfType([
            PropTypes.string,
            PropTypes.number,
        ]),
        hakAkses: PropTypes.string,
    }),
    roleList: PropTypes.PropTypes.arrayOf(PropTypes.shape({
        name: PropTypes.string.isRequired,
        value: PropTypes.string.isRequired,
    })).isRequired,
    setRoleValue: PropTypes.func.isRequired,
    rolesAllowedPinToMaintained: PropTypes.arrayOf(PropTypes.string),
    rolesAllowedRoleToMaintained: PropTypes.arrayOf(PropTypes.string),
    rolesNeedVerification: PropTypes.arrayOf(PropTypes.string),
    handlePhotoPreview: PropTypes.func.isRequired,
};

InformasiUtama.defaultProps = {
    errors: {},
    detailFormRaw: {},
    rolesAllowedPinToMaintained: [],
    rolesAllowedRoleToMaintained: [],
    rolesNeedVerification: [],
};

export default InformasiUtama;
