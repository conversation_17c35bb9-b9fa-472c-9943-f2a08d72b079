/* eslint-disable react/forbid-prop-types */
import React, {
  Fragment, useCallback, useContext, useEffect, useMemo, useRef, useState,
} from 'react';
import update from 'immutability-helper';
import { get } from 'lodash';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { PlusOutline, ChevronLeftOutline, TrashOutline } from '@majoo-ui/icons';
import { foundations } from '@majoo-ui/core';
import moment from 'moment';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { Trans, useTranslation } from 'react-i18next';
import {
  Paper, Button, Heading, ToastContext, Flex, Separator,
  Card as ProtoCard, AlertDialogFunction, FormGroup, FormLabel, FormHelper,
  Paragraph, InputSwitch, InputText, InputRadioGroup,
  InputRadio, InputTimePicker, Box, Tag, Table, Text
} from '@majoo-ui/react';
import { Controller, useForm } from 'react-hook-form';
import { styled } from '../../../stitches.config';

import CoreHOC from '../../../core/CoreHOC';
import { fetchNotification } from '../../../actions/notificationActions';
import { catchError } from '../../../utils/helper';
import * as masterApi from '../../../data/master';
import * as employeeApi from '../../../data/employee';
import * as masterSelectors from '../../../data/master/selectors';
import { updateSettingNotification } from '../../../data/settings';
import { PERMISSIONS } from '../../../constants/permissions';
import { getAccountType } from '../../../utils/savedLocalStorage';
import { FavoriteWrapper, TooltipGuidance } from '../../../components/retina';

import ModalPopup from '../../../components/modalpopup/Container';
import PilihKaryawanNew from './components/ModalPilihKaryawanNew';

const Card = styled(ProtoCard, {
  boxShadow: 'unset',
  border: '1px solid $gray100',
});

const Notification = ({ ...props }) => {
  const { t, ready, i18n: { language } } = useTranslation(['Settings/notificationSetting', 'translation', 'Pengaturan/changeNotification']);
  const { addToast } = useContext(ToastContext);
  const {
    idCabang, branchList, filterBranch, notification: notifRedux, router
  } = props;

  const notification = { ...notifRedux, employee_data: [] };
  const { showProgress, hideProgress } = props;
  const modalBuySupport = useRef();
  const [modalKaryawan, setModal] = useState(false);
  const [permissionList, setPermissionList] = useState([]);
  const [sbranchList, setBranchList] = useState([]);
  const {
    watch, setValue, control,
    getValues, handleSubmit,
  } = useForm({
    resolver: yupResolver(
      yup.object({
        notification: yup.object({
          date_email: yup.string().when('is_hari', {
            is: val => val === '1',
            then: yup.string()
              .required(t('helper.errEmail'))
              .matches('^([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,3})+(\\s?[,]\\s?|$))+$', t('helper.errFormat')),
          }),
          week_email: yup.string().when('is_minggu', {
            is: val => val === '1',
            then: yup.string()
              .required(t('helper.errEmail'))
              .matches('^([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,3})+(\\s?[,]\\s?|$))+$', t('helper.errFormat')),
          }),
          month_email: yup.string().when('is_bulan', {
            is: val => val === '1',
            then: yup.string()
              .required(t('helper.errEmail'))
              .matches('^([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,3})+(\\s?[,]\\s?|$))+$', t('helper.errFormat')),
          }),
        }),
      }),
    ),
    defaultValues: {
      notification,
    },
  });

  const selectedBranch = useMemo(() => (!filterBranch ? idCabang : filterBranch), [filterBranch, idCabang]);

  const removeValue = useCallback((list, value, separator) => {
    const separate = separator || ',';
    const values = list.split(separate);
    for (let i = 0; i < values.length; i += 1) {
      if (values[i] === value) {
        values.splice(i, 1);
        return values.join(separate);
      }
    }
    return list;
  }, []);

  const togleModal = useCallback(() => {
    setModal(prev => !prev);
  }, [modalKaryawan]);

  const branchName = useCallback(() => {
    const findName = sbranchList.find(x => String(x.id) === String(selectedBranch));
    if (findName) return findName.name;
    return null;
  }, [sbranchList, idCabang, filterBranch]);
  const [loading, setLoading] = useState(false);

  const _fetchDataHandler = (id) => {
    const { fetchNotification: fetchNotificationProps } = props;
    showProgress();
    setLoading(true)
    fetchNotificationProps(id).then((res) => {
      setValue('notification', { ...res.data, employee_data: [] });

      if (res.data.id_employee) {
        employeeApi.getList({ id_employee: res.data.id_employee, limit: res.data.id_employee.split(',').length }).then((emp) => {
          setValue('notification.employee_data', emp.data.reduce(
            (prev, next) => [
              ...prev,
              {
                id: String(next.id),
                name: next.name,
                available: true,
                id_cabang: next.id_cabang || '',
                nama_cabang: next.outlet_name,
                id_permission: next.permission_id,
                nama_permission: next.permission_name,
              },
            ],
            [],
          ));
        })
        setLoading(false);
        hideProgress();
        return;
      }
      setValue('notification.employee_data', []);
      setLoading(false);
      hideProgress();
    })
  };

  const buySupport = () => {
    const { router } = props;
    router.push('/support/buy');
  };

  const updateHandler = async () => {
    showProgress();
    const {
      is_hari: isHari,
      date_email: dateEmail,
      is_tutup_kasir: isTutupKasir,
      date_before: time,
      is_minggu: isMinggu,
      week_email: weekEmail,
      is_bulan: isBulan,
      month_email: monthEmail,
      is_reminder_iventory: isReminderIventory,
      employee_data: employeeData,
      id_employee_change_data,
      add_data,
      change_data,
    } = getValues('notification');

    const payload = {};
    Object.assign(payload, {
      is_tutup_kasir: isTutupKasir,
      id_outlet: selectedBranch,
      is_hari: isHari,
      date_email: dateEmail,
      is_minggu: isMinggu,
      week_email: weekEmail,
      is_bulan: isBulan,
      month_email: monthEmail,
      is_reminder_iventory: isReminderIventory,
      id_employee: employeeData.map(e => e.id).join(','),
      id_employee_change_data,
      add_data,
      change_data
    });

    if (String(isTutupKasir) === '0') {
      Object.assign(payload, { time: moment(time).format('HH:mm') });
    }

    const accountType = getAccountType();
    if (accountType === 'free' && (isHari || isMinggu || isBulan)) {
      modalBuySupport.current.showPopup();
      return;
    }

    updateSettingNotification(payload).then((response) => {
      if (!response.status) {
        throw new Error(response.msg);
      }
      _fetchDataHandler(selectedBranch);
      addToast({
        title: t('toast.success', { ns: 'translation' }),
        description: (
          <Trans
            t={t}
            i18nKey="toast.success"
            values={{ branch: branchName() }}
          />
        ),
        variant: 'success',
      });
    }).catch(() => {
      addToast({
        title: t('toast.error', { ns: 'translation' }),
        description: (
          <Trans
            t={t}
            i18nKey="toast.failed"
            values={{ branch: branchName() }}
          />
        ),
        variant: 'failed',
      });
    }).finally(() => {
      hideProgress();
      const newNotification = update(notification, {
        time: {
          $set: new Date(time),
        },
      });

      setValue('notification', newNotification);
    });
  };

  const onSaveDialog = () => {
    const dialog = new AlertDialogFunction({
      title: t('modal.title'),
      description: (
        <Trans
          t={t}
          i18nKey="modal.desc"
          values={{ branch: branchName() }}
        />
      ),
      labelCancel: t('label.cancel', { ns: 'translation' }),
      labelConfirm: t('label.continue', { ns: 'translation' }),
      onConfirm: updateHandler,
    });
    dialog.show();
  };

  useEffect(() => {
    if (ready) {
      _fetchDataHandler(selectedBranch);

      masterApi.fetchPermission().then((response) => {
        if (!response.status) throw new Error(response.msg);

        const retval = masterSelectors.toObjectPermission(response.data);
        const dataRole = retval.filter(x => x.id !== PERMISSIONS.KASIR && x.id !== PERMISSIONS.STAFF);
        dataRole.reverse().push({ id: '', name: t('label.allAccess', 'Semua Akses') });
        setPermissionList(dataRole.reverse());
      }).catch((err) => {
        addToast({
          title: t('toast.somethingWrong', { ns: 'translation' }),
          description: catchError(err),
          variant: 'failed',
        });
      });

      _fetchDataHandler(selectedBranch);
    }
  }, [ready, language, filterBranch, idCabang]);

  useEffect(() => {
    const _branchList = branchList.reduce((prev, next) => [...prev, {
      id: next[0],
      name: next[1],
    }], []);
    _branchList.reverse().push({ id: '-', name: 'Semua Cabang' });
    setBranchList(_branchList.reverse());
  }, [branchList]);

  const MailHelper = () => (
    <FormHelper>
      <Trans t={t} i18nKey="helper.mail" />
    </FormHelper>
  );

  return (
    <Paper responsive css={{ position: 'relative', padding: 'unset', userSelect: 'none' }}>
      <div id="a-dialog" />
      <Button
        css={{
          paddingLeft: '0',
          ml: '$spacing-06',
          left: '-8px',
          mt: '15px',
          '&:hover': {
            backgroundColor: 'inherit',
            outline: 'none',
          },
          '> div': {
            display: 'flex',
          },
        }}
        onClick={() => router.goBack()}
        buttonType="ghost"
        leftIcon={<ChevronLeftOutline color="currentColor" />}
      >
        {t('label.back', { ns: 'translation' })}
      </Button>
      <Heading css={{ padding: '30px $spacing-06' }} heading="pageTitle">
        {t('heading.sendSalesSummary', 'Kirim Ringkasan Penjualan')}
      </Heading>
      <Separator responsive css={{ my: 15 }} />
      <Flex gap={6} direction="column" css={{ padding: '$spacing-06 $spacing-06 $spacing-07' }}>
        <Card
          color="dark"
          css={{
            padding: '24px 16px', display: 'flex', flexDirection: 'column', gap: 24,
          }}
        >
          <Flex justify="between">
            <Heading heading="sectionTitle">
              {t('heading.dailySummary', 'Ringkasan Penjualan Harian')}
            </Heading>
            <Controller
              name="notification.is_hari"
              control={control}
              render={({ field: { value, onChange } }) => (
                <InputSwitch
                  dataOnLabel="ON"
                  dataOffLabel="OFF"
                  checked={value === '1'}
                  onCheckedChange={x => onChange(x ? '1' : '0')}
                />
              )}
            />
          </Flex>
          <Flex direction="column" gap={7}>
            <FormGroup responsive="input">
              <FormLabel>{t('label.sentOn', 'Waktu Kirim Notifikasi')}</FormLabel>
              <Controller
                name="notification.is_tutup_kasir"
                control={control}
                render={({ field: { value, onChange } }) => (
                  <InputRadioGroup value={value} onValueChange={x => onChange(x)} direction="column" css={{ gap: 16 }}>
                    <InputRadio value="1" label={t('label.cashierClose')} />
                    <Flex align="center" gap={5}>
                      <InputRadio value="0" label={t('label.onTime')} />
                      <Controller
                        name="notification.date_before"
                        control={control}
                        render={({ field: { value: v, onChange: oC } }) => (
                          <InputTimePicker
                            disabled={watch('notification.is_tutup_kasir') === '1'}
                            onChange={x => oC(x)}
                            css={{ width: 120 }}
                            value={v ? new Date(v) : new Date()}
                          />
                        )}
                      />
                    </Flex>
                  </InputRadioGroup>
                )}
              />
            </FormGroup>
            <FormGroup responsive="input">
              <FormLabel>{t('label.mailDestination', 'Email Tujuan')}</FormLabel>
              <Controller
                name="notification.date_email"
                control={control}
                render={({ field: { value, onChange }, formState: { errors } }) => (
                  <Fragment>
                    <InputText
                      value={value}
                      onChange={x => onChange(x.target.value)}
                      onKeyUp={(k) => {
                        if (k.key === 'Enter') onChange(value += ',');
                      }}
                      placeholder="Contoh: <EMAIL>, <EMAIL>"
                    />
                    {get(errors, 'notification.date_email')
                      && <FormHelper error>{get(errors, 'notification.date_email.message')}</FormHelper>}
                    <MailHelper />
                    <Box>
                      {value && value.split(',').map(x => (
                        <Tag
                          isRemovable
                          css={{ mr: 5, maxWidth: 'unset' }}
                          onRemove={() => onChange(removeValue(value, x, ','))}
                        >
                          {x}
                        </Tag>
                      ))}
                    </Box>
                  </Fragment>
                )}
              />
            </FormGroup>
          </Flex>
        </Card>
        <Card color="dark" css={{ padding: '24px 16px', display: 'flex', flexDirection: 'column', gap: 24 }}>
          <Flex justify="between">
            <Heading heading="sectionTitle">
              {t('heading.weeklySummary', 'Ringkasan Penjualan Mingguan')}
            </Heading>
            <Controller
              name="notification.is_minggu"
              control={control}
              render={({ field: { value, onChange } }) => (
                <InputSwitch
                  dataOnLabel="ON"
                  dataOffLabel="OFF"
                  checked={value === '1'}
                  onCheckedChange={x => onChange(x ? '1' : '0')}
                />
              )}
            />
          </Flex>
          <FormGroup responsive="input">
            <FormLabel>{t('label.mailDestination', 'Email Tujuan')}</FormLabel>
            <Controller
              name="notification.week_email"
              control={control}
              render={({ field: { value, onChange }, formState: { errors } }) => (
                <Fragment>
                  <InputText
                    value={value}
                    onChange={x => onChange(x.target.value)}
                    onKeyUp={(k) => {
                      if (k.key === 'Enter') onChange(value += ',');
                    }}
                    placeholder="Contoh: <EMAIL>, <EMAIL>"
                  />
                  {get(errors, 'notification.week_email')
                    && <FormHelper error>{get(errors, 'notification.week_email.message')}</FormHelper>}
                  <MailHelper />
                  <Box>
                    {value && value.split(',').map(x => (
                      <Tag
                        isRemovable
                        css={{ mr: 5, maxWidth: 'unset' }}
                        onRemove={() => onChange(removeValue(value, x, ','))}
                      >
                        {x}
                      </Tag>
                    ))}
                  </Box>
                </Fragment>
              )}
            />
          </FormGroup>
        </Card>
        <Card color="dark" css={{ padding: '24px 16px', display: 'flex', flexDirection: 'column', gap: 24 }}>
          <Flex justify="between">
            <Heading heading="sectionTitle">
              {t('heading.monthlySummary', 'Ringkasan Penjualan Bulanan')}
            </Heading>
            <Controller
              name="notification.is_bulan"
              control={control}
              render={({ field: { value, onChange } }) => (
                <InputSwitch
                  dataOnLabel="ON"
                  dataOffLabel="OFF"
                  checked={value === '1'}
                  onCheckedChange={x => onChange(x ? '1' : '0')}
                />
              )}
            />
          </Flex>
          <FormGroup responsive="input">
            <FormLabel>{t('label.mailDestination', 'Email Tujuan')}</FormLabel>
            <Controller
              name="notification.month_email"
              control={control}
              render={({ field: { value, onChange }, formState: { errors } }) => (
                <Fragment>
                  <InputText
                    value={value}
                    onChange={x => onChange(x.target.value)}
                    onKeyUp={(k) => {
                      if (k.key === 'Enter') onChange(value += ',');
                    }}
                    placeholder="Contoh: <EMAIL>, <EMAIL>"
                  />
                  {get(errors, 'notification.month_email')
                    && <FormHelper error>{get(errors, 'notification.month_email.message')}</FormHelper>}
                  <MailHelper />
                  <Box>
                    {value && value.split(',').map(x => (
                      <Tag
                        isRemovable
                        css={{ mr: 5, maxWidth: 'unset' }}
                        onRemove={() => onChange(removeValue(value, x, ','))}
                      >
                        {x}
                      </Tag>
                    ))}
                  </Box>
                </Fragment>
              )}
            />
          </FormGroup>
        </Card>
        <Card color="dark" css={{ padding: '24px 16px', display: 'flex', flexDirection: 'column', gap: 24 }}>
          <Flex justify="between">
            <Flex direction="column" gap={2}>
              <Heading heading="sectionTitle">
                {t('heading.inventoryNotif', 'Pengingat Inventori')}
              </Heading>
              <Paragraph paragraph="shortContentRegular">
                {t('helper.reminder')}
              </Paragraph>
            </Flex>
            <Controller
              name="notification.is_reminder_iventory"
              control={control}
              render={({ field: { value, onChange } }) => (
                <InputSwitch
                  dataOnLabel="ON"
                  dataOffLabel="OFF"
                  checked={value === '1'}
                  onCheckedChange={x => onChange(x ? '1' : '0')}
                />
              )}
            />
          </Flex>
          <Flex direction="column" gap={5}>
            <Flex justify="between" wrap="wrap" css={{ mt: '18px' }}>
              <Text
                css={{ color: '#272A2A', fontSize: '14px', fontWeight: '600' }}
              >
                {t('receiver_2', 'Karyawan/Penerima', {
                  ns: 'Pengaturan/changeNotification',
                })}
              </Text>
              <Button
                onClick={() => setModal(true)}
                buttonType="primary"
              >
                {t('choose_emp', 'Pilih Karyawan', {
                  ns: 'Pengaturan/changeNotification',
                })}
              </Button>
            </Flex>
            <Table
              css={{ mt: '24px', width: '100%' }}
              columns={[
                {
                  Header: t('emp_name', 'Nama Karyawan', {
                    ns: 'Pengaturan/changeNotification',
                  }),
                  accessor: 'name',
                  isMobileHeader: true,
                },
                {
                  Header: 'Outlet',
                  accessor: 'nama_cabang',
                },
                {
                  Header: t('access', 'Hak Akses', {
                    ns: 'Pengaturan/changeNotification',
                  }),
                  accessor: 'nama_permission',
                },
                {
                  Header: '',
                  accessor: 'delete',
                  unsortable: true,
                  Cell: ({ row }) => (
                    <Box
                      css={{ cursor: 'pointer' }}
                      onClick={() => {
                        const result = watch('notification.employee_data').filter(d => d.id !== row.id);

                        setValue('notification.employee_data', result);
                      }}
                    >
                      <TrashOutline />
                    </Box>
                  ),
                },
              ]}
              data={watch('notification.employee_data')}
              headerCss={{
                backgroundColor: '#FAFAFA',
              }}
              isLoading={loading}
              totalData={watch('notification.employee_data').length}
            />
            {modalKaryawan && (
              <Controller
                name="notification.employee_data"
                control={control}
                render={({ field: { value, onChange } }) => (
                  <PilihKaryawanNew
                    t={t}
                    open={modalKaryawan}
                    onOpenChange={() => setModal(false)}
                    permissions={permissionList}
                    value={value.map(v => v.id).join(',')}
                    showProgress={props.showProgress}
                    hideProgress={props.hideProgress}
                    onSubmit={rows => {
                      if (rows.length === 0) {
                        onChange(value);
                      } else {
                        onChange(rows);
                      }
                    }}
                  />
                )}
              />
            )}
          </Flex>
        </Card>
      </Flex>
      <Flex
        css={{
          padding: '10px 42px 24px',
          backgroundColor: '$white',
          width: '$full',
          position: 'sticky',
          bottom: 0,
          left: 0,
        }}
        justify="end"
      >
        <Button onClick={handleSubmit(onSaveDialog)}>
          {t('save', { ns: 'translation' }, 'Simpan')}
        </Button>
      </Flex>
      <ModalPopup
        confirmText="Beli Sekarang"
        cancelText="Batal"
        confirmHandle={buySupport}
        ref={modalBuySupport}
      >
        <h3 className="modal-title">Aktifkan Notifikasi</h3>
        <p>
          Upgrade layanan anda sekarang ke Business / Business PRO untuk dapat mengaktifkan laporan notifikasi via Email melalui halaman Layanan &gt; Layanan Berbayar &gt; Beli Support
        </p>
      </ModalPopup>
    </Paper>
  );
};

Notification.propTypes = {
  getPassport: PropTypes.func,
  fetchNotification: PropTypes.func,
  branchList: PropTypes.arrayOf(PropTypes.shape({})),
  filterBranch: PropTypes.string.isRequired,
  idCabang: PropTypes.string,
  showProgress: PropTypes.func.isRequired,
  hideProgress: PropTypes.func.isRequired,
  router: PropTypes.shape({
    push: PropTypes.func,
  }),
  notification: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.shape({})),
    PropTypes.shape({}),
  ]),
  cabangList: PropTypes.arrayOf(PropTypes.shape({})),
};

Notification.defaultProps = {
  fetchNotification: () => { },
  getPassport: () => { },
  branchList: [],
  idCabang: '',
  router: {
    push: () => {
    },
  },
  notification: {},
  cabangList: [],
};

const mapStateToProps = state => ({
  cabangList: state.branch.list,
  notification: state.notification.notificationResult,
  filterBranch: state.branch.filter,
  branchList: state.branch.listOption,
});

const mapDispatchToProps = dispatch => ({
  fetchNotification: payload => dispatch(fetchNotification(payload)),
});

export default connect(mapStateToProps, mapDispatchToProps)(CoreHOC(Notification));