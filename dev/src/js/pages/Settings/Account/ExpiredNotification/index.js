import React, { useContext } from 'react';
import {
    Box,
    Card,
    Heading,
    Text,
    Flex,
    InputSuffix,
    InputSwitch,
    Table,
    Button,
    AlertDialog,
    ToastContext,
    InputGroup,
    InputNumber,
    FormHelper,
} from '@majoo-ui/react';
import { Controller, useForm } from 'react-hook-form';
import { connect } from 'react-redux';
import { useTranslation } from 'react-i18next';
import {
    ChevronLeftOutline,
} from '@majoo-ui/icons';
import CoreHOC from '../../../../core/CoreHOC';
import { TABLE_COLUMN } from './constants';
import { defaultForm, resolver } from './form';
import PilihKaryawanNew from '../components/ModalPilihKaryawanNew';
import { useExpiredNotificationSettings } from './hooks/useExpiredNotificationSettings';
import { useTranslationHook } from './utils/lang.utils';

const ExpiredNotification = (props) => {
    const {
        branchList, showProgress, hideProgress, router,
    } = props;
    const toast = useContext(ToastContext);

    // translation for popup karyawan
    const { t } = useTranslation([
        'Settings/notificationSetting',
        'translation',
        'Pengaturan/changeNotification',
    ]);

    const { LANG_DATA, LANG_KEY, TransComponent, currentLang } = useTranslationHook();
    const forms = useForm({ defaultValues: defaultForm, resolver: resolver(LANG_DATA) });
    const { formState: { errors, isSubmitting }, register, watch } = forms;

    const {
        loading,
        handleSetFinalForm,
        handleConfirm,
        permissionList,
        show,
        setShow,
        employeePopup,
        setEmployeePopup,
        handleDelete,
        handleChange,
        outletName,
    } = useExpiredNotificationSettings({
        ...props, ...forms, toast, LANG_DATA, LANG_KEY, TransComponent, currentLang,
    });
    const isDisabled = watch('is_reminder_expired') === '0';

    return (
        <Card color="dark" responsive css={{ padding: 0 }}>
            <Box css={{ padding: '16px 24px 32px 24px' }}>
                <Button
                    css={{
                        paddingLeft: '0',
                        left: '-8px',
                        mb: '30px',
                        '&:hover': {
                            backgroundColor: 'inherit',
                            outline: 'none',
                        },
                        '> div': {
                            display: 'flex',
                        },
                    }}
                    onClick={() => router.goBack()}
                    buttonType="ghost"
                    leftIcon={<ChevronLeftOutline color="currentColor" />}
                >
                    {LANG_DATA.BACK}
                </Button>
                <Heading heading="pageTitle">
                    {LANG_DATA.TITLE}
                </Heading>
            </Box>
            <Box css={{ border: '1px solid #EEF0F0', width: '100%' }} />
            <Box css={{ padding: '32px 24px' }}>
                <Flex
                    justify="between"
                    wrap="wrap"
                    css={{
                        width: '100%',
                        border: '1px solid #EEF0F0',
                        borderRadius: '8px',
                        padding: '24px 16px',
                    }}
                >
                    <Box>
                        <Flex align="center" css={{ gap: '8px', mb: '4px' }}>
                            <Text
                                css={{ color: '#272A2A', fontSize: '16px', fontWeight: '600' }}
                            >
                                {LANG_DATA.FORM.SEND_NOTIF.TITLE}
                            </Text>
                        </Flex>
                        <Text css={{ fontSize: '14px', fontWeight: '400' }}>
                            {LANG_DATA.FORM.SEND_NOTIF.DESCRIPTION}
                        </Text>
                    </Box>
                    <Box>
                        <InputSwitch
                            checked={watch('is_reminder_expired') === '1'}
                            onCheckedChange={e => handleChange('is_reminder_expired', e ? '1' : '0')}
                            dataOnLabel="ON"
                            dataOffLabel="OFF"
                        />
                    </Box>
                </Flex>
                <Flex
                    direction="column"
                    gap={6}
                    css={{
                        width: '100%',
                        mt: '24px',
                        border: '1px solid #EEF0F0',
                        borderRadius: '8px',
                        padding: '24px 16px',
                    }}
                >
                    <Box>
                        <Flex align="center" css={{ gap: '8px', mb: '4px' }}>
                            <Text
                                css={{ color: '#272A2A', fontSize: '16px', fontWeight: '600' }}
                            >
                                {LANG_DATA.FORM.TIME.TITLE}
                            </Text>
                        </Flex>
                        <Text css={{ fontSize: '14px', fontWeight: '400' }}>
                            {LANG_DATA.FORM.TIME.DESCRIPTION}
                        </Text>
                    </Box>
                    <Flex
                        justify="between"
                        wrap="wrap"
                        align="center"
                    >
                        <Box>
                            <Text
                                css={{ color: '#272A2A', fontSize: '14px', fontWeight: '600' }}
                            >
                                {LANG_DATA.FORM.TIME.LABEL}
                            </Text>
                        </Box>
                        <Box css={{ width: '80%' }}>
                            {watch('id_cabang') ? (
                                <InputGroup isInvalid={!!errors.days_before_expired}>
                                    <InputNumber
                                        {...register('days_before_expired')}
                                        defaultValue={watch('days_before_expired')}
                                        allowNegative={false}
                                        isAllowed={({ floatValue }) => floatValue === undefined || (floatValue >= 0 && floatValue <= 1000)}
                                        onValueChange={({ floatValue }) => handleChange('days_before_expired', floatValue)}
                                        placeholder={LANG_DATA.FORM.TIME.PLACEHOLDER}
                                        css={{ border: 'none', borderRadius: '8px 0px 0px 8px !important' }}
                                        disabled={isDisabled}
                                        isInvalid={!!errors.days_before_expired}
                                    />
                                    <InputSuffix>{LANG_DATA.FORM.TIME.SUFFIX}</InputSuffix>
                                </InputGroup>
                            ) : null}
                            {errors.days_before_expired && <FormHelper error>{errors.days_before_expired.message}</FormHelper>}
                        </Box>
                    </Flex>
                </Flex>
                <Flex
                    justify="between"
                    wrap="wrap"
                    css={{
                        width: '100%',
                        mt: '24px',
                        border: '1px solid #EEF0F0',
                        borderRadius: '8px',
                        padding: '24px 16px',
                    }}
                >
                    <Box css={{ width: '100%' }}>
                        <Flex align="center" justify="between" wrap="wrap">
                            <Box>
                                <Box css={{ mb: '4px' }}>
                                    <Text
                                        css={{
                                            color: '#272A2A',
                                            fontSize: '16px',
                                            fontWeight: '600',
                                        }}
                                    >
                                        {LANG_DATA.FORM.RECEIVER.TITLE}
                                    </Text>
                                </Box>
                                <Text css={{ fontSize: '14px', fontWeight: '400' }}>
                                    {LANG_DATA.FORM.RECEIVER.DESCRIPTION}
                                </Text>
                            </Box>
                            <Button
                                onClick={() => setEmployeePopup(true)}
                                buttonType="primary"
                                disabled={loading || isSubmitting || isDisabled}
                            >
                                {LANG_DATA.FORM.RECEIVER.SELECT_EMPLOYEE}
                            </Button>
                        </Flex>
                        <Table
                            css={{ mt: '24px', width: '100%' }}
                            columns={TABLE_COLUMN(LANG_DATA, handleDelete, isDisabled)}
                            data={watch('id_employee')}
                            headerCss={{
                                backgroundColor: '#FAFAFA',
                            }}
                            isLoading={loading}
                            totalData={watch('id_employee').length}
                        />
                    </Box>
                </Flex>
                <Flex justify="end">
                    <Button
                        onClick={handleSetFinalForm}
                        css={{ mt: '30px' }}
                        disabled={!Array.isArray(branchList) || loading || isSubmitting}
                        buttonType="primary"
                    >
                        {LANG_DATA.SAVE}
                    </Button>
                </Flex>
            </Box>
            {show && (
                <AlertDialog
                    title={LANG_DATA.MODAL_CONFIRMATION.TITLE}
                    description={(
                        <TransComponent i18nKey={LANG_KEY.MODAL_CONFIRMATION.DESCRIPTION}>
                            {{ outlet: outletName }}
                        </TransComponent>
                    )}
                    disabledButton={isSubmitting}
                    hideCloseButton={isSubmitting}
                    open={show}
                    onCancel={() => setShow(false)}
                    onConfirm={() => handleConfirm()}
                    labelCancel={LANG_DATA.MODAL_CONFIRMATION.CANCEL}
                    labelConfirm={LANG_DATA.MODAL_CONFIRMATION.SAVE}
                />
            )}
            {employeePopup && (
                <Controller
                    name="id_employee"
                    control={forms.control}
                    render={({ field: { value, onChange } }) => (
                        <PilihKaryawanNew
                            t={t}
                            open={employeePopup}
                            onOpenChange={() => setEmployeePopup(false)}
                            permissions={permissionList}
                            value={value.map(v => v.id).join(',')}
                            showProgress={showProgress}
                            hideProgress={hideProgress}
                            onSubmit={(rows) => {
                                if (rows.length === 0) {
                                    onChange(value);
                                } else {
                                    onChange(rows);
                                }
                            }}
                        />
                    )}
                />
            )}
        </Card>
    );
};

const mapStateToProps = state => ({
    branchList: state.branch.list,
});

export default connect(mapStateToProps)(CoreHOC(ExpiredNotification));
