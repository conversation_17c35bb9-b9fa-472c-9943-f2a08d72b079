import { yupResolver } from '@hookform/resolvers/yup';
import { string, object, array, number } from 'yup';
import { TIMING_OPTION } from './enum';

export const defaultForm = {
    is_notif_wa_penjualan_perjam: 0,
    wa_penjualan_perjam_setting: {
        wa_sender_identifier: '',
        wa_sender_number: '',
        notif_time_type: TIMING_OPTION.OPERATIONAL,
        notif_time_start: '09:00',
        notif_time_end: '18:00',
        notif_recipient: [{ value: '' }],
    },
};

export const resolver = t =>
    yupResolver(
        object({
            is_notif_wa_penjualan_perjam: number(),
            wa_penjualan_perjam_setting: object().when('is_notif_wa_penjualan_perjam', {
                is: 1,
                then: object({
                    wa_sender_identifier: string().required(
                        t('penjualan_per_jam_notif.form.validation.senderRequired', 'Nomor pengirim harus dipilih'),
                    ),
                    wa_sender_number: string(),
                    notif_time_start: string().when('notif_time_type', {
                        is: TIMING_OPTION.SCHEDULED,
                        then: string().required(
                            t('penjualan_per_jam_notif.form.validation.startTimeRequired', 'Waktu mulai harus diisi'),
                        ),
                    }),
                    notif_time_end: string().when('notif_time_type', {
                        is: TIMING_OPTION.SCHEDULED,
                        then: string().required(
                            t('penjualan_per_jam_notif.form.validation.endTimeRequired', 'Waktu selesai harus diisi'),
                        ),
                    }),
                    notif_recipient: array()
                        .of(
                            object({
                                value: string()
                                    .required(
                                        t(
                                            'tutup_kasir_notif.form.validation.recipientRequired',
                                            'Nomor penerima harus diisi',
                                        ),
                                    )
                                    .min(
                                        8,
                                        t(
                                            'tutup_kasir_notif.form.validation.minPhoneDigit',
                                            'Nomor penerima minimal 10 digit',
                                        ),
                                    )
                                    .test(
                                        'is-not-sender',
                                        t(
                                            'tutup_kasir_notif.form.validation.recipientSameAsSender',
                                            'Nomor penerima tidak boleh sama dengan nomor pengirim',
                                        ),
                                        (value, ctx) => {
                                            if (ctx.from && ctx.from.length > 1) {
                                                let fromIndex = 1;
                                                fromIndex = ctx.from.findIndex(
                                                    val => !!val.value && !!val.value.wa_sender_number,
                                                );
                                                if (fromIndex > -1) {
                                                    return value !== ctx.from[fromIndex].value.wa_sender_number;
                                                }
                                                return true;
                                            }
                                            return true;
                                        },
                                    ),
                            }),
                        )
                        .min(
                            1,
                            t(
                                'tutup_kasir_notif.form.validation.recipientsRequired',
                                'Minimal satu nomor penerima harus diisi',
                            ),
                        ),
                }),
            }),
        }),
    );
