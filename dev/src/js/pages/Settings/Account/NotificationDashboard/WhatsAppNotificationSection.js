import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Box, Heading, Flex, Text, Button, Paragraph, TagStatus } from '@majoo-ui/react';
import { SalesArrangementOutline, CalendarOutline } from '@majoo-ui/icons';
import { colors } from '~/stitches.config';
import { getWhatsAppFeatures } from '~/data/whatsAppSetting';
import moment from 'moment';

const WhatsAppNotificationSection = ({ router, t, outletId }) => {
    const [whatsAppFeatures, setWhatsAppFeatures] = useState({});

    const reportNotif = whatsAppFeatures.notif_laporan || {};

    const fetchWhatsAppFeatures = async () => {
        try {
            const res = await getWhatsAppFeatures({
                outlet_id: outletId,
            });
            if (!res.data) throw new Error(res.message);
            setWhatsAppFeatures(
                res.data.reduce((acc, item) => {
                    acc[item.feature_code] = item;
                    return acc;
                }, {}),
            );
        } catch (error) {
            setWhatsAppFeatures({});
        }
    };

    useEffect(() => {
        fetchWhatsAppFeatures();
    }, [outletId]);

    return (
        <Flex
            gap={5}
            css={{
                width: '100%',
                mt: '$spacing-09',
                flexDirection: 'column',
            }}
        >
            {/* Header Section */}
            <Flex
                css={{
                    width: '100%',
                }}
                align="center"
                justify="between"
            >
                <Flex gap={2} direction="column" css={{ flex: 1 }}>
                    <Flex align="center" gap={3}>
                        <Heading color="primary" heading="sectionTitle">
                            {t('whatsapp_integration', 'Kirim Laporan via WhatsApp Terintegrasi')}
                        </Heading>
                        {reportNotif.expired_add_on && (
                            <TagStatus type="success" size="sm" css={{ maxWidth: 'unset', alignItems: 'center' }}>
                                {t('active_until', 'Aktif hingga')} {moment(reportNotif.expired_add_on).format('DD/MM/YYYY')}
                            </TagStatus>
                        )}
                    </Flex>
                    <Text variant="helper">
                        {t(
                            'whatsapp_integration_desc',
                            'Otomatisasi pengiriman laporan tutup kasir, penjualan per jam, dan berbagai laporan lain melalui integrasi notifikasi WhatsApp',
                        )}
                    </Text>
                </Flex>
                <Flex align="center" gap={4}>
                    {reportNotif.expired_add_on ? (
                        <Button
                            buttonType={reportNotif.is_have_add_on ? "secondary" : 'primary'}
                            size="sm"
                            onClick={() => router.push('/message-setting/setting-whatsapp')}
                        >
                            {t('set_number', 'Atur Nomor WhatsApp')}
                        </Button>
                    ) : (
                        <React.Fragment>
                            <Flex align="center">
                                <Paragraph paragraph="shortContentBold">Rp 50.000</Paragraph>
                                <Paragraph>
                                    /{t('period.month', 'Bulan', { ns: 'translation' }).toLowerCase()}
                                </Paragraph>
                            </Flex>
                            <Button buttonType="primary" size="sm" onClick={() => router.push('/support/buy?tab=support')}>
                                {t('buy_addon', 'Beli Add-On')}
                            </Button>
                        </React.Fragment>
                    )}
                </Flex>
            </Flex>

            {/* Notification Items */}
            <Flex gap={3} direction="column">
                {/* Cash Register Report */}
                <Flex
                    css={{
                        width: '100%',
                        border: '1px solid $bgBorder',
                        borderRadius: '$lg',
                        padding: '$spacing-03 $spacing-05',
                    }}
                    align="center"
                    justify="between"
                >
                    <Flex align="center" gap={5}>
                        <Flex
                            align="center"
                            justify="center"
                            css={{
                                minWidth: '48px',
                                minHeight: '48px',
                                borderRadius: '50%',
                                backgroundColor: '$bgGray',
                            }}
                        >
                            <SalesArrangementOutline color={colors.bgDark} size={24} />
                        </Flex>
                        <Box>
                            <Heading heading="sectionSubTitle" color="primary">
                                {t('cash_register_report', 'Laporan Tutup Kasir')}
                            </Heading>
                            <Text variant="helper">
                                {t(
                                    'cash_register_report_desc',
                                    'Otomatiskan pengiriman laporan tutup kasir via WhatsApp',
                                )}
                            </Text>
                        </Box>
                    </Flex>
                    <Button
                        buttonType="ghost"
                        size="sm"
                        disabled={!reportNotif.is_have_add_on}
                        onClick={() => router.push('/pengaturan-bisnis/tutup-kasir-notification')}
                    >
                        {t('set_notif', 'Atur Notifikasi')}
                    </Button>
                </Flex>

                {/* Hourly Sales Report */}
                <Flex
                    css={{
                        width: '100%',
                        border: '1px solid $bgBorder',
                        borderRadius: '$lg',
                        padding: '$spacing-03 $spacing-05',
                    }}
                    align="center"
                    justify="between"
                >
                    <Flex align="center" gap={5}>
                        <Flex
                            align="center"
                            justify="center"
                            css={{
                                minWidth: '48px',
                                minHeight: '48px',
                                borderRadius: '50%',
                                backgroundColor: '$bgGray',
                            }}
                        >
                            <SalesArrangementOutline color={colors.bgDark} size={24} />
                        </Flex>
                        <Box>
                            <Heading heading="sectionSubTitle" color="primary">
                                {t('hourly_sales_report', 'Laporan Penjualan Per Jam')}
                            </Heading>
                            <Text variant="helper">
                                {t(
                                    'hourly_sales_report_desc',
                                    'Otomatiskan pengiriman laporan penjualan per jam via WhatsApp',
                                )}
                            </Text>
                        </Box>
                    </Flex>
                    <Button
                        buttonType="ghost"
                        size="sm"
                        disabled={!reportNotif.is_have_add_on}
                        onClick={() => router.push('/pengaturan-bisnis/penjualan-per-jam-notification')}
                    >
                        {t('set_notif', 'Atur Notifikasi')}
                    </Button>
                </Flex>

                {/* Attendance Report */}
                <Flex
                    css={{
                        width: '100%',
                        border: '1px solid $bgBorder',
                        borderRadius: '$lg',
                        padding: '$spacing-03 $spacing-05',
                    }}
                    align="center"
                    justify="between"
                >
                    <Flex align="center" gap={5}>
                        <Flex
                            align="center"
                            justify="center"
                            css={{
                                minWidth: '48px',
                                minHeight: '48px',
                                borderRadius: '50%',
                                backgroundColor: '$bgGray',
                            }}
                        >
                            <CalendarOutline color={colors.bgDark} size={24} />
                        </Flex>
                        <Box>
                            <Heading heading="sectionSubTitle" color="primary">
                                {t('attendance_report', 'Laporan Absensi')}
                            </Heading>
                            <Text variant="helper">
                                {t('attendance_report_desc', 'Otomatiskan pengiriman laporan absensi via WhatsApp')}
                            </Text>
                        </Box>
                    </Flex>
                    <Button
                        buttonType="ghost"
                        size="sm"
                        disabled={!reportNotif.is_have_add_on}
                        onClick={() => router.push('/pengaturan-bisnis/laporan-absensi-notification')}
                    >
                        {t('set_notif', 'Atur Notifikasi')}
                    </Button>
                </Flex>
            </Flex>
        </Flex>
    );
};

WhatsAppNotificationSection.propTypes = {
    router: PropTypes.shape({
        push: PropTypes.func,
    }).isRequired,
    t: PropTypes.func.isRequired,
    outletId: PropTypes.number.isRequired,
};

export default WhatsAppNotificationSection;
