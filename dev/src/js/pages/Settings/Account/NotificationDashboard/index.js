import React, { useEffect, useState, useMemo } from 'react';
import { Box, Card, Heading, Flex, Text, Button } from '@majoo-ui/react';
import { SalesArrangementOutline, PencilSquareOutline, InventoriesOutline } from '@majoo-ui/icons';
import { connect } from 'react-redux';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import CoreHOC from '../../../../core/CoreHOC';
import { BannerText } from '../../../../components/retina';
import WhatsappNotificationLogModal from '../components/WhatsappNotificationLogModal';
import WhatsAppNotificationSection from './WhatsAppNotificationSection';

const NotificationDashboard = props => {
    const { router, filterBranch, idCabang, branchList, hideProgress } = props;
    const [sbranchList, setBranchList] = useState([]);
    const { t } = useTranslation('Pengaturan/changeNotification');
    const [openWhatsappLogModal, setOpenWhatsappLogModal] = useState(false);
    useEffect(() => {
        const _branchList = branchList.reduce(
            (prev, next) => [
                ...prev,
                {
                    id: next[0],
                    name: next[1],
                },
            ],
            [],
        );
        setBranchList(_branchList.reverse());

        hideProgress();
    }, [branchList]);

    const selectedBranchId = useMemo(() => (!filterBranch ? idCabang : filterBranch), [filterBranch, idCabang]);

    const branchInfo = useMemo(() => {
        const findName = sbranchList.find(x => String(x.id) === String(selectedBranchId));
        if (findName) return { name: findName.name, id: findName.id };
        return { name: '', id: '' };
    }, [sbranchList, selectedBranchId]);

    return (
        <React.Fragment>
            <Card color="dark" responsive css={{ padding: 0 }}>
                <Flex justify="between" css={{ padding: '32px 24px' }}>
                    <Heading heading="pageTitle">
                        {t('title', 'Pengaturan Notifikasi')} - {branchInfo.name}
                    </Heading>
                    <Button buttonType="ghost" onClick={() => setOpenWhatsappLogModal(true)}>
                        {t('whatsapp_notif_log', 'Log Notifikasi WhatsApp')}
                    </Button>
                </Flex>
                <Box css={{ border: '1px solid #EEF0F0', width: '100%' }} />
                <Box css={{ padding: '16px 24px' }}>
                    <BannerText css={{ my: 0 }} />
                </Box>
                <Box css={{ padding: '32px 24px' }}>
                    <Flex
                        css={{
                            width: '100%',
                            border: '1px solid #EEF0F0',
                            borderRadius: '8px',
                            padding: '16px',
                        }}
                        align="center"
                        justify="between"
                    >
                        <Flex align="center" css={{ gap: '16px' }}>
                            <Flex
                                align="center"
                                justify="center"
                                css={{
                                    minWidth: '48px',
                                    minHeight: '48px',
                                    borderRadius: '50%',
                                    backgroundColor: '#FAFAFA',
                                }}
                            >
                                <SalesArrangementOutline color="#404545" size={24} />
                            </Flex>
                            <Box>
                                <Text css={{ color: '#272A2A', fontSize: '14px', fontWeight: '600' }}>
                                    {t('send_summary', 'Kirim Ringkasan Penjualan')}
                                </Text>
                                <Text css={{ fontSize: '12px', fontWeight: '400' }}>
                                    {t('send_summary_d', 'Atur notifikasi untuk ringkasan penjualan Anda')}
                                </Text>
                            </Box>
                        </Flex>
                        <Button buttonType="ghost" onClick={() => router.push('/pengaturan-bisnis/notification')}>
                            {t('set_notif', 'Atur Notifikasi')}
                        </Button>
                    </Flex>
                    <Flex
                        css={{
                            width: '100%',
                            border: '1px solid #EEF0F0',
                            borderRadius: '8px',
                            padding: '16px',
                            mt: '24px',
                        }}
                        align="center"
                        justify="between"
                    >
                        <Flex align="center" css={{ gap: '16px' }}>
                            <Flex
                                align="center"
                                justify="center"
                                css={{
                                    minWidth: '48px',
                                    minHeight: '48px',
                                    borderRadius: '50%',
                                    backgroundColor: '#FAFAFA',
                                }}
                            >
                                <PencilSquareOutline color="#404545" size={24} />
                            </Flex>
                            <Box>
                                <Text css={{ color: '#272A2A', fontSize: '14px', fontWeight: '600' }}>
                                    {t('send_changed_notif', 'Kirim Notifikasi Perubahan')}
                                </Text>
                                <Text css={{ fontSize: '12px', fontWeight: '400' }}>
                                    {t('send_changed_notif_d', 'Atur notifikasi untuk Perubahan pada data Anda')}
                                </Text>
                            </Box>
                        </Flex>
                        <Button
                            buttonType="ghost"
                            onClick={() =>
                                router.push(`/pengaturan-bisnis/change-notification?outlet_id=${branchInfo.id}`)
                            }
                        >
                            {t('set_notif', 'Atur Notifikasi')}
                        </Button>
                    </Flex>
                    <Flex
                        css={{
                            width: '100%',
                            border: '1px solid #EEF0F0',
                            borderRadius: '8px',
                            padding: '16px',
                            mt: '24px',
                        }}
                        align="center"
                        justify="between"
                    >
                        <Flex align="center" css={{ gap: '16px' }}>
                            <Flex
                                align="center"
                                justify="center"
                                css={{
                                    minWidth: '48px',
                                    minHeight: '48px',
                                    borderRadius: '50%',
                                    backgroundColor: '#FAFAFA',
                                }}
                            >
                                <InventoriesOutline color="#404545" size={24} />
                            </Flex>
                            <Box>
                                <Text css={{ color: '#272A2A', fontSize: '14px', fontWeight: '600' }}>
                                    {t('exp_notif', 'Notifikasi Dtok Kadaluwarsa')}
                                </Text>
                                <Text css={{ fontSize: '12px', fontWeight: '400' }}>
                                    {t('exp_notif_d', 'Atur notifikasi untuk stok kadaluwarsa')}
                                </Text>
                            </Box>
                        </Flex>
                        <Button
                            buttonType="ghost"
                            onClick={() =>
                                router.push(`/pengaturan-bisnis/expired-notification?outlet_id=${branchInfo.id}`)
                            }
                        >
                            {t('set_notif', 'Atur Notifikasi')}
                        </Button>
                    </Flex>

                    <WhatsAppNotificationSection router={router} t={t} outletId={selectedBranchId} />
                </Box>
            </Card>
            {openWhatsappLogModal && (
                <WhatsappNotificationLogModal
                    open={openWhatsappLogModal}
                    onOpenChange={setOpenWhatsappLogModal}
                    t={t}
                />
            )}
        </React.Fragment>
    );
};

NotificationDashboard.propTypes = {
    router: PropTypes.shape({
        push: PropTypes.func,
    }).isRequired,
    filterBranch: PropTypes.string,
    idCabang: PropTypes.string,
    branchList: PropTypes.arrayOf(
        PropTypes.oneOfType([
            PropTypes.string,
            PropTypes.number,
            PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])),
        ]),
    ),
    hideProgress: PropTypes.func,
};

NotificationDashboard.defaultProps = {
    filterBranch: '',
    idCabang: '',
    branchList: [],
    hideProgress: () => {},
};

const mapStateToProps = state => ({
    branchList: state.branch.listOption,
});

export default connect(mapStateToProps, null)(CoreHOC(NotificationDashboard));
