import React, { Fragment, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
    Box,
    Card,
    Heading,
    Text,
    Flex,
    InputSwitch,
    Button,
    AlertDialog,
    FormGroup,
    FormLabel,
    Separator,
    InputSelect,
    FormHelper,
    InputTimePicker,
    InputNumber,
    IconButton,
    DevicePreview,
} from '@majoo-ui/react';
import { Controller, useFieldArray, useForm, useWatch } from 'react-hook-form';
import { connect } from 'react-redux';
import { ChevronLeftOutline, PlusOutline, TrashOutline } from '@majoo-ui/icons';
import { Trans, useTranslation } from 'react-i18next';
import { removeCountryCodePrefix } from '~/utils/helper';
import CoreHOC from '../../../../core/CoreHOC';
import { defaultForm, resolver } from './form';
import { useLaporanAbsensiNotification } from './hooks/useLaporanAbsensiNotification';
import { formatTimeToString, parseTimeString } from '../TutupKasirNotification/form';

const LaporanAbsensiNotification = props => {
    const { branchList, router, filterBranch, idCabang } = props;
    const { t } = useTranslation('Pengaturan/whatsappNotification');
    const forms = useForm({ defaultValues: defaultForm, resolver: resolver(t), mode: 'onChange' });
    const {
        setValue,
        formState: { isSubmitting },
        control,
        trigger,
    } = forms;
    const {
        append: appendRecipient,
        fields: recipientFields,
        remove: removeRecipient,
    } = useFieldArray({
        name: 'wa_absensi_setting.notif_recipient',
        control,
    });
    const isLaporanAbsensiNotif = useWatch({ control, name: 'is_notif_wa_absensi' });
    const senderNumber = useWatch({
        control,
        name: 'wa_absensi_setting.wa_sender_identifier',
    });

    const {
        loading,
        handleSetFinalForm,
        handleConfirm,
        showAlertDialog,
        setShowAlertDialog,
        senderOptions,
        messagePreview,
        isFetchingSenders,
        fetchSenders,
    } = useLaporanAbsensiNotification({
        ...props,
        ...forms,
        t,
    });

    useEffect(() => {
        if (senderNumber && senderOptions && senderOptions.length > 0) {
            const senderOption = senderOptions.find(option => option.value === senderNumber);
            if (senderOption) {
                setValue('wa_absensi_setting.wa_sender_number', removeCountryCodePrefix(senderOption.name));
            }
        }
    }, [senderOptions, senderNumber]);

    return (
        <Card color="dark" responsive css={{ padding: 0 }}>
            <Box css={{ padding: '$spacing-05 $spacing-06' }}>
                <Button
                    css={{
                        paddingLeft: '0',
                        left: '-8px',
                        mb: '$spacing-03',
                        '&:hover': {
                            backgroundColor: 'inherit',
                            outline: 'none',
                        },
                        '> div': {
                            display: 'flex',
                        },
                    }}
                    onClick={() => router.replace('/pengaturan-bisnis/notification/dashboard')}
                    buttonType="ghost"
                    leftIcon={<ChevronLeftOutline color="currentColor" />}
                >
                    {t('back', 'Kembali')}
                </Button>
                <Heading heading="pageTitle">
                    {t('laporan_absensi_notif.form.title', 'Notifikasi Laporan Absensi')}
                </Heading>
            </Box>
            <Separator />
            <Box css={{ padding: '32px 24px', display: 'flex', flexDirection: 'column', gap: '$cozy' }}>
                <FormGroup responsive="input">
                    <FormLabel>
                        {t('laporan_absensi_notif.form.sendNotif.title', 'Kirim Notifikasi Laporan Absensi')}
                    </FormLabel>
                    <Controller
                        name="is_notif_wa_absensi"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                            <InputSwitch
                                checked={value === 1}
                                onCheckedChange={e => onChange(e ? 1 : 0)}
                                dataOnLabel="ON"
                                dataOffLabel="OFF"
                            />
                        )}
                    />
                </FormGroup>
                {isLaporanAbsensiNotif === 1 && (
                    <Fragment>
                        <FormGroup responsive="input">
                            <Flex direction="column" gap={2}>
                                <FormLabel
                                    css={{
                                        '> span': {
                                            color: '$textPrimary',
                                            fontSize: '$sectionSubTitle',
                                            fontWeight: 600,
                                        },
                                    }}
                                >
                                    {t('laporan_absensi_notif.form.senderNumber.title', 'Nomor Pengirim')}
                                </FormLabel>
                                <Text variant="helper">
                                    {t(
                                        'laporan_absensi_notif.form.senderNumber.description',
                                        'Nomor yang digunakan untuk mengirim notifikasi',
                                    )}
                                </Text>
                            </Flex>
                            <Controller
                                name="wa_absensi_setting.wa_sender_identifier"
                                control={control}
                                render={({ field: { value, onChange }, fieldState: { error } }) => (
                                    <Flex direction="column" gap={2}>
                                        <InputSelect
                                            value={senderOptions.find(option => option.value === value)}
                                            option={senderOptions}
                                            onChange={selectedOption => {
                                                onChange(selectedOption.value);
                                                setValue(
                                                    'wa_absensi_setting.wa_sender_number',
                                                    removeCountryCodePrefix(selectedOption.name),
                                                );
                                                trigger('wa_absensi_setting.notif_recipient');
                                            }}
                                            isLoading={isFetchingSenders}
                                            fetchData={fetchSenders}
                                            onSearch={query => fetchSenders({ query, index: 1 })}
                                            placeholder={t(
                                                'laporan_absensi_notif.form.senderNumber.placeholder',
                                                'Pilih nomor pengirim',
                                            )}
                                            isInvalid={!!error}
                                            css={{
                                                maxWidth: 300,
                                            }}
                                        />
                                        {error && <FormHelper error>{error.message}</FormHelper>}
                                    </Flex>
                                )}
                            />
                        </FormGroup>
                        <Box>
                            <FormGroup responsive="input">
                                <FormLabel>
                                    {t('laporan_absensi_notif.form.scheduledTime.title', 'Waktu Kirim Notifikasi')}
                                </FormLabel>
                                <Flex direction="column" gap={3}>
                                    <Controller
                                        name="wa_absensi_setting.notif_time"
                                        control={control}
                                        render={({
                                            field: { value: scheduleValue, onChange: onScheduleChange },
                                            fieldState: { error },
                                        }) => (
                                            <>
                                                <InputTimePicker
                                                    value={parseTimeString(scheduleValue)}
                                                    onChange={selectedTime => {
                                                        onScheduleChange(formatTimeToString(selectedTime));
                                                    }}
                                                    disabledMinute
                                                    css={{
                                                        maxWidth: 120,
                                                    }}
                                                />
                                                {error && <FormHelper error>{error.message}</FormHelper>}
                                            </>
                                        )}
                                    />
                                    <Text variant="helper" color="secondary">
                                        {t(
                                            'laporan_absensi_notif.form.scheduledTime.description',
                                            'Notifikasi akan dikirim ke nomor penerima pada waktu yang ditentukan. Pastikan waktu kirim sesuai kebutuhan untuk mendapatkan data yang lebih akurat.',
                                        )}
                                    </Text>
                                </Flex>
                            </FormGroup>
                        </Box>
                        <FormGroup responsive="input">
                            <Flex direction="column" gap={2}>
                                <FormLabel
                                    css={{
                                        '> span': {
                                            color: '$textPrimary',
                                            fontSize: '$sectionSubTitle',
                                            fontWeight: 600,
                                        },
                                    }}
                                >
                                    {t('laporan_absensi_notif.form.recipients.title', 'Nomor Penerima')}
                                </FormLabel>
                                <Text variant="helper">
                                    {t(
                                        'laporan_absensi_notif.form.recipients.description',
                                        'Pilih hingga 10 nomor untuk menerima notifikasi. Pastikan penerima menyimpan nomor pengirim agar notifikasi terkirim',
                                    )}
                                </Text>
                            </Flex>
                            <Flex direction="column" gap={5} css={{ maxWidth: 332 }}>
                                {recipientFields.map((field, index) => (
                                    <Controller
                                        key={field.id}
                                        name={`wa_absensi_setting.notif_recipient.${index}.value`}
                                        control={control}
                                        render={({ field: { value, onChange }, fieldState: { error } }) => (
                                            <Flex direction="column" gap={2}>
                                                <Flex align="center" gap={3}>
                                                    <InputNumber
                                                        isInvalid={!!error}
                                                        value={value}
                                                        thousandSeparator={false}
                                                        prefix="+62"
                                                        maxLength={16}
                                                        allowEmptyFormatting
                                                        onValueChange={e => {
                                                            onChange(e.value);
                                                        }}
                                                    />
                                                    {index > 0 ? (
                                                        <IconButton onClick={() => removeRecipient(index)}>
                                                            <TrashOutline color="currentColor" />
                                                        </IconButton>
                                                    ) : (
                                                        <Box css={{ size: 24 }} />
                                                    )}
                                                </Flex>
                                                {error && <FormHelper error>{error.message}</FormHelper>}
                                            </Flex>
                                        )}
                                    />
                                ))}

                                <Button
                                    buttonType="ghost"
                                    leftIcon={<PlusOutline color="currentColor" />}
                                    css={{ width: 160 }}
                                    disabled={recipientFields.length >= 10}
                                    onClick={() => appendRecipient({ value: '' })}
                                >
                                    {t('laporan_absensi_notif.form.recipients.addRecipient', 'Tambah Penerima')}
                                </Button>
                            </Flex>
                        </FormGroup>
                        <FormGroup responsive="input">
                            <FormLabel>{t('laporan_absensi_notif.form.preview.title', 'Tampilan Pesan')}</FormLabel>
                            <DevicePreview
                                content={{
                                    sender:
                                        senderNumber && senderOptions.find(option => option.value === senderNumber)
                                            ? senderOptions.find(option => option.value === senderNumber)?.name
                                            : 'No Pengirim',
                                    message: () => messagePreview,
                                }}
                                typeDevice="iphone"
                                variant="whatsapp"
                                size={200}
                            />
                        </FormGroup>
                    </Fragment>
                )}
                <Flex justify="end">
                    <Button
                        onClick={handleSetFinalForm}
                        css={{ mt: '30px' }}
                        disabled={!Array.isArray(branchList) || loading || isSubmitting}
                        buttonType="primary"
                    >
                        {t('save', 'Simpan')}
                    </Button>
                </Flex>
            </Box>
            {showAlertDialog && (
                <AlertDialog
                    title={t('modalConfirmation.title', 'Simpan Pengaturan')}
                    description={
                        <Trans
                            t={t}
                            i18nKey="modalConfirmation.description"
                            defaults="Notifikasi outlet <strong>{{outlet}}</strong> akan disimpan sesuai dengan pengaturan yang telah dilakukan. Lanjutkan?"
                            components={{ bold: <strong /> }}
                            values={{
                                outlet: branchList.find(
                                    branch =>
                                        branch.id_cabang ===
                                        (filterBranch || idCabang || branchList[branchList.length - 1].id_cabang),
                                ).cabang_name,
                            }}
                        />
                    }
                    disabledButton={isSubmitting}
                    hideCloseButton={isSubmitting}
                    open={showAlertDialog}
                    onCancel={() => setShowAlertDialog(false)}
                    onConfirm={() => {
                        setShowAlertDialog(false);
                        handleConfirm();
                    }}
                    labelCancel={t('modalConfirmation.cancel', 'Batal')}
                    labelConfirm={t('modalConfirmation.continue', 'Ya, Lanjutkan')}
                />
            )}
        </Card>
    );
};

LaporanAbsensiNotification.propTypes = {
    branchList: PropTypes.arrayOf(
        PropTypes.shape({
            id_cabang: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
            cabang_name: PropTypes.string,
        }),
    ),
    router: PropTypes.shape({
        replace: PropTypes.func,
    }),
    filterBranch: PropTypes.string,
    idCabang: PropTypes.string,
};

LaporanAbsensiNotification.defaultProps = {
    branchList: [],
    router: {
        replace: () => {},
    },
    filterBranch: '',
    idCabang: '',
};

const mapStateToProps = state => ({
    branchList: state.branch.list,
});

export default connect(mapStateToProps)(CoreHOC(LaporanAbsensiNotification));
