import { yupResolver } from '@hookform/resolvers/yup';
import { string, object, array, number } from 'yup';

export const defaultForm = {
    is_notif_wa_absensi: 0,
    wa_absensi_setting: {
        wa_sender_identifier: '',
        wa_sender_number: '',
        notif_time: '00:00',
        notif_recipient: [{ value: '' }],
    },
};

export const resolver = t =>
    yupResolver(
        object({
            is_notif_wa_absensi: number(),
            wa_absensi_setting: object().when('is_notif_wa_absensi', {
                is: 1,
                then: object({
                    wa_sender_identifier: string().required(
                        t('laporan_absensi_notif.form.validation.senderRequired', 'Nomor pengirim harus dipilih'),
                    ),
                    notif_time: string().required(
                        t('laporan_absensi_notif.form.validation.timeRequired', 'Waktu harus diisi'),
                    ),
                    wa_sender_number: string(),
                    notif_recipient: array()
                        .of(
                            object({
                                value: string()
                                    .required(
                                        t(
                                            'tutup_kasir_notif.form.validation.recipientRequired',
                                            'Nomor penerima harus diisi',
                                        ),
                                    )
                                    .min(
                                        8,
                                        t(
                                            'tutup_kasir_notif.form.validation.minPhoneDigit',
                                            'Nomor penerima minimal 10 digit',
                                        ),
                                    )
                                    .test(
                                        'is-not-sender',
                                        t(
                                            'tutup_kasir_notif.form.validation.recipientSameAsSender',
                                            'Nomor penerima tidak boleh sama dengan nomor pengirim',
                                        ),
                                        (value, ctx) => {
                                            if (ctx.from && ctx.from.length > 1) {
                                                let fromIndex = 1;
                                                fromIndex = ctx.from.findIndex(
                                                    val => !!val.value && !!val.value.wa_sender_number,
                                                );
                                                if (fromIndex > -1) {
                                                    return value !== ctx.from[fromIndex].value.wa_sender_number;
                                                }
                                                return true;
                                            }
                                            return true;
                                        },
                                    ),
                            }),
                        )
                        .min(
                            1,
                            t(
                                'tutup_kasir_notif.form.validation.recipientsRequired',
                                'Minimal satu nomor penerima harus diisi',
                            ),
                        ),
                }),
            }),
        }),
    );
