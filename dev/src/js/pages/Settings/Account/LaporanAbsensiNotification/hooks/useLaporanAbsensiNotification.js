import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { catchError, normalizePhoneNumber } from '~/utils/helper';
import * as whatsAppService from '../../../../../data/whatsAppSetting';
import * as notificationService from '../../../../../data/notifications';

export const useLaporanAbsensiNotification = props => {
    const {
        filterBranch,
        showProgress,
        hideProgress,
        idCabang,
        trigger,
        branchList,
        t,
        addNotification,
        getValues,
        reset,
    } = props;
    const [loading, setLoading] = useState(true);
    const [showAlertDialog, setShowAlertDialog] = useState(false);
    const [isFetchingSenders, setIsFetchingSenders] = useState(false);
    const [pageIndex, setPageIndex] = useState(1);
    const [hasMoreSenders, setHasMoreSenders] = useState(true);
    const [senderOptions, setSenderOptions] = useState([]);
    const outletId = filterBranch || idCabang;
    const outletName = useMemo(() => {
        if (outletId && branchList.length) return branchList.find(branch => branch.id_cabang === outletId).cabang_name;
        return '';
    }, [outletId, branchList]);

    const fetchSettings = async () => {
        try {
            setLoading(true);
            const response = await notificationService.getNotificationSettings(outletId);
            if (response.data) {
                const settings = response.data;
                const formValues = {
                    is_notif_wa_absensi: Number(settings.is_notif_wa_absensi),
                    wa_absensi_setting: {
                        wa_sender_identifier: settings.wa_absensi_setting?.wa_sender_identifier || '',
                        notif_time: settings.wa_absensi_setting?.notif_time || '00:00',
                        notif_recipient: settings.wa_absensi_setting?.notif_recipient?.map(phone => ({
                            value: phone.replace('+62', ''),
                        })) || [{ value: '' }],
                    },
                };
                reset(formValues);
            }
        } catch (error) {
            addNotification({
                title: t('toast.error', { ns: 'translation' }),
                message: catchError(error),
                level: 'error',
            });
        } finally {
            setLoading(false);
        }
    };

    const fetchSenders = useCallback(
        async ({ query, index }) => {
            if (!hasMoreSenders && index === undefined) return;

            setIsFetchingSenders(true);

            try {
                const payload = {
                    per_page: 10,
                    page: index !== undefined ? index : pageIndex,
                    ...(query && { search: query }),
                    category: 'notif_laporan',
                };
                const res = await whatsAppService.getListWhatsApp(outletId, payload);

                if (res.data) {
                    const { data: list, meta } = res;
                    const newOptions = list.map(item => ({
                        value: item.account_id,
                        name: normalizePhoneNumber(item.phone_number),
                    }));

                    if (query !== undefined && (index === 1 || pageIndex === 1)) {
                        setSenderOptions(newOptions);
                    } else {
                        setSenderOptions(prev => [...prev, ...newOptions]);
                    }

                    setHasMoreSenders(meta.current_page < meta.last_page);
                    setPageIndex(meta.current_page + 1);
                }
            } catch (error) {
                addNotification({
                    title: t('toast.error', { ns: 'translation' }),
                    message: catchError(error),
                    level: 'error',
                });
            } finally {
                setIsFetchingSenders(false);
            }
        },
        [pageIndex, hasMoreSenders, outletId, addNotification],
    );

    const messagePreview = useMemo(
        () => (
            <div
                style={{
                    fontFamily: 'Inter, sans-serif',
                    fontSize: '10px',
                    lineHeight: '1.21',
                    color: '#272A2A',
                    textAlign: 'left',
                }}
            >
                <div
                    style={{
                        textTransform: 'uppercase',
                        fontWeight: '400',
                        marginBottom: '19px',
                        textAlign: 'center',
                    }}
                >
                    LAPORAN ABSENSI
                </div>

                <div style={{ marginBottom: '8px' }}>
                    <div style={{ marginBottom: '4px' }}>Outlet: {outletName}</div>
                </div>

                <hr style={{ border: 'none', borderTop: '0.75px solid #121212', margin: '8px 0' }} />

                <div style={{ marginBottom: '8px' }}>
                    <div style={{ marginBottom: '4px' }}>Karyawan: Arial Putra</div>
                    <div style={{ marginBottom: '4px' }}>Check In: 08:00</div>
                    <div style={{ marginBottom: '4px' }}>Check Out: 18:10</div>
                    <div style={{ marginBottom: '4px' }}>Total Jam Kerja: 10 jam 10 menit</div>
                </div>

                <hr style={{ border: 'none', borderTop: '0.75px solid #121212', margin: '8px 0' }} />

                <div style={{ marginBottom: '8px' }}>
                    <div style={{ marginBottom: '4px' }}>Karyawan: Helvetica Putri</div>
                    <div style={{ marginBottom: '4px' }}>Check In: 08:00</div>
                    <div style={{ marginBottom: '4px' }}>Check Out: 18:10</div>
                    <div style={{ marginBottom: '4px' }}>Total Jam Kerja: 10 jam 10 menit</div>
                </div>
            </div>
        ),
        [outletName],
    );

    const handleSetFinalForm = async () => {
        const isValid = await trigger();
        if (isValid) {
            setShowAlertDialog(true);
        }
    };

    const handleConfirm = async () => {
        setLoading(true);
        try {
            const formData = getValues();
            const payload = {
                setting_type: 2,
                id_outlet: outletId.toString(),
                is_notif_wa_absensi: formData.is_notif_wa_absensi,
            };

            if (formData.is_notif_wa_absensi === 1) {
                payload.wa_absensi_setting = {
                    wa_sender_identifier: formData.wa_absensi_setting.wa_sender_identifier,
                    notif_time: formData.wa_absensi_setting.notif_time,
                    notif_recipient: formData.wa_absensi_setting.notif_recipient.map(
                        recipient => `+62${recipient.value}`,
                    ),
                };
            }

            const response = await notificationService.updateWhatsappNotificationSettings(payload);

            if (response) {
                addNotification({
                    title: t('toast.success', { ns: 'translation' }),
                    message: t('laporan_absensi_notif.success', 'Pengaturan notifikasi laporan absensi berhasil disimpan'),
                    level: 'success',
                });
                setShowAlertDialog(false);
            }
        } catch (error) {
            addNotification({
                title: t('toast.error', { ns: 'translation' }),
                message: catchError(error),
                level: 'error',
            });
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (outletId) {
            fetchSenders({ index: 1 });
            fetchSettings();
        }
    }, [outletId]);

    useEffect(() => {
        if (loading) {
            showProgress();
        } else {
            hideProgress();
        }
    }, [loading]);

    return {
        loading,
        handleSetFinalForm,
        handleConfirm,
        showAlertDialog,
        setShowAlertDialog,
        outletName,
        senderOptions,
        messagePreview,
        isFetchingSenders,
        fetchSenders,
    };
};
