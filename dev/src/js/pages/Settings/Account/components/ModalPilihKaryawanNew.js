/* eslint-disable react/forbid-prop-types */
import React, { useCallback, useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import _, { debounce } from 'lodash';
import { connect } from 'react-redux';
import {
  ModalDialog,
  ModalDialogContent,
  ModalDialogTitle,
  ModalDialogFooter,
  Button,
  Grid,
  InputSearchbox,
  InputSelect,
  Table,
  RowSelectionColumn,
} from '@majoo-ui/react';
import useEmployeesPopup from '../ChangeNotification/hooks/useEmployeePopup';
import { isMobile } from '../../../../config/config';

const column = t => [
  RowSelectionColumn,
  {
    Header: t('tableEmployer.name'),
    accessor: 'name',
    colWidth: 250,
    isMobileHeader: true,
  },
  {
    Header: t('tableEmployer.outlet'),
    accessor: 'nama_cabang',
  },
  {
    Header: t('tableEmployer.access'),
    accessor: 'nama_permission',
  },
];

const PilihK<PERSON>awan = ({
  open,
  onOpenChange,
  permissions,
  onSubmit,
  value,
  t,
  showProgress,
  hideProgress,
  listOutlet,
}) => {
  const {
    loading,
    total,
    tableProps,
    setTableProps,
    hasMore,
    data: employes,
    setQuery,
    setAccess,
  } = useEmployeesPopup({
    t,
    showProgress,
    hideProgress,
  });

  const [outlets, setOutlets] = useState([]);
  const [selectedIds, setSelectedIds] = useState([]);
  const [selectedId, setSelectedId] = useState({});
  const [filter, setFilter] = useState({
    outlet: '',
    permission: '',
  });

  const [permissionOpt, setPermission] = useState([]);

  const selectedChanges = (val) => {
    setSelectedIds(val);
  };

  const handleSearch = debounce((e) => {
    if (e === '' || e === undefined) {
      setTableProps(prev => ({ ...prev, pageIndex: 0 }));
      setQuery(prev => ({ ...prev, search: '' }));
    } else if (e !== undefined) {
      setTableProps(prev => ({ ...prev, pageIndex: 0 }));
      setQuery(prev => ({ ...prev, search: e.toLowerCase() }));
    }
  }, 700);

  const handleChangeFilter = useCallback(
    (key, x) => {
      setQuery(prev => ({
        ...prev,
        ...(key === 'outlet' ? { id_outlet: x } : {}),
      }));

      if (key === 'permission') setAccess(prev => ({ ...prev, search: x }));

      setFilter(prev => ({ ...prev, [key]: x }));

      setTableProps(prev => ({ ...prev, pageIndex: 0 }));
    },
    [filter],
  );

  const onSave = () => {
    const data = [];
    for (let i = 0; i < selectedIds.length; i += 1) {
      const f = employes.find(
        item => String(item.id) === String(selectedIds[i]),
      );
      if (f) data.push(f);
    }
    onSubmit(data);
    onOpenChange();
  };

  useEffect(() => {
    if (employes.length > 0) {
      const outletList = listOutlet.map(outlet => ({
        name: outlet[1],
        value: outlet[0],
      }));

      setOutlets(outletList);
    }
    if (permissions.length > 0) {
      setPermission(permissions.map(x => ({ ...x, value: x.id })));
    }
  }, [employes.length, permissions]);

  useEffect(() => {
    const ids = {};
    if (value) {
      const arrValue = value.split(',');
      arrValue.forEach((x) => {
        Object.assign(ids, { [x]: true });
      });
    }
    setSelectedId(ids);
  }, [value]);

  return (
    <ModalDialog open={open} modal onOpenChange={onOpenChange} isMobile={isMobile.matches} size="auto">
      <ModalDialogTitle>{t('label.receiver')}</ModalDialogTitle>
      <ModalDialogContent css={{ maxHeight: 'calc(100vh - 200px)', padding: '1em 1.5em', '> .PJLV.PJLV-ifGHEql-css': { div: { margin: 0 } } }}>
        <Grid
          css={{
            mb: '1em',
            'grid-template-columns': 'repeat(1, 1fr)',
            gap: 17,
            mx: '$spacing-03',
            '@md': {
              'grid-template-columns': 'repeat(3, 1fr)',
            },
          }}
        >
          <InputSearchbox
            onChange={handleSearch}
            size="sm"
            placeholder={t('placeholder.search', { ns: 'translation' })}
          />
          <InputSelect
            onChange={x => handleChangeFilter('outlet', x.value)}
            size="sm"
            option={[
              { value: '', name: t('label.allOutlets', 'Semua Outlet') },
              ...outlets,
            ]}
            placeholder={t('placeholder.outlet')}
          />
          <InputSelect
            onChange={x => handleChangeFilter(
                'permission',
                x.name === t('label.allAccess', 'Semua Akses') ? '' : x.name,
              )
            }
            size="sm"
            option={permissionOpt}
            placeholder={t('placeholder.access')}
          />
        </Grid>
        <Table
          selectedIds={selectedId}
          data={employes}
          totalData={total}
          isInfiniteScroll={employes.length > 0}
          isLoading={loading}
          pageIndex={tableProps.pageIndex}
          hasMoreItems={hasMore}
          hideDataInfo
          fetchData={payload => setTableProps(payload)}
          onSelectedChange={selectedChanges}
          columns={column(t)}
          css={{
            ...(employes.length > 0
              ? {
                '&::-webkit-scrollbar': {
                  width: '8px',
                },
                '&::-webkit-scrollbar-thumb': {
                  width: '8px',
                  backgroundColor: 'rgba(39, 42, 42, 0.6)',
                  borderRadius: '59px',
                },
                '&::-webkit-scrollbar-track': {
                  borderRadius: '59px',
                  width: '8px',
                  backgroundColor: '#EEF0F0',
                },
                maxHeight: 'calc(100vh - 400px)',
                '@md': {
                  maxHeight: 'calc(100vh - 270px)',
                },
              }
              : {
                maxHeight: 'fit-content',
                overflowY: 'hidden',
              }),
          }}
        />
      </ModalDialogContent>
      <ModalDialogFooter css={{ gap: 8 }}>
        <Button onClick={onOpenChange} buttonType="ghost">
          {t('label.cancel', { ns: 'translation' })}
        </Button>
        <Button onClick={onSave}>{t('save', { ns: 'translation' })}</Button>
      </ModalDialogFooter>
    </ModalDialog>
  );
};

PilihKaryawan.propTypes = {
  open: PropTypes.bool.isRequired,
  onOpenChange: PropTypes.func.isRequired,
  employes: PropTypes.array,
  permissions: PropTypes.array,
  onSubmit: PropTypes.func,
  value: PropTypes.string.isRequired,
  t: PropTypes.func,
};

PilihKaryawan.defaultProps = {
  employes: [],
  permissions: [],
  onSubmit: () => { },
  t: (key, _def) => _def || key,
};

const mapStateToProps = state => ({
  listOutlet: state.branch.listOption,
});

export default connect(mapStateToProps, null)(PilihKaryawan);
