/* eslint-disable react/forbid-prop-types */
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
    ModalDialog,
    ModalDialogContent,
    ModalDialogTitle,
    ModalDialogFooter,
    Button,
    InputSearchbox,
    InputSelect,
    Table,
    Banner,
    BannerDescription,
    Flex,
} from '@majoo-ui/react';
import { CircleInfoOutline } from '@majoo-ui/icons';

const getDummyData = (t) => [
    {
        id: 1,
        senderNumber: '+62812-3456-7890',
        recipientNumber: '+62821-9876-5432',
        type: t('whatsapp_log_modal.filters.types.cash_register_report', '<PERSON><PERSON><PERSON>'),
        sendTime: '2024-01-15 14:30:25',
        status: t('whatsapp_log_modal.filters.status.success', 'Sukses'),
    },
    {
        id: 2,
        senderNumber: '+62813-1111-2222',
        recipientNumber: '+62822-3333-4444',
        type: t('whatsapp_log_modal.filters.types.cash_register_report', '<PERSON><PERSON><PERSON>'),
        sendTime: '2024-01-15 13:15:10',
        status: t('whatsapp_log_modal.filters.status.failed', 'Gagal'),
    },
    {
        id: 3,
        senderNumber: '+62814-5555-6666',
        recipientNumber: '+62823-7777-8888',
        type: t('whatsapp_log_modal.filters.types.sales_report', 'Laporan Penjualan'),
        sendTime: '2024-01-15 12:45:33',
        status: t('whatsapp_log_modal.filters.status.success', 'Sukses'),
    },
    {
        id: 4,
        senderNumber: '+62815-9999-0000',
        recipientNumber: '+62824-1111-2222',
        type: t('whatsapp_log_modal.filters.types.system_notification', 'Notifikasi Sistem'),
        sendTime: '2024-01-15 11:20:15',
        status: t('whatsapp_log_modal.filters.status.success', 'Sukses'),
    },
    {
        id: 5,
        senderNumber: '+62816-3333-4444',
        recipientNumber: '+62825-5555-6666',
        type: t('whatsapp_log_modal.filters.types.sales_report', 'Laporan Penjualan'),
        sendTime: '2024-01-15 10:05:42',
        status: t('whatsapp_log_modal.filters.status.failed', 'Gagal'),
    },
];

const getColumns = (t) => [
    {
        Header: t('whatsapp_log_modal.table.columns.sender_number', 'NO PENGIRIM'),
        accessor: 'senderNumber',
        minWidth: 150,
    },
    {
        Header: t('whatsapp_log_modal.table.columns.recipient_number', 'NO PENERIMA'),
        accessor: 'recipientNumber',
        minWidth: 150,
    },
    {
        Header: t('whatsapp_log_modal.table.columns.type', 'TIPE'),
        accessor: 'type',
        minWidth: 180,
    },
    {
        Header: t('whatsapp_log_modal.table.columns.send_time', 'WAKTU PENGIRIMAN'),
        accessor: 'sendTime',
        minWidth: 180,
    },
    {
        Header: t('whatsapp_log_modal.table.columns.status', 'STATUS'),
        accessor: 'status',
        minWidth: 100,
    },
];

const WhatsappNotificationLogModal = ({ open, onOpenChange, t }) => {
    const [searchQuery, setSearchQuery] = useState('');
    const [typeFilter, setTypeFilter] = useState('');
    const [statusFilter, setStatusFilter] = useState('');

    const dummyData = getDummyData(t);

    const filteredData = dummyData.filter(item => {
        const matchesSearch =
            !searchQuery ||
            item.senderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
            item.recipientNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
            item.type.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesType = !typeFilter || item.type === typeFilter;
        const matchesStatus = !statusFilter || item.status === statusFilter;

        return matchesSearch && matchesType && matchesStatus;
    });

    

    return (
        <ModalDialog open={open} modal onOpenChange={onOpenChange} size="auto">
            <ModalDialogTitle>{t('whatsapp_log_modal.title', 'Log Notifikasi WhatsApp')}</ModalDialogTitle>
            <ModalDialogContent
                css={{
                    maxHeight: 'calc(100vh - 200px)',
                    overflow: 'auto',
                }}
            >
                <Banner
                    css={{
                        marginBottom: '$spacing-05',
                        backgroundColor: '$bgGray',
                        border: '1px solid $bgBorder',
                        borderRadius: '$lg',
                        padding: '$spacing-04 $spacing-05',
                    }}
                    bannerBlock
                >
                    <Flex align="center" gap={2}>
                        <CircleInfoOutline />
                        <BannerDescription>
                            {t('whatsapp_log_modal.info_banner', 'Menampilkan seluruh aktivitas yang dilakukan dalam 30 hari terakhir')}
                        </BannerDescription>
                    </Flex>
                </Banner>

                <Flex
                    gap={3}
                    css={{
                        marginBottom: '$spacing-05',
                        flexDirection: 'column',
                        '@md': {
                            flexDirection: 'row',
                            alignItems: 'center',
                        },
                    }}
                >
                    <InputSearchbox
                        placeholder={t('whatsapp_log_modal.search_placeholder', 'Cari ...')}
                        onChange={setSearchQuery}
                        css={{
                            flex: 1.5,
                        }}
                    />
                    <InputSelect
                        placeholder={t('whatsapp_log_modal.filters.all_types', 'Semua Tipe')}
                        value={typeFilter ? { value: typeFilter, name: typeFilter } : { value: '', name: t('whatsapp_log_modal.filters.all_types', 'Semua Tipe') }}
                        onChange={option => setTypeFilter(option.value)}
                        option={[
                            { value: '', name: t('whatsapp_log_modal.filters.all_types', 'Semua Tipe') },
                            { value: t('whatsapp_log_modal.filters.types.cash_register_report', 'Laporan Tutup Kasir'), name: t('whatsapp_log_modal.filters.types.cash_register_report', 'Laporan Tutup Kasir') },
                            { value: t('whatsapp_log_modal.filters.types.sales_report', 'Laporan Penjualan'), name: t('whatsapp_log_modal.filters.types.sales_report', 'Laporan Penjualan') },
                            { value: t('whatsapp_log_modal.filters.types.system_notification', 'Notifikasi Sistem'), name: t('whatsapp_log_modal.filters.types.system_notification', 'Notifikasi Sistem') },
                        ]}
                        size="sm"
                        css={{
                            flex: 1,
                        }}
                    />
                    <InputSelect
                        placeholder={t('whatsapp_log_modal.filters.all_status', 'Semua Status')}
                        value={
                            statusFilter
                                ? { value: statusFilter, name: statusFilter }
                                : { value: '', name: t('whatsapp_log_modal.filters.all_status', 'Semua Status') }
                        }
                        onChange={option => setStatusFilter(option.value)}
                        option={[
                            { value: '', name: t('whatsapp_log_modal.filters.all_status', 'Semua Status') },
                            { value: t('whatsapp_log_modal.filters.status.success', 'Sukses'), name: t('whatsapp_log_modal.filters.status.success', 'Sukses') },
                            { value: t('whatsapp_log_modal.filters.status.failed', 'Gagal'), name: t('whatsapp_log_modal.filters.status.failed', 'Gagal') },
                        ]}
                        size="sm"
                        css={{
                            flex: 1,
                        }}
                    />
                </Flex>

                <Table data={filteredData} columns={getColumns(t)} totalData={filteredData.length} />
            </ModalDialogContent>
            <ModalDialogFooter css={{ justifyContent: 'flex-end' }}>
                <Button buttonType="ghost" onClick={() => onOpenChange(false)}>
                    {t('whatsapp_log_modal.buttons.close', 'Tutup')}
                </Button>
            </ModalDialogFooter>
        </ModalDialog>
    );
};

WhatsappNotificationLogModal.propTypes = {
    open: PropTypes.bool.isRequired,
    onOpenChange: PropTypes.func.isRequired,
    t: PropTypes.func,
};

WhatsappNotificationLogModal.defaultProps = {
    t: (key, _def) => _def || key,
};

export default WhatsappNotificationLogModal;
