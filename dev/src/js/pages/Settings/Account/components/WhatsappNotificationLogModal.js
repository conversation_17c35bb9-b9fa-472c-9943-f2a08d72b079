/* eslint-disable react/forbid-prop-types */
import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
    ModalDialog,
    ModalDialogContent,
    ModalDialogTitle,
    ModalDialogFooter,
    Button,
    InputSearchbox,
    InputSelect,
    Table,
    Banner,
    BannerDescription,
    Flex,
} from '@majoo-ui/react';
import { CircleInfoOutline } from '@majoo-ui/icons';
import { debounce } from 'lodash';
import moment from 'moment';
import { getWhatsAppNotifLog } from '~/data/whatsAppSetting';
import { usePartialState } from '../../../../utils/usePartialState';

const mapApiDataToTableFormat = (apiData, t, language) =>
    apiData.map((item, index) => ({
        id: index + 1,
        senderNumber: item.sender_number || '-',
        recipientNumber: item.recipient || '-',
        type: language === 'en' ? item.feature_name_en : item.feature_name,
        sendTime: item.send_at ? moment(item.send_at).format('DD MMM YYYY, HH:mm') : '-',
        status:
            item.status === 'success'
                ? t('whatsapp_log_modal.filters.status.success', 'Sukses')
                : t('whatsapp_log_modal.filters.status.failed', 'Gagal'),
    }));

const getColumns = t => [
    {
        Header: t('whatsapp_log_modal.table.columns.sender_number', 'NO PENGIRIM'),
        accessor: 'senderNumber',
        minWidth: 150,
    },
    {
        Header: t('whatsapp_log_modal.table.columns.recipient_number', 'NO PENERIMA'),
        accessor: 'recipientNumber',
        minWidth: 150,
    },
    {
        Header: t('whatsapp_log_modal.table.columns.type', 'TIPE'),
        accessor: 'type',
        minWidth: 180,
    },
    {
        Header: t('whatsapp_log_modal.table.columns.send_time', 'WAKTU PENGIRIMAN'),
        accessor: 'sendTime',
        minWidth: 180,
    },
    {
        Header: t('whatsapp_log_modal.table.columns.status', 'STATUS'),
        accessor: 'status',
        minWidth: 100,
    },
];

const WhatsappNotificationLogModal = ({ open, onOpenChange, translation }) => {
    const {
        t,
        i18n: { language },
    } = translation;
    const [filters, setFilters] = usePartialState({
        searchQuery: '',
        typeFilter: '',
        statusFilter: '',
    });
    const [data, setData] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [tableMeta, setTableMeta] = usePartialState({
        totalData: 0,
        currentPage: 0,
        pageSize: 10,
    });

    const debouncedSearch = useCallback(
        debounce(query => {
            setFilters({ searchQuery: query });
            setTableMeta({ currentPage: 0 });
        }, 500),
        [setFilters, setTableMeta],
    );

    const fetchData = useCallback(
        async (params = {}) => {
            setIsLoading(true);
            try {
                const payload = {
                    page: (params.pageIndex || tableMeta.currentPage) + 1,
                    per_page: params.pageSize || tableMeta.pageSize,
                    ...(filters.searchQuery && { search: filters.searchQuery }),
                    ...(filters.typeFilter && { type: filters.typeFilter }),
                    ...(filters.statusFilter && { status: filters.statusFilter }),
                };

                const response = await getWhatsAppNotifLog(payload);

                if (response && response.data) {
                    const mappedData = mapApiDataToTableFormat(response.data, t, language);
                    setData(mappedData);
                    setTableMeta(
                        response.meta
                            ? {
                                  totalData: response.meta.total,
                                  currentPage: response.meta.page - 1,
                                  pageSize: response.meta.per_page,
                              }
                            : {
                                  totalData: mappedData.length,
                                  currentPage: params.pageIndex || 0,
                                  pageSize: params.pageSize || 10,
                              },
                    );
                }
            } catch (error) {
                setData([]);
                setTableMeta({ totalData: 0 });
            } finally {
                setIsLoading(false);
            }
        },
        [filters, tableMeta.currentPage, tableMeta.pageSize, t],
    );

    useEffect(() => {
        if (open) {
            fetchData({ pageIndex: 0 });
        }
    }, [open, filters]);

    const filteredData = data.filter(item => {
        if (!filters.searchQuery) return true;

        return (
            item.senderNumber.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||
            item.recipientNumber.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||
            item.type.toLowerCase().includes(filters.searchQuery.toLowerCase())
        );
    });

    return (
        <ModalDialog open={open} modal onOpenChange={onOpenChange} size="auto">
            <ModalDialogTitle>{t('whatsapp_log_modal.title', 'Log Notifikasi WhatsApp')}</ModalDialogTitle>
            <ModalDialogContent
                css={{
                    maxHeight: 'calc(100vh - 200px)',
                    overflow: 'auto',
                }}
            >
                <Banner
                    css={{
                        marginBottom: '$spacing-05',
                        backgroundColor: '$bgGray',
                        border: '1px solid $bgBorder',
                        borderRadius: '$lg',
                        padding: '$spacing-04 $spacing-05',
                    }}
                    bannerBlock
                >
                    <Flex align="center" gap={2}>
                        <CircleInfoOutline />
                        <BannerDescription>
                            {t(
                                'whatsapp_log_modal.info_banner',
                                'Menampilkan seluruh aktivitas yang dilakukan dalam 30 hari terakhir',
                            )}
                        </BannerDescription>
                    </Flex>
                </Banner>

                <Flex
                    gap={3}
                    css={{
                        marginBottom: '$spacing-05',
                        flexDirection: 'column',
                        '@md': {
                            flexDirection: 'row',
                            alignItems: 'center',
                        },
                    }}
                >
                    <InputSearchbox
                        placeholder={t('whatsapp_log_modal.search_placeholder', 'Cari ...')}
                        onChange={debouncedSearch}
                        css={{
                            flex: 1.5,
                        }}
                    />
                    <InputSelect
                        placeholder={t('whatsapp_log_modal.filters.all_types', 'Semua Tipe')}
                        onChange={option => {
                            setFilters({ typeFilter: option.value });
                        }}
                        option={[]}
                        size="sm"
                        css={{
                            flex: 1,
                        }}
                    />
                    <InputSelect
                        placeholder={t('whatsapp_log_modal.filters.all_status', 'Semua Status')}
                        value={
                            filters.statusFilter
                                ? {
                                      value: filters.statusFilter,
                                      name:
                                          filters.statusFilter === 'success'
                                              ? t('whatsapp_log_modal.filters.status.success', 'Sukses')
                                              : t('whatsapp_log_modal.filters.status.failed', 'Gagal'),
                                  }
                                : { value: '', name: t('whatsapp_log_modal.filters.all_status', 'Semua Status') }
                        }
                        onChange={option => {
                            setFilters({ statusFilter: option.value });
                            setTableMeta({ currentPage: 0 });
                        }}
                        option={[
                            { value: '', name: t('whatsapp_log_modal.filters.all_status', 'Semua Status') },
                            { value: 'success', name: t('whatsapp_log_modal.filters.status.success', 'Sukses') },
                            { value: 'failed', name: t('whatsapp_log_modal.filters.status.failed', 'Gagal') },
                        ]}
                        size="sm"
                        css={{
                            flex: 1,
                        }}
                    />
                </Flex>
                <Table
                    data={filteredData}
                    columns={getColumns(t)}
                    totalData={tableMeta.totalData}
                    isLoading={isLoading}
                    fetchData={fetchData}
                    pageIndex={tableMeta.currentPage}
                    css={{
                        width: 800,
                    }}
                />
            </ModalDialogContent>
            <ModalDialogFooter css={{ justifyContent: 'flex-end' }}>
                <Button buttonType="ghost" onClick={() => onOpenChange(false)}>
                    {t('whatsapp_log_modal.buttons.close', 'Tutup')}
                </Button>
            </ModalDialogFooter>
        </ModalDialog>
    );
};

WhatsappNotificationLogModal.propTypes = {
    open: PropTypes.bool.isRequired,
    onOpenChange: PropTypes.func.isRequired,
    translation: PropTypes.shape({
        t: PropTypes.func,
        i18n: PropTypes.shape({
            language: PropTypes.string,
        }),
    }),
};

WhatsappNotificationLogModal.defaultProps = {
    translation: {
        t: (key, _def) => _def || key,
        i18n: {
            language: 'id',
        },
    },
};

export default WhatsappNotificationLogModal;
