import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { catchError, normalizePhoneNumber } from '~/utils/helper';
import * as whatsAppService from '../../../../../data/whatsAppSetting';
import * as notificationService from '../../../../../data/notifications';
import { TIMING_OPTION } from '../enum';

export const useTutupKasirNotification = props => {
    const {
        filterBranch,
        showProgress,
        hideProgress,
        idCabang,
        trigger,
        branchList,
        t,
        addNotification,
        getValues,
        reset,
    } = props;
    const [loading, setLoading] = useState(true);
    const [showAlertDialog, setShowAlertDialog] = useState(false);
    const [isFetchingSenders, setIsFetchingSenders] = useState(false);
    const [pageIndex, setPageIndex] = useState(1);
    const [hasMoreSenders, setHasMoreSenders] = useState(true);
    const [senderOptions, setSenderOptions] = useState([]);
    const outletId = filterBranch || idCabang;
    const outletName = useMemo(() => {
        if (outletId && branchList.length) return branchList.find(branch => branch.id_cabang === outletId).cabang_name;
        return '';
    }, [outletId, branchList]);

    const fetchSettings = async () => {
        setLoading(true);
        try {
            const response = await notificationService.getNotificationSettings(outletId);
            if (response && response.data) {
                const settings = response.data;
                const formValues = {
                    is_notif_wa_tutup_kasir: Number(settings.is_notif_wa_tutup_kasir),
                    wa_tutup_kasir_setting: {
                        wa_sender_identifier: settings.wa_tutup_kasir_setting?.wa_sender_identifier || '',
                        notif_time_type: settings.wa_tutup_kasir_setting?.notif_time_type
                            ? Number(settings.wa_tutup_kasir_setting?.notif_time_type)
                            : TIMING_OPTION.IMMEDIATE,
                        notif_time: settings.wa_tutup_kasir_setting?.notif_time || '00:00',
                        notif_recipient: settings.wa_tutup_kasir_setting?.notif_recipient?.map(phone => ({
                            value: phone.replace('+62', ''),
                            id: phone,
                        })) || [{ value: '' }],
                    },
                };
                reset(formValues);
            }
        } catch (error) {
            addNotification({
                title: t('toast.error', { ns: 'translation' }),
                message: catchError(error),
                level: 'error',
            });
        } finally {
            setLoading(false);
        }
    };

    const fetchSenders = useCallback(
        async ({ query, index }) => {
            if (!hasMoreSenders && index === undefined) return;

            setIsFetchingSenders(true);

            try {
                const payload = {
                    per_page: 10,
                    page: index !== undefined ? index : pageIndex,
                    ...(query && { search: query }),
                    category: 'notif_laporan',
                };
                const res = await whatsAppService.getListWhatsApp(outletId, payload);

                if (res.data) {
                    const { data: list, meta } = res;
                    const newOptions = list.map(item => ({
                        value: item.account_id,
                        name: normalizePhoneNumber(item.phone_number),
                    }));

                    if (query !== undefined && (index === 1 || pageIndex === 1)) {
                        setSenderOptions(newOptions);
                    } else {
                        setSenderOptions(prev => [...prev, ...newOptions]);
                    }

                    setHasMoreSenders(meta.current_page < meta.last_page);
                    setPageIndex(meta.current_page + 1);
                }
            } catch (error) {
                addNotification({
                    title: t('toast.error', { ns: 'translation' }),
                    message: catchError(error),
                    level: 'error',
                });
            } finally {
                setIsFetchingSenders(false);
            }
        },
        [pageIndex, hasMoreSenders, outletId, addNotification],
    );

    const messagePreview = useMemo(
        () => (
            <div
                style={{
                    fontFamily: 'Inter, sans-serif',
                    fontSize: '10px',
                    lineHeight: '1.21',
                    color: '#272A2A',
                    textAlign: 'left',
                }}
            >
                <div
                    style={{
                        textTransform: 'uppercase',
                        fontWeight: '400',
                        marginBottom: '19px',
                    }}
                >
                    LAPORAN TUTUP KASIR
                    <br />
                    TRANSAKSI PENJUALAN
                </div>

                <div style={{ marginBottom: '8px' }}>
                    <div style={{ marginBottom: '4px' }}>Outlet: {outletName}</div>
                    <div style={{ marginBottom: '4px' }}>Kasir: Dita Sukasi</div>
                    <div style={{ marginBottom: '4px' }}>Waktu Buka: 13/12/49, 00:00</div>
                    <div style={{ marginBottom: '4px' }}>Waktu Tutup: 13/12/49, 13:00</div>
                </div>

                <hr style={{ border: 'none', borderTop: '0.75px solid #121212', margin: '8px 0' }} />

                <div style={{ marginBottom: '8px' }}>
                    <div>Modal Awal: 200.000</div>
                </div>

                <hr style={{ border: 'none', borderTop: '0.75px solid #121212', margin: '8px 0' }} />

                <div style={{ marginBottom: '8px' }}>
                    <div style={{ marginBottom: '4px' }}>Tunai: 50.000</div>
                    <div style={{ marginBottom: '4px' }}>EDC: 98.400</div>
                    <div style={{ marginBottom: '4px' }}>BCA: 98.400</div>
                    <div style={{ marginBottom: '4px' }}>Transfer Bank: 40.000</div>
                    <div style={{ marginBottom: '4px' }}>Mandiri: 40.000</div>
                    <div style={{ marginBottom: '4px' }}>Wallet: 185.050</div>
                    <div style={{ marginBottom: '4px' }}>QRIS: 0.00</div>
                    <div style={{ marginBottom: '4px' }}>OVO: 150.000</div>
                    <div style={{ marginBottom: '4px' }}>Weborder: 34.000</div>
                    <div style={{ marginBottom: '4px' }}>Deposit: 0</div>
                    <div>Total Penerimaan Kasir:373.450</div>
                </div>

                <hr style={{ border: 'none', borderTop: '0.75px solid #121212', margin: '8px 0' }} />

                <div style={{ marginBottom: '8px' }}>
                    <div style={{ marginBottom: '4px' }}>Toko Online: 34.000</div>
                    <div style={{ marginBottom: '4px' }}>Consumer App: 0</div>
                    <div>Total Penerimaan Online: 34.000</div>
                </div>

                <hr style={{ border: 'none', borderTop: '0.75px solid #121212', margin: '8px 0' }} />

                <div style={{ marginBottom: '8px' }}>
                    <div>Total Penerimaan: 400.000</div>
                </div>

                <hr style={{ border: 'none', borderTop: '0.75px solid #121212', margin: '8px 0' }} />

                <div style={{ marginBottom: '8px' }}>
                    <div style={{ marginBottom: '4px' }}>Refund Tunai: 0</div>
                    <div style={{ marginBottom: '4px' }}>Refund Non Tunai: 0</div>
                    <div>Total Refund: 0</div>
                </div>

                <hr style={{ border: 'none', borderTop: '0.75px solid #121212', margin: '8px 0' }} />

                <div style={{ marginBottom: '8px' }}>
                    <div>Saldo Akhir: 625.000</div>
                </div>

                <hr style={{ border: 'none', borderTop: '0.75px solid #121212', margin: '8px 0' }} />

                <div style={{ marginBottom: '8px' }}>
                    <div style={{ marginBottom: '4px' }}>Voucher: 0</div>
                    <div style={{ marginBottom: '4px' }}>Poin: 0</div>
                    <div>Komplimen: 0</div>
                </div>

                <hr style={{ border: 'none', borderTop: '0.75px solid #121212', margin: '8px 0' }} />

                <div style={{ marginBottom: '8px' }}>
                    <div style={{ marginBottom: '4px' }}>Penerimaan Pajak: 44.000</div>
                    <div>Penerimaan Service Charge: 37.000</div>
                </div>

                <hr style={{ border: 'none', borderTop: '0.75px solid #121212', margin: '8px 0' }} />

                <div style={{ marginBottom: '8px', color: '#000000' }}>
                    <div style={{ marginBottom: '4px' }}>Transaksi Selesai: 6</div>
                    <div style={{ marginBottom: '4px' }}>Transaksi Belum Terbayar: 2</div>
                    <div>Transaksi Belum Terbayar (Rp): 90.000</div>
                </div>

                <hr style={{ border: 'none', borderTop: '0.75px solid #121212', margin: '8px 0' }} />

                <div style={{ marginBottom: '8px', color: '#000000' }}>
                    <div style={{ marginBottom: '4px' }}>Total Tunai Sistem: 302.000</div>
                    <div style={{ marginBottom: '4px' }}>Total Tunai Aktual: 250.000</div>
                    <div>Selisih: 52.000</div>
                </div>

                <hr style={{ border: 'none', borderTop: '0.75px solid #121212', margin: '8px 0' }} />

                <div style={{ marginBottom: '8px', color: '#000000' }}>
                    <div>Info Transaksi E-Commerce per 13/12/2049, 00:00 - 13:00</div>
                </div>

                <div style={{ marginBottom: '8px', color: '#000000' }}>
                    <div style={{ marginBottom: '4px' }}>Masuk & Belum Selesai: 10</div>
                    <div style={{ marginBottom: '4px' }}>Tokopedia (5): 300.000</div>
                    <div>GoFood (5): 200.000</div>
                </div>

                <div style={{ color: '#000000' }}>
                    <div style={{ marginBottom: '4px' }}>Selesai: 3</div>
                    <div style={{ marginBottom: '4px' }}>Shopee (2): 150.000</div>
                    <div>GrabFood (1): 5.000</div>
                </div>
            </div>
        ),
        [outletName],
    );

    const handleSetFinalForm = async () => {
        const isValid = await trigger();
        if (isValid) {
            setShowAlertDialog(true);
        }
    };

    const handleConfirm = async () => {
        setLoading(true);
        try {
            const formData = getValues();

            const payload = {
                setting_type: 0,
                id_outlet: outletId.toString(),
                is_notif_wa_tutup_kasir: formData.is_notif_wa_tutup_kasir,
            };

            if (formData.is_notif_wa_tutup_kasir === 1) {
                payload.wa_tutup_kasir_setting = {
                    wa_sender_identifier: formData.wa_tutup_kasir_setting.wa_sender_identifier,
                    notif_time_type: formData.wa_tutup_kasir_setting.notif_time_type,
                    notif_recipient: formData.wa_tutup_kasir_setting.notif_recipient.map(
                        recipient => `+62${recipient.value}`,
                    ),
                };

                if (formData.wa_tutup_kasir_setting.notif_time_type === TIMING_OPTION.SCHEDULED) {
                    payload.wa_tutup_kasir_setting.notif_time = formData.wa_tutup_kasir_setting.notif_time;
                }
            }

            const response = await notificationService.updateWhatsappNotificationSettings(payload);

            if (response) {
                addNotification({
                    title: t('toast.success', { ns: 'translation' }),
                    message: t('tutup_kasir_notif.success', 'Pengaturan notifikasi tutup kasir berhasil disimpan'),
                    level: 'success',
                });

                setShowAlertDialog(false);
            }
        } catch (error) {
            addNotification({
                title: t('toast.error', { ns: 'translation' }),
                message: catchError(error),
                level: 'error',
            });
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (outletId) {
            fetchSenders({ index: 1 });
            fetchSettings();
        }
    }, [outletId]);

    useEffect(() => {
        if (loading) {
            showProgress();
        } else {
            hideProgress();
        }
    }, [loading]);

    return {
        loading,
        handleSetFinalForm,
        handleConfirm,
        showAlertDialog,
        setShowAlertDialog,
        outletName,
        senderOptions,
        messagePreview,
        isFetchingSenders,
        fetchSenders,
    };
};
