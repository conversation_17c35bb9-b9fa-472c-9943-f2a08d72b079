import { yupResolver } from '@hookform/resolvers/yup';
import { string, object, array, number } from 'yup';
import { TIMING_OPTION } from './enum';

export const defaultForm = {
    is_notif_wa_tutup_kasir: 0,
    wa_tutup_kasir_setting: {
        wa_sender_identifier: '',
        wa_sender_number: '',
        notif_time_type: TIMING_OPTION.IMMEDIATE,
        notif_time: '00:00',
        notif_recipient: [{ value: '' }],
    },
};

export const resolver = t =>
    yupResolver(
        object({
            is_notif_wa_tutup_kasir: number(),
            wa_tutup_kasir_setting: object().when('is_notif_wa_tutup_kasir', {
                is: 1,
                then: object({
                    wa_sender_identifier: string().required(
                        t('tutup_kasir_notif.form.validation.senderRequired', 'Nomor pengirim harus dipilih'),
                    ),
                    wa_sender_number: string(),
                    notif_recipient: array()
                        .of(
                            object({
                                value: string()
                                    .required(
                                        t(
                                            'tutup_kasir_notif.form.validation.recipientRequired',
                                            'Nomor penerima harus diisi',
                                        ),
                                    )
                                    .min(
                                        8,
                                        t(
                                            'tutup_kasir_notif.form.validation.minPhoneDigit',
                                            'Nomor penerima minimal 10 digit',
                                        ),
                                    )
                                    .test(
                                        'is-not-sender',
                                        t(
                                            'tutup_kasir_notif.form.validation.recipientSameAsSender',
                                            'Nomor penerima tidak boleh sama dengan nomor pengirim',
                                        ),
                                        (value, ctx) => {
                                            if (ctx.from && ctx.from.length > 1) {
                                                let fromIndex = 1;
                                                fromIndex = ctx.from.findIndex(
                                                    val => !!val.value && !!val.value.wa_sender_number,
                                                );
                                                if (fromIndex > -1) {
                                                    return value !== ctx.from[fromIndex].value.wa_sender_number;
                                                }
                                                return true;
                                            }
                                            return true;
                                        },
                                    ),
                            }),
                        )
                        .min(
                            1,
                            t(
                                'tutup_kasir_notif.form.validation.recipientsRequired',
                                'Minimal satu nomor penerima harus diisi',
                            ),
                        ),
                }),
            }),
        }),
    );

// Helper function to format time from Date to HH:mm string
export const formatTimeToString = date => {
    if (!date) return '';
    const d = new Date(date);
    const hours = d.getHours().toString().padStart(2, '0');
    const minutes = d.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
};

// Helper function to parse HH:mm string to Date
export const parseTimeString = timeString => {
    if (!timeString) return new Date();
    const [hours, minutes] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours, 10));
    date.setMinutes(parseInt(minutes, 10));
    date.setSeconds(0);
    return date;
};
