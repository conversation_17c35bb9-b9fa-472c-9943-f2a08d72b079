const TYPE_ORDER_META = (t) => ([
    {
        id: '1',
        title: 'Table',
    },
    {
        id: '11',
        title: t('orderType.reservation', 'Reservasi'),
    },
    {
        id: '10',
        title: 'Free Table',
    },
    {
        id: '7',
        title: t('orderType.service', 'Jasa'),
    },
    {
        id: '2',
        title: t('orderType.takeAway', 'Bungkus'),
    },
    {
        id: '9',
        title: 'Invoice',
    },
    {
        id: '4',
        title: t('orderType.delivery', 'Pengiriman'),
    },
    {
        id: '99',
        title: t('orderType.other', 'Lainnya'),
    },
    {
        id: '24',
        title: t('orderType.onlineStore', 'Toko Online'),
    },
    {
        id: '60',
        title: 'Toko Supplies',
    },
    {
        id: '6',
        title: 'Ojek Online',
    },
    {
        id: '39',
        title: 'Quick Service',
    },
]);

const TAX_METHOD_TYPE = (t) => ([
    {
        value: 1,
        label: t('radioLabelBeforeTax', 'Pajak Sebelum Diskon'),
    },
    {
        value: 2,
        label: t('radioLabelAfterTax', 'Pajak Setelah Diskon'),
    },
    {
        value: 3,
        label: t('radioLabelIncludeTax', 'Harga Termasuk Pajak'),
    },
]);

const customTableMeta = t => [
    {
        header: (t('customHeaderPopup', 'Nama Outlet')),
        field: 'name',
    },
];

export {
    TYPE_ORDER_META,
    TAX_METHOD_TYPE,
    customTableMeta,
};
