import React, { useState, useEffect, useContext, useCallback } from 'react';
import {
  Box,
  Card,
  Heading,
  Text,
  Flex,
  Tooltip,
  CheckboxGroup,
  InputCheckbox,
  InputSwitch,
  Table,
  Button,
  AlertDialog,
  Grid,
  GridItem,
  ToastContext,
} from '@majoo-ui/react';
import { Controller, useForm } from 'react-hook-form';
import { connect } from 'react-redux';
import { Trans, useTranslation } from 'react-i18next';
import {
  CircleInfoOutline,
  ChevronLeftOutline,
  TrashOutline,
} from '@majoo-ui/icons';
import omit from 'lodash/omit';
import useNotifSettings from './hooks/useNotifSettings';
import CoreHOC from '../../../../core/CoreHOC';
import { DATA_ON, FORM_DEFAULT_VALUES, KEY_LABEL } from './constants';
import PilihKaryawanNew from '../components/ModalPilihKaryawanNew';
import * as masterApi from '../../../../data/master';
import { PERMISSIONS } from '../../../../constants/permissions';
import * as masterSelectors from '../../../../data/master/selectors';
import { putNotification } from '../../../../data/notifications';
import { catchError } from '../../../../utils/helper';

const Index = props => {
  const { t } = useTranslation([
    'Settings/notificationSetting',
    'translation',
    'Pengaturan/changeNotification',
  ]);

  const [show, setShow] = useState(false);
  const [employeePopup, setEmployeePopup] = useState(false);
  const [permissionList, setPermissionList] = useState([]);
  const toast = useContext(ToastContext);

  const forms = useForm({ defaultValues: FORM_DEFAULT_VALUES });

  const settings = useNotifSettings({ ...props, ...forms });

  useEffect(() => {
    const fetchAccess = () => {
      props.showProgress();
      masterApi
        .fetchPermission()
        .then(response => {
          if (!response.status) throw new Error(response.msg);

          const retval = masterSelectors.toObjectPermission(response.data);
          const dataRole = retval.filter(
            x => x.id !== PERMISSIONS.KASIR && x.id !== PERMISSIONS.STAFF,
          );
          dataRole
            .reverse()
            .push({ id: '', name: t('label.allAccess', 'Semua Akses') });
          setPermissionList(dataRole.reverse());
        })
        .catch(err =>
          toast.addToast({
            title: t('toast.access_f', 'Gagal mengambil data hak akses', {
              ns: 'Pengaturan/changeNotification',
            }),
            description: catchError(err),
            variant: 'failed',
            position: 'top-right',
          }),
        )
        .finally(() => props.hideProgress());
    };

    fetchAccess();
  }, [props.filterBranch, props.idCabang]);

  return (
    <Card color="dark" responsive css={{ padding: 0 }}>
      <Box css={{ padding: '16px 24px 32px 24px' }}>
        <Button
          css={{
            paddingLeft: '0',
            left: '-8px',
            mb: '30px',
            '&:hover': {
              backgroundColor: 'inherit',
              outline: 'none',
            },
            '> div': {
              display: 'flex',
            },
          }}
          onClick={() => props.router.goBack()}
          buttonType="ghost"
          leftIcon={<ChevronLeftOutline color="currentColor" />}
        >
          {t('back', 'Kembali', { ns: 'Pengaturan/changeNotification' })}
        </Button>
        <Heading heading="pageTitle">
          {t('send_changed_notif', 'Kirim Notifikasi Perubahan', {
            ns: 'Pengaturan/changeNotification',
          })}
        </Heading>
      </Box>
      <Box css={{ border: '1px solid #EEF0F0', width: '100%' }} />
      <Box css={{ padding: '32px 24px' }}>
        <Flex
          justify="between"
          wrap="wrap"
          css={{
            width: '100%',
            border: '1px solid #EEF0F0',
            borderRadius: '8px',
            padding: '24px 16px',
          }}
        >
          <Box>
            <Flex align="center" css={{ gap: '8px', mb: '4px' }}>
              <Text
                css={{ color: '#272A2A', fontSize: '16px', fontWeight: '600' }}
              >
                {t('activate_notif', 'Aktifkan Notifikasi: Penambahan Data', {
                  ns: 'Pengaturan/changeNotification',
                })}
              </Text>
              <Tooltip
                label={t(
                  'activate_notif_tooltip',
                  'Notifikasi akan tampil pada dashboard dan POS',
                  { ns: 'Pengaturan/changeNotification' },
                )}
                side="top"
                align="start"
              >
                <CircleInfoOutline />
              </Tooltip>
            </Flex>
            <Text css={{ fontSize: '14px', fontWeight: '400' }}>
              {t(
                'activate_notif_d',
                'majoo akan mengirim notifikasi ketika admin menambah data berikut:',
                { ns: 'Pengaturan/changeNotification' },
              )}
            </Text>
            <Box css={{ mt: '24px' }}>
              <Grid gapX={2} gapY={1} columns={2} flow="row">
                {settings.add_data.map((data, idx) => (
                  <GridItem key={`${data.label}_${idx}_add_data`}>
                    <InputCheckbox
                      label={t(data.key, data.label, {
                        ns: 'Pengaturan/changeNotification',
                      })}
                      onCheckedChange={e => forms.setValue(`add_data.${data.key}`, e)}
                      value={KEY_LABEL[data.label]}
                      checked={forms.watch(
                        `add_data.${KEY_LABEL[data.label]}`,
                      )}
                    />
                  </GridItem>
                ))}
              </Grid>
            </Box>
          </Box>
          <Box>
            <InputSwitch
              checked={Object.values(forms.watch('add_data') || {}).some(
                Boolean,
              )}
              onCheckedChange={e => {
                if (Object.values(forms.watch('add_data') || {}).every(Boolean))
                  return forms.setValue(
                    'add_data',
                    FORM_DEFAULT_VALUES.add_data,
                  );

                return forms.setValue('add_data', DATA_ON);
              }}
              dataOnLabel="ON"
              dataOffLabel="OFF"
            />
          </Box>
        </Flex>
        <Flex
          justify="between"
          wrap="wrap"
          css={{
            width: '100%',
            mt: '24px',
            border: '1px solid #EEF0F0',
            borderRadius: '8px',
            padding: '24px 16px',
          }}
        >
          <Box>
            <Flex align="center" css={{ gap: '8px', mb: '4px' }}>
              <Text
                css={{ color: '#272A2A', fontSize: '16px', fontWeight: '600' }}
              >
                {t('changed_notif', 'Aktifkan Notifikasi: Perubahan Data', {
                  ns: 'Pengaturan/changeNotification',
                })}
              </Text>
              <Tooltip
                label={t(
                  'activate_notif_tooltip',
                  'Notifikasi akan tampil pada dashboard dan POS',
                  { ns: 'Pengaturan/changeNotification' },
                )}
                side="top"
                align="start"
              >
                <CircleInfoOutline />
              </Tooltip>
            </Flex>
            <Text css={{ fontSize: '14px', fontWeight: '400' }}>
              {t(
                'changed_notif_d',
                'majoo akan mengirim notifikasi ketika admin merubah data berikut:',
                { ns: 'Pengaturan/changeNotification' },
              )}
            </Text>
            <Box css={{ mt: '24px' }}>
              <Grid gapX={2} gapY={1} columns={2} flow="row">
                {settings.change_data.map((data, idx) => (
                  <GridItem key={`${data.label}_${idx}_change_data`}>
                    <InputCheckbox
                      label={t(data.key, data.label, {
                        ns: 'Pengaturan/changeNotification',
                      })}
                      onCheckedChange={e => forms.setValue(`change_data.${data.key}`, e)}
                      value={KEY_LABEL[data.label]}
                      checked={forms.watch(
                        `change_data.${KEY_LABEL[data.label]}`,
                      )}
                    />
                  </GridItem>
                ))}
              </Grid>
            </Box>
          </Box>
          <Box>
            <InputSwitch
              checked={Object.values(forms.watch('change_data') || {}).some(Boolean)}
              onCheckedChange={e => {
                if (
                  Object.values(forms.watch('change_data') || {}).every(Boolean)
                )
                  return forms.setValue(
                    'change_data',
                    FORM_DEFAULT_VALUES.change_data,
                  );

                return forms.setValue('change_data', DATA_ON);
              }}
              dataOnLabel="ON"
              dataOffLabel="OFF"
            />
          </Box>
        </Flex>
        <Flex
          justify="between"
          wrap="wrap"
          css={{
            width: '100%',
            mt: '24px',
            border: '1px solid #EEF0F0',
            borderRadius: '8px',
            padding: '24px 16px',
          }}
        >
          <Box css={{ width: '100%' }}>
            <Flex align="center" justify="between" wrap="wrap">
              <Box>
                <Box css={{ mb: '4px' }}>
                  <Text
                    css={{
                      color: '#272A2A',
                      fontSize: '16px',
                      fontWeight: '600',
                    }}
                  >
                    {t('receiver', 'Penerima Notifikasi', {
                      ns: 'Pengaturan/changeNotification',
                    })}
                  </Text>
                </Box>
                <Text css={{ fontSize: '14px', fontWeight: '400' }}>
                  {t(
                    'receiver_desc',
                    'Notifikasi akan dikirimkan ke aplikasi majoo karyawan yang dipilih',
                    { ns: 'Pengaturan/changeNotification' },
                  )}
                </Text>
              </Box>
              <Button
                onClick={() => setEmployeePopup(true)}
                buttonType="primary"
                disabled={settings.loading || forms.formState.isSubmitting}
              >
                {t('choose_emp', 'Pilih Karyawan', {
                  ns: 'Pengaturan/changeNotification',
                })}
              </Button>
            </Flex>
            <Box css={{ mt: '18px' }}>
              <Text
                css={{ color: '#272A2A', fontSize: '14px', fontWeight: '600' }}
              >
                {t('receiver_2', 'Karyawan/Penerima', {
                  ns: 'Pengaturan/changeNotification',
                })}
              </Text>
            </Box>
            <Table
              css={{ mt: '24px', width: '100%' }}
              columns={[
                {
                  Header: t('emp_name', 'Nama Karyawan', {
                    ns: 'Pengaturan/changeNotification',
                  }),
                  accessor: 'name',
                  isMobileHeader: true,
                },
                {
                  Header: 'Outlet',
                  accessor: 'nama_cabang',
                },
                {
                  Header: t('access', 'Hak Akses', {
                    ns: 'Pengaturan/changeNotification',
                  }),
                  accessor: 'nama_permission',
                },
                {
                  Header: '',
                  accessor: 'delete',
                  unsortable: true,
                  Cell: ({ row }) => (
                    <Box
                      css={{ cursor: 'pointer' }}
                      onClick={() => {
                        const result = forms.watch('employee_data').filter(d => d.id !== row.id);

                        forms.setValue('employee_data', result);
                      }}
                    >
                      <TrashOutline />
                    </Box>
                  ),
                },
              ]}
              data={forms.watch('employee_data')}
              headerCss={{
                backgroundColor: '#FAFAFA',
              }}
              isLoading={settings.loading}
              totalData={forms.watch('employee_data').length}
            />
          </Box>
        </Flex>
        <Flex justify="end">
          <Button
            onClick={() => setShow(true)}
            css={{ mt: '30px' }}
            disabled={
              !Array.isArray(props.branchList) ||
              settings.loading ||
              forms.formState.isSubmitting
            }
            buttonType="primary"
          >
            {t('save', 'Simpan', { ns: 'Pengaturan/changeNotification' })}
          </Button>
        </Flex>
      </Box>
      {show && (
        <AlertDialog
          title={t('title', 'Pengaturan Notifikasi', {
            ns: 'Pengaturan/changeNotification',
          })}
          description={
            <Text css={{ color: '#272A2A', fontSize: '14px' }}>
              <Trans
                ns="Pengaturan/changeNotification"
                i18nKey="save_desc"
                defaults="Notifikasi outlet <strong>{{outlet_name}}</strong> akan disimpan sesuai dengan pengaturan yang telah dilakukan. Lanjutkan?"
                components={{ bold: <strong /> }}
                values={{
                  outlet_name:
                    props.branchList.find(
                      branch =>
                        branch.id_cabang ===
                        (props.filterBranch || props.location.query.outlet_id),
                    ).cabang_name || '',
                }}
              />
            </Text>
          }
          disabledButton={forms.formState.isSubmitting}
          hideCloseButton={forms.formState.isSubmitting}
          onCancel={() => setShow(false)}
          onConfirm={forms.handleSubmit(async values => {
            try {
              await putNotification(omit({ ...values, id_employee_change_data: values.employee_data.map(e => e.id).join(',') }, 'employee_data'));

              return toast.addToast({
                title: t('toast.success', 'Berhasil', {
                  ns: 'Pengaturan/changeNotification',
                }),
                description: (
                  <Trans
                    ns="Pengaturan/changeNotification"
                    i18nKey="toast.save_desc"
                    defaults="Pengaturan Notifikasi <strong>{{outlet_name}}</strong> berhasil disimpan"
                    components={{ bold: <strong /> }}
                    values={{
                      outlet_name:
                        props.branchList.find(
                          branch =>
                            branch.id_cabang ===
                            (props.filterBranch ||
                              props.location.query.outlet_id),
                        ).cabang_name || '',
                    }}
                  />
                ),
                variant: 'success',
                position: 'top-right',
              });
            } catch (err) {
              toast.addToast({
                title: t('toast.error_save', 'Gagal menyimpan data', {
                  ns: 'Pengaturan/changeNotification',
                }),
                description: catchError(err),
                variant: 'failed',
                position: 'top-right',
              });
            } finally {
              setShow(false);
            }
          })}
          open={show}
          labelCancel={t('cancel', 'Batal', {
            ns: 'Pengaturan/changeNotification',
          })}
          labelConfirm={t('continue', 'Ya, Lanjutkan', {
            ns: 'Pengaturan/changeNotification',
          })}
        />
      )}
      {employeePopup && (
        <Controller
          name="employee_data"
          control={forms.control}
          render={({ field: { value, onChange } }) => (
            <PilihKaryawanNew
              t={t}
              open={employeePopup}
              onOpenChange={() => setEmployeePopup(false)}
              permissions={permissionList}
              value={value.map(v => v.id).join(',')}
              showProgress={props.showProgress}
              hideProgress={props.hideProgress}
              onSubmit={rows => {
                if (rows.length === 0) {
                  onChange(value);
                } else {
                  onChange(rows);
                }
              }}
            />
          )}
        />
      )}
    </Card>
  );
};

const mapStateToProps = state => ({
  branchList: state.branch.list,
});

export default connect(mapStateToProps)(CoreHOC(Index));
