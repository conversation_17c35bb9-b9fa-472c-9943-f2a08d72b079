const CONSUMER_APP_STATUS = {
    APPROVED: "Approved",
    REJECTED: "Rejected",
    WAITING_APPROVAL: "Waiting Approval",
    WEB_ORDER_INACTIVE: "WO-403",
    CONSUMER_APP_INACTIVE: "ACT-404",
    SUBSCRIPTION_NOT_ALLOWED: "SUB-403",
};

const CUSTOMIZATION_STATUS = {
    UNSET: { id: null, label: "Belum Dikustomisasi" },
    DRAFT: { id: 2, label: "Draft" },
    ONPROCESS: { id: 0, label: "Sedang Diproses" },
    PUBLISHED: { id: 1, label: "Sudah Publish" },
};

const SETUP_STATUS = {
    UNSET: "Belum diatur",
    UNPUBLISHED: "Belum pernah publish",
    UPDATED: "Diperbarui pada",
    PUBLISHED: "Dipublish pada",
    LAST_PUBLISHED: "Terakhir publish pada",
};

const SETUP_TYPE = {
    ICON: "ICON",
    STYLE: "STYLE",
    PUBLISH: "PUBLISH",
    BANNER: "BANNER",
};

const THEME_STEP = ["Tema", "Splash Screen", "Preview"];

const STEP_TEMA = 1;
const STEP_SPLASH_SCREEN = 2;
const STEP_PREVIEW = 3;

const DATA_TIPE_SPLASH_SCREEN = [
    { value: "splashscreen_image", text: "Gambar" },
    { value: "splashscreen_full", text: "Gambar 1 Layar" },
];

const TIPE_SPLASH_SCREEN = {
    TEXT: "text",
    SPLASHSCREEN_IMG: "splashscreen_image",
    SPLASHSCREEN_FULL: "splashscreen_full",
};

const SETUP_METHOD = {
    ICON: "icon",
    STYLE: "style",
    SPLASHSCREEN_IMG: "splashscreen_image",
    SPLASHSCREEN_FULL: "splashscreen_full",
};

const BANNER_STATUS = {
    ACTIVE: 1,
    NONACTIVE: 0,
};

const BANNER_FORM_ACTION_TYPE = {
    ADD: "add",
    DETAIL: "detail",
};

const BANNER_TARGET_PAGE_OPTIONS = [
    { value: "beranda", text: "Beranda" },
    { value: "produk", text: "Halaman Produk" },
    { value: "promo", text: "Halaman Promo" },
    // { value: "notifikasi", text: "Halaman Notifikasi" },
];

const BANNER_TARGET_PAGE_DETAILS = {
    PROMO: "promo",
    NOTIFIKASI: "notifikasi",
};

const BANNER_TYPE = {
    MAIN: 1,
    SUPPORT: 0,
};

const BANNER_CHANGE_STATUS_ACTION = {
    SUPPORT: "support",
    DELETE: "delete",
    ARCHIVE: "archive",
};

const PRODUCT_VIEW_MODE_OPTIONS = [
    { value: "grid", text: "Grid" },
    { value: "list", text: "List" },
];

export {
    CONSUMER_APP_STATUS,
    CUSTOMIZATION_STATUS,
    SETUP_STATUS,
    SETUP_TYPE,
    THEME_STEP,
    STEP_TEMA,
    STEP_SPLASH_SCREEN,
    STEP_PREVIEW,
    DATA_TIPE_SPLASH_SCREEN,
    TIPE_SPLASH_SCREEN,
    SETUP_METHOD,
    BANNER_STATUS,
    BANNER_FORM_ACTION_TYPE,
    BANNER_TARGET_PAGE_OPTIONS,
    BANNER_TARGET_PAGE_DETAILS,
    BANNER_TYPE,
    BANNER_CHANGE_STATUS_ACTION,
    PRODUCT_VIEW_MODE_OPTIONS
};
