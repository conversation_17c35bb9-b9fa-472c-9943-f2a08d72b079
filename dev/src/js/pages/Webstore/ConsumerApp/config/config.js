import { phonePreviewDeviceType } from "../../../../config/enum";

const devicePreviewSwitchOptions = [
    { text: "Android", value: phonePreviewDeviceType.ANDROID },
    { text: "iPhone", value: phonePreviewDeviceType.IPHONE },
];

const devicePreviewSwitchOptionsDisabled = [
    { text: "Android", value: phonePreviewDeviceType.ANDROID, disabled: true },
    { text: "iPhone", value: phonePreviewDeviceType.IPHONE, disabled: true },
];

const defaultThemeData = {
    font: "Inter",
    primary_color: "#00B7B5",
    secondary_color: "#FFFFFF",
    primary_text_color: "#272A2A",
    secondary_text_color: "#FFFFFF",
    accent_color: "#FA6C07",
};

const defaultSplashScreenData = {
    font: "Inter",
    type: "",
    text: "",
    text_align: "left",
    image: null,
};

const setupBannerDateFormat = {
    as_props: "DD/MM/YYYY",
    as_request: "YYYY-MM-DD",
};

export {
    devicePreviewSwitchOptions,
    devicePreviewSwitchOptionsDisabled,
    defaultThemeData,
    defaultSplashScreenData,
    setupBannerDateFormat,
};
