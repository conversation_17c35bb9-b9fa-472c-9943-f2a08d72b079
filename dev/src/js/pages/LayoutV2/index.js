/* eslint-disable react/prop-types */
import {
    CollapsibleSidebarOutline as CollapsibleSidebarOutlineRaw,
    ExpandSidebarOutline as ExpandSidebarOutlineRaw,
} from '@majoo-ui/icons';
import { Drawer, IconButton, Box, ToastContext } from '@majoo-ui/react';
import moment from 'moment';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Helmet } from 'react-helmet';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import isEmpty from 'lodash/isEmpty';
import { useTranslation } from 'react-i18next';
import { get } from 'lodash';
import { useHistory, useLocation, useParams } from 'react-router-dom';
import ErrorBoundary from '~/v2-components/ErrorBoundary';
import ChatwootWidget from '~/components/Chat/ChatwootWidget';
import * as outletSelector from '../../data/outlets/selectors';
import {
    setContentHeaderButtons,
    setContentHeaderCalendar,
    setContentHeaderFilterCalendar,
    setContentOverlay,
    setLoaderState,
    setShowNotifSupport,
    setSupportNeeded,
    setWindowHeight,
    setContentHeaderFloat,
    setCustomBreadCrumbs,
} from '../../actions/layoutActions';
import { setExpandList, unsetExpandList } from '../../actions/menuActions';
import { setUnreadMsg, setUnreadNotif, setUnreadMsgSmall, setRecalculateNotif } from '../../actions/settingActions';
import { getIdUser } from '../../utils/passport';
import { fetchInfoUsaha } from '../../actions/userActions';
import { fetchAccountInfo } from '../../actions/accountInfoActions';
import { fetchSupportHistorySocket } from '../../actions/supportActions';
import { fetchOutletExp, filterCabang, updateCabangPush } from '../../actions/branchActions';
// import ChatPanel from '../../components/layout/NotificationWrapper/ChatPanel';
import initSociomile from '../../utils/sociomile';
import Onboarding from '../../components/onBoarding/Container';
import { layoutsActions } from '../../data/layouts/layouts.reducer';
import {
    getDefaultPage,
    getNearestExpiredOutlets,
    getOnboardingTask,
    isSupportExpired,
    getOnBoardingCustomData,
} from '../../services/session';
import { styled } from '../../stitches.config';
import { catchError, normalizePhoneNumber } from '../../utils/helper';
import { useMediaQuery } from '../../utils/useMediaQuery';
import useDidMountEffect from '../../utils/useDidMountEffect';
import userUtils from '../../utils/user.util';
import Header from './Header';
import { useConstructor } from './hooks';
import Sidebar from './Sidebar';
import { LAINNYA_CHILD_MENU_ID } from './Sidebar/mobile/MobileOutletSection';
import {
    defineLoc,
    getBannerMenuActive,
    headerMenuId,
    resetMenu,
    searchMenuByKey,
    setCalendarHandler,
    showToast,
    _setGlobalMessage,
    checkFoodOrderAccessibleMenu,
    isSubscriptionIncludesAddon,
    checkMarketplaceAccessibleMenu,
    allOutletDisabled,
    getContentTranslation,
    outletLoanWhitelist,
} from './utils';
import { LIFECYCLE_STATUS } from '../../config/enum';
import PageBanner from './PageBanner';
import ContentHeader from '../../components/layout/ContentHeader';
import PopUpExpiredV2 from '../Support/components/PopUpExpiredV2';
import UnauthorizedBlockerV2 from './Dialog/UnauthorizedBlockerV2';
import UnauthorizedBlockerAddOns from './Dialog/UnauthorizedBlockerAddons';
import ExpirationWarningV2 from './ExpirationWarning';
import usePrevious from '../../utils/usePrevious';
import { getBalance, getDashboardWebstore } from '../../data/webOrder';
import { INTEGRATION_STATUS } from '../TokoOnline/SalesSetting/utils';
import { getInfoAddon, getSupportInfo } from '../../data/supports';
import { getListOrderMarketplace } from '../../data/marketplace';
import {
    WHITELISTS, POPUP_RESTRICT_TYPES, URLS_INVOICE, URLS_PRODUCT,
} from './RestrictPopup/enum';
import RestrictPopup from './RestrictPopup';
import RestrictPopupMenu from './RestrictPopupMenu';
import useNavigatorOnLine from '../../hooks/UseNavigatorOnline';
import i18n from '../../i18n';
import { ACCOUNT_TYPE } from '../Support/buy/retina/enum';
import { getSubscriptionType, isStarterBasicSubscription } from '../../v2-utils';
import VirtualAccountPaymentFooter from '../../components/virtualaccount/VirtualAccountPaymentFooter';
import TaskCompleteDialog from './Coachmark/TaskCompleteDialog';
import Loading from '../../components/Loading';
import { getMenuPathList, getMenuRedirectPath } from './Header/utils';
import DeleteWarning from './DeleteWarning';
import ExpiredModal from './ExpiredModal';
import BeforeExpiredModal from './BeforeExpiredModal';
import TermsAndConditionModal from './TermsAndConditionModal';

const CollapsibleSidebarOutline = styled(CollapsibleSidebarOutlineRaw, {
    color: '$primary500',
    size: '16px',
});

const ExpandSidebarOutline = styled(ExpandSidebarOutlineRaw, {
    color: '$white',
    size: '16px',
});

const Content = styled('div', {
    // marginTop: '56px',
    variants: {
        isMobile: {
            true: {
                backgroundColor: 'white',
                paddingTop: '$spacing-08',
            },
        },
    },
});

const Container = styled('div', {
    // overflow: 'hidden'
});

const FoldButton = styled(IconButton, {
    boxShadow:
        '0px 1px 4px -1px rgba(0, 0, 0, 0.08), 0px 2px 8px -1px rgba(0, 0, 0, 0.06), 0px 6px 12px -1px rgba(0, 0, 0, 0.05), 0px 15px 15px -1px rgba(0, 0, 0, 0.04)',
    variants: {
        collapsed: {
            true: {
                left: '68px',
            },
            false: {
                left: '239px',
            },
        },
    },
});

/* ENUM */
const _menu = {
    BELI_LANGGANAN_SUPPORT: '272',
    PENGATURAN_OUTLET: '243',
    INBOX: '27926',
    ORDER_ONLINE_PENGATURAN_AKUN: '28506',
    ORDER_ONLINE_PENGAJUAN_AKTIVASI: '28334',
};
/* END ENUM */

let hidden = null;
let visibilityChange = null;
if (typeof document.hidden !== 'undefined') {
    // Opera 12.10 and Firefox 18 and later support
    hidden = 'hidden';
    visibilityChange = 'visibilitychange';
} else if (typeof document.msHidden !== 'undefined') {
    hidden = 'msHidden';
    visibilityChange = 'msvisibilitychange';
} else if (typeof document.webkitHidden !== 'undefined') {
    hidden = 'webkitHidden';
    visibilityChange = 'webkitvisibilitychange';
}


const useRouterHook = () => {
    const history = useHistory();
    const location = useLocation();

    return {
        location,
        ...history,
    };
};

const LayoutV2 = ({
    statusMenu,
    menuPrivilege,
    children,
    fetchMenu,
    isLoaderShow,
    usaha,
    businessProfile,
    isTourShow,
    setNotFetched,
    outletList,
    selectedOutletId,
    accType,
    accountProfile,
    selectOutlet,
    onboardingTask,
    onBoardingCustomData,
    contentTranslations,
    loan,
    tncStatus,
    ...restProps
}) => {
    const router = useRouterHook();
    const params = useParams();
    const props = { ...restProps, params };
    const [firstDate, setFirstDate] = useState(`01/${moment().format('MM/YYYY')}`);
    const [lastDate, setLastDate] = useState(`${moment().daysInMonth()}/${moment().format('MM/YYYY')}`);
    const [isShowOverlay, setIsShowOverlay] = useState(false);
    const [isShowProgress, setIsShowProgress] = useState(false);
    const [progressLabel, setProgressLabel] = useState('');
    const [showUnauthorizedBlocker, setShowUnauthorizedBlocker] = useState(false);
    const [showBlocker, setShowBlocker] = useState(false);
    const [printSupport, setPrintSupport] = useState('');
    const [isFoldedMenu, setIsFoldedMenu] = useState(false);
    const [isCollapsed, setIsCollapsed] = useState(false);
    const [isToggleMenu, setIsToggleMenu] = useState(false);
    const [isHover, setIsHover] = useState(false);
    const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);
    const [isContentOffside, setIsContentOffside] = useState(false);
    const [webstoreInfo, setWebstoreInfo] = useState(null);
    const [isShowWebstore, setIsShowWebstore] = useState(false);
    const [marketplaceInfo, setMarketplaceInfo] = useState(null);
    const [restrictMenu, setRestrictMenu] = useState('');
    const [language, setLanguage] = useState(i18n.language || 'id');
    const [openRestrictMenuSQ, setOpenRestrictMenuSQ] = useState(false);
    const [openRestrictMenuBook, setOpenRestrictMenuBook] = useState(false);
    const isOnline = useNavigatorOnLine();
    const lastStatusIsOnline = React.useRef(true);
    const [showMarketplaceBlocker, setShowMarketplaceBolcker] = useState(false);
    const [showFoodOrderBlocker, setShowFoodOrderBlocker] = useState(false);
    const [isAddonSupportExpired, setIsAddonSupportExpired] = useState(false);
    const [customBlockerMessage, setCustomBlockerMessage] = useState('');
    const [openTaskCompleteDialog, setOpenTaskCompleteDialog] = useState(false);
    const [showExpiredModal, setShowExpiredModal] = useState(false);
    const [blockerDescription, setBlockerDescription] = useState('');
    const [showBeforeExpiredModal, setShowBeforeExpiredModal] = useState(false);
    const [showTermModal, setShowTermModal] = useState(false);
    const { addToast } = React.useContext(ToastContext);
    const { t } = useTranslation(['translation', 'ftue']);

    const contentRef = useRef();

    const defaultPage = getDefaultPage();

    const prevMenu = usePrevious(props.layoutData.menu);
    const prevOnboardingTask = usePrevious(onboardingTask);
    const isExpired = isSupportExpired();

    const handleVisibilityChange = () => {
        if (!document[hidden]) {
            const { userStrap } = props;
            if (
                userStrap &&
                userStrap.userId &&
                typeof getIdUser === 'function' &&
                String(userStrap.userId) !== String(getIdUser())
            ) {
                window.location.reload();
            }
        }
    };

    useConstructor(async () => {
        await fetchMenu(language);
        await getOnboardingTask();
        await getOnBoardingCustomData();
    });

    const isMobile = useMediaQuery('(max-width: 1090px)');

    const handleBalanceWebstore = async outletId => {
        try {
            const res = await getBalance({ outlet_id: outletId });
            setWebstoreInfo(prevState => ({
                ...prevState,
                loading: false,
                balance: res.data,
            }));
        } catch (error) {
            setWebstoreInfo(prevState => ({
                ...prevState,
                loading: false,
                balance: 0,
            }));
            showToast(
                addToast,
                {
                    title: 'Gagal fetch saldo webstore',
                    message: catchError(error),
                    level: 'failed',
                },
                isMobile,
                isOnline,
            );
        }
    };

    const handleFetchWebstore = async () => {
        setWebstoreInfo(prevState => ({ ...prevState, loading: true }));
        try {
            const res = await getDashboardWebstore();
            const { data_integration: dataIntegration } = res;
            const outletId = dataIntegration.id_outlet;
            if (dataIntegration.status_integrasi === INTEGRATION_STATUS.NOT_INTEGRATED) {
                setWebstoreInfo(prevState => ({
                    ...prevState,
                    outletId,
                    weblink: res.merchant_weblink_uri,
                    loading: false,
                    balance: 0,
                }));
                return;
            }
            setWebstoreInfo(prevState => ({
                ...prevState,
                outletId,
                weblink: res.merchant_weblink_uri,
            }));
            await handleBalanceWebstore(outletId);
        } catch (error) {
            setWebstoreInfo(prevState => ({ ...prevState, loading: false }));
            showToast(
                addToast,
                {
                    title: 'Gagal fetch dashboard webstore',
                    message: catchError(error),
                    level: 'failed',
                    errorCode: error.cause.err_code,
                },
                isMobile,
                isOnline,
            );
        }
    };

    const handleTokoOnlineClicked = menuId => {
        const isMenuTokoOnline = menuId === headerMenuId.TOKO_ONLINE;
        setIsShowWebstore(isMenuTokoOnline);
        if (isMenuTokoOnline) {
            handleFetchWebstore(menuId);
        }
    };

    const handleLanguageChanged = useCallback(lang => {
        setLanguage(lang);
    }, []);

    useDidMountEffect(() => {
        fetchMenu(language);
    }, [language]);

    useEffect(() => {
        i18n.on('languageChanged', handleLanguageChanged);
        return () => {
            i18n.off('languageChanged', handleLanguageChanged);
        };
    }, [handleLanguageChanged]);

    useEffect(() => {
        const { setCalendar, setWindowHeight: setHeight } = props;
        setCalendar(firstDate, lastDate);
        window.addEventListener('load', () => {
            initSociomile();
        });
        setHeight(window.innerHeight);
        window.addEventListener('resize', () => setHeight(window.innerHeight));
        document.addEventListener(visibilityChange, handleVisibilityChange, false);
        resetMenu(props, undefined, undefined, menuId => handleTokoOnlineClicked(menuId));
        return () => document.removeEventListener(visibilityChange, handleVisibilityChange);
    }, []);

    useEffect(() => {
        const loc = defineLoc(props);
        const activeMenuDetail = searchMenuByKey(props.layoutData.menu, loc, 'url');
        if (activeMenuDetail) {
            props.firstRenderDetailPrivilege(activeMenuDetail);
            resetMenu(props, props.layoutData.menu, props.location.pathname, menuId => handleTokoOnlineClicked(menuId));
        }
    }, [props.location.pathname]);

    useEffect(() => {
        if (props.layoutData.activeMenu === LAINNYA_CHILD_MENU_ID && !isMobile) {
            resetMenu(props, props.layoutData.menu, defaultPage, menuId => handleTokoOnlineClicked(menuId));
        }
    }, [isMobile]);

    const handleFetchMarketplaceInfo = async () => {
        try {
            const momentNow = moment();
            const query = {
                page: 1,
                limit: 1,
                order_date_end: momentNow.format('YYYY-MM-DD'),
                order_date_start: moment(momentNow).subtract(7, 'day').format('YYYY-MM-DD'),
            };
            const { data } = await getListOrderMarketplace(query);
            const orderSummary = data.order_status_summary;
            const orderPendingCount = Number(orderSummary.pending);
            const orderTotalCount =
                Number(orderSummary.pending) + Number(orderSummary.processing) + Number(orderSummary.shipping);
            setMarketplaceInfo(prevState => ({
                ...prevState,
                orderPendingCount,
                orderTotalCount,
            }));
        } catch (error) {
            setMarketplaceInfo(null);
            showToast(
                addToast,
                {
                    title: 'Gagal fetch info order marketplace',
                    message: catchError(error.message),
                    level: 'failed',
                    errorCode: error.cause.err_code,
                },
                isMobile,
                isOnline,
            );
        }
    };

    useEffect(() => {
        handleFetchMarketplaceInfo();
    }, [props.layoutData.activeMenuGroup]);

    useEffect(() => {
        const { sentSupportNeeded } = props;
        let loc = props.location.pathname;
        loc = loc.substring(1, loc.length);

        // TODO: to check, belum ada perubahan sama sekali
        if (Object.keys(props.params).length > 0) {
            const arrLoc = loc.split('/');
            arrLoc.splice(-2, 2);
            loc = arrLoc.join('/');
        }
        const data = props.layoutData.menu;
        const activeMenu = searchMenuByKey(data, loc, 'url');

        if (
            activeMenu &&
            typeof activeMenu.print_support_need !== 'undefined' &&
            activeMenu.print_support_need !== null &&
            activeMenu.print_support_need !== ''
        ) {
            if (Array.isArray(activeMenu.print_support_need)) {
                sentSupportNeeded(activeMenu.print_support_need);
            } else {
                sentSupportNeeded(activeMenu.print_support_need.split(','));
            }
        } else {
            sentSupportNeeded([
                'BUSINESS',
                'BUSINESS PRO',
                'ADVANCE',
                'STARTER',
                'TRIAL',
                'ENTERPRISE',
                'MAXIMA STARTER',
                'MAXIMA ADVANCE',
                'PRIME+',
            ]);
        }
    }, [props.layoutData.menu, props.outlets, props.showNotifExp, props.filterBranch]);

    useDidMountEffect(() => {
        const {
            layoutData: { menu },
        } = props;
        if (!prevMenu || (prevMenu && prevMenu.length === 0)) {
            resetMenu(props, menu, undefined, menuId => handleTokoOnlineClicked(menuId));
        }
    }, [props.layoutData.menu]);

    const handleFloatContent = () => {
        if (!contentRef.current) return;
        const { top = 0 } = contentRef.current.getBoundingClientRect();
        if (top <= -65 && !isContentOffside) {
            setIsContentOffside(true);
            window.removeEventListener('scroll', handleFloatContent);
        } else if (top > -65 && isContentOffside) {
            setIsContentOffside(false);
            window.removeEventListener('scroll', handleFloatContent);
        }
    };

    useEffect(() => {
        const { contentFloat } = props;
        if (contentFloat) {
            window.addEventListener('scroll', handleFloatContent);
        } else {
            window.removeEventListener('scroll', handleFloatContent);
        }
    }, [props.contentFloat, isContentOffside]);

    useEffect(() => {
        setIsContentOffside(false);
    }, [props.contentFloat]);

    const onCheckMenuBlockHandler = async (id, path) => {
        let _id = id;
        /*
        const { layoutData } = this.props;

        // PREVENT RELOAD WHEN CLICKING THE SAME MENU
        if (id === layoutData.activeMenu) {
            return true;
        }
        */

        // ketika user expired dan navigasi ke halaman selain pembelian support dan pengaturan cabang
        if (isSupportExpired()) {
            if (
                _id === _menu.PENGATURAN_OUTLET ||
                _id === _menu.BELI_LANGGANAN_SUPPORT ||
                (_id === undefined && path === 'informasi-akun') ||
                _id === _menu.INBOX ||
                path === 'message/inbox'
            ) {
                return false;
            }

            setShowBlocker(true);

            return true;
        }

        if (path === 'message-setting/inbox') {
            const messageMenuDetail = searchMenuByKey(props.layoutData.menu, path, 'url');
            if (messageMenuDetail && 'id' in messageMenuDetail) _id = messageMenuDetail;
        }

        const isBlocked = await checkSupportType(_id);
        // const isBlocked = false;
        if (isBlocked) {
            setShowUnauthorizedBlocker(true);

            return true;
        }

        const isMarketplaceBlocked = await checkMarketplaceSupport(_id);
        if (isMarketplaceBlocked) {
            setShowMarketplaceBolcker(true);
            return true;
        }

        const isFoodOrderBlocker = await checkFoodOrderSupport(_id);
        if (isFoodOrderBlocker) {
            setShowFoodOrderBlocker(true);
            return true;
        }

        return false;
    };

    const checkAddonEcommerce = async () => {
        const parentId = userUtils.getLocalConfigByKey('parentId');
        const branchId = userUtils.getLocalConfigByKey('branchId');
        setIsShowProgress(true);
        try {
            const res = await getSupportInfo({ id_merchant: parentId });
            if ('data' in res && 'TOKOONLINE' in res.data[branchId].addon && res.data[branchId].addon.TOKOONLINE.status)
                return true;
            return false;
        } catch (error) {
            return false;
        } finally {
            setIsShowProgress(false);
        }
    };

    const checkAddonFoodOrder = async () => {
        const parentId = userUtils.getLocalConfigByKey('parentId');
        const branchId = props.filterBranch;
        const outletIds = props.outlets.map(i => i.id_cabang);
        setIsShowProgress(true);
        try {
            let result = false;
            const res = await getSupportInfo({ id_merchant: parentId });
            if (res.data) {
                if (branchId === '') {
                    (outletIds || []).forEach(i => {
                        if (i !== '' && res.data[i].addon && 'ADDONGRAB' in res.data[i].addon) {
                            if (
                                !res.data[i].addon.ADDONGRAB.status &&
                                moment().isAfter(
                                    moment(res.data[i].addon.ADDONGRAB.exp_date, 'YYYY-MM-DD hh:mm:ss'),
                                    's',
                                )
                            ) {
                                setIsAddonSupportExpired(true);
                            } else {
                                result = true;
                            }
                        }
                    });
                } else if (res.data[branchId].addon && 'ADDONGRAB' in res.data[branchId].addon) {
                    if (
                        !res.data[branchId].addon.ADDONGRAB.status &&
                        moment().isAfter(
                            moment(res.data[branchId].addon.ADDONGRAB.exp_date, 'YYYY-MM-DD hh:mm:ss'),
                            's',
                        )
                    ) {
                        setIsAddonSupportExpired(true);
                    } else {
                        result = true;
                    }
                }
            }
            return result;
        } catch (error) {
            return false;
        } finally {
            setIsShowProgress(false);
        }
    };

    const checkAddonMarketplace = async () => {
        const parentId = userUtils.getLocalConfigByKey('parentId');
        const branchId = props.filterBranch;
        const outletIds = props.outlets.map(i => i.id_cabang);
        setIsShowProgress(true);
        try {
            let result = false;
            const res = await getSupportInfo({ id_merchant: parentId });
            if (res.data) {
                if (branchId === '') {
                    (outletIds || []).forEach(i => {
                        if (i !== '' && res.data[i].addon && 'TOKOONLINE' in res.data[i].addon) {
                            if (
                                !res.data[i].addon.TOKOONLINE.status &&
                                moment().isAfter(
                                    moment(res.data[i].addon.TOKOONLINE.exp_date, 'YYYY-MM-DD hh:mm:ss'),
                                    's',
                                )
                            ) {
                                setIsAddonSupportExpired(true);
                            } else {
                                result = true;
                            }
                        }
                    });
                } else if (res.data[branchId].addon && 'TOKOONLINE' in res.data[branchId].addon) {
                    if (
                        !res.data[branchId].addon.TOKOONLINE.status &&
                        moment().isAfter(
                            moment(res.data[branchId].addon.TOKOONLINE.exp_date, 'YYYY-MM-DD hh:mm:ss'),
                            's',
                        )
                    ) {
                        setIsAddonSupportExpired(true);
                    } else {
                        result = true;
                    }
                }
            }
            return result;
        } catch (error) {
            return false;
        } finally {
            setIsShowProgress(false);
        }
    };

    const checkFoodOrderSupport = async id => {
        let isBlocked = false;
        const clicked = searchMenuByKey(props.layoutData.menu, id, 'id');
        const supportType = userUtils.getLocalConfigByKey('accountType');

        const isMenuMarketplace = checkFoodOrderAccessibleMenu(get(clicked, 'name', '').toLowerCase());
        if (isMenuMarketplace && isSubscriptionIncludesAddon(supportType)) {
            const hasAddonEcommerce = await checkAddonFoodOrder();
            isBlocked = !hasAddonEcommerce;
        }

        return isBlocked;
    };

    const checkMarketplaceSupport = async id => {
        let isBlocked = false;
        const clicked = searchMenuByKey(props.layoutData.menu, id, 'id');
        const supportType = userUtils.getLocalConfigByKey('accountType');

        const isMenuMarketplace = checkMarketplaceAccessibleMenu(get(clicked, 'name', '').toLowerCase());
        if (isMenuMarketplace && isSubscriptionIncludesAddon(supportType)) {
            const hasAddonEcommerce = await checkAddonMarketplace();
            isBlocked = !hasAddonEcommerce;
        }

        return isBlocked;
    };

    const checkEcommerceAccessibleMenu = name => {
        switch (name) {
            case '30098':
            case '30134':
            case '30152':
                return true;
            default:
                return false;
        }
    };

    const checkSupportType = async id => {
        let isBlocked = false;
        const clicked = searchMenuByKey(props.layoutData.menu, id, 'id');
        const supportType = userUtils.getLocalConfigByKey('accountType');
        if (!clicked || (clicked && clicked.support_needed === null)) {
            return false;
        }

        const arrMenu = clicked && 'support_needed' in clicked ? clicked.support_needed.split(',') : [];
        const excludePackage = [
            ACCOUNT_TYPE.TRIAL,
            ACCOUNT_TYPE.BUSINESS,
            ACCOUNT_TYPE.BUSINESS_PRO,
            ACCOUNT_TYPE.REGULER,
            ACCOUNT_TYPE.MAXIMA_STARTER,
            ACCOUNT_TYPE.STARTER_BASIC,
            ...(!isStarterBasicSubscription() ? [ACCOUNT_TYPE.STARTER] : []),
        ];
        const excludeTrial = arrMenu.filter(x => !excludePackage.includes(x));

        isBlocked = arrMenu.indexOf(String(supportType)) === -1;
        const printSupport = excludeTrial.join(', ');
        setPrintSupport(printSupport);
        const isMenuOrderOnline =
            id === _menu.ORDER_ONLINE_PENGAJUAN_AKTIVASI || id === _menu.ORDER_ONLINE_PENGATURAN_AKUN;
        if (isMenuOrderOnline && isBlocked) {
            const hasAddonGrab = await checkAddonGrab();
            if (hasAddonGrab) isBlocked = false;
        }

        const isMenuEcommerce = checkEcommerceAccessibleMenu(clicked.id.toLowerCase());
        let hasAddonEcommerce;
        let hasAddonMarketplace;
        if (isMenuEcommerce && ['MAXIMA STARTER', 'STARTER'].includes(supportType)) {
            hasAddonEcommerce = await checkAddonEcommerce();
            isBlocked = !hasAddonEcommerce;
        }

        const isMenuMarketplace = checkFoodOrderAccessibleMenu(clicked.id.toLowerCase());
        if (isMenuMarketplace && ['MAXIMA STARTER', 'STARTER'].includes(supportType)) {
            hasAddonMarketplace = await checkAddonFoodOrder();
            isBlocked = !hasAddonMarketplace;
        }

        return isBlocked;
    };

    const checkAddonGrab = async () => {
        let result = false;

        const res = await getInfoAddon({ id_outlet: props.idCabang });

        if ('data' in res && 'ADDONGRAB' in res.data && res.data.ADDONGRAB.status) {
            result = true;
        }

        return result;
    };

    const getContentMarginLeft = () => {
        if (isMobile) return 0;
        if (isFoldedMenu) return '80px';
        return '280px';
    };

    const accountType = userUtils.getLocalConfigByKey('accountType');
    const listWarnedOutletId = getNearestExpiredOutlets();
    const nearestExp = [];
    if (props.outlets && props.outlets.length > 0) {
        listWarnedOutletId.outlets.forEach(warned => {
            const outlet = props.outlets.find(o => String(o.id_cabang) === String(warned.id));
            if (outlet) {
                Object.assign(warned, {
                    name: outlet.cabang_name,
                });
                nearestExp.push(warned);
            }
        });
    }

    const activateSociomile = () => {
        SOCIOMILE.to('login');
    };

    const blockerCloseHandler = val => {
        if (val === 0) {
            setShowBlocker(false);
        } else {
            setShowUnauthorizedBlocker(false);
            setShowFoodOrderBlocker(false);
            setShowMarketplaceBolcker(false);
            setIsAddonSupportExpired(false);
            setCustomBlockerMessage('');
        }
    };

    const showUnauthorizedBasicStarter = (customMessage = '') => {
        setCustomBlockerMessage(customMessage);
        setPrintSupport([ACCOUNT_TYPE.STARTER, ACCOUNT_TYPE.ADVANCE, ACCOUNT_TYPE.PRIME].join(', '));
        setShowUnauthorizedBlocker(true);
    };

    const showUnauthorizedNotPrime = (customMessage = '') => {
        setCustomBlockerMessage(customMessage);
        setPrintSupport([ACCOUNT_TYPE.PRIME, ACCOUNT_TYPE.PRIMEPLUS].join(', '));
        setShowUnauthorizedBlocker(true);
    };

    const menuBanner = getBannerMenuActive(props.layoutData);

    const forceRoutingBuySupport = (isDirect = false) => {
        setShowExpiredModal(false);
        if (isDirect) router.push('/support/buy');
        let currentSubscription = getSubscriptionType();
        if (currentSubscription === ACCOUNT_TYPE.ENTERPRISE) currentSubscription = ACCOUNT_TYPE.PRIME;
        let sType = 'ADVANCE';
        switch (accountType) {
            case 'BUSINESS':
                sType = 'BUSINESS';
                break;
            case 'REGULER':
                sType = 'REGULER';
                break;
            case 'TRIAL':
                sType = 'ADVANCE';
                break;
            default:
                sType = currentSubscription;
                break;
        }
        router.push(`/support/buy?support=${sType}`);
    };

    const onCloseRestrictPopupMenu = () => {
        setOpenRestrictMenuSQ(false);
        setOpenRestrictMenuBook(false);
    };

    const showPopupExpired = (customDesc = '') => {
        setBlockerDescription(customDesc);
        setShowBlocker(true);
    };

    useEffect(() => {
        let defaultMenu = getDefaultPage();
        if (props.layoutData.menu) {
            [...props.layoutData.menu].reverse().forEach(menu => {
                const menuPaths = getMenuPathList(menu);
                const path = getMenuRedirectPath(defaultPage, menu);
                if (path === defaultPage || !menuPaths.includes(defaultPage)) {
                    defaultMenu = path;
                }
            });
        }
        return statusMenu === 'done-fetched' && !menuPrivilege.isCanView && router.push(defaultMenu) && setNotFetched();
    }, [statusMenu, menuPrivilege.isCanView]);

    const contentHeader = style => (
        <ContentHeader
            setGlobalMessage={_setGlobalMessage}
            router={router}
            shouldShowWarningMessage={nearestExp.length > 0}
            warningMessageData={nearestExp}
            warningMessageDay={listWarnedOutletId.day}
            style={style}
            isLoaderShow={isLoaderShow}
            isFoldedMenu={isFoldedMenu}
            isMobile={isMobile}
            hasMenuBanner={!isEmpty(menuBanner)}
            t={t}
        />
    );

    const childrenWithProps = React.Children.map(children, child =>
        React.cloneElement(child, {
            router,
            assignFilterCalendar: props.setFilterCalendar,
            assignCalendar: (start, end, onchange, rangeLimit) =>
                setCalendarHandler(props, start, end, onchange, rangeLimit),
            assignButtons: props.dispatchButton,
            assignFloatContent: props.dispatchFloatContent,
            assignCustomBreadCrumbs: props.dispatchCustomBreadCrumbs,
            showOverlay: () => setIsShowOverlay(true),
            hideOverlay: () => setIsShowOverlay(false),
            showProgress: label => {
                setIsShowProgress(true);
                setProgressLabel(label || '');
            },
            isShowProgress,
            hideProgress: () =>
                setTimeout(() => {
                    setIsShowProgress(false);
                    props.setLoader(false);
                    setProgressLabel('');
                }, 0),
            addNotification: (
                notification = {
                    title: t('toast.somethingWrong', 'Terjadi Kesalahan'),
                    message: t('toast.failGotData', 'Gagal mendapatkan data'),
                    level: 'failed',
                    dismissAfter: 3000,
                },
            ) => showToast(addToast, notification, isMobile, isOnline),
            isCollapsed,
            setIsCollapsed,
            isFoldedMenu,
            setIsFoldedMenu,
            calendar: props.calendar,
            filterBranch: props.filterBranch,
            setGlobalMessage: data => _setGlobalMessage(props, data),
            language,
            isMobile,
            showUnauthorizedBasicStarter,
            isLoaderShow,
            isTourShow,
            setOpenTaskCompleteDialog,
            showPopupExpired,
            activateSociomile,
            showUnauthorizedNotPrime,
            getContentTranslation: (key = '', translations = contentTranslations, lang = language) =>
                getContentTranslation(translations, lang, key),
            isContentOffside,
        }),
    );

    const handleSetOutletLoan = () => {
        const { listOutletLoan } = loan;

        if (listOutletLoan.length > 0) {
            if (selectedOutletId) {
                const selectedOutletLoan = listOutletLoan.find(x => String(x.outlet_id) === String(selectedOutletId));
                if (!selectedOutletLoan) selectOutlet(String(listOutletLoan[0].outlet_id));
            } else {
                selectOutlet(String(listOutletLoan[0].outlet_id));
            }
        }
    };

    useEffect(() => {
        if (URLS_INVOICE.includes(props.location.pathname) && !WHITELISTS.SALES_QUOTATION.includes(accountType)) {
            setOpenRestrictMenuSQ(true);
        }
        if (URLS_PRODUCT.includes(props.location.pathname) && !WHITELISTS.MENU_BOOK.includes(accountType)) {
            setOpenRestrictMenuBook(true);
        }
    }, [props.location.pathname, accountType]);

    useEffect(() => {
        if (!isOnline && lastStatusIsOnline.current) {
            showToast(
                addToast,
                {
                    title: t('toast.offline.title', 'Internet Bermasalah'),
                    level: 'failed',
                    message: t('toast.offline.description', 'Tidak ada koneksi internet, mohon periksa jaringan'),
                    id: 'offline-connection',
                },
                isMobile,
            );
            lastStatusIsOnline.current = false;
            return;
        }
        if (!lastStatusIsOnline.current && isOnline) {
            showToast(
                addToast,
                {
                    title: t('toast.online.title', 'Online!'),
                    level: 'success',
                    message: t('toast.online.description', 'You are already connected to the internet network'),
                    id: 'online-connection',
                },
                isMobile,
            );
            lastStatusIsOnline.current = true;
        }
    }, [isOnline]);

    useEffect(() => {
        const {
            location: { pathname },
        } = router;

        if (allOutletDisabled.includes(pathname)) {
            if (!selectedOutletId) {
                if (outletLoanWhitelist.includes(pathname)) handleSetOutletLoan();
                else if (outletList.length) {
                    const primaryOutlet = outletList.find(outlet => outlet.cabang_is_primary === '1');
                    if (accType === 'Manager') {
                        selectOutlet(String(outletList[1].id_cabang));
                    } else {
                        selectOutlet(
                            String(outletList.length === 1 ? outletList[0].id_cabang : primaryOutlet.id_cabang),
                        );
                    }
                }
            } else if (outletLoanWhitelist.includes(pathname)) handleSetOutletLoan();
        }
    }, [router, selectedOutletId, outletList, loan.listOutletLoan]);

    useEffect(() => {
        if (prevOnboardingTask && onboardingTask) {
            const prevProgress = Object.values(prevOnboardingTask).reduce((acc, value) => {
                if (value === 1) return acc + value;
                return acc;
            }, 0);
            const currentProgress = Object.values(onboardingTask).reduce((acc, value) => {
                if (value === 1) return acc + value;
                return acc;
            }, 0);
            if (prevProgress < 3 && currentProgress === 3) {
                const timer = setTimeout(() => {
                    setOpenTaskCompleteDialog(true);
                }, 1000);
                return () => clearTimeout(timer);
            }
        }
        return () => {};
    }, [onboardingTask]);

    useEffect(() => {
        const { userStrap } = props;
        if (
            isExpired &&
            userStrap &&
            [LIFECYCLE_STATUS.GRACE, LIFECYCLE_STATUS.HOT].includes(userStrap.lifecycleStatus)
        ) {
            setTimeout(() => setShowExpiredModal(isExpired), 800);
        }
    }, [isExpired]);

    useEffect(() => {
        const listExpDateOutlets = userUtils.getLocalConfigByKey('outletExp');
        const branchId = userUtils.getLocalConfigByKey('branchId');
        const expdateOutlet = listExpDateOutlets[branchId];
        const diffDate = moment(expdateOutlet, 'YYYY-MM-DD HH:mm').diff(moment(), 'days');

        if (props.location.pathname === '/support/buy' && !isExpired && props.countCloseModalExpired === 0 && diffDate <= 3) {
            setShowBeforeExpiredModal(true);
        }
    }, [props.location.pathname, isExpired]);

    useEffect(() => {
        if (!tncStatus) setShowTermModal(true);
    }, [tncStatus])

    return (
        <React.Fragment>
            <Container>
                <Helmet>
                    <title>Dashboard | majoo: Aplikasi Wirausaha Lengkap Bikin Bisnis Jadi Maju</title>
                    <meta
                        name="description"
                        content="Monitor semua cabang darimanapun dan mudah mengatur penjualan, absensi, stok, keuangan, pelanggan loyalti, promosi, promosi."
                    />
                </Helmet>
                {openTaskCompleteDialog && (
                    <TaskCompleteDialog
                        open={openTaskCompleteDialog}
                        handleClose={() => setOpenTaskCompleteDialog(false)}
                        t={t}
                    />
                )}
                <Onboarding />
                {/* block popup */}
                {showBlocker && (
                    // RETINA VERSION
                    <PopUpExpiredV2
                        onCloseEvent={() => blockerCloseHandler(0)}
                        router={props.router}
                        outlets={props.outlets}
                        isMobile={isMobile}
                        forceRoutingBuySupport={forceRoutingBuySupport}
                        description={
                            blockerDescription && <div dangerouslySetInnerHTML={{ __html: blockerDescription }} />
                        }
                    />
                )}
                {showUnauthorizedBlocker && (
                    <UnauthorizedBlockerV2
                        customMessage={customBlockerMessage}
                        supportName={printSupport}
                        onCloseEvent={blockerCloseHandler}
                        router={router}
                    />
                )}
                {showMarketplaceBlocker && (
                    <UnauthorizedBlockerAddOns
                        isExpired={isAddonSupportExpired}
                        supportName="Marketplace"
                        onCloseEvent={blockerCloseHandler}
                        router={router}
                        t={t}
                    />
                )}
                {showFoodOrderBlocker && (
                    <UnauthorizedBlockerAddOns
                        isExpired={isAddonSupportExpired}
                        supportName="Food Order"
                        onCloseEvent={blockerCloseHandler}
                        router={router}
                        t={t}
                    />
                )}
                <RestrictPopup disabledCloseBtn restrictMenu={restrictMenu} setRestrictMenu={setRestrictMenu} />
                {openRestrictMenuSQ && (
                    <RestrictPopupMenu
                        router={router}
                        isMobile={isMobile}
                        onCloseEvent={onCloseRestrictPopupMenu}
                        path={props.location.pathname}
                    />
                )}

                {openRestrictMenuBook && (
                    <RestrictPopupMenu
                        router={router}
                        isMobile={isMobile}
                        onCloseEvent={onCloseRestrictPopupMenu}
                        path={props.location.pathname}
                        type={POPUP_RESTRICT_TYPES.MENU_BOOK}
                        hideTitle
                    />
                )}
                {showTermModal && <TermsAndConditionModal isOpen={showTermModal} onOpenChange={() => setShowTermModal(false)} isMobile={isMobile}/>}
                {showExpiredModal && <ExpiredModal open={showExpiredModal} onOpenChange={() => setShowExpiredModal(false)} isMobile={isMobile} onBuy={forceRoutingBuySupport} router={router} />}
                {showBeforeExpiredModal && <BeforeExpiredModal isOpen={showBeforeExpiredModal} onClose={() => setShowBeforeExpiredModal(false)} onBuy={forceRoutingBuySupport} />}

                {/* RETINA VERSION */}
                {(nearestExp.length > 0 || (!isSupportExpired() && accountType === ACCOUNT_TYPE.TRIAL)) && (
                    <ExpirationWarningV2
                        day={listWarnedOutletId.day}
                        data={nearestExp}
                        onExtendHandler={forceRoutingBuySupport}
                        isToggleMenu={isFoldedMenu}
                        isMobile={isMobile}
                        isTrial={accountType === ACCOUNT_TYPE.TRIAL}
                    />
                )}

                <DeleteWarning isToggleMenu={isFoldedMenu} isMobile={isMobile} />

                <VirtualAccountPaymentFooter
                    onNavigateToBuySupport={forceRoutingBuySupport}
                    isToggleMenu={isFoldedMenu}
                    isMobile={isMobile}
                    router={router}
                />

                <Drawer
                    open={mobileDrawerOpen}
                    onOpenChange={setMobileDrawerOpen}
                    side="left"
                    css={{ zIndex: '$popover' }}
                >
                    <Header
                        {...props}
                        isShowProgress={isShowProgress}
                        forceRoutingBuySupport={forceRoutingBuySupport}
                        router={router}
                        showProgress={() => setIsShowProgress(true)}
                        hideProgress={() => {
                            setIsShowProgress(false);
                            props.setLoader(false);
                        }}
                        isMobile={isMobile}
                        handleParentMenuClick={handleTokoOnlineClicked}
                        marketplaceInfo={marketplaceInfo}
                        setShowBlocker={setShowBlocker}
                        t={t}
                        checkSupportType={checkSupportType}
                        setShowUnauthorizedBlocker={setShowUnauthorizedBlocker}
                        day={listWarnedOutletId.day}
                    />

                    <Box css={{ height: isMobile ? '60px' : '56px' }} />

                    <Sidebar
                        router={router}
                        accountType={accountType}
                        onCheckMenuBlocked={onCheckMenuBlockHandler}
                        onCollapsedChange={setIsCollapsed}
                        onDrawerOpenChange={setMobileDrawerOpen}
                        isCollapsed={isCollapsed}
                        isFoldedMenu={isFoldedMenu}
                        isMobile={isMobile}
                        activateSociomile={activateSociomile}
                        webstoreInfo={isShowWebstore ? webstoreInfo : null}
                        marketplaceInfo={marketplaceInfo}
                        language={language}
                        setOpenRestrictMenuSQ={setOpenRestrictMenuSQ}
                        setOpenRestrictMenuBook={setOpenRestrictMenuBook}
                        selectOutlet={selectOutlet}
                    >
                        <ChatwootWidget
                            websiteToken={process.env.MAJOO_CHAT_TOKEN}
                            baseUrl={process.env.MAJOO_CHAT_BASE_URL}
                            user={accountProfile}
                            customAttributes={businessProfile}
                        />
                    </Sidebar>

                    <Content
                        css={{
                            '& .content-wrapper': {
                                marginLeft: getContentMarginLeft(),
                                transition: 'margin 0.5s',
                                paddingBottom: '$spacing-05',
                                ...(nearestExp.length > 0 && {
                                    paddingBottom: isMobile ? '$spacing-11' : '$spacing-09',
                                }),
                            },
                        }}
                        ref={contentRef}
                        isMobile={isMobile}
                    >
                        {!isEmpty(menuBanner) && !isLoaderShow && (
                            <PageBanner
                                menuObj={menuBanner}
                                bannerImageKey={isMobile ? 'image_mobile' : 'image_desktop'}
                                setMarginLeft={() => getContentMarginLeft()}
                                isFoldedMenu={isFoldedMenu}
                                isMobile={isMobile}
                            />
                        )}

                        {isContentOffside && props.contentFloat && !isLoaderShow && (
                            <Box
                                css={{
                                    position: 'sticky',
                                    zIndex: '$docked',
                                    height: '$full',
                                    backgroundColor: 'white',
                                    top: '56px',
                                    width: '100%',
                                    paddingLeft: getContentMarginLeft(),
                                    boxShadow:
                                        '0px 1px 1px rgba(0, 0, 0, 0.07), 0px 2px 4px rgba(0, 0, 0, 0.05), 0px 4px 10px rgba(0, 0, 0, 0.05)',
                                }}
                            >
                                {props.contentFloat}
                            </Box>
                        )}

                        {isTourShow
                            ? null
                            : contentHeader({
                                  transition: 'margin 0.5s',
                                  marginLeft: getContentMarginLeft(),
                              })}
                        <ErrorBoundary>{childrenWithProps}</ErrorBoundary>
                    </Content>
                </Drawer>
                {!isMobile && (
                    <React.Fragment>
                        <FoldButton
                            buttonType={`${isCollapsed ? 'primary' : 'secondary'}`}
                            collapsed={isCollapsed}
                            onClick={() => {
                                setIsFoldedMenu(!isFoldedMenu);
                                setIsCollapsed(!isCollapsed);
                            }}
                            css={{
                                position: 'fixed',
                                top: '83px',
                                zIndex: '$banner',
                                transition: 'left 0.5s',
                                backgroundColor: `${isCollapsed ? '$bgGreen' : '$white'}`,
                            }}
                        >
                            {isCollapsed ? (
                                <ExpandSidebarOutline color="currentColor" />
                            ) : (
                                <CollapsibleSidebarOutline color="currentColor" />
                            )}
                        </FoldButton>
                        {/* TODO: remove sociomile after all alredy migrate to chatwoot */}
                        {/* <ChatPanel
                            clickHandler={activateSociomile}
                            accountType={accountType}
                            router={router}
                            isToggleMenu={isFoldedMenu}
                            isHover={!isCollapsed}
                            onHoverHandler={hoverStatus => {
                                if (isFoldedMenu) setIsCollapsed(!hoverStatus);
                            }}
                        /> */}
                    </React.Fragment>
                )}
            </Container>
            <Loading showLoading={isShowProgress || isLoaderShow} label={progressLabel} />
        </React.Fragment>
    );
};

export default connect(
    store => ({
        calendar: store.layout.calendar,
        filterBranch: store.branch.filter,
        isOverlayShow: store.layout.contentOverlay,
        isLoaderShow: store.layout.loaderShow,
        usaha: store.user,
        businessProfile: {
            ownerName: store.user.profile.user_name,
            ownerPhone: store.user.profile.user_usaha_notlp,
            merchantName: store.user.profile.user_usaha_name,
            merchantEmail: store.user.profile.user_usaha_email,
        },
        showNotifExp: store.layout.showNotifExp,
        outlets: store.branch.list,
        layoutData: {
            menu: store.layouts.menu,
            activeMenu: store.layouts.activeMenu,
            activeMenuGroup: store.layouts.activeMenuGroup,
            expandedMenu: store.layouts.expandedMenu,
        },
        userStrap: store.users.strap,
        menuPrivilege: store.layouts.detailPrivilege,
        statusMenu: store.layouts.status,
        contentFloat: store.layout.contentFloat,
        isTourShow: store.onBoardingReducer,
        outletList: outletSelector.getListBranchByPrivilege(store.branch.list, store.users.strap.permissionId),
        selectedOutletId: store.branch.filter,
        accType:
            store &&
            store.accountInfo &&
            store.accountInfo.accountInfoResult &&
            store.accountInfo.accountInfoResult.hak_akses,
        tncStatus: store &&
            store.accountInfo &&
            store.accountInfo.accountInfoResult &&
            store.accountInfo.accountInfoResult.has_parent === 0 ? 
            store.accountInfo.accountInfoResult.tnc_mal_status : 1,
        accountProfile: {
            email: store.accountInfo.accountInfoResult.user_email,
            name: store.accountInfo.accountInfoResult.user_name,
            phone_number: normalizePhoneNumber(store.accountInfo.accountInfoResult.user_notlp),
            outletName: store.accountInfo.accountInfoResult.cabang_name,
        },
        onboardingTask: store.users.strap.onboardingTask,
        onBoardingCustomData: store.users.strap.onBoardingCustomData,
        contentTranslations: store.translations.contentTranslations,
        loan: store.loan,
        countCloseModalExpired: store.virtualAccountPayment.countCloseModalExpired,
    }),
    dispatch => ({
        setFilterCalendar: (data = null, value = null, onchange = null) => {
            dispatch(
                setContentHeaderFilterCalendar({
                    data,
                    value,
                    onchange,
                }),
            );
        },
        setCalendar: (start = null, end = null, onchange, rangeLimit = null) => {
            dispatch(
                setContentHeaderCalendar({
                    start,
                    end,
                    onchange,
                    rangeLimit,
                }),
            );
        },
        dispatchButton: (buttons = []) => dispatch(setContentHeaderButtons(buttons)),
        dispatchFloatContent: payload => dispatch(setContentHeaderFloat(payload)),
        dispatchCustomBreadCrumbs: payload => dispatch(setCustomBreadCrumbs(payload)),
        setExpandList: (level, idParent) => dispatch(setExpandList(level, idParent)),
        unsetExpandList: (level, idParent) => dispatch(unsetExpandList(level, idParent)),
        setWindowHeight: height => dispatch(setWindowHeight(height)),
        setShowNotif: payload => dispatch(setShowNotifSupport(payload)),
        setOverlay: payload => dispatch(setContentOverlay(payload)),
        setLoader: payload => dispatch(setLoaderState(payload)),
        sentSupportNeeded: payload => dispatch(setSupportNeeded(payload)),
        setUnreadMsg: payload => dispatch(setUnreadMsg(payload)),
        setUnreadNotif: payload => dispatch(setUnreadNotif(payload)),
        setUnreadMsgSmall: payload => dispatch(setUnreadMsgSmall(payload)),
        setRecalculateNotif: payload => dispatch(setRecalculateNotif(payload)),
        fetchProfile: () => {
            dispatch(fetchInfoUsaha());
        },
        fetchAccountInfo: () => dispatch(fetchAccountInfo()),
        fetchSupportHistorySocket: payload => dispatch(fetchSupportHistorySocket(payload)),
        updateCabangPush: payload => dispatch(updateCabangPush(payload)),
        fetchOutletExp: payload => dispatch(fetchOutletExp(payload)),
        selectOutlet: idCabang => {
            dispatch(filterCabang(idCabang));
        },
        ...bindActionCreators(
            {
                fetchMenu: layoutsActions.fetchMenu,
                onUpdateActiveGroupMenu: layoutsActions.updateActiveMenuGroup,
                onUpdateActiveMenu: layoutsActions.updateActiveMenu,
                onUpdateExpandedMenu: layoutsActions.updateExpandedMenu,
                firstRenderDetailPrivilege: layoutsActions.firstRenderDetailPrivilege,
                onMenuChange: layoutsActions.onMenuChange,
                setNotFetched: layoutsActions.setNotFetched,
            },
            dispatch,
        ),
    }),
)(LayoutV2);
