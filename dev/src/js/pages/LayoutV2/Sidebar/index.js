/* eslint-disable react/prop-types */
import { Box, DrawerContent } from '@majoo-ui/react';
import React, { useEffect, useMemo, useState } from 'react';
import { getAnalytics, logEvent } from '@firebase/analytics';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
  isMobileOnly as isMobileDevice,
} from 'react-device-detect';
import { fetchCabang, fetchOutletGroup } from '../../../actions/branchActions';
import { fetchOutletLoanWhitelist } from '../../../actions/loanActions';
// import ChatPanel from '../../../components/layout/NotificationWrapper/ChatPanel';
import { layoutsActions } from '../../../data/layouts/layouts.reducer';
import layoutsSelector from '../../../data/layouts/layouts.selector';
import * as outletSelector from '../../../data/outlets/selectors';
import { colors, styled } from '../../../stitches.config';
import { isHasSession } from '../../../utils/passport';
import { handleSetActiveGroupMenu } from '../Header/utils';
import { redirectToLogin, searchMenuByKey, defineLoc } from '../utils';
import MiniSidebarNav from './MiniSidebarNav';
import MobileOutletSection, { LAINNYA_CHILD_MENU_ID, LAINNYA_MENU_ID } from './mobile/MobileOutletSection';
import MobileSidebarNav from './mobile/MobileSidebarNav';
import OutletSection from './OutletSection';
import SidebarNav from './SidebarNav';
import WebstoreInfo from './WebstoreInfo/index';
import { addAdditionalSidebarMenu, removeSidebarMenu, removeSidebarMenuLevel2 } from './utils';
import { useMediaQuery } from '../../../utils/useMediaQuery';
import userUtil from '../../../utils/user.util';
import { URLS_INVOICE, WHITELISTS, URLS_PRODUCT } from '../RestrictPopup/enum';
import { getDefaultPage } from '../../../services/session';
import { useOutletsKasbon } from './useOutletsKasbon';

const StyledSidebar = styled('aside', {
  position: 'fixed',
  height: '100%',
  backgroundColor: '#04C99E',
  transition: 'width 0.5s',
  zIndex: 1200,
  width: '280px',
  variants: {
    collapsed: {
      false: {
        width: '280px',
      },
      true: {
        width: '80px',
      },
    },
    isMobile: {
      true: {
        position: 'unset',
        // position: 'fixed',
        // left: 0,
        // top: 0,
        height: '100%',
        backgroundColor: 'white',
        zIndex: 10000,
        width: '360px',
        overflowY: 'auto',
        overflowX: 'hidden',
        '-ms-overflow-style': 'none',
        scrollbarWidth: 'none',
        '&::-webkit-scrollbar': {
          display: 'none',
        },
      },
    },
  },
});

const StyledDrawerContent = styled(DrawerContent, {
  maxWidth: '360px',
});

const RelativeLayout = styled('div', {
  position: 'relative',
});

const Sidebar = ({
  headerMenus,
  onDrawerOpenChange,
  onCollapsedChange,
  isCollapsed,
  isFoldedMenu,
  isMobile,
  sidebarMenus: sidebarMenuList,
  mobileSidebarMenus,
  activeMenuGroup,
  activeMenu,
  expandedMenu,
  onUpdateActiveGroupMenu,
  onMenuChange,
  onCheckMenuBlocked,
  router,
  accountType,
  fetchOutlets,
  fetchOutletGroupList,
  activateSociomile,
  unreadMsg,
  unreadNotif,
  webstoreInfo,
  marketplaceInfo,
  isShowProgress,
  outletList,
  outletGroupList,
  menuList,
  selectOutlet,
  selectedOutletId,
  selectedHeaderOutlet,
  language,
  accountInfo,
  setOpenRestrictMenuSQ,
  setOpenRestrictMenuBook,
  fetchOutletLoanWhitelistAction,
  children,
}) => {
  const ALL_OUTLETS_NAME = language === 'en' ? 'All Outlets' : 'Semua Outlet';

  const [hasCollapsed, setHasCollapsed] = useState(false);
  const [sidebarMenus, setSidebarMenus] = useState(sidebarMenuList);
  const [parentActiveMenuId, setParentActiveMenuId] = useState('');
  const [outletName, setOutletName] = useState(ALL_OUTLETS_NAME);
  const [employeeSubMenus, setEmployeeSubMenus] = useState([]);
  const isWebstoreInfo = Boolean(webstoreInfo && (!isFoldedMenu || !isCollapsed));
  const { } = useOutletsKasbon({ router, outletList, fetchOutletLoanWhitelistAction });

  const [disabledOutletGroup, setDisabledOutletGroup] = useState(true);

  const _onMenuClick = async ({
    name, level, id, detail, alias,
  }, path, menus) => {
    // kondisi ketika ada session
    // event.preventDefault();

    if (isHasSession()) {
      // kondisi apabila menu yang di click adalah link

      if (alias === 'MJEMRGNCY') {
        const isEmergency = true;
        // activateZE(isEmergency);
        activateSociomile();
        return false;
      }

      if (detail.length === 0) {
        // close mobile menu
        if (id !== 'favorite-menu') {
          onDrawerOpenChange(false);
        }

        // kondisi apabila navigasi ditolak
        const isBlocked = await onCheckMenuBlocked(id);
        if (isBlocked) {
          return false;
        }
      }

      onMenuChange(
        level, id, detail, name,
      );

      if (URLS_INVOICE.includes(path) && !WHITELISTS.SALES_QUOTATION.includes(accountType)) {
        setOpenRestrictMenuSQ(true);

        return false;
      }
      if (URLS_PRODUCT.includes(path) && !WHITELISTS.MENU_BOOK.includes(accountType)) {
        setOpenRestrictMenuBook(true);

        return false;
      }

      if (path) router.push(path);

      // if (isSupportExpired()
      //     && detail.length === 0
      //     // && (detail.url !== '/support/buy' || detail.url !== '/pengaturan-bisnis/cabang')
      //     && (id !== '272' && id !== '243')
      // ) {
      //     menuClickHandler(id);
      //     if (hideMobileMenu && detail.length === 0) {
      //         hideMobileMenu();
      //     }
      //     event.preventDefault();
      // } else {
      //     if (detail.length === 0) {
      //         const isBlocked = menuClickHandler(id);

      //         if (isBlocked) {
      //             event.preventDefault();
      //         } else {
      //             onMenuClick(level, id, detail);
      //         }
      //     }
      //     else if (hideMobileMenu && detail.length === 0) {
      //         hideMobileMenu();
      //     }
      // }

      // LOG EVENT FOR EMPLOYEE SUBMENU
      if (employeeSubMenus.includes(name)) {
        const { accountInfoResult: { user_email, hak_akses } } = accountInfo;
        const merchantId = userUtil.getLocalConfigByKey('parentId');
        const analytics = getAnalytics();
        logEvent(analytics, 'payroll_page_visit', {
          user_email,
          user_role: hak_akses,
          merchant_id: merchantId,
          outlet_id: selectedOutletId,
        });
      }
    } else { // kondisi ketika session habis
      redirectToLogin(router);
    }
    return true;
  };

  useEffect(() => {
    if (!sidebarMenuList) return;
    // const parentsAndRemovedMenus = [
    // {
    //   parentMenuIds: [],
    //   removedMenuId: '29957', // Menu: "Daftar Pesanan" from "Toko Online"
    // },
    // {
    //   parentMenuIds: ['192'],
    //   removedMenuId: '26525', // Menu: "Daftar Pembayaran Pembelian" from "Keuangan - Pengeluaran"
    // },
    // {
    //   parentMenuIds: [],
    //   removedMenuId: '9', // Menu: "Inventori" from "Penjualan"
    // }
    // ]

    const parentsAndAdditionalMenus = [];

    const modifiedMenus = addAdditionalSidebarMenu({
      parentsAndAdditionalMenus,
      sidebarMenus: (isMobile && isMobileDevice) ? mobileSidebarMenus : sidebarMenuList,
      activeMenuGroup,
    });

    setSidebarMenus(modifiedMenus);
  }, [sidebarMenuList, isMobile]);

  useEffect(() => {
    fetchOutlets(true, language);
    fetchOutletGroupList();
  }, []);

  useEffect(() => {
    const defaultPage = getDefaultPage();

    if (!isMobile) {
      onDrawerOpenChange(false);
      if (activeMenu === LAINNYA_CHILD_MENU_ID) {
        _onMenuClick(headerMenus[0].detail[0], defaultPage);
      }
    }
  }, [isMobile, activeMenu]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setHasCollapsed(isCollapsed);
    }, 500);
    return () => clearTimeout(timeout);
  }, [isCollapsed]);

  useEffect(() => {
    let selectedOutletName = '';
    if (selectedHeaderOutlet === '') {
      selectedOutletName = ALL_OUTLETS_NAME;
    } else if (selectedHeaderOutlet.includes('-')) {
      // selected group outlet
      const selectedGroupOutlet = outletGroupList.find(groupOutlet => groupOutlet.id_cabangs === selectedHeaderOutlet);
      selectedOutletName = selectedGroupOutlet ? selectedGroupOutlet.group_name : '';
    } else {
      // selected single outlet
      const selectedBranch = outletList.find(outlet => outlet.id_cabang === selectedHeaderOutlet);
      selectedOutletName = selectedBranch ? selectedBranch.cabang_name : '';
    }
    setOutletName(selectedOutletName);
  }, [JSON.stringify(outletList), selectedHeaderOutlet, JSON.stringify(outletGroupList)]);

  // GET EMPLOYEE SUBMENUS
  // NEED TO REFACTOR THE FUNCTION SO OTHER MENU CAN USE IF NEEDED
  useEffect(() => {
    const employeeMenus = headerMenus.find(({ name }) => (name === 'KARYAWAN' || name === 'EMPLOYEE'));
    const subMenus = [];
    if (employeeMenus) {
      employeeMenus.detail.map((item) => {
        if (item.detail.length) {
          item.detail.map(subItem => subMenus.push(subItem.name));
        } else {
          subMenus.push(item.name);
        }
      });
    }
    setEmployeeSubMenus(subMenus);
  }, []);

  useEffect(() => {
    const loc = defineLoc({ location: router.location, params: {} });
    const activeMenuDetail = searchMenuByKey(menuList, loc, 'url');

    const searchURL = new URLSearchParams(window.location.search);
    const enableOutletGroup = searchURL.get('enable_outlet_group') === '1';
    if (enableOutletGroup) {
      setDisabledOutletGroup(false);
    } else if (activeMenuDetail) setDisabledOutletGroup(activeMenuDetail.is_can_use_group_outlet !== '1');

    // jika page not allowed group outlet (disabled), dan outlet masih aktif di group outlet, maka set to outlet utama
    if (activeMenuDetail && activeMenuDetail.is_can_use_group_outlet !== '1') {
      const findOutletGroup = outletGroupList.find(x => x.id_cabangs === selectedHeaderOutlet);
      const idOutletUtama = userUtil.getLocalConfigByKey('branchId');
      if (findOutletGroup) selectOutlet(String(idOutletUtama));
    }
  }, [router.location.pathname, JSON.stringify(menuList)]);

  return !isMobile ? (
    <RelativeLayout>
      <StyledSidebar
        collapsed={isCollapsed}
        isMobile={isMobile}
        onMouseEnter={() => {
          if (hasCollapsed) {
            onCollapsedChange(false);
          }
        }}
        onMouseLeave={() => {
          if (isFoldedMenu) onCollapsedChange(true);
        }}
      >
        <OutletSection outletName={outletName} isCollapsed={isCollapsed} router={router} disabledOutletGroup={disabledOutletGroup} />
        {isWebstoreInfo && <WebstoreInfo data={webstoreInfo} />}
        <RelativeLayout
          css={{
            paddingBottom: '16px',
            height: `calc(100% - ${isWebstoreInfo ? 362 : 194}px)`,
            overflowY: 'auto',
            overflowX: 'hidden',
            '-ms-overflow-style': 'none',
            scrollbarWidth: 'thin',
            '&::-webkit-scrollbar': {
              width: '8px',
              borderRadius: '8px',
              backgroundColor: colors.primary600,
            },
            '&::-webkit-scrollbar-thumb': {
              width: '8px',
              borderRadius: '8px',
              backgroundColor: colors.primary700,
            },
          }}
        >
          {
            !isCollapsed
              ? (
                <React.Fragment>
                  <SidebarNav
                    activeMenu={activeMenu}
                    expandedMenu={expandedMenu}
                    detail={sidebarMenus}
                    onMenuClick={async (menu, path, menus) => {
                      const onMenuClick = await _onMenuClick(menu, path, menus);
                      if (onMenuClick && !menu.hasDetails) {
                        setParentActiveMenuId(menu.mainParentId);
                      }
                    }}
                    isCollapsed={isCollapsed}
                    unreadMsg={unreadMsg}
                    unreadNotif={unreadNotif}
                    marketplaceInfo={marketplaceInfo}
                  />
                  {children}
                </React.Fragment>
              )
              : (
                <MiniSidebarNav
                  menuList={sidebarMenus}
                  activeMenu={activeMenu}
                  expandedMenu={expandedMenu}
                  parentActiveMenuId={parentActiveMenuId}
                  collapsed={isCollapsed}
                />
              )
          }
        </RelativeLayout>

      </StyledSidebar>
    </RelativeLayout>
  ) : (
    <StyledDrawerContent side="left">
      <StyledSidebar isMobile={isMobile}>
        <MobileOutletSection
          outletName={outletName}
          menus={headerMenus.filter(menu => !isMobileDevice || Number(menu.menu_cms_mobile) > 0)}
          selectedHeaderMenuId={activeMenuGroup}
          activeMenu={activeMenu}
          onMenuChange={selectedMenu => handleSetActiveGroupMenu(selectedMenu, { onUpdateActiveGroupMenu, onMenuChange, router }, isMobile)}
          onOtherClick={async () => {
            router.push('/mobile/lainnya');
            await onMenuChange('', LAINNYA_MENU_ID, []);
            await onCheckMenuBlocked();
            onDrawerOpenChange(false);
          }}
          closeDrawer={() => onDrawerOpenChange(false)}
          router={router}
          isShowProgress={isShowProgress}
          disabledOutletGroup={disabledOutletGroup}
        />
        <RelativeLayout
          css={{
            height: 'fit-content',
            paddingBottom: '80px',
          }}
        >
          <MobileSidebarNav
            activeMenu={activeMenu}
            expandedMenu={expandedMenu}
            detail={sidebarMenus}
            onMenuClick={async (menu, path, menus) => {
              const onMenuClick = await _onMenuClick(menu, path, menus);
              if (onMenuClick && !menu.hasDetails) {
                setParentActiveMenuId(menu.mainParentId);
              }
            }}
            isCollapsed={isCollapsed}
            unreadMsg={unreadMsg}
            unreadNotif={unreadNotif}
            marketplaceInfo={marketplaceInfo}
          />
          {/* <ChatPanel
            clickHandler={activateSociomile}
            accountType={accountType}
            router={router}
            isMobile={isMobile}
            isToggleMenu={isFoldedMenu}
            isHover={!isCollapsed}
          /> */}
          {React.cloneElement(children, { isMobile: true })}
        </RelativeLayout>
      </StyledSidebar>
    </StyledDrawerContent>
  );
};

const mapStateToProps = state => ({
  headerMenus: state.layouts.menu,
  activeMenuGroup: state.layouts.activeMenuGroup,
  activeMenu: state.layouts.activeMenu,
  expandedMenu: state.layouts.expandedMenu,
  menuParents: state.layouts.expandedMenu,
  sidebarMenus: layoutsSelector.getVisibleNav(state.layouts),
  mobileSidebarMenus: layoutsSelector.getVisibleNav({
    ...state.layouts,
    menu: layoutsSelector.getAllowedMobileMenu(
      layoutsSelector.getModifiedMobileMenuList(state.layouts.menu, state.layouts.activeMenuGroup),
    ),
  }),
  unreadMsg: state.setting.unreadMsg,
  unreadNotif: state.setting.unreadNotif,
  outletList: outletSelector.getListBranchByPrivilege(
    state.branch.list,
    state.users.strap.permissionId,
  ),
  outletGroupList: state.branch.groupList,
  selectedOutletId: state.branch.filter,
  selectedHeaderOutlet: state.branch.selectedHeaderOutlet,
  accountInfo: state.accountInfo,
  favorite: state.layouts.favorite,
  menuList: state.layouts.menu,
});

const mapDispatchToProps = dispatch => ({
  fetchOutlets: (withOption, language) => {
    dispatch(fetchCabang(withOption, language));
  },
  fetchOutletGroupList: () => dispatch(fetchOutletGroup()),
  fetchOutletLoanWhitelistAction: params => dispatch(fetchOutletLoanWhitelist(params)),
  ...bindActionCreators({
    onUpdateActiveGroupMenu: layoutsActions.updateActiveMenuGroup,
    onUpdateActiveMenu: layoutsActions.updateActiveMenu,
    onUpdateExpandedMenu: layoutsActions.updateExpandedMenu,
    onMenuChange: layoutsActions.onMenuChange,
  }, dispatch),
});

export default connect(mapStateToProps, mapDispatchToProps)(Sidebar);
