import React, {
  useEffect, useRef, useState,
} from 'react';
import PropTypes from 'prop-types';
import {
  EllipsisVerticalOutline,
  HeroFilled,
  NotificationFilled,
} from '@majoo-ui/icons';
import {
  Portal,
} from '@majoo-ui/helpers';
import {
  Avatar,
  ButtonGroup,
  ButtonGroupItem,
  DrawerTrigger,
  Flex,
  Heading,
  IconButton,
  Image,
  NotificationBadge,
  Text,
  ToastContext,
  LoadingBar,
} from '@majoo-ui/react';
import moment from 'moment';
import { connect } from 'react-redux';
import { useHistory } from 'react-router-dom';
import webstomp from 'webstomp-client';
import Logo from '../../../../assets/images/majoo-logo.svg';
import { statusMenuInMobile } from '../../../components/layout/settings/enum';
import SaveConfirm from '../../../components/modalpopup/SaveConfirm';
import { amqpVHost } from '../../../config/config';
import { loginRabbitMQ } from '../../../data/auth';
import * as settingApi from '../../../data/settings';
import { colors, styled } from '../../../stitches.config';
import { getAcronym, addElipsisText, toTitleCase } from '../../../utils/helper';
import { getIdUser } from '../../../utils/passport';
import userUtils from '../../../utils/user.util';
import AccountDialog from '../Dialog/AccountDialog';
import NotificationDialog from '../Dialog/NotificationDialog';
import { useMediaQuery } from '../../../utils/useMediaQuery';
import {
  fetchLatestMessage,
  fetchMessageHeader,
  startFetchingData,
} from './hooks';
import { handleSetActiveGroupMenu } from './utils';
import OtherDropdownMenu from './components/OtherDropdownMenu';
import BadgeOrderMarketplace from './components/BadgeOrderMarketplace';
import { isSupportExpired } from '../../../services/session';
import { WHITELISTS, POPUP_RESTRICT_TYPES } from '../RestrictPopup/enum';
import RestrictPopup from '../RestrictPopup';
import AccountDisplayWrapper from '../../../components/virtualaccount/AccountDisplayWrapper';
import PaymentInstructionDialog from '../../../components/virtualaccount/PaymentInstructionDialog';
import AvatarProfile from '../Dialog/AvatarProfile';

const IPWebsocket = process.env.IP_WEB_SOCKET;

let loginCount = 0;

const HeaderContainer = styled(Flex, {
  width: '100%',
  position: 'fixed',
  top: 0,
  zIndex: '$banner',
});

const Container = styled(Flex, {
  width: '100%',
  boxShadow: '0px 2px 8px #********',
  background: '$white',
  height: '56px',
  variants: {
    isMobile: {
      true: {
        height: '60px',
      },
    },
  },
});

const LeftSideContainer = styled(Flex, {
  flex: 1,
  alignItems: 'center',
  marginLeft: '$spacing-05',
});

const RightSideContainer = styled(Flex, {
  flex: 1,
  alignItems: 'center',
  justifyContent: 'end',
  marginRight: '$spacing-08',
  variants: {
    isMobile: {
      true: {
        marginRight: '$spacing-06',
      },
    },
  },
});

// TODO: fix filled SVG notification Icon
const NotificationIcon = styled(NotificationFilled, {
  width: '18.5px',
  height: '20px',
});

const HeroIcon = styled(HeroFilled, {
  size: '24px',
  color: '$gray900',
});

const MajooLogo = styled(Image, {
  cursor: 'pointer',
});

const ActionButton = styled(IconButton, {});

const Header = (props) => {
  const browserHistory = useHistory();
  const {
    activeMenuGroup,
    onUpdateActiveGroupMenu,
    userCompanyLogo,
    businessName,
    userName,
    progressAccount,
    progressBusiness,
    progressProfile,
    dispatch,
    menuList,
    isMobile,
    onMenuChange,
    unreadMsg,
    unreadNotif,
    filterBranch,
    forceRoutingBuySupport,
    isShowProgress,
    fetchProfile,
    fetchAccountInfo: fetchAccountInfoProp,
    fetchSupportHistorySocket: fshsc,
    updateCabangPush: ucp,
    fetchOutletExp: fole,
    setRecalculateNotif: srn,
    router,
    handleParentMenuClick,
    marketplaceInfo,
    setShowBlocker,
    userEmail,
    userRole,
    branchData,
    isMultiLanguage,
    t,
    checkSupportType,
    setShowUnauthorizedBlocker,
    userStrap: { branchId },
    day,
  } = props;
  const defaultPage = userUtils.getLocalConfigByKey('defaultPage');
  const smallViewport = window.matchMedia('(max-width: 1280px)').matches;

  const { addToast } = React.useContext(ToastContext);

  const [accountExpiration, setAccountExpiration] = useState();
  const [restrictMenu, setRestrictMenu] = useState('');
  const [accountType, setAccountType] = useState(
    userUtils.getLocalConfigByKey('accountType'),
  );
  const [forceLogoutOpen, setForceLogoutOpen] = useState(false);

  startFetchingData(dispatch);
  const { fetchLatestData, latestMessageList, latestNotifList } = fetchLatestMessage(filterBranch, t);
  const { reloadMsgUnread, messageHeaderResult } = fetchMessageHeader(dispatch);
  const client = useRef(null);
  const forceLogout = useRef(null);
  const paymentInstructionDialogRef = useRef(null);

  const setterRecalculateNotif = (recalculateStatusVal) => {
    srn(recalculateStatusVal);
  };

  const subscribeRabbit = (res) => {
    const idUser = getIdUser();
    const parentId = userUtils.getLocalConfigByKey('parentId');
    const onConnect = () => {
      client.current.subscribe(
        `/exchange/mxuserdirect/usaha${parentId}`,
        (m) => {
          const responsePushMsg = JSON.parse(m.body);
          if (responsePushMsg.push_type.toLowerCase() === 'business profile') {
            fetchProfile();
          }
        },
      );

      client.current.subscribe(
        `/exchange/mxcmsdirect/usaha${parentId}`,
        (m) => {
          const responsePushMsg = JSON.parse(m.body);
          if (responsePushMsg.push_type.toLowerCase() === 'delete message') {
            reloadMsgUnread();
          }
        },
      );

      client.current.subscribe(`/exchange/mxuserdirect/user${idUser}`, (m) => {
        const responsePushMsg = JSON.parse(m.body);
        if (responsePushMsg.push_type.toLowerCase() === 'message') {
          reloadMsgUnread();
        }

        if (responsePushMsg.push_type.toLowerCase() === 'acount profile') {
          fetchAccountInfoProp();
        }
      });
      const expOutlet = userUtils.getLocalConfigByKey('outletExp');
      Object.keys(expOutlet).forEach((key) => {
        client.current.subscribe(`/exchange/mxcmsdirect/cabang.${key}`, (m) => {
          const responsePushMsg = JSON.parse(m.body);
          if (
            responsePushMsg.push_type.toLowerCase()
            === 'support payment history'
          ) {
            const urlAddress = window.location.pathname;
            if (urlAddress === '/support/buy') {
              fshsc(responsePushMsg.data);
            }
          }

          if (responsePushMsg.push_type.toLowerCase() === 'update cabang') {
            ucp(responsePushMsg.data);
          }

          if (responsePushMsg.push_type.toLowerCase() === 'error login') {
            localStorage.setItem(
              'errorMessage',
              JSON.stringify(
                responsePushMsg.data !== undefined ? responsePushMsg.data : [],
              ),
            );
          }

          if (
            responsePushMsg.push_type.toLowerCase() === 'support force logout'
          ) {
            const dataSocket = responsePushMsg.data;
            const urlAddress = window.location.pathname;

            // snap.hide();
            if (
              dataSocket.is_pro === '1'
              && dataSocket.changeSupport === true
            ) {
              forceLogout.current.showPopup();
              setForceLogoutOpen(true);
            }

            if (dataSocket.is_pro === '1') {
              const accountExp = moment(
                dataSocket.outlet_exp[key],
                'YYYY-MM-DD',
              ).format('D MMM YYYY');
              setAccountExpiration(accountExp);
              setAccountType(dataSocket.support_name);
              localStorage.setItem(
                'outlet_exp',
                JSON.stringify(dataSocket.outlet_exp),
              );

              if (
                dataSocket.outlet_list !== undefined
                && dataSocket.outlet_list.length > 0
              ) {
                ucp(dataSocket.outlet_list);
              }
            }

            if (urlAddress === '/pengaturan-bisnis/cabang') {
              fole('1');
            }
          }

          if (responsePushMsg.push_type === 'recalculate_cogs') {
            const dataSocket = responsePushMsg.data;
            const recalculateVal = Number(dataSocket.is_recalculate);
            setterRecalculateNotif(Boolean(recalculateVal));
          }
        });
      });
    };

    const onError = () => {
      if (loginCount <= 2) {
        subscribeRabbit(res);
      }
      loginCount += 1;
    };

    client.current.heartbeat.outgoing = 20000;
    client.current.heartbeat.incoming = 0;
    client.current.reconnect_delay = 5000;
    client.current.debug = () => { };
    client.current.connect(
      res.username_push,
      res.password,
      onConnect,
      onError,
      amqpVHost,
    );
  };

  const listenRabbitMQ = () => {
    const idUser = getIdUser();
    const WsOver = webstomp.over;
    const ws = new WebSocket(IPWebsocket, 'v12.stomp');
    const clients = new WsOver(ws);
    client.current = clients;
    loginRabbitMQ({
      id_user: idUser,
    })
      .then((res) => {
        if (res.status) {
          subscribeRabbit(res);
        } else {
          throw res.msg;
        }
      })
      .catch((e) => {
        const onDisconnect = (error) => {
          console.log('disconnected', error);
        };
        clients.disconnect(onDisconnect);

        addToast({
          title: 'Gagal mendapatkan user RabbitMQ',
          description: e,
          position: 'top-right',
          variant: 'failed',
          dismissAfter: 5000,
        });
      });
  };

  const fetchDataRecalculateHeader = async () => {
    const branchId = userUtils.getLocalConfigByKey('branchId');
    const res = await settingApi.fetchNotifHeaderV2(branchId);
    if (res) {
      const { data, error } = res;
      if (!error) setterRecalculateNotif(data.status);
    }
  };

  const avatarName = businessName && businessName.trim() ? businessName : userName || '';

  useEffect(() => {
    if (messageHeaderResult && messageHeaderResult.status) {
      listenRabbitMQ();
    }
  }, [messageHeaderResult]);

  useEffect(() => {
    fetchDataRecalculateHeader();
    const outletExp = userUtils.getLocalConfigByKey('outletExp');
    if (outletExp && Object.keys('outletExp').length > 0) {
      const branchId = userUtils.getLocalConfigByKey('branchId');
      const expTime = outletExp[branchId];

      if (expTime) {
        setAccountExpiration(moment(expTime).format('D MMM YYYY'));
      }
    }
    return () => {
      if (client.current && client.current.connected) {
        client.current.disconnect();
      }
    };
  }, []);

  useEffect(() => {
    fetchLatestData(0);
  }, [unreadMsg]);

  useEffect(() => {
    fetchLatestData(1);
  }, [unreadNotif]);

  const renderNotificationDialog = () => (
    <NotificationDialog
      isMobile={isMobile}
      latestDataList={{
        latestMessageList,
        latestNotifList,
      }}
      refetchData={async (isNotif) => {
        await reloadMsgUnread();
        await fetchLatestData(Number(isNotif));
      }}
      unreadNotif={unreadNotif}
      unreadMsg={unreadMsg}
      {...props}
    >
      <ActionButton
        buttonType="secondary"
        css={{
          marginRight: '$spacing-06',
        }}
      >
        {unreadMsg > 0 || unreadNotif > 0 ? (
          <NotificationBadge
            variant="important"
            dot
            css={{
              top: '10%',
              right: '20%',
              zIndex: 0,
              backgroundColor: '$red500 !important',
              borderWidth: '1px !important',
              padding: '0px !important',
            }}
          >
            <NotificationIcon color={colors.iconGreen} />
          </NotificationBadge>
        ) : (
          <NotificationIcon color={colors.iconGreen} />
        )}
      </ActionButton>
    </NotificationDialog>
  );

  const renderAccountDialog = () => (
    <AccountDialog
      router={router}
      logo={userCompanyLogo}
      businessName={avatarName}
      userName={userName}
      accountType={toTitleCase(accountType)}
      accountExpiration={accountExpiration}
      progressAccount={progressAccount}
      progressBusiness={progressBusiness}
      progressProfile={progressProfile}
      forceRoutingBuySupport={forceRoutingBuySupport}
      isMobile={isMobile}
      filterBranch={filterBranch}
      branchId={branchId}
      branchData={branchData}
      day={day}
      paymentInstructionDialog={paymentInstructionDialogRef.current}
    >
      <ActionButton
        buttonType="secondary"
        css={
          isMobile && {
            width: 'fit-content',
            height: '32px',
            padding: 'inherit',
          }
        }
      >
        {!isMobile ? (
          <EllipsisVerticalOutline />
        ) : (
          <Avatar img={userCompanyLogo}>
            {getAcronym(avatarName).substring(0, 2)}
          </Avatar>
        )}
      </ActionButton>
    </AccountDialog>
  );

  const headerMenus = menuList
    .filter(
      menu => menu.menu_cms_mobile !== statusMenuInMobile.MOBILE_ONLY,
    );

  const isMediumScreen = useMediaQuery('(max-width: 1300px)');
  const isSmallMediumScreen = useMediaQuery('(max-width: 1200px)');
  const maxMainMenu = isSmallMediumScreen ? 4 : isMediumScreen ? 5 : 6;
  const mainMenus = headerMenus.slice(0, maxMainMenu);
  const otherMenus = headerMenus.slice(maxMainMenu, headerMenus.length);

  // enable this function when you need to restrict some menu, currently disable request by Madagascar SL
  // const handleRestrictSupportMenu = useCallback((menu) => {
  //   // this object can change by needs to restrict
  //   const restrictMenus = {
  //     restrictEmployeeMenu: false,
  //   };

  //   if (menu.name && menu.name.toLowerCase() === 'karyawan' && !WHITELISTS.EMPLOYEE_MENU.includes(accountType))
  //     restrictMenus.restrictEmployeeMenu = true;

  //   return restrictMenus;
  // }, []);

  const onMenuClick = async (menu) => {
    // const { restrictEmployeeMenu } = handleRestrictSupportMenu(menu);

    // if (restrictEmployeeMenu) return setRestrictMenu(POPUP_RESTRICT_TYPES.EMPLOYEE_MENU);

    const isBlocked = await checkSupportType(menu.id);

    if (isBlocked) {
      setShowUnauthorizedBlocker(true);
      return;
    }

    if (isSupportExpired()) {
      setShowBlocker(true);
    } else {
      // get current id outlet filtered or not filtered
      const getCurrentOutlet = filterBranch || (branchData.find(branch => branch.cabang_is_primary === '1')
        ? branchData.find(branch => branch.cabang_is_primary === '1').id_cabang : '');

      const payloadEmployeeAnalytic = {
        user_email: userEmail,
        user_role: userRole,
        merchant_id: userUtils.getLocalConfigByKey('parentId'),
        outlet_id: getCurrentOutlet,
      };

      handleParentMenuClick(menu.id);
      handleSetActiveGroupMenu(menu, {
        onUpdateActiveGroupMenu,
        onMenuChange,
        router,
        payload: { employee: payloadEmployeeAnalytic },
      });
    }
  };

  return (
    <React.Fragment>
      <HeaderContainer direction="column">
        <Container align="center" isMobile={isMobile}>
          {!isMobile ? (
            <React.Fragment>
              <LeftSideContainer>
                <MajooLogo
                  src={Logo}
                  onClick={() => browserHistory.push(`/${defaultPage}`)}
                />
              </LeftSideContainer>
              <Flex css={{ flex: 2 }} gap={2} justify="center" align="center">
                <ButtonGroup
                  value={activeMenuGroup}
                >
                  {mainMenus.map(menu => (
                    <ButtonGroupItem
                      isToggle={false}
                      key={menu.id}
                      value={menu.id}
                      css={{
                        width: 'max-content',
                        whiteSpace: 'nowrap',
                        minWidth: 'unset',
                        height: '36px',
                      }}
                      onClick={() => onMenuClick(menu)}
                    >
                      {(menu.name || '').toLowerCase() === 'toko online' && marketplaceInfo ? (
                        <React.Fragment>
                          {toTitleCase(menu.name)}
                          <BadgeOrderMarketplace marketplaceInfo={marketplaceInfo} />
                        </React.Fragment>
                      )
                        : toTitleCase(menu.name)}
                    </ButtonGroupItem>
                  ))}
                </ButtonGroup>
                {otherMenus.length > 0 && (
                  <OtherDropdownMenu
                    activeMenuId={activeMenuGroup}
                    otherMenus={otherMenus}
                    onMenuClick={onMenuClick}
                  />
                )}
              </Flex>
              <RightSideContainer>
                <Flex align="center">
                  {renderNotificationDialog()}
                  <AccountDisplayWrapper>
                    <AvatarProfile
                      logo={userCompanyLogo}
                      businessName={smallViewport ? addElipsisText(businessName, 20) : businessName}
                      userName={smallViewport ? addElipsisText(userName, 30) : userName}
                    />
                    {renderAccountDialog()}
                  </AccountDisplayWrapper>
                </Flex>
              </RightSideContainer>
            </React.Fragment>
          ) : (
            <React.Fragment>
              <LeftSideContainer gap={2}>
                <DrawerTrigger asChild>
                  <ActionButton buttonType="secondary">
                    <HeroIcon />
                  </ActionButton>
                </DrawerTrigger>
                <Image src={Logo} height="32px" />
              </LeftSideContainer>
              <RightSideContainer isMobile>
                {renderNotificationDialog()}
                <AccountDisplayWrapper>
                  {renderAccountDialog()}
                </AccountDisplayWrapper>
              </RightSideContainer>
            </React.Fragment>
          )}
        </Container>
        <Portal style={{
          top: isMobile ? 60 : 56, left: 0, width: '100%', position: 'fixed', zIndex: 1401,
        }}
        >
          <LoadingBar
            showProgressOnly
            show={isShowProgress}
            colors={[colors.primary200, colors.primary500]}
            size="sm"
          />
        </Portal>
      </HeaderContainer>
      {forceLogoutOpen ? (
        <SaveConfirm
          confirmMsg="Update layanan berlangganan outlet anda
                            berhasil, silahkan logout dan login kembali
                            untuk memperbarui sistem layanan
                            berlangganan anda."
          ref={forceLogout}
          headerTitle="Berhasil!"
          confirmText="Logout Sekarang"
          cancelText="Nanti Saja"
          confirmHandle={() => {
            browserHistory.push('/auth/logout');
          }}
        />
      ) : null}
      <RestrictPopup disabledCloseBtn={false} restrictMenu={restrictMenu} setRestrictMenu={setRestrictMenu} />
      <PaymentInstructionDialog ref={paymentInstructionDialogRef} />
    </React.Fragment>
  );
};

Header.propTypes = {
  activeMenuGroup: PropTypes.string.isRequired,
  onUpdateActiveGroupMenu: PropTypes.func.isRequired,
  userCompanyLogo: PropTypes.string,
  businessName: PropTypes.string,
  userName: PropTypes.string.isRequired,
  progressAccount: PropTypes.number,
  progressBusiness: PropTypes.number,
  progressProfile: PropTypes.number,
  dispatch: PropTypes.func.isRequired,
  menuList: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  branchData: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  isMobile: PropTypes.bool.isRequired,
  onMenuChange: PropTypes.func.isRequired,
  unreadMsg: PropTypes.number.isRequired,
  unreadNotif: PropTypes.number.isRequired,
  filterBranch: PropTypes.string.isRequired,
  forceRoutingBuySupport: PropTypes.func.isRequired,
  isShowProgress: PropTypes.bool,
  fetchProfile: PropTypes.func.isRequired,
  fetchAccountInfo: PropTypes.func.isRequired,
  fetchSupportHistorySocket: PropTypes.func.isRequired,
  updateCabangPush: PropTypes.func.isRequired,
  fetchOutletExp: PropTypes.func.isRequired,
  setRecalculateNotif: PropTypes.func.isRequired,
  router: PropTypes.shape({}).isRequired,
  handleParentMenuClick: PropTypes.func.isRequired,
  marketplaceInfo: PropTypes.shape({
    orderPendingCount: PropTypes.number,
    orderTotalCount: PropTypes.number,
  }),
  setShowBlocker: PropTypes.func.isRequired,
  isMultiLanguage: PropTypes.number,
  t: PropTypes.func.isRequired,
};

Header.defaultProps = {
  userCompanyLogo: undefined,
  businessName: '',
  progressAccount: undefined,
  progressBusiness: undefined,
  progressProfile: undefined,
  marketplaceInfo: undefined,
  isMultiLanguage: 0,
  isShowProgress: false,
};

const mapStateToProps = state => ({
  activeMenuGroup: state.layouts.activeMenuGroup,
  menuList: state.layouts.menu,
  userCompanyLogo: state.user.profile.user_usaha_logo_path,
  businessName: state.user.profile.user_usaha_name,
  userName: state.accountInfo.accountInfoResult.user_name,
  userEmail: state.accountInfo.accountInfoResult.user_email,
  userRole: state.accountInfo.accountInfoResult.hak_akses,
  branchData: state.branch.list,
  progressAccount: state.accountInfo.accountInfoResult.progress_info_account,
  progressBusiness: state.accountInfo.accountInfoResult.progress_info_business,
  progressProfile: state.accountInfo.accountInfoResult.progress_info_profile,
  unreadMsg: state.setting.unreadMsg,
  unreadNotif: state.setting.unreadNotif,
  isMultiLanguage: state.users.strap.isMultiLanguage,
});

export default connect(mapStateToProps)(Header);
