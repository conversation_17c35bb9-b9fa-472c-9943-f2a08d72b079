import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import { Box, Flex, Image } from '@majoo-ui/react';
import { connect } from 'react-redux';
import Slider from 'react-slick';
import { ChevronDownOutline, ChevronUpOutline } from '@majoo-ui/icons';
import { foundations } from '@majoo-ui/core';
import { parseToURL } from '../../../utils/helper';
import { analyticLogEvent } from '../../../v2-utils';
import majooIcon from '../../../../assets/images/icon-majoo-color.svg';
import { SLIDER_SETTINGS } from './utils';

const handleAnalyticLogEvent = ({
  userEmail, userStrap, singleLineText, bannerImage,
}) => {
  const payload = {
    page_referrer: window.location.pathname,
    user_email: userEmail,
    outlet_id: userStrap.branchId,
    banner_name: singleLineText,
    action_banner_image: bannerImage,
  };
  analyticLogEvent('banner_image', payload);
};

const PageBanner = ({
  menuObj, bannerImageKey, setMarginLeft, isFoldedMenu, isMobile, userEmail, userStrap,
}) => {
  const [isExpanded, setExpanded] = useState(true);

  const isOpenInNewTab = (link) => {
    const {
      location: { hostname },
    } = window;
    return link.includes(hostname) ? '_self' : '_blank';
  };

  const handleToggleExpand = () => {
    setExpanded(!isExpanded);
  };

  const { single_line: singleLineText } = menuObj;

  const iconContainer = {
    position: 'absolute',
    right: !isMobile ? 5 : 10,
    top: 5,
    transition: 'top 0.5s',
  };

  const iconWrapper = {
    background: '$bgDimmer',
    cursor: 'pointer',
    borderRadius: '$round',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    border: 'none',
    appearance: 'none',
  };

  return (
    <Box
      css={{
        display: 'grid',
        alignContent: 'center',
        mx: '$spacing-05',
        mb: '$spacing-05',
        background: '$bgGray',
        '@lg': { ml: setMarginLeft(), mr: 0, mt: '$spacing-07' },
      }}
    >
      {(menuObj.image_content || []).length > 0 ? (
        <Box
          css={{
            position: 'relative',
            margin: '0 auto',
            width: 'calc(100vw - 32px)',
            '@xl': { width: '100%', maxWidth: '1006px' },
            '@3xl': { width: '100%', maxWidth: '1206px', margin: '0 auto' },
          }}
        >
          <Slider {...SLIDER_SETTINGS}>
            {isExpanded ? (menuObj.image_content || []).map(banner => (
              <Box css={{ maxHeigth: '200px', '@lg': { maxHeigth: '130px' } }}>
                <a
                  onClick={() => {
                    handleAnalyticLogEvent({
                      userEmail, userStrap, singleLineText: menuObj.single_line, bannerImage: banner[bannerImageKey],
                    });
                  }}
                  href={banner.page_banner_link ? parseToURL(banner.page_banner_link) : '#'}
                  target={banner.page_banner_link && isOpenInNewTab(banner.page_banner_link)}
                  rel="noreferrer"
                >
                  <LazyLoadImage
                    alt="banner majoo"
                    src={banner[bannerImageKey]}
                    delayTime={5000}
                    style={{
                      borderRadius: '8px', margin: '0 auto', width: '100%', heigth: '100%',
                    }}
                  />
                </a>
              </Box>
            )) : (menuObj.image_content || []).map(banner => (
              <Box
                css={{
                  background: '$bgWhite',
                  padding: '$spacing-03',
                  borderRadius: '$lg',
                }}
              >
                <Flex align="center" css={{ gap: '$spacing-03' }}>
                  <Image src={majooIcon} css={{ mr: '$spacing-02' }} />
                  <a
                    onClick={() => {
                      handleAnalyticLogEvent({
                        userEmail, userStrap, singleLineText: menuObj.single_line, bannerImage: banner[bannerImageKey],
                      });
                    }}
                    href={banner.page_banner_link && parseToURL(banner.page_banner_link) || '#'}
                    target={banner.page_banner_link && isOpenInNewTab(banner.page_banner_link)}
                    rel="noreferrer"
                  >
                    {singleLineText || 'Promo dari Majoo'}
                  </a>
                </Flex>
              </Box>
            ))}
          </Slider>
          <Box css={iconContainer} onClick={() => handleToggleExpand()}>
            <Flex css={iconWrapper}>
              {isExpanded ? (
                <ChevronUpOutline color={foundations.colors.iconSecondary} />
              ) : (
                <ChevronDownOutline color={foundations.colors.iconSecondary} />
              )}
            </Flex>
          </Box>
        </Box>
      ) : null}
    </Box>
  );
};

PageBanner.propTypes = {
  menuObj: PropTypes.shape({}).isRequired,
  bannerImageKey: PropTypes.string.isRequired,
  setMarginLeft: PropTypes.func.isRequired,
  isFoldedMenu: PropTypes.bool.isRequired,
  isMobile: PropTypes.bool.isRequired,
};

const mapStateToProps = state => ({
  userEmail: state.accountInfo.accountInfoResult.user_email,
  userStrap: state.users.strap,
});

export default connect(mapStateToProps)(PageBanner);
