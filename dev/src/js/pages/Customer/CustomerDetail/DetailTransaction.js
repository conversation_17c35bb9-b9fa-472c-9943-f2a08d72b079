import React from 'react';
import PropTypes from 'prop-types';
import { PagePopup } from '~/pages/Report/TransactionV2/components/PagePopup';
import {
    PageDialog,
    PageDialogTitle,
    PageDialogContent,
    Box,
} from '@majoo-ui/react';
import { useTranslation } from 'react-i18next';

const DetailTransaction = ({
    isOpen,
    onOpenChange,
    dataDetail,
    ...restProps
}) => {
    const { t, i18n } = useTranslation(['Penjualan/Laporan/Karyawan/komisi', 'translation', 'Penjualan/Laporan/detail', 'Penjualan/Pelanggan/customerList']);

    return (
        <PageDialog layer="$modal" open={isOpen} defaultOpen={false} onOpenChange={onOpenChange}>
            <PageDialogTitle>{t('Penjualan/Pelanggan/customerList:transcationDetail.detail.pageTitle', 'Faktur Detail Penjualan')}</PageDialogTitle>
            <PageDialogContent
                css={{
                    '@sm': { padding: 0 },
                    '@md': { padding: '40px 40px 32px 40px', display: 'flex', justifyContent: 'center' },
                }}
            >
                <Box css={{ height: 'max-content', '@md': { paddingBottom: '$spacing-06'} }}>
                    {dataDetail && (
                        <PagePopup
                            data={dataDetail}
                            dataSelected={dataDetail.dataSelected}
                            printData={dataDetail.printData}
                            isCanRefund={dataDetail.isCanRefund}
                            isMarketplace={dataDetail.isMarketplace}
                            isAllRefunded={dataDetail.is_all_refunded === 1}
                            withBatchNumber
                            t={t}
                            currentLang={i18n.language}
                            showDetailPopup={isOpen}
                            onOpenChange={onOpenChange}
                            isDetailOnly
                            {...restProps}
                        />
                    )}
                </Box>
            </PageDialogContent>
        </PageDialog>
    );
};

DetailTransaction.propTypes = {
    isOpen: PropTypes.bool,
    onOpenChange: PropTypes.func,
    dataDetail: PropTypes.oneOfType([PropTypes.object]),
    dataOrder: PropTypes.oneOfType([PropTypes.array]),
    dataInfo: PropTypes.oneOfType([PropTypes.object]),
    dataPayment: PropTypes.oneOfType([PropTypes.array]),
    dataRefund: PropTypes.oneOfType([PropTypes.array]),
    pageTitle: PropTypes.string,
    headerTitle: PropTypes.string,
    showOrderTime: PropTypes.bool,
    showDetailNotes: PropTypes.bool,
    showPromo: PropTypes.bool,
    apiVersion: PropTypes.number,
    customHeaderContent: PropTypes.shape({
        title: PropTypes.string,
        caption: PropTypes.string,
    }),
    customOrderSectionContent: PropTypes.shape({
        leftSide: PropTypes.shape([]),
        rightSide: PropTypes.shape([]),
    }),
    customPaymentSectionContent: PropTypes.instanceOf(Array),
    isMobile: PropTypes.bool,
    withBatchNumber: PropTypes.bool,
};

DetailTransaction.defaultProps = {
    isOpen: false,
    onOpenChange: () => { },
    dataDetail: {},
    dataOrder: [],
    dataInfo: {},
    dataPayment: [],
    dataRefund: [],
    pageTitle: '',
    headerTitle: 'No Transaksi',
    showOrderTime: false,
    showDetailNotes: false,
    showPromo: true,
    apiVersion: undefined,
    customHeaderContent: {},
    customOrderSectionContent: {},
    customPaymentSectionContent: [],
    isMobile: false,
    withBatchNumber: false,
};

export default DetailTransaction;
