/* eslint-disable react/prop-types */
import React, { forwardRef, useState, useContext } from 'react';
import moment from 'moment';
import _ from 'lodash';
import XLSX from 'xlsx/dist/xlsx.core.min';
import {
  Box,
  RowSelectionColumn,
  RowMenuColumn,
  IconButton,
  TableRow,
  TableCell,
  Paragraph,
  InputSelect,
  FormLabel,
  InputText,
  InputSelectTag,
  InputTextArea,
  InputDatePicker,
  InputNumber,
  InputCounter,
  InputTimePicker,
  ModalDialog,
  ModalDialogTrigger,
  ModalDialogTitle,
  ModalDialogContent,
  FormHelper,
  FormGroup,
  CheckboxGroup,
  InputCheckbox,
  Flex,
  InputSignature,
  ToastContext,
} from '@majoo-ui/react';
import { EyeOutline } from '@majoo-ui/icons';
import useDidMountEffect from '~/utils/useDidMountEffect';
import { uploadImage } from '~/data/upload';
import { schemeTransactionNumber } from '../../../../utils/generator';
import { dataURItoBlob, formatCurrency, formatThousandSeparator, removeFirstLeadingZero, catchError } from '../../../../utils/helper';
import * as customerApi from '../../../../data/customers';
import { getPassport } from '../../../../utils/passport';
import { API_BASE } from '../../../../config/config';
import UploadComponent from '../components/uploadComponent';

// STYLING //
export const responsivePaper = {
    '@sm': {
        padding: 0,
        backgroundColor: 'transparent',
        boxShadow: 'none',
    },
    '@md': {
        padding: '$spacing-05',
        backgroundColor: '$white',
        boxShadow: '0px 2px 12px #00000014',
    },
};

export const responsiveFieldLabel = {
    alignItems: 'start !important',
    '& > span': {
        color: '$textPrimary',
        fontSize: '12px',
        fontWeight: 500,
        lineHeight: '16px',
        letterSpacings: '$section-sub-title',
        '@md': {
            fontWeight: 600,
            fontSize: '$section-sub-title',
            lineHeight: '$section-sub-title',
        },
    },
};

export const responsiveForm = {
    display: 'flex',
    flexDirection: 'column',
    margin: '10px 0 20px',
    justifyContent: 'space-between',
    alignItems: 'start',
    '@md': { flexDirection: 'row' },
};

export const responsiveLabel = {
    width: '100%',
    marginBottom: '10px',
    '@md': { width: '30%' },
};

export const responsiveField = {
    width: '100%',
    '@md': { width: '70%' },
};

export const responsiveCustomField = {
    display: 'flex',
    flexDirection: 'column',
    margin: '10px 0 20px',
    justifyContent: 'space-between',
    alignItems: 'end',
};

export const responsiveFormAdditional = {
    display: 'flex',
    flexDirection: 'column',
    position: 'relative',
    padding: '$spacing-06',
    gap: '$spacing-05',
};

export const responsiveBoxAdditional = {
    padding: '$spacing-06',
    border: '1px solid $bgBorder',
    borderRadius: '8px',
};

export const responsiveCustomLabel = { width: '100%', marginBottom: '10px' };
// STYLING //

// FUNCTION //
export const tokenUpload = getPassport();
export const uploadImageAddress = `${API_BASE}upload/do_upload`;

export const DATA_FIELD_LIST = {
    INPUT_SELECT_MULTIPLE: 1,
    INPUT_SELECT_SINGLE: 2,
    INPUT_CHECKBOX_MULTIPLE: 3,
    INPUT_TEXT: 4,
    INPUT_TEXT_AREA: 5,
    INPUT_DATE_PICKER: 6,
    INPUT_PHONE: 7,
    INPUT_NUMBER_COUNTER: 8,
    INPUT_FILE: 9,
    INPUT_NUMBER_AUTO_GENERATE_NUMBER_BASE: 10,
    INPUT_NUMBER_AUTO_GENERATE_TIME_BASE: 11,
    INPUT_SIGNATURE: 12,
    INPUT_TIME_PICKER: 13,
};

export const generateCustomerCode = businessName => {
    let codeBusinessName = '';

    const outletName = businessName.split(/\s/g);
    if (outletName.length > 1) {
        codeBusinessName = outletName[0].substr(0, 1) + outletName[1].substr(0, 1);
    } else if (outletName.length === 1) {
        const codeAwal = outletName[0].substr(0, 2);
        if (codeAwal.length < 2) {
            const oneStringRandom = String.fromCharCode(Math.floor(Math.random() * 26) + 97);
            codeBusinessName = codeAwal + oneStringRandom;
        } else {
            codeBusinessName = codeAwal;
        }
    }

    const codeDate = schemeTransactionNumber(moment().format('YYMMDDHHmmssSS'));

    const codeRandom = Math.random().toString(36).substr(3, 4);
    const customerCode = (codeBusinessName + codeDate + codeRandom).toUpperCase();
    return customerCode;
};

export const replaceNullWithDefaultValue = (val, defaultValue = '') => val || defaultValue;

export const createFormData = (data = null, { businessName }) => {
    let birthDate = moment().toDate();
    let cityId = data ? replaceNullWithDefaultValue(data.id_kota) : '';

    if (data && data.id_kota === '0') cityId = '-';
    if (data && data.customer_ttl && moment(data.customer_ttl).isValid()) {
        birthDate = moment(data.customer_ttl).toDate();
    }
    if (!data) birthDate = moment().subtract(20, 'years').format('DD/MM/YYYY');

    return {
        id: data ? data.id_customer : '',
        customerCode: data ? replaceNullWithDefaultValue(data.customer_code) : generateCustomerCode(businessName),
        customerName: data ? replaceNullWithDefaultValue(data.customer_name) : '',
        customerGroupId: data ? replaceNullWithDefaultValue(data.M_Category_Customer_id_category_customer, '') : '',
        gender: data ? data.customer_gender : 'L',
        email: data ? replaceNullWithDefaultValue(data.customer_email) : '',
        phone: data ? removeFirstLeadingZero(replaceNullWithDefaultValue(data.customer_notlp)) : '',
        birthDate,
        address: data ? replaceNullWithDefaultValue(data.customer_alamat) : '',
        cityId,
        cityText: data ? replaceNullWithDefaultValue(data.customer_kota) : '',
        customerCatatan: data ? replaceNullWithDefaultValue(data.customer_catatan) : '',
        customerImagePath: data ? replaceNullWithDefaultValue(data.customer_image_path) : '',
        customerPoint: data.poin.sisa ? data.poin.sisa : '0',
        customerNote: data ? replaceNullWithDefaultValue(data.customer_catatan) : '',
        dialingCode: data ? replaceNullWithDefaultValue(data.dialing_code, '+62') : '+62',
        dialingCodeId: data ? replaceNullWithDefaultValue(data.id_dialing_code, '99') : '99',
    };
};

export const createPayload = data => {
    const payload = {
        customer_code: data.customerCode,
        customer_name: data.customerName,
        customer_email: data.email,
        customer_notlp: data.phone,
        customer_gender: data.gender,
        customer_alamat: data.address,
        customer_kota: String(data.cityId) === '-' ? data.cityText : null,
        id_kota: String(data.cityId),
        customer_ttl: moment(data.birthDate, 'DD/MM/YYYY').format('YYYY-MM-DD'),
        id_category_customer: String(data.customerGroupId) === '' ? null : data.customerGroupId,
        is_cms: 1,
        customer_catatan: data.customerNote,
        customer_image_path: data.customerImagePath,
        id_dialing_code: data.dialingCodeId || '99',
        dialing_code: data.dialingCode || '+62',
    };

    return payload;
};

export const getInitForm = businessName => {
    // const initForm = {
    //   id: '',
    //   customerCode: generateCustomerCode(businessName),
    //   customerName: `Johnson ${moment().format('HH:mm:ss')}`,
    //   email: `gimafi2154+${moment().format('HHmmss')}@temptami.com`,
    //   phone: '085159015955',
    //   gender: 'L',
    //   address: 'Bantulan',
    //   cityText: 'Kab. Bantul',
    //   cityId: '222',
    //   birthDate: moment().subtract(20, 'years').toDate(),
    //   customerGroupId: '',
    //   customerNote: moment().format('HH:mm:ss'),
    //   customerImagePath: '',
    // };
    const initForm = {
        id: '',
        customerCode: generateCustomerCode(businessName),
        customerName: '',
        email: '',
        phone: '',
        gender: '',
        address: '',
        cityText: '',
        cityId: '',
        birthDate: moment().toDate(),
        customerGroupId: '',
        customerNote: '',
        customerImagePath: '',
    };
    return initForm;
};

export const createCustomDataPayload = dataMaster => {
    return Array.isArray(dataMaster)
        ? dataMaster.map(dt => ({
              id: dt.id,
              ...(dt._id && { _id: dt._id }),
              name: dt.name,
              sections: Array.isArray(dt.sections)
                  ? dt.sections.map(section => ({
                        id: section.id,
                        name: section.name,
                        columns: Array.isArray(section.columns) ? section.columns.map(column => column) : [],
                    }))
                  : [],
          }))
        : [];
};

export const createOwnershipDataPayload = data => {
    const payload = data[0].sections.map(dt => ({
        form_id: dt.id,
        form_field: dt.columns.map(x => ({
            id: x.id,
            answer: x.answer,
        })),
    }));

    return payload;
};

export const defaultParams = {
    pageIndex: 0,
    pageSize: 10,
    sortAccessor: undefined,
    sortDirection: 'ASC',
    filterSearch: undefined,
    ownershipId: undefined,
};

export const accessorMap = {
    code: 'code',
    name: 'name',
    alamat: 'alamat',
    notlp: 'notlp',
    gender: 'gender',
    poin: 'poin',
    saldo_deposit: 'saldo_deposit',
};

export const uploadCustomerData = async file => {
    if (file === null) throw new Error('Harap pilih file terlebih dahulu.');

    const rowList = [];
    const workbook = XLSX.read(file, { type: 'binary' });

    workbook.SheetNames.forEach(sheetName => {
        if (sheetName === 'Data') {
            const roa = XLSX.utils.sheet_to_row_object_array(workbook.Sheets[sheetName]);
            if (roa.length > 0) {
                roa.forEach(row => {
                    const obj = {
                        customer_code: row['Kode Pelanggan'],
                        customer_name: row['Nama Pelanggan'],
                        customer_group: row['Group Pelanggan'],
                        customer_gender: row['Jenis Kelamin'],
                        customer_email: row.Email,
                        customer_ttl: row['Tanggal Lahir'],
                        customer_image_path: row['Foto Pelanggan'],
                        dialing_code: row['Kode Negara'],
                        customer_phone: row['No Telepon'],
                        customer_address: row.Alamat,
                        customer_city: row.Kota,
                        customer_point: row.Poin,
                        customer_catatan: row.Catatan,
                    };
                    rowList.push(obj);
                });
            }
        }
    });

    const filtered = rowList.filter(x => x.customer_name !== undefined && x.customer_phone !== undefined);

    if (filtered.length === 0) {
        throw new Error('Data pelanggan gagal diimpor');
    }
    const payload = { data: JSON.stringify(filtered) };

    const response = await customerApi.uploadBulk(payload);
    return response;
};
// FUNCTION //

// TABLE //
export const tableColumn = (
    onChangeRowMenu,
    t,
    isPrimePlus = false,
    isHasMasterOwnership = false,
    additionalTableColumn = [],
    setPageDefaultColumn = () => {},
) => {
    const columns = [
        {
            ...RowSelectionColumn,
            sticky: 'left',
        },
        {
            Header: t('headerTable.name', 'Nama'),
            accessor: 'name',
            sticky: 'left',
            stickyPosition: 31,
        },
        {
            Header: t('headerTable.code', 'Kode Pelanggan'),
            accessor: 'code',
        },
        {
            Header: t('headerTable.address', 'Alamat'),
            accessor: 'alamat',
        },
        {
            Header: t('headerTable.phone', 'Telepon'),
            accessor: 'notlp',
            Cell: cell => {
                const {
                    row: {
                        original: { notlp_view: noTelp },
                    },
                } = cell;
                return <Box>{noTelp || '-'}</Box>;
            },
        },
        {
            Header: t('headerTable.gender', 'Jenis Kelamin'),
            accessor: 'gender',
            Cell: cell => {
                const { value } = cell;
                const dataGender = value !== null ? value : '';
                return (
                    <Box>
                        {dataGender.toLowerCase() === 'p'
                            ? t('form.label.female', 'Perempuan')
                            : t('form.label.male', 'Laki-Laki')}
                    </Box>
                );
            },
        },
        {
            Header: t('headerTable.email', 'Email'),
            accessor: 'email',
        },
        {
            Header: t('headerTable.birthDate', 'Tanggal Lahir'),
            accessor: 'ttl',
        },
        {
            Header: t('headerTable.point', 'Poin'),
            accessor: 'poin',
            unsortable: true,
            Cell: cell => {
                const { value } = cell;
                return <Box>{formatThousandSeparator(value || 0)}</Box>;
            },
        },
        {
            Header: t('headerTable.deposit', 'Saldo Deposit'),
            accessor: 'saldo_deposit',
            unsortable: true,
            Cell: cell => {
                const { value } = cell;
                return <Box>{formatCurrency(value, { maximumFractionDigits: 0 })}</Box>;
            },
        },
        {
            Header: t('headerTable.group', 'Grup Pelanggan'),
            accessor: 'category_name',
        },
        {
            Header: '',
            accessor: 'ownerships',
            Cell: ({ row: { original } }) => {
                const { ownerships } = original;
                return ownerships.map((item, index) => (
                    // eslint-disable-next-line react/no-array-index-key
                    <TableRow key={`${item.category_name}_${index}`} subRow>
                        <td style={{ backgroundColor: '$bgGray' }} />
                        <td style={{ backgroundColor: '$bgGray' }} />
                        <TableCell
                            subCell
                            css={{
                                paddingTop: '24px !important',
                                paddingBottom: '24px !important',
                            }}
                        >
                            <Paragraph paragraph="shortContentBold" style={{ fontWeight: '600' }}>
                                {item.category_name || '-'}
                            </Paragraph>
                        </TableCell>
                        <td style={{ backgroundColor: '$bgGray' }} />
                        <TableCell
                            subCell
                            css={{
                                paddingTop: '24px !important',
                                paddingBottom: '24px !important',
                            }}
                        >
                            <Paragraph paragraph="shortContentBold" style={{ fontWeight: '400' }}>
                                {item.total || '-'}
                            </Paragraph>
                        </TableCell>
                        <td style={{ backgroundColor: '$bgGray' }} />
                        <td style={{ backgroundColor: '$bgGray' }} />
                        <td style={{ backgroundColor: '$bgGray' }} />
                        <td style={{ backgroundColor: '$bgGray' }} />
                    </TableRow>
                ));
            },
            isSubRow: true,
            isCustomSubRow: true,
        },
        ...additionalTableColumn,
        RowMenuColumn(
            [
                {
                    title: t('label.edit', { ns: 'translation' }, 'Ubah'),
                    onClick: props => {
                        const { id } = props.row.original;
                        if (id) onChangeRowMenu(props.row.original, 'edit');
                    },
                },
                {
                    title: t('headerTable.transaction', 'Lihat Daftar Transaksi Pelanggan'),
                    onClick: props => {
                        const { id } = props.row.original;
                        if (id) onChangeRowMenu(props.row.original, 'detail');
                    },
                },
                {
                    title: t('headerTable.customerDetail', 'Lihat Detail Pelanggan'),
                    onClick: props => {
                        const { id } = props.row.original;
                        if (id) onChangeRowMenu(props.row.original, 'detailCustomer');
                    },
                },
                ...(isHasMasterOwnership
                    ? [
                          {
                              title: t('headerTable.owner', 'Tambah Kepemilikan'),
                              onClick: props => {
                                  const { id } = props.row.original;
                                  if (id) onChangeRowMenu(props.row.original, 'addOwnership');
                              },
                          },
                      ]
                    : []),
                {
                    title: t('filter.btnDelete', 'Hapus'),
                    onClick: props => {
                        const { id } = props.row.original;
                        if (id) onChangeRowMenu(props.row.original, 'delete');
                    },
                },
            ],
            undefined,
            { cssContent: { maxWidth: '250px' } },
        ),
    ];

    setPageDefaultColumn();
    return columns;
};

// TABLE //
export const defaultDataCustomerInformation = {
    code: '',
    name: '',
    birth_date: {
        original: '',
        display: '',
        age: 0,
    },
    gender: '',
    city: {
        id: '',
        name: '',
    },
    address: '',
    telephone: '',
    email: '',
    group: '',
};

export const defaultCustomerDetail = {
    id: '',
    image_path: '',
    point: 0,
    last_active: '',
};

export const defaultOwnershipForm = {
    categoryDataMaster: '',
    dataMasterName: '',
};

export const repeatedDataMeta = (onOpenHistory, t) => [
    {
        Header: t('additionalData.tableColumn.reservationNo'),
        accessor: 'reservasi_no',
        isToggled: false,
        unsortable: true,
    },
    {
        Header: t('additionalData.tableColumn.provider'),
        accessor: 'penyedia_jasa',
        isMobileHeader: true,
        unsortable: true,
        Cell: ({ value, row: { original } }) => <div>{`${value} - ${original.posisi_penyedia_jasa}`}</div>,
    },
    {
        Header: t('additionalData.tableColumn.createdDate'),
        accessor: 'created_at',
        unsortable: true,
    },
    {
        Header: t('additionalData.tableColumn.updatedDate'),
        accessor: 'updated_at',
        unsortable: true,
    },
    {
        id: 'eye',
        Header: () => null,
        Cell: ({ row }) => (
            <IconButton buttonType="secondary" onClick={() => onOpenHistory(true, row.original.id)}>
                <EyeOutline />
            </IconButton>
        ),
        isAction: true,
    },
];

export const optionPerspective = t => [
    {
        name: t('form.detail.formDisplay'),
        value: '1',
    },
    {
        name: t('form.detail.transactionDisplay'),
        value: '2',
    },
];

export const DELETE_TYPE = {
    CUSTOMER: 'customer',
    OWNERSHIP: 'ownership',
};
// FIELD //
export const PreviewImageDialog = forwardRef(({ previewTitle, previewImage, css }, ref) => (
    <ModalDialog layer="$modal">
        <ModalDialogTrigger asChild>
            <div ref={ref} />
        </ModalDialogTrigger>
        <ModalDialogTitle
            css={{
                textOverflow: 'ellipsis',
                overflow: 'hidden',
                width: 300,
                whiteSpace: 'nowrap',
            }}
        >
            {previewTitle}
        </ModalDialogTitle>
        <ModalDialogContent css={{ textAlign: '-webkit-center', padding: 12, ...css }}>
            <img src={previewImage} alt="Preview" style={{ borderRadius: 8, objectFit: 'fill' }} />
        </ModalDialogContent>
    </ModalDialog>
));

export const FormField = ({ dt, indexFormDataMaster, indexMasterDataForm, indexField, onChangeValue, t }) => {
    const { addToast } = useContext(ToastContext);
    const [answer, setAnswer] = useState(dt.answer);
    let selectOption = [];

    useDidMountEffect(() => {
        onChangeValue(indexFormDataMaster, indexMasterDataForm, indexField, answer);
    }, [answer]);

    switch (dt.type) {
        case DATA_FIELD_LIST.INPUT_SELECT_MULTIPLE: {
            const choices = dt.choices.map(item => ({
                id: item,
                name: item,
            }));
            let answerValue = (answer || '').replace(/\[|\]/g, '');
            try {
                answerValue = answer ? JSON.parse(answer) : [];
            } catch (e) {
                answerValue = answerValue.split(',');
            }
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.is_required ? 'required' : 'label'}
                        >
                            {dt.name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <InputSelectTag
                            placeholder={t('placeholder.select', { ns: 'translation' })}
                            option={choices}
                            onChange={val => {
                                if (val && val.length > 0) {
                                    setAnswer(JSON.stringify(val));
                                } else {
                                    setAnswer('');
                                }
                            }}
                            value={answerValue}
                            sortBy={null}
                        />
                    </Box>
                </Box>
            );
        }
        case DATA_FIELD_LIST.INPUT_SELECT_SINGLE: {
            selectOption = dt.choices.map(item => ({
                value: item,
                name: item,
            }));

            const valueSelected = { value: answer, name: answer };

            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.is_required ? 'required' : 'label'}
                        >
                            {dt.name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <InputSelect
                            placeholder={t('placeholder.select', { ns: 'translation' })}
                            option={selectOption}
                            embedSearch
                            value={answer ? valueSelected : null}
                            onChange={e => {
                                if (e) {
                                    setAnswer(e.value);
                                } else {
                                    setAnswer('');
                                }
                            }}
                        />
                    </Box>
                </Box>
            );
        }
        case DATA_FIELD_LIST.INPUT_CHECKBOX_MULTIPLE: {
            const choices = dt.choices || [];
            let answerValue = (answer || '').replace(/\[|\]/g, '');
            try {
                answerValue = answer ? JSON.parse(answer) : [];
            } catch (e) {
                answerValue = answerValue.split(',');
            }
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.is_required ? 'required' : 'label'}
                        >
                            {dt.name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <CheckboxGroup
                            defaultValue={answerValue}
                            onChange={value => {
                                if (value && value.length > 0) {
                                    setAnswer(JSON.stringify(value));
                                } else {
                                    setAnswer('');
                                }
                            }}
                        >
                            <Flex gap={11}>
                                <Flex direction="column" gap={2}>
                                    {choices.slice(0, Math.ceil(choices.length / 2)).map(choice => (
                                        <InputCheckbox key={choice} label={choice} value={choice} />
                                    ))}
                                </Flex>
                                <Flex direction="column" gap={2}>
                                    {choices.slice(Math.ceil(choices.length / 2)).map(choice => (
                                        <InputCheckbox key={choice} label={choice} value={choice} />
                                    ))}
                                </Flex>
                            </Flex>
                        </CheckboxGroup>
                    </Box>
                </Box>
            );
        }
        case DATA_FIELD_LIST.INPUT_TEXT:
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.is_required ? 'required' : 'label'}
                        >
                            {dt.name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <FormGroup>
                            <FormLabel count={answer ? answer.length : 0} maxCount={40} />
                            <InputText
                                value={answer}
                                maxLength="40"
                                placeholder={t('placeholder.inputHere', { ns: 'translation' })}
                                onChange={e => {
                                    if (e && e.target) {
                                        setAnswer(e.target.value);
                                    } else {
                                        setAnswer('');
                                    }
                                }}
                            />
                        </FormGroup>
                    </Box>
                </Box>
            );
        case DATA_FIELD_LIST.INPUT_TEXT_AREA:
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.is_required ? 'required' : 'label'}
                        >
                            {dt.name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <FormGroup>
                            <FormLabel count={answer ? answer.length : 0} maxCount={500} />
                            <InputTextArea
                                placeholder={`${t('placeholder.inputHere', { ns: 'translation' })}`}
                                size="lg"
                                maxLength="500"
                                onChange={e => {
                                    if (e && e.target) {
                                        setAnswer(e.target.value);
                                    } else {
                                        setAnswer('');
                                    }
                                }}
                                value={answer}
                                css={{ minHeight: '75px !important' }}
                            />
                        </FormGroup>
                    </Box>
                </Box>
            );
        case DATA_FIELD_LIST.INPUT_DATE_PICKER:
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.is_required ? 'required' : 'label'}
                        >
                            {dt.name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <InputDatePicker
                            placeholder={`${t('placeholder.selectDate', { ns: 'translation' })}`}
                            type="single"
                            size="lg"
                            value={answer ? moment(answer, 'DD-MM-YYYY').toDate() : null}
                            onChange={e => {
                                if (e) {
                                    setAnswer(moment(e).format('DD-MM-YYYY'));
                                } else {
                                    setAnswer('');
                                }
                            }}
                        />
                    </Box>
                </Box>
            );
        case DATA_FIELD_LIST.INPUT_PHONE:
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.is_required ? 'required' : 'label'}
                        >
                            {dt.name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <InputNumber
                            placeholder={`${t('placeholder.inputHere', { ns: 'translation' })}`}
                            value={answer}
                            thousandSeparator={false}
                            size="lg"
                            onChange={e => {
                                if (e && e.target) {
                                    setAnswer(e.target.value);
                                } else {
                                    setAnswer('');
                                }
                            }}
                            allowLeadingZeros
                        />
                    </Box>
                </Box>
            );
        case DATA_FIELD_LIST.INPUT_NUMBER_COUNTER:
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.is_required ? 'required' : 'label'}
                        >
                            {dt.name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <InputCounter
                            min={0}
                            value={Number(answer || 0)}
                            disabledDecrement={Number(answer || 0) === 0}
                            disableManualInput
                            onChange={value => {
                                setAnswer(String(value));
                            }}
                            css={{
                                width: 200,
                            }}
                        />
                    </Box>
                </Box>
            );
        case DATA_FIELD_LIST.INPUT_FILE:
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.is_required ? 'required' : 'label'}
                        >
                            {dt.name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <UploadComponent
                            value={answer}
                            setValue={val => {
                                setAnswer(val);
                            }}
                            {...{ uploadImageAddress, tokenUpload, PreviewImageDialog }}
                        />
                    </Box>
                </Box>
            );
        case DATA_FIELD_LIST.INPUT_NUMBER_AUTO_GENERATE_NUMBER_BASE:
        case DATA_FIELD_LIST.INPUT_NUMBER_AUTO_GENERATE_TIME_BASE:
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.is_required ? 'required' : 'label'}
                        >
                            {dt.name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <FormGroup>
                            <InputNumber
                                value={answer}
                                readOnly
                                isReadOnly
                                thousandSeparator={false}
                                size="lg"
                                disabled
                            />
                            <FormHelper>{t('form.label.autoFieldDesc')}</FormHelper>
                        </FormGroup>
                    </Box>
                </Box>
            );
        case DATA_FIELD_LIST.INPUT_SIGNATURE:
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.is_required ? 'required' : 'label'}
                        >
                            {dt.name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <InputSignature
                            defaultValue={dt.answer}
                            onChange={async val => {
                                if (val) {
                                    try {
                                        const file = dataURItoBlob(val);
                                        const formdata = new FormData();
                                        const mimeType = file.type.split('/');
                                        formdata.append('userfile', file, `image.${mimeType[1]}`);
                                        const res = await uploadImage(formdata);
                                        if (!res.item_image_path) throw Error(res.msg);
                                        setAnswer(res.item_image_path);
                                    } catch (error) {
                                        addToast({
                                            title: t('error.uploadFail', { ns: 'translation' }),
                                            description: catchError(error),
                                            variant: 'failed',
                                        });
                                    }
                                } else {
                                    setAnswer(val);
                                }
                            }}
                        />
                    </Box>
                </Box>
            );
        case DATA_FIELD_LIST.INPUT_TIME_PICKER: {
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.is_required ? 'required' : 'label'}
                        >
                            {dt.name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <InputTimePicker
                            placeholder={`${t('placeholder.selectTime', { ns: 'translation' })}`}
                            value={answer ? moment(answer, 'HH:mm') : null}
                            onChange={e => {
                                if (e) {
                                    setAnswer(moment(e).format('HH:mm'));
                                } else {
                                    setAnswer('');
                                }
                            }}
                            css={{
                                width: '100%',
                            }}
                            onInputOpenChange={open => {
                                if (open && !answer) {
                                    setAnswer(moment(new Date()).format('HH:mm'));
                                }
                            }}
                        />
                    </Box>
                </Box>
            );
        }
        default:
            return <React.Fragment />;
    }
};
// FIELD //
