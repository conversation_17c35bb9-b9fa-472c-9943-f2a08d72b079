/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import {
  ModalDialogTitle, ModalDialogContent, ModalDialogFooter, DialogClose,
  InputRadioGroup, InputRadio, ModalDialog, Button, StepWizard, Box,
  Image, Flex, Text,
} from '@majoo-ui/react';

export const PELANGGAN_CATEGORY_ID = '76';
export const SATU_SEHAT_CATEGORY_ID = '77';

const CategoryDataMasterPopUp = ({
  isOpen, onOpenChange, masterDataCategory, value,
  onChangeValue, t, defaultIcon, allMasterOwnershipList,
}) => {
  const [activeStep, setActiveStep] = useState(1);
  const [tempValue, setTempValue] = useState('');
  const completedSteps = new Array(masterDataCategory.length).fill(true);
  const activeFormCategories = allMasterOwnershipList.map(form => form.id_form_category);

  useEffect(() => {
    if (value !== '') {
      setTempValue(value);
    }
  }, [value]);

  const renderComponent = data => (
    <InputRadioGroup
      direction="column"
      value={tempValue}
      gap={5}
      onValueChange={val => setTempValue(val)}
      css={{ paddingLeft: '$spacing-03' }}
    >
      {data.filter(item => item.name !== 'Pelanggan' && item.name !== 'Satu Sehat').map(item => (
        <InputRadio
          id={item.value}
          value={item.value}
          data-icon={item.icon}
          data-name={item.name}
          disabled={activeFormCategories.length > 0 && !activeFormCategories.includes(String(item.value))}
          label={(
            <Flex gap={3}>
              <Image src={item.icon || defaultIcon} />
              <Text>{item.name}</Text>
            </Flex>
          )}
        />
      ))}
    </InputRadioGroup>
  );

  return (
    <React.Fragment>
      <ModalDialog
        open={isOpen} onOpenChange={() => onOpenChange(false)} modal
        css={{
          width: 'auto',
          '@md': {
            maxWidth: 690,
          },
        }}
      >
        <ModalDialogTitle>
          {t('modal.masterCategory', 'Kategori Data Master')}
        </ModalDialogTitle>
        <ModalDialogContent
          css={{
            '& .stepperInstance': {
              gap: '$spacing-05',
            },
          }}
        >
          <StepWizard
            completedSteps={completedSteps}
            initialStep={activeStep}
            navContents={masterDataCategory}
            onStepChange={e => setActiveStep(e)}
            scrollTop
            hideCheckedIcon
          >
            {masterDataCategory.map(item => (
              <Box key={item.title} css={{ height: '300px', overflowY: 'auto' }}>
                {renderComponent(item.child)}
              </Box>
            ))}
          </StepWizard>
        </ModalDialogContent>
        <ModalDialogFooter
          css={{
            display: 'flex',
            gap: '$compact',
          }}
        >
          <DialogClose asChild>
            <Button
              aria-label="Close"
              buttonType="ghost"
              size="sm"
            >
              {t('label.cancel', { ns: 'translation' })}
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button
              aria-label="Close"
              size="sm"
              variant="green"
              onClick={() => onChangeValue(tempValue)}
            >
              {t('label.choose', { ns: 'translation' })}
            </Button>
          </DialogClose>
        </ModalDialogFooter>
      </ModalDialog>
    </React.Fragment>
  );
};

CategoryDataMasterPopUp.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onOpenChange: PropTypes.func.isRequired,
  value: PropTypes.string.isRequired,
  onChangeValue: PropTypes.func.isRequired,
  masterDataCategory: PropTypes.shape({
    name: PropTypes.string,
    child: PropTypes.shape({
      value: PropTypes.string,
      name: PropTypes.string,
      icon: PropTypes.string,
    }),
  }).isRequired,
  defaultIcon: PropTypes.string,
  allMasterOwnershipList: PropTypes.arrayOf(PropTypes.shape({})),
};

CategoryDataMasterPopUp.defaultProps = {
  allMasterOwnershipList: [],
  defaultIcon: 'https://services.mangkujagat.com/mayang/uploads/expansion/jasa/default.png',
};

export default CategoryDataMasterPopUp;
