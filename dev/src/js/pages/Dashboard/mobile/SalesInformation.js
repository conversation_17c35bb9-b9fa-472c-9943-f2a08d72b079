import React, { useState, useEffect } from 'react';
import {
  Paragraph, Badge, Box, Heading, Text, Flex, Tooltip,
} from '@majoo-ui/react';
import { ChevronUpOutline, CircleInfoOutline } from '@majoo-ui/icons';
import { foundations } from '@majoo-ui/core';
import propTypes from 'prop-types';
import { styled } from '../../../stitches.config';
import { formatCurrency, formatThousandSeparator } from '../../../utils/helper';

const { colors } = foundations;

const PercentageBadge = styled(Badge, {
  '& > svg': {
    size: 24,
    fill: 'inherit',
    '& > path': {
      fill: 'inherit',
    },
  },
  variants: {
    transparent: {
      true: {
        backgroundColor: 'transparent',
      },
    },
    status: {
      zero: {
        color: '$textSecondary',
        backgroundColor: 'transparent',
      },
      up: {
        color: '$primary500',
        fill: '$primary500',
        backgroundColor: '$green50',
        '& > svg': {
          transform: 'rotate(0deg)',
        },
      },
      down: {
        color: '$red500',
        fill: '$red500',
        backgroundColor: '$red50',
        '& > svg': {
          transform: 'rotate(180deg)',
        },
      },
    },
  },

  compoundVariants: [
    {
      transparent: true,
      css: {
        backgroundColor: 'transparent',
      },
    },
  ],
});

// TODO: Remove when query exported from majoo-ui
function useMediaQuery(query) {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }
    const listener = () => {
      setMatches(media.matches);
    };
    media.addEventListener('change', listener);
    return () => media.removeEventListener('change', listener);
  }, [matches, query]);

  return matches;
}

export const SalesInformation = ({
  size = 'lg',
  transparentBadge = false,
  label,
  percentage,
  total,
  isCurrency,
  noCompact = false,
  bordered,
  isNewSize,
  isDashboard,
  labelInfoTooltip,
  showDecimal = false,
}) => {

  const currencyOptions = {
    maximumFractionDigits: 0,
  };

  const compactCurrencyOptions = {
    minimumFractionDigits: 2,
    notation: 'compact',
    compactDisplay: 'short',
  };

  const formattedCurrency = (value) => {
    if (isCurrency) {
      if (!noCompact) {
        return formatCurrency(value, { ...compactCurrencyOptions, minimumFractionDigits: value === 0 ? 0 : 2 });
      }
      if (showDecimal) {
        return formatCurrency(value, { maximumFractionDigits: 2 });
      }
      return formatCurrency(value, currencyOptions);
    }
    return formatThousandSeparator(value);
  };

  const status = (value) => {
    const up = value >= 0;
    const zero = value === 0;
    if (zero) {
      return 'zero';
    }
    if (up) {
      return 'up';
    }
    return 'down';
  };

  return (
    <Box
      css={{
        display: 'flex',
        padding: '$spacing-03',
        flexDirection: 'column-reverse',
        gap: '$spacing-03',
        borderRadius: '$lg',
        border: bordered ? '1px solid #EEF0F0' : 'none',
      }}
    >
      <Flex gap={3} justify={bordered ? 'start' : 'between'} align="center">
        {size === 'lg' ? (
          <Heading as="h2" heading="h2">
            {formattedCurrency(total)}
          </Heading>
        ) : (
          <Text
            css={{
              fontWeight: 600,
              lineHeight: isNewSize ? '28px' : '20px',
              fontSize: (isNewSize && !isDashboard) ? 18 : 14,
              '@md': { fontWeight: 600, lineHeight: '28px', fontSize: 18 },
            }}
            color="primary"
          >
            {formattedCurrency(total)}
          </Text>
        )}
        {(!!percentage && percentage !== 0)
          && (
            <PercentageBadge
              as="span"
              transparent={transparentBadge}
              status={status(percentage)}
              css={{
                padding: size === 'sm' && 0,
                margin: size === 'sm' && 0,
                ...size === 'lg' && {
                  maxHeight: '$spacing-06',
                  padding: `$spacing-01 6px $spacing-01 ${transparentBadge ? '0' : '$spacing-03'}`,
                },
                justifyContent: 'left',
                width: 'fit-content',
              }}
            >
              {transparentBadge && (
                <ChevronUpOutline />
              )}
              <Text variant="label" css={{ '@md': { marginTop: 4 } }}>{`${Math.abs(percentage)}%`}</Text>
              {!transparentBadge && (
                <ChevronUpOutline />
              )}
            </PercentageBadge>
          )
        }
      </Flex>
      {
        size === 'lg' ? (
          <Flex gap={2} align="center">
            <Paragraph color="secondary">
              {label}
            </Paragraph>
            {labelInfoTooltip && (
              <Tooltip side="top" label={labelInfoTooltip}>
                <CircleInfoOutline size={18} color={colors.gray500} />
              </Tooltip>
            )}
          </Flex>
        ) : (
          <Flex gap={2} align="center">
            <Text
              variant="caption"
              color="secondary"
            >
              {label}
            </Text>
            {labelInfoTooltip && (
              <Tooltip side="top" label={labelInfoTooltip} withClick>
                <CircleInfoOutline size={18} color={colors.gray500} />
              </Tooltip>
            )}
          </Flex>
        )
      }
    </Box >
  );
};

SalesInformation.propTypes = {
  /** Size of Sales Information */
  size: propTypes.oneOf(['lg', 'sm']).isRequired,
  /** Badge background color */
  transparentBadge: propTypes.bool,
  /** Format number to IDR currency */
  isCurrency: propTypes.bool,
  total: propTypes.number.isRequired,
  percentage: propTypes.number.isRequired,
  label: propTypes.string.isRequired,
  noCompact: propTypes.bool,
  bordered: propTypes.bool,
  isNewSize: propTypes.bool,
  isDashboard: propTypes.bool,
  labelInfoTooltip: propTypes.bool,
  showDecimal: propTypes.bool,
};

SalesInformation.defaultProps = {
  transparentBadge: undefined,
  noCompact: false,
  isCurrency: undefined,
  bordered: true,
  isNewSize: false,
  isDashboard: false,
  labelInfoTooltip: false,
  showDecimal: false,
};
