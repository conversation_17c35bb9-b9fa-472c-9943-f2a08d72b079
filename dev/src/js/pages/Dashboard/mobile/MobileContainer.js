import React, { useContext } from 'react';
import {
    Box,
    Paper,
    Separator,
    Skeleton,
    Tabs, TabsList, TabsTrigger, TabsContent,
    Accordion,
    AccordionItem,
    AccordionTrigger,
    AccordionContent,
    Heading,
    Flex,
} from '@majoo-ui/react';
import {
    ChevronDownOutline,
} from '@majoo-ui/icons';
import TabTitle from '../../Report/Sales/Reservation/components/TabContent/TabTitle';
import {
    SUMMARY_SELECTOR, lineChartXAxisKey, periodOptions, responsivePaper,
} from '../utils/utils';
import { LineChart } from '../../../components/chart';
import { TitleSection } from '../ContainerPenjualan';
import { SalesIncreasement } from '../../../components/retina';
import SalesSummarySection from './SalesSummarySection';
import EcommerceList from './EcommerceList';
import { styled } from '../../../stitches.config';
import BestSellingProduct from './BestSellingProduct';
import { StockMinimum } from './SalesStockMinimum';
import PaymentMethod from './PaymentMethod';
import DashboardContext from '../context/DashboardContext';

const CONTENT_TABS = {
    DAY: 'jam',
    MONTH: 'hari',
};

const styles = {
    plain: {
        boxShadow: 'none',
    },
    tabHeader: {
        boxShadow: 'none',
        borderBottom: '1px solid $gray150',
        width: '170px',
        margin: 'auto',
    },
};

const ArrowContainer = styled(Flex, {
    borderRadius: '50%',
    border: '1px solid $bgBorder',
    padding: '1px',
});


function MobileContainer() {
    const {
        t,
        isMobile,
        isTablet,
        chart,
        language,
        currentDate,
        period,
        increasementNominal,
        goToPromoReportPage,
        lineChartLegend,
        sales,
        ecommerce,
        bestSell,
        pieChartData,
        payment,
        setPeriod,
        setLineChartLegend,
    } = useContext(DashboardContext);
    return (
        <Box css={{ marginBottom: '20px' }}>
            {chart.isLoading ? (<Skeleton />) : (
                <Paper css={responsivePaper}>
                    <TitleSection t={t} lang={language} date={currentDate} period={period.value} percentage="" isMobile />
                    <Separator
                        css={{
                            '@sm': { my: 15 },
                            '@md': { display: 'block', my: 5 },
                        }}
                    />
                    <Tabs
                        defaultValue={period.value || CONTENT_TABS.DAY}
                        onValueChange={(val) => {
                            setPeriod(periodOptions(t).find(option => option.value === val));
                            const periodValue = val === CONTENT_TABS.DAY ? t('translation:period.hour') : t('translation:period.day');
                            setLineChartLegend(d => d.map(l => (l.dataKey === 'count' ? { ...l, name: t('options.totalCustomPeriod', { period: periodValue }), yAxisId: 'right' } : { ...l, name: t('options.omzetCustomPeriod', { period: periodValue }), yAxisId: 'left' })));
                        }}
                        css={styles.plain}
                    >
                        <TabsList css={styles.tabHeader}>
                            <TabsTrigger
                                value={CONTENT_TABS.DAY}
                                size="sm"
                                css={styles.plain}
                            >
                                <TabTitle
                                    title={t('translation:period.day', 'Hari')}
                                    isActive={period.value === CONTENT_TABS.DAY}
                                />
                            </TabsTrigger>
                            <TabsTrigger
                                value={CONTENT_TABS.MONTH}
                                size="sm"
                                css={styles.plain}
                            >
                                <TabTitle
                                    title={t('translation:period.month', 'Bulan')}
                                    isActive={period.value === CONTENT_TABS.MONTH}
                                />
                            </TabsTrigger>
                        </TabsList>
                        <TabsContent value={CONTENT_TABS.DAY} css={styles.plain}>
                            <SalesSummarySection salesData={sales.data} salesSelector={SUMMARY_SELECTOR} t={t} />
                            <Box
                                css={{
                                    '@sm': { display: 'block' },
                                    '@md': { display: 'flex', justifyContent: 'space-between' },
                                }}
                            >
                                <SalesIncreasement
                                    value={increasementNominal}
                                    promoPageHandler={goToPromoReportPage}
                                    isTablet={isTablet}
                                    t={t}
                                />
                            </Box>
                            <Box css={{ mt: '$cozy', '@md': { margin: '$spacing-05 $spacing-03' } }}>
                                <LineChart
                                    data={chart.data}
                                    legendData={lineChartLegend}
                                    xAxisKey={lineChartXAxisKey}
                                    period={period.value}
                                    customXAxisFormat={tick => new Intl.DateTimeFormat(
                                        'id-ID',
                                        period.value === 'hari'
                                            ? { day: '2-digit' }
                                            : { hour: '2-digit', minute: '2-digit' },
                                    ).format(new Date(tick))
                                    }
                                    isEmptyData={chart.data.length <= 0}
                                />
                            </Box>
                        </TabsContent>
                        <TabsContent value={CONTENT_TABS.MONTH} css={styles.plain}>
                            <SalesSummarySection salesData={sales.data} salesSelector={SUMMARY_SELECTOR} isMobile={isMobile} t={t} />
                            <Box
                                css={{
                                    '@sm': { display: 'block' },
                                    '@md': { display: 'flex', justifyContent: 'space-between' },
                                }}
                            >
                                <SalesIncreasement
                                    value={increasementNominal}
                                    promoPageHandler={goToPromoReportPage}
                                    isTablet={isTablet}
                                    t={t}
                                />
                            </Box>
                            <Box css={{ mt: '$cozy', '@md': { margin: '$spacing-05 $spacing-03' } }}>
                                <LineChart
                                    data={chart.data}
                                    legendData={lineChartLegend}
                                    xAxisKey={lineChartXAxisKey}
                                    period={period.value}
                                    customXAxisFormat={tick => new Intl.DateTimeFormat(
                                        'id-ID',
                                        period.value === 'hari'
                                            ? { day: '2-digit' }
                                            : { hour: '2-digit', minute: '2-digit' },
                                    ).format(new Date(tick))
                                    }
                                    isEmptyData={chart.data.length <= 0}
                                />
                            </Box>
                        </TabsContent>
                    </Tabs>
                </Paper>
            )}
            <Separator css={{ width: '100%', height: '8px !important', margin: '$spacing-06 0' }} />
            <Accordion
                type="single"
            >
                <AccordionItem
                    key="ecommerce"
                    value="ecommerce"
                    css={{
                        padding: '$spacing-03 0',
                    }}
                >
                    <AccordionTrigger
                        arrowColor="black"
                        customArrow={(
                            <ArrowContainer align="center" justify="center">
                                <ChevronDownOutline className="icon-cdf" />
                            </ArrowContainer>
                        )}
                    >
                        <Heading color="primary" heading="sectionTitle">
                            E-Commerce
                        </Heading>
                    </AccordionTrigger>
                    <AccordionContent>
                        <EcommerceList
                            ecommerceData={ecommerce.data}
                            period={period}
                            isError={ecommerce.isError}
                        />
                    </AccordionContent>
                    <Separator css={{ width: '100%', margin: '$spacing-05 0 0' }} />
                </AccordionItem>

                <AccordionItem
                    key="bestsell"
                    value="bestsell"
                    css={{
                        padding: '$spacing-03 0',
                    }}
                >
                    <AccordionTrigger
                        arrowColor="black"
                        customArrow={(
                            <ArrowContainer align="center" justify="center">
                                <ChevronDownOutline className="icon-cdf" />
                            </ArrowContainer>
                        )}
                    >
                        <Heading color="primary" heading="sectionTitle">
                            {t('label.bestSeller', 'Produk Terlaris')}
                        </Heading>
                    </AccordionTrigger>
                    <AccordionContent>
                        <BestSellingProduct
                            t={t}
                            bestSellingData={bestSell.data}
                            period={period}
                            isError={bestSell.isError}
                        />
                    </AccordionContent>
                    <Separator css={{ width: '100%', margin: '$spacing-05 0 0' }} />
                </AccordionItem>
                <AccordionItem
                    key="stockmin"
                    value="stockmin"
                    css={{
                        padding: '$spacing-03 0',
                    }}
                >
                    <AccordionTrigger
                        arrowColor="black"
                        customArrow={(
                            <ArrowContainer align="center" justify="center">
                                <ChevronDownOutline className="icon-cdf" />
                            </ArrowContainer>
                        )}
                    >
                        <Heading color="primary" heading="sectionTitle">
                            {t('label.stockMin', 'Stok Terendah')}
                        </Heading>
                    </AccordionTrigger>
                    <AccordionContent>
                        <StockMinimum t={t} data={pieChartData.data} isError={pieChartData.isError} />
                    </AccordionContent>
                    <Separator css={{ width: '100%', margin: '$spacing-05 0 0' }} />
                </AccordionItem>
                <AccordionItem
                    key="payment"
                    value="payment"
                    css={{
                        padding: '$spacing-03 0',
                    }}
                >
                    <AccordionTrigger
                        arrowColor="black"
                        customArrow={(
                            <ArrowContainer align="center" justify="center">
                                <ChevronDownOutline className="icon-cdf" />
                            </ArrowContainer>
                        )}
                    >
                        <Heading color="primary" heading="sectionTitle">
                            {t('label.paymentMethod', 'Metode Pembayaran')}
                        </Heading>
                    </AccordionTrigger>
                    <AccordionContent>
                        <PaymentMethod t={t} lang={language} paymentMethodData={payment.data} />
                    </AccordionContent>
                    <Separator css={{ width: '100%', margin: '$spacing-05 0 0' }} />
                </AccordionItem>
            </Accordion>
        </Box>
    );
}

export default MobileContainer;
