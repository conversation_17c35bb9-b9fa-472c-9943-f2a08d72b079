import React from 'react';
import {
  Box,
  Flex,
  Grid,
  Separator,
  Heading,
  Text,
  InputSelect,
} from '@majoo-ui/react';
import { CalendarOutline } from '@majoo-ui/icons';
import PropTypes from 'prop-types';
import moment from 'moment';
import { FavoriteWrapper, SalesInformation, TooltipGuidance } from '../../components/retina';

const DEFAULT_SUMMARY_SELECTOR = {
  TOTAL_SALES: 'sales',
  TOTAL_SALES_MARKETPLACE: 'marketplace_sales',
  GROSS_SALES: 'gross_sales',
  GROSS_PROFIT: 'gross_profit',
  PAYMENTS: 'payments',
  TRX: 'transactions',
  PRODUCTS: 'products',
};

/**
* @component
*
* @param {Date} date Date for current month text
* @param {String} percentage percentage comparison
* @period {('jam'|'hari')} period
* */
export const TitleSection = ({
  date, percentage = '', period = 'hari', isMobile, ...props
}) => {
  const { t, lang } = props;
  const currentDate = new Date(date);

  return (
    <Box
      css={{
        marginBottom: '$spacing-03',
        '@sm': { padding: 0, height: 'auto' },
        '@md': { height: 60, padding: '0px $spacing-03' },
      }}
    >
      <FavoriteWrapper css={{ marginBottom: '$spacing-03' }}>
        <Heading
          as="h1"
          heading="pageTitle"
        >
          {t('title', 'Penjualan')}
        </Heading>
        <TooltipGuidance />
      </FavoriteWrapper>
      <Flex
        css={{
          alignItems: 'center',
          '@sm': { display: 'none' },
          '@md': { display: 'flex' },
        }}
      >
        <CalendarOutline />
        <Box
          css={{ marginLeft: '$spacing-04', '& > *': { display: 'inline' } }}
        >
          <Text
            variant="label"
            color="secondary"
          >
            {moment(currentDate).locale(lang).format(`${period === 'hari' ? '' : 'DD'} MMMM YYYY`)}
          </Text>
          {!isMobile && (
            <React.Fragment>
              <Text variant="caption">&nbsp;</Text>
              <Text variant="caption" color="secondary">
                {t('label.percentage', { percentage, period: period === 'hari' ? t('translation:period.month') : t('translation:period.day') }).toLowerCase()}
              </Text>
            </React.Fragment>
          )}
        </Box>
      </Flex>
    </Box>
  );
};

TitleSection.propTypes = {
  date: PropTypes.instanceOf(Date).isRequired,
  percentage: PropTypes.string.isRequired,
  period: PropTypes.oneOf(['hari', 'jam']).isRequired,
  t: PropTypes.func,
  lang: PropTypes.string,
  isMobile: PropTypes.bool,
};

TitleSection.defaultProps = {
  t: string => string,
  lang: 'id',
  isMobile: false,
};

export const FilterSection = ({ period, periodOptions, setPeriod }) => (
  <Flex
    css={{
      '@sm': {
        // Jika ada buton group
        // flexDirection: 'column',
        // gap: '$compact',
        // alignItems: 'start',
        height: 'auto',
      },
      '@md': {
        height: 64,
        flexDirection: 'row',
        gap: 0,
        justifyContent: 'space-between',
        alignItems: 'center',
      },
    }}
  >
    <Box css={{ '@sm': { width: 169 }, '@md': { width: 126 } }}>
      <InputSelect size="sm" onChange={c => setPeriod(c)} value={period} option={periodOptions} />
    </Box>
    {/* Button Group Sementara di Hide dulu menunggu fase selanjutnya */}
    {/* TODO: implementasi */}
    {/* <ButtonGroup */}
    {/*   css={{ border: '1px solid $gray150' }} */}
    {/*   defaultValue="Semua" */}
    {/*   size={{ '@sm': 'md', '@md': 'lg' }} */}
    {/* > */}
    {/*   <ButtonGroupItem value="Semua">Semua</ButtonGroupItem> */}
    {/*   <ButtonGroupItem value="Online">Online</ButtonGroupItem> */}
    {/*   <ButtonGroupItem value="Offline">Offline</ButtonGroupItem> */}
    {/* </ButtonGroup> */}
  </Flex>
);

FilterSection.propTypes = {
  period: PropTypes.shape({}).isRequired,
  periodOptions: PropTypes.arrayOf(PropTypes.object).isRequired,
  setPeriod: PropTypes.func.isRequired,
};

const INITIAL_SUMMARY_DATA = {
  sales: {},
  marketplace_sales: {},
  ecommerce_sales: {},
  gross_sales: {},
  gross_profit: {},
  receipt: {},
  payments: {},
  transactions: {},
  products: {},
};

export const SalesSummarySection = ({
  salesData, salesSelector, t, isMobile,
}) => {
  let data = INITIAL_SUMMARY_DATA;

  const summarySelector = salesSelector || DEFAULT_SUMMARY_SELECTOR;

  if (salesData.length > 0 && salesData) {
    salesData.forEach((item) => {
      const {
        // eslint-disable-next-line camelcase
        curr_total, growth_percent, is_currency, label,
      } = item;
      data = {
        ...data,
        [item.key_label]: {
          persen: growth_percent, total: curr_total, label, is_currency,
        },
      };
    });
  } else {
    data = Object.entries(data).reduce((obj, [key, _]) => {
      // eslint-disable-next-line no-param-reassign
      obj[key] = {
        persen: 0, total: 0, label: 0, is_currency: 0,
      };
      return obj;
    }, {});
  }

  return (
    <Flex
      css={{
        justifyContent: 'space-between',
        '@sm': {
          flexDirection: 'column',
          padding: '$compact $spacing-03 0px $spacing-03',
          overflowX: 'scroll',
          scrollbarWidth: 0,
          '&::-webkit-scrollbar': { width: 0, height: 0 },
        },
        '@md': { flexDirection: 'row', padding: '0px $spacing-03' },
      }}
    >
      <Flex css={{ alignItems: 'center', '@md': { flexBasis: '30%' } }}>
        <SalesInformation
          size="lg"
          percentage={data[summarySelector.TOTAL_SALES].persen || 0}
          total={data[summarySelector.TOTAL_SALES].total || 0}
          label={t('label.summary.salesSummary')}
          isCurrency
          noCompact
          isReverse
          showDecimal={(data[summarySelector.TOTAL_SALES].total || 0) % 1 !== 0}
        />
      </Flex>
      <Box css={{ flexGrow: 1, '@sm': { display: 'block' }, '@md': { display: 'flex', justifyContent: 'start' } }}>
        <Flex css={{ alignItems: 'center' }}>
          <Separator
            orientation="vertical"
            css={{
              height: '75%', margin: '0px $spacing-05', '@sm': { display: 'none' }, '@md': { display: 'block' },
            }}
          />
        </Flex>
        <Box>
          {isMobile && (
            <Box css={{ margin: '$spacing-05 0' }}>
              <SalesInformation
                size="sm"
                percentage={data[summarySelector.TOTAL_SALES_MARKETPLACE].persen || 0}
                total={data[summarySelector.TOTAL_SALES_MARKETPLACE].total || 0}
                label={t('label.summary.ecommerce')}
                noCompact
                isCurrency
                transparentBadge
                isReverse
                isNewSize
                isDashboard
                showDecimal={(data[summarySelector.TOTAL_SALES_MARKETPLACE].total || 0) % 1 !== 0}
              />
              <Separator css={{ margin: '$spacing-05 0' }} />
            </Box>
          )}
          <Grid
            css={{
              margin: '$spacing-05 0',
              rowGap: '$spacing-05',
              columnGap: '$spacing-06',
              alignItems: 'start',
              '@sm': { 'grid-template-columns': 'repeat(2, 1fr)' },
              '@md': { 'grid-template-columns': 'repeat(3, 1fr)' },
            }}
          >
            {!isMobile && (
              <SalesInformation
                size="sm"
                percentage={data[summarySelector.TOTAL_SALES_MARKETPLACE].persen || 0}
                total={data[summarySelector.TOTAL_SALES_MARKETPLACE].total || 0}
                label={t('label.summary.ecommerce')}
                isCurrency
                transparentBadge
                isReverse
                isNewSize
                isDashboard
                showDecimal={(data[summarySelector.TOTAL_SALES_MARKETPLACE].total || 0) % 1 !== 0}
              />
            )}
            <Box css={{ '@md': { borderLeft: '1px solid $bgBorder', padding: '0 10px' } }}>
              <SalesInformation
                size="sm"
                percentage={data[summarySelector.GROSS_PROFIT].persen || 0}
                total={data[summarySelector.GROSS_PROFIT].total || 0}
                label={t('label.summary.grossProfit')}
                isCurrency
                transparentBadge
                isReverse
                isNewSize
                isDashboard
                showDecimal={(data[summarySelector.GROSS_PROFIT].total || 0) % 1 !== 0}
              />
            </Box>
            {!isMobile ? (
              <Box css={{ '@md': { borderLeft: '1px solid $bgBorder', padding: '0 10px' } }}>
                <SalesInformation
                  size="sm"
                  percentage={data[summarySelector.GROSS_SALES].persen || 0}
                  total={data[summarySelector.GROSS_SALES].total || 0}
                  label={t('label.summary.grossSales')}
                  isCurrency
                  transparentBadge
                  isReverse
                  isNewSize
                  isDashboard
                  showDecimal={(data[summarySelector.GROSS_SALES].total || 0) % 1 !== 0}
                />
              </Box>
            ) : (
              <SalesInformation
                size="sm"
                percentage={data[summarySelector.PAYMENTS].persen || 0}
                total={data[summarySelector.PAYMENTS].total || 0}
                label={t('label.summary.income')}
                isCurrency
                transparentBadge
                isReverse
                isNewSize
                isDashboard
                showDecimal={(data[summarySelector.PAYMENTS].total || 0) % 1 !== 0}
              />
            )}
          </Grid>
          <Separator />
          <Grid
            css={{
              margin: '$spacing-05 0',
              rowGap: '$spacing-05',
              columnGap: '$spacing-06',
              alignItems: 'start',
              '@sm': { 'grid-template-columns': 'repeat(2, 1fr)' },
              '@md': { 'grid-template-columns': 'repeat(3, 1fr)' },
            }}
          >
            {!isMobile && (
              <SalesInformation
                size="sm"
                percentage={data[summarySelector.PAYMENTS].persen || 0}
                total={data[summarySelector.PAYMENTS].total || 0}
                label={t('label.summary.income')}
                isCurrency
                transparentBadge
                isReverse
                isNewSize
                isDashboard
                showDecimal={(data[summarySelector.PAYMENTS].total || 0) % 1 !== 0}
              />
            )}
            <Box css={{ '@md': { borderLeft: '1px solid $bgBorder', padding: '0 10px' } }}>
              <SalesInformation
                size="sm"
                percentage={data[summarySelector.PRODUCTS].persen || 0}
                total={data[summarySelector.PRODUCTS].total || 0}
                label={t('label.summary.product')}
                transparentBadge
                isReverse
                isNewSize
                isDashboard
              />
            </Box>
            <Box css={{ '@md': { borderLeft: '1px solid $bgBorder', padding: '0 10px' } }}>
              <SalesInformation
                size="sm"
                percentage={data[summarySelector.TRX].persen || 0}
                total={data[summarySelector.TRX].total || 0}
                label={t('label.summary.transaction')}
                transparentBadge
                isReverse
                isNewSize
                isDashboard
              />
            </Box>
          </Grid>
          <Separator
            css={{
              '@sm': { display: 'block' },
              '@md': { display: 'none' },
            }}
          />
        </Box>
      </Box>
    </Flex>
  );
};

SalesSummarySection.propTypes = {
  salesData: PropTypes.arrayOf(PropTypes.object).isRequired,
  salesSelector: PropTypes.object,
  isMobile: PropTypes.bool,
  t: PropTypes.func,
};

SalesSummarySection.defaultProps = {
  salesSelector: undefined,
  ecommerceData: [],
  isMobile: false,
  t: string => string,
};
