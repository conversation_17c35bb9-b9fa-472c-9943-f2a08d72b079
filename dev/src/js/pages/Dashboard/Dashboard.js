/* eslint-disable react/no-unused-prop-types */
import React, {
  useContext,
} from 'react';
import { connect } from 'react-redux';
import * as PropTypes from 'prop-types';
import {
  Box,
  Paper,
  Separator,
  Skeleton,
} from '@majoo-ui/react';
import CoreHOC from '../../core/CoreHOC';
import {
  FilterSection, SalesSummarySection,
  StockMinimum, TitleSection,
} from '.';
import { LineChart } from '../../components/chart';
import { BannerText, SalesIncreasement } from '../../components/retina';
import BestSellingProduct from './BestSellingProduct';
import PaymentMethod from './PaymentMethod';
import EcommerceList from './Ecommerce';
import layoutsSelector from '../../data/layouts/layouts.selector';
import TaskBanner from './TaskBanner';
import DashboardProvider from './context/DashboardProvider';
import DashboardContext from './context/DashboardContext';
import {
  responsivePaper, periodOptions, lineChartXAxisKey, widgetPaper,
} from './utils/nondatamart-utils';
import MobileContainer from './mobile/MobileContainer';

const Container = () => {
  const {
    router,
    onboardingTask,
    joinDate,
    t,
    isMobile,
    isTablet,
    chart,
    language,
    currentDate,
    period,
    increasementNominal,
    goToPromoReportPage,
    lineChartLegend,
    sales,
    ecommerce,
    bestSell,
    pieChartData,
    payment,
    setPeriod,
    setLineChartLegend,
  } = useContext(DashboardContext);

  return (
    <Box css={{ userSelect: 'none' }}>
      <TaskBanner
        onboardingTask={onboardingTask}
        joinDate={joinDate}
        router={router}
        t={t}
      />
      {!isMobile ? (
        <React.Fragment>
          <Box css={{ marginBottom: '20px' }}>
            {chart.isLoading ? (<Skeleton />) : (
              <Paper css={responsivePaper}>
                <TitleSection t={t} lang={language} date={currentDate} period={period.value} percentage="" />
                <Separator
                  css={{
                    '@sm': { display: 'block', margin: '15px 0' },
                    '@md': { margin: 0 },
                  }}
                />
                <BannerText />
                <Box
                  css={{
                    '@sm': { display: 'block' },
                    '@md': { display: 'flex', justifyContent: 'space-between' },
                  }}
                >
                  <FilterSection
                    period={period}
                    setPeriod={(p) => {
                      setPeriod(p);
                      const periodValue = p.value === 'jam' ? t('translation:period.hour') : t('translation:period.day');
                      setLineChartLegend(d => d.map(l => (l.dataKey === 'count' ? { ...l, name: t('options.totalCustomPeriod', { period: periodValue }), yAxisId: 'right' } : { ...l, name: t('options.omzetCustomPeriod', { period: periodValue }), yAxisId: 'left' })));
                    }}
                    periodOptions={periodOptions(t)}
                  />
                  <Separator
                    css={{
                      '@sm': { display: 'block', margin: '15px 0' },
                      '@md': { display: 'none' },
                    }}
                  />
                  <SalesIncreasement
                    value={increasementNominal}
                    promoPageHandler={goToPromoReportPage}
                    isTablet={isTablet}
                    t={t}
                  />
                </Box>
                <Separator
                  css={{
                    '@sm': { display: 'none' },
                    '@md': { display: 'block' },
                  }}
                />
                <SalesSummarySection salesData={sales.data} isMobile={isMobile} t={t} />
                <Separator />
                <Box css={{ mt: '$cozy', '@md': { margin: '$spacing-05 $spacing-03' } }}>
                  <LineChart
                    data={chart.data}
                    legendData={lineChartLegend}
                    xAxisKey={lineChartXAxisKey}
                    period={period.value}
                    customXAxisFormat={tick => new Intl.DateTimeFormat(
                      'id-ID',
                      period.value === 'hari'
                        ? { day: '2-digit' }
                        : { hour: '2-digit', minute: '2-digit' },
                    ).format(new Date(tick))
                    }
                    title={t('label.graphic', 'Grafik Penjualan')}
                    isEmptyData={chart.data.length <= 0}
                  />
                </Box>
              </Paper>
            )}
          </Box>

          {ecommerce.isLoading ? (
            <Skeleton
              patternCount={1}
              css={{
                mb: '$spacing-05',
                maxHeight: '250px',
              }}
            />
          ) : (
            <Box css={{ marginBottom: '20px' }}>
              <Paper>
                <EcommerceList
                  ecommerceData={ecommerce.data}
                  period={period}
                  isError={ecommerce.isError}
                />
              </Paper>
            </Box>
          )}

          <Box
            css={{
              display: 'grid',
              '@sm': {
                gridTemplateColumns: '1fr',
                gap: '$cozy',
                minHeight: 224,
              },
              '@md': {
                maxHeight: 'auto',
                gap: '$compact',
                gridTemplateColumns: 'repeat(3, 1fr)',
              },
            }}
          >
            {bestSell.isLoading ? (
              <Skeleton
                patternCount={1}
                css={{
                  maxHeight: '250px',
                }}
              />
            ) : (
              <Paper css={widgetPaper}>
                <BestSellingProduct
                  t={t}
                  bestSellingData={bestSell.data}
                  period={period}
                  isError={bestSell.isError}
                />
              </Paper>
            )
            }

            {pieChartData.isLoading ? (
              <Skeleton
                patternCount={1}
                css={{
                  maxHeight: '250px',
                }}
              />
            ) : (
              <Paper css={widgetPaper}>
                <StockMinimum t={t} data={pieChartData.data} isError={pieChartData.isError} />
              </Paper>
            )}

            {payment.isLoading ? (
              <Skeleton
                patternCount={1}
                css={{
                  maxHeight: '250px',
                }}
              />
            ) : (
              <Paper css={{ ...widgetPaper, minHeight: 224 }}>
                <PaymentMethod t={t} lang={language} paymentMethodData={payment.data} />
              </Paper>
            )}
          </Box>
        </React.Fragment>
      ) : (
        <MobileContainer />
      )}
    </Box>
  );
};

const Dashboard = props => (
  <DashboardProvider parentProps={props}>
    <Container />
  </DashboardProvider>
);

Dashboard.propTypes = {
  filterBranch: PropTypes.string,
  calendar: PropTypes.shape({
    start: PropTypes.string,
    end: PropTypes.string,
    rangeLimit: PropTypes.number,
    onchange: PropTypes.func,
  }).isRequired,
  sidebarMenus: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  onboardingTask: PropTypes.shape({}),
  router: PropTypes.shape({
    push: PropTypes.func,
  }).isRequired,
  joinDate: PropTypes.string,
};

Dashboard.defaultProps = {
  filterBranch: '',
  onboardingTask: {},
  joinDate: '2023-09-01 00:00:00',
};

function mapStateToProps(state) {
  return {
    logo: state.user.profile.user_usaha_logo_path,
    namaUsaha: state.user.profile.user_usaha_name,
    namaUser: state.accountInfo.accountInfoResult.user_name,
    sidebarMenus: layoutsSelector.getVisibleNav(state.layouts),
    onboardingTask: state.users.strap.onboardingTask,
    joinDate: state.users.strap.joinDate,
  };
}

export default CoreHOC(connect(mapStateToProps)(Dashboard));
