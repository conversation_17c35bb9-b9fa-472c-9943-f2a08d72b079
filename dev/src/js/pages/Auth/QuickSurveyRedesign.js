// TODO: check apakah ada set persitent storage yang kurang
import React, { useState } from 'react';
import PropTypes from 'prop-types';

import {
  Box, Image, Heading,
  ToastContext, InputText, Paragraph, InputRadioGroup, InputRadio, FormHelper, Button, FormLabel, LoadingBar, Flex,
} from '@majoo-ui/react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useTranslation } from 'react-i18next';
import { Footer, WelcomeBackground } from './component';
import CoreHOC from '../../core/CoreHOC';
import logoMajoo from '../../../assets/images/majoo-logo-sm.svg';
import { registrationSurvey } from '../../data/users';
import { setConfigStorage } from '../../services/session';
import initSociomile from '../../utils/sociomile';

import { colors } from '../../stitches.config';

const schema = yup.object({
  pertanyaan: yup.string(),
  jawaban: yup.string(),
  alasan: yup.string().when('jawaban', {
    is: val => val === 'Sudah',
    then: yup.string().required('required'),
    otherwise: yup.string().notRequired(),
  }),
  knowFrom: yup.string().required('required'),
  knowFromOther: yup.string().when('knowFrom', {
    is: val => val === 'Lainnya',
    then: yup.string().required('required'),
    otherwise: yup.string().notRequired(),
  }),
}).required();

const QuickSurvey = (props) => {
  const { router } = props;
  const {
    register, handleSubmit, formState: { errors }, setValue,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      jawaban: 'Belum',
      alasan: '',
      pertanyaan: 'Pernah menggunakan aplikasi lain?',
      knowFromQuestion: 'Dari manakah Anda mengetahui majoo?',
      knowFrom: '',
      knowFromOther: '',
    },
  });
  const [radio, setRadio] = useState({
    firstQuestion: 'Belum',
    secondQuestion: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const { addToast } = React.useContext(ToastContext);

  const { t } = useTranslation('Auth/survey');

  const unknownError = (e) => {
    addToast({ title: 'Error!', variant: 'failed', description: e });
  };

  const onSubmit = async (data) => {
    setIsLoading(true);
    const payload = [
      {
        pertanyaan: data.pertanyaan,
        jawaban: data.jawaban,
        alasan: data.alasan,
      },
      {
        pertanyaan: data.knowFromQuestion,
        jawaban: data.knowFrom,
        alasan: data.knowFromOther,
      },
    ];

    try {
      await registrationSurvey(payload[0]);
      const result = await registrationSurvey(payload[1]);

      if (result.status) {
        const tobePersisted = {};
        const {
          data: {
            outletExp,
            support,
          },
        } = result;

        Object.assign(tobePersisted, {
          status_user: 3,
          support,
          outlet_exp: outletExp, // kalau relogin akan me-replace data dari login
        });

        setConfigStorage(tobePersisted);
        router.push('/dashboard');
        initSociomile();
      } else {
        throw result.msg;
      }
    } catch (err) {
      let errMessage = 'Fail to send survey';

      if (typeof err === 'string') {
        errMessage = err;
      }
      unknownError(errMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRadio = (e) => {
    setValue('jawaban', e);
    setRadio(prev => ({ ...prev, firstQuestion: e }));
    if (e === 'Belum') setValue('alasan', '');
  };

  const handleRadioKnowFrom = (e) => {
    setValue('knowFrom', e);
    setRadio(prev => ({ ...prev, secondQuestion: e }));
  };

  return (
    <React.Fragment>
      <LoadingBar
        showProgressOnly
        show={isLoading}
        colors={[colors.primary200, colors.primary500]}
        size="sm"
        css={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100% !important',
          padding: '0 !important',
        }}
      />
      <Box
        css={{
          display: 'flex',
          backgroundColor: '$gray50',
          flexDirection: 'column',
          '@lg': {
            flexDirection: 'row',
          },
        }}
      >
        <WelcomeBackground />
        <Box
          css={{
            display: 'flex',
            justifyContent: 'flex-start',
            flexDirection: 'column',
            alignItems: 'center',
            flex: 1,
            mx: '$compact',
            marginTop: 40,
            '@md': {
              minWidth: 598,
              justifyContent: 'center',
              alignItems: 'center',
              mx: 0,
              my: '$compact',
            },
          }}
        >
          <Box
            css={{
              boxShadow: '$small',
              borderRadius: '$lg',
              backgroundColor: '$white',
              padding: '24px 16px 40px',
              width: '100%',
              '@md': { width: 486, padding: '24px 40px 32px', mt: 'auto' },
            }}
            as="form"
            method="post"
            onSubmit={handleSubmit(onSubmit)}
          >
            <Flex
              gap={5}
              direction="column"
              align="center"
              css={{
                margin: 'auto', mb: 32, '@md': { mb: 24 },
              }}
            >
              <Image src={logoMajoo} alt="Logo Majoo" width={186} height={60} />
              <Heading as="h1" heading="h3" align="center">{t('title', 'Survei Majoo')}</Heading>
            </Flex>
            <Box css={{
              maxWidth: 406, mb: '$compact', '@md': { mb: 12 },
            }}
            >
              <Paragraph paragraph="longContentRegular">
                {t('description', 'Apakah Anda pernah menggunakan aplikasi lain sebelumnya?')}
              </Paragraph>
            </Box>
            <InputRadioGroup direction={{ '@initial': 'column', '@md': 'row' }} gap={5} defaultValue="Belum" {...register('jawaban')} onValueChange={(e) => { handleRadio(e); }} css={{ maxWidth: 400, mb: 24 }}>
              <InputRadio value="Belum" label={t('no', 'Tidak, Majoo yang pertama')} />
              <InputRadio value="Sudah" label={t('yes', 'Ya, Pernah')} />
            </InputRadioGroup>
            {radio.firstQuestion === 'Sudah' && (
              <Box css={{ marginBottom: '$spacing-04' }}>
                <FormLabel
                  htmlFor="alasan"
                  css={{ marginBottom: 8, color: '$textPrimary' }}
                >
                  {t('label', 'Nama Aplikasi')}
                </FormLabel>
                <InputText
                  id="alasan"
                  name="alasan"
                  type="text"
                  placeholder={t('placeholder', 'Contoh:MokoPOS, Kasier, dll')}
                  {...register('alasan')}
                  isInvalid={!!errors.alasan}
                />
                {
                  errors.alasan && (
                    <FormHelper
                      error
                      css={{ marginTop: '$spacing-03 !important' }}
                    >
                      {t(`errors.${errors.alasan.message}`)}
                    </FormHelper>
                  )
                }
              </Box>
            )}
            <Box css={{
              maxWidth: 334, mb: '$compact', '@md': { mb: 12 },
            }}
            >
              <Paragraph paragraph="longContentRegular">
                {t('labelKnowFrom', 'Dari manakah Anda mengetahui majoo?')}
              </Paragraph>
            </Box>
            <InputRadioGroup
              {...register('knowFrom')}
              onValueChange={handleRadioKnowFrom}
              css={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fill, minmax(150px, 1fr))',
                  gap: '$spacing-04',
                  gridColumnGap: '$spacing-09',
                  maxWidth: 400,
                  mb: 24,
                }}
            >
              <InputRadio value="Search Engine (Google)" label="Search Engine (Google)" />
              <InputRadio value="Sales Majoo" label={t('majooSales', 'Sales Majoo')} />
              <InputRadio value="Sosial Media" label={t('socialMedia', 'Sosial Media')} />
              <InputRadio value="Rekan/Partner Bisnis" label={t('partner', 'Rekan/Partner Bisnis')} />
              <InputRadio value="Lainnya" label={t('other', 'Lainnya')} />
            </InputRadioGroup>
            {
              errors.knowFrom && (
                <FormHelper
                  error
                  css={{ marginTop: '$spacing-03 !important' }}
                >
                  {t(`errors.${errors.knowFrom.message}`)}
                </FormHelper>
              )
            }
            {radio.secondQuestion === 'Lainnya' && (
                <Box css={{ marginBottom: '$spacing-04' }}>
                  <InputText
                    id="knowFromOther"
                    name="knowFromOther"
                    type="text"
                    placeholder={t('other', 'Lainnya')}
                    {...register('knowFromOther')}
                    isInvalid={!!errors.knowFromOther}
                  />
                  {
                    errors.knowFromOther && (
                      <FormHelper
                        error
                        css={{ marginTop: '$spacing-03 !important' }}
                      >
                        {t(`errors.${errors.knowFromOther.message}`)}
                      </FormHelper>
                    )
                  }
                </Box>
            )}
            <Button
              type="submit"
              size="lg"
              disabled={isLoading}
              css={{
                mt: '$cozy',
                fontWeight: 600,
                fontSize: 16,
                width: '100%',
                '@md': {
                  mt: radio === 'Sudah' ? 24 : 40,
                },
              }}
            >
              {t('send', 'Kirim')}
            </Button>
          </Box>
          <Footer />
        </Box>
      </Box>
    </React.Fragment>
  );
};

QuickSurvey.propTypes = {
  router: PropTypes.shape({
    push: PropTypes.func,
  }),
};

QuickSurvey.defaultProps = {
  router: {
    push: () => { },
  },
};

const Child = CoreHOC(QuickSurvey);

const Wrap = props => (
  <Box
    css={{
      '& footer': { display: 'none' },
    }}
  >
    <Child {...props} />
  </Box>
);

export default Wrap;
