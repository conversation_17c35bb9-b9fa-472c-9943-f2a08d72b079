import React, {
  useState, useEffect, useCallback,
} from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import {
  Box, Heading, LoadingBar, PageDialog, ToastContext,
} from '@majoo-ui/react';
import { debounce } from 'lodash';
import { useTranslation } from 'react-i18next';
import { fetchJenisUsaha } from '../../actions/usahaActions';
import CoreHOC from '../../core/CoreHOC';
import {
  getDataKota, newUpdateUsahaUser, getWizzardData, createInitialItem,
} from '../../data/users';
import { setConfigStorage } from '../../services/session';
import tokenUtils from '../../utils/token.util';
import { fetchUnit } from '../../data/settings';
import { getUnitOptions } from '../../data/settings/selectors';
import {
  StepComponent, OnboardStep1, OnboardStep2, Footer,
} from './component';

import { colors } from '../../stitches.config';

const OnboardWizard = (props) => {
  const {
    getJenisUsaha, jenisUsaha, userStrap, router, idCabang,
  } = props;
  const [step, setStep] = useState(1);
  const [locationOptions, setLocationOptions] = useState([]);
  const { addToast } = React.useContext(ToastContext);
  const [usahaName, setUsahaName] = useState('');
  const [units, setUnits] = useState([]);
  const [outletIDState, setOutletIDState] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useTranslation(['Auth/step', 'translation']);

  const unknownError = (e) => {
    addToast({ title: 'Error!', variant: 'failed', description: e });
  };

  const getUnits = async () => {
    try {
      const result = await fetchUnit();

      if (result.status) {
        const metrics = getUnitOptions(result.data);

        setUnits(metrics.map(item => ({ name: item.name, value: item.id })));
      } else {
        throw result.msg;
      }
    } catch (e) {
      let errMessage = 'Fail to get units.';

      if (typeof e === 'string') {
        errMessage = e;
      }
      unknownError(errMessage);
    }
  };

  const getLocation = useCallback(async () => {
    const params = {
      limit: 1000,
    };

    const response = await getDataKota(params);
    if (response.status) {
      const listLokasiUsaha = response.data
        .map(data => ({
          value: `${data.id_kota}-${data.id_provinsi}`,
          name: `${data.kota_name} - ${data.provinsi_name}`,
        }));
      setLocationOptions(listLokasiUsaha);
    }
  }, []);

  const getPreWizardData = async () => {
    let idUser = null;

    idUser = tokenUtils.getTokenPayload('id');
    if (!idUser) {
      unknownError('Data wizard could be anomaly.');
    }

    try {
      const result = await getWizzardData({ id: idUser });
      if (result.status) {
        setUsahaName(result.data.nama_usaha);
      }
    } catch (e) {
      unknownError(e.message);
    }
  };

  const handleGetLocation = useCallback(debounce((s) => { }, 800), []);

  const onSubmit = async (data) => {
    setIsLoading(true);
    const payload = {
      username: data.username,
      usaha_name: data.usaha_name,
      jenis_usaha: data.jenis_usaha,
      id_kota: data.id_kota,
      id_provinsi: data.id_provinsi,
      lama_beroperasi: data.lama_beroperasi,
      is_cms: 1,
    };

    try {
      const result = await newUpdateUsahaUser(payload);
      if (result.status) {
        const { id_cabang: branchId } = result.data;

        // Perlu state karena jika get dari local perlu timeout
        setOutletIDState(String(branchId));

        // TODO: perlu set storage yang lainnya ? coba cek tokennya juga beda ga?
        setConfigStorage({
          id_cabang: branchId,
          status_user: 1,
        });
        setStep(2);
      } else {
        throw result.msg;
      }
    } catch (e) {
      let errMessage = 'Something went wrong';

      if (typeof e === 'string') {
        errMessage = e;
      }
      unknownError(errMessage);
    }
    setIsLoading(false);
  };

  const onSubmit2 = async (data) => {
    setIsLoading(true);
    const payload = {
      id_outlet: idCabang ? JSON.stringify([String(idCabang)]) : JSON.stringify([String(outletIDState)]),
      image_path: data.image_path,
      name: data.name,
      nama_satuan: data.nama_satuan,
      category_item_name: data.category_item_name,
      harga_jual: Number(data.harga_jual),
      harga_modal: 0, // tidak ditampilkan langsung di 0 kan
      harga_beli: 0, // tidak ditampilkan langsung di 0 kan
      use_cogs: false, // tidak ditampilkan langsung dinonaktifkan
      // use_cogs: +isCogs,
      // stock: Number(stock),
    };
    try {
      const result = await createInitialItem({ ...payload });

      if (result && result.status) {
        setConfigStorage({
          status_user: 2,
          journey: { web: '0' },
        });
        router.replace('/quick-survey');
      } else {
        throw result.msg;
      }
    } catch (e) {
      let errMessage = 'Something went wrong';
      if (typeof err === 'string') {
        errMessage = e;
      }
      unknownError(errMessage);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    if (userStrap && userStrap.userStatus) {
      let { userStatus } = userStrap;
      userStatus = parseInt(userStatus, 10);

      if (userStatus === 1) {
        setStep(2);
      }
      if (userStatus === 2) {
        router.replace('/quick-survey');
      }
    }
    if (userStrap && userStrap.token) {
      getJenisUsaha();
    }
    getLocation();
    getPreWizardData();
    getUnits();
  }, []);

  const Child = useCallback(() => {
    switch (step) {
      case 2:
        return <OnboardStep2 units={units} onSuccess={onSubmit2} isLoading={isLoading} />;
      default:
        return <OnboardStep1 onSuccess={onSubmit} jenisUsaha={jenisUsaha} locationOptions={locationOptions} handleGetLocation={handleGetLocation} usahaName={usahaName} isLoading={isLoading} />;
    }
  }, [step, units.length, isLoading, jenisUsaha, usahaName, locationOptions.length]);

  return (
    <React.Fragment>
      <PageDialog
        css={{
          '& > div > div:first-child': { display: 'none', '@md': { display: 'flex' } },
          '& div[type="button"]': { display: 'none' },
        }}
        open
      >
        <PageDialog.Title />
        <PageDialog.Content css={{
          maxHeight: 'unset', display: 'flex', flexDirection: 'column', '@md': { display: 'block' },
        }}
        >
          <Box css={{
            backgroundColor: '$gray50',
            display: 'flex',
            alignItems: 'center',
            gap: '$cozy',
            flexDirection: 'column',
            padding: '0px 16px 0px',
            width: '100%',
            '@md': {
              padding: '0px', width: 'auto', gap: '40px', my: '$compact',
            },
          }}
          >
            <Box css={{
              width: '100%',
            }}
            >
              {
                isLoading
                && (
                  <LoadingBar
                    showProgressOnly
                    show={isLoading}
                    colors={[colors.primary200, colors.primary500]}
                    size="sm"
                    css={{
                      width: '100% !important',
                      padding: '0 !important',
                    }}
                  />
                )
              }
            </Box>
            <Heading as="h3" heading={{ '@initial': 'sectionTitle', '@md': 'h3' }} color="secondary" align="center">{t('title', '2 Langkah Mudah Menyiapkan Akun Majoo')}</Heading>
            <StepComponent active={step} t={t} />
            <Child />
          </Box>
          <Box css={{ display: 'block', backgroundColor: '$gray50', '@md': { display: 'none' } }}>
            <Footer />
          </Box>
        </PageDialog.Content>
      </PageDialog>
    </React.Fragment>
  );
};

OnboardWizard.propTypes = {
  getJenisUsaha: PropTypes.func.isRequired,
  jenisUsaha: PropTypes.arrayOf(PropTypes.object).isRequired,
  router: PropTypes.shape({
    replace: PropTypes.func,
  }).isRequired,
  userStrap: PropTypes.shape({
    token: PropTypes.string,
    userStatus: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number,
    ]),
    branchId: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number,
    ]),
  }),
  idCabang: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
};

OnboardWizard.defaultProps = {
  userStrap: {
    token: '',
  },
  idCabang: '',
};

export default CoreHOC(connect(
  state => ({
    jenisUsaha: state.usaha.jenisUsaha,
    userStrap: state.users.strap,
  }),
  dispatch => ({
    getJenisUsaha: () => {
      dispatch(fetchJenisUsaha());
    },
  }),
)(OnboardWizard));
