import React from 'react';
import PropTypes from 'prop-types';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  Box, Heading, FormHelper, InputText, Paragraph, Button, Flex, Image,
} from '@majoo-ui/react';
import logoMajoo from '../../../../assets/images/majoo-logo-sm.svg';
import { useTranslation } from 'react-i18next';

const schemaPhone = yup.object({
  phone: yup.string()
    .required('required')
    .min(8, 'min')
    .max(13, 'max'),
}).required();

const FormPhone = ({ onSuccess, isLoading }) => {
  const {
    register, handleSubmit, formState: { errors },
  } = useForm({ resolver: yupResolver(schemaPhone) });

  const { t } = useTranslation('Auth/phoneVerification');

  return (
    <Box
      css={{
        boxShadow: '$small',
        borderRadius: '$lg',
        backgroundColor: '$white',
        padding: '24px 16px 40px',
        width: '100%',
        '@md': { width: 486, padding: '24px 40px 32px', mt: 'auto' },
      }}
      as="form"
      method="post"
      onSubmit={handleSubmit(onSuccess)}
    >
      <Flex
        gap={5}
        direction="column"
        align="center"
        css={{
          margin: 'auto', mb: 32, '@md': { mb: 24 },
        }}
      >
        <Image src={logoMajoo} alt="Logo Majoo" width={186} height={60} />
        <Heading as="h1" heading="h3" align="center">{t('phone.title', 'Proteksi Akun')}</Heading>
      </Flex>
      <Box css={{
        maxWidth: 368, margin: 'auto', mb: '$spacing-06', '@md': { mb: 36 },
      }}
      >
        <Paragraph paragraph="longContentRegular" align="center">
          {t('phone.description', 'Untuk keamanan data, akun ini akan diproteksi dengan kode OTP yang akan dikirimkan melalui Whatsapp')}
        </Paragraph>
      </Box>
      <InputText
        id="phone"
        name="phone"
        type="number"
        placeholder={t('phone.placeholder', 'Contoh: 0812 xxxx xxxx')}
        isInvalid={!!errors.phone}
        {...register('phone')}
      />
      {
        errors.phone && (
          <FormHelper
            error
            css={{ marginTop: '$spacing-03 !important' }}
          >
            {t(`phone.errors.${errors.phone.message}`)}
          </FormHelper>
        )
      }
      <Button
        type="submit"
        size="lg"
        css={{
          mt: 24,
          fontWeight: 600,
          fontSize: 16,
          width: '100%',
        }}
        disabled={isLoading}
      >
        {t('phone.button', 'Kirim Kode OTP')}
      </Button>
    </Box>
  );
};

FormPhone.propTypes = {
  onSuccess: PropTypes.func.isRequired,
};

export default FormPhone;
