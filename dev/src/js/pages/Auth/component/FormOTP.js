import React, { useRef, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  Box, Heading, FormHelper, InputText, Paragraph, Button, Text, Flex, Image,
} from '@majoo-ui/react';
import { Link, useHistory } from 'react-router-dom';
import { Trans, useTranslation } from 'react-i18next';
import { numberVerificationEnum } from '../setting/section';
import logoMajoo from '../../../../assets/images/majoo-logo-sm.svg';

const schemaOTP = yup.object({
  otp: yup.string()
    .required('required'),
}).required();

const FormOTP = ({
  onSuccess, onErrors, resendCode, phone, duration, resendLimit, isLoading,
}) => {
  const browserHistory = useHistory();

  if (!phone) {
    browserHistory.push('/auth/phone-verification');
    return null;
  }
  const [countDown, setCountDown] = useState(duration.current);
  const timer = useRef();
  const {
    register, handleSubmit, formState: { errors }, setError,
  } = useForm({ resolver: yupResolver(schemaOTP) });

  const { t } = useTranslation('Auth/phoneVerification');

  const handleResendCode = () => {
    duration.current = 30;
    resendCode();
  };

  useEffect(() => {
    if (onErrors) {
      setError('otp', { type: 'wrong-otp', message: 'invalid' });
    }
  }, [onErrors]);

  useEffect(() => {
    timer.current = setInterval(() => {
      duration.current -= 1;
      setCountDown(duration.current);
    }, 1000);

    return () => { clearInterval(timer.current); };
  }, []);

  return (
    <Box
      css={{
        boxShadow: '$small',
        borderRadius: '$lg',
        backgroundColor: '$white',
        padding: '24px 16px 40px',
        width: '100%',
        '@md': { width: 486, padding: '24px 40px 32px', mt: 'auto' },
      }}
      as="form"
      method="post"
      onSubmit={handleSubmit(onSuccess)}
    >
      <Flex
        gap={5}
        direction="column"
        align="center"
        css={{
          margin: 'auto', mb: 32, '@md': { mb: 24 },
        }}
      >
        <Image src={logoMajoo} alt="Logo Majoo" width={186} height={60} />
        <Heading as="h1" heading="h3" align="center">{t('verify.title', 'Kode OTP')}</Heading>
      </Flex>
      <Box css={{
        maxWidth: 368, margin: 'auto', mb: '$spacing-06', '@md': { mb: 36 },
      }}
      >
        <Paragraph paragraph="longContentRegular" align="center">
          <Trans t={t} i18nKey="verify.description">
            Masukkan 4 digit kode OTP yang telah dikirimkan ke
            {{ phone }}
          </Trans>
        </Paragraph>
      </Box>
      <InputText
        id="otp"
        name="otp"
        type="number"
        placeholder={t('verify.placeholder', 'Masukan kode OTP')}
        isInvalid={!!errors.otp}
        {...register('otp')}
      />
      {errors.otp && (
        <FormHelper
          error
          css={{ marginTop: '$spacing-03 !important' }}
        >
          {t(`verify.errors.${errors.otp.message}`)}
        </FormHelper>
      )}
      <Button
        type="submit"
        size="lg"
        css={{
          mt: 24,
          fontWeight: 600,
          fontSize: 16,
          width: '100%',
        }}
        disabled={isLoading}
      >
        {t('verify.button', 'Proteksi Akun')}
      </Button>
      <Box
        css={{
          display: 'flex',
          justifyContent: 'center',
          marginTop: 24,
        }}
      >
        <Paragraph paragraph="shortContentRegular">
          {t('verify.wrongNumber', 'Nomor salah ? ')}
          <Link to={`/auth/phone-verification?page=${numberVerificationEnum.INPUT_NUMBER}`}>
            <Text
              as="span"
              variant="contentButton"
              css={{
                margin: 0,
                color: '$textGreen',
                ml: '$spacing-05',
                '@md': { ml: 8 },
              }}
            >
              {t('verify.change', 'Ubah No Ponsel')}
            </Text>
          </Link>
        </Paragraph>
      </Box>
      <Box
        css={{
          display: 'flex',
          marginTop: 32,
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paragraph paragraph="shortContentRegular">
          {t('verify.notReceived', 'Belum menerima kode OTP?')}
        </Paragraph>
        {countDown < 1 && resendLimit > 0 ? (
          <Paragraph paragraph="shortContentRegular">
            <Box as="a" onClick={() => handleResendCode()} css={{ color: '$textGreen', cursor: 'pointer' }}>{t('verify.resendCode', 'Kirim ulang kode.')}</Box>
          </Paragraph>
        ) : resendLimit > 0 && (
          <Paragraph paragraph="shortContentRegular">
            {t('verify.wait', 'Mohon tunggu ')}
            <Box as="span" css={{ color: '$textGreen' }}>{countDown}</Box>
            {t('verify.second', ' detik untuk kirim ulang.')}
          </Paragraph>
        )}
        {resendLimit === 0 && (
          <Paragraph paragraph="shortContentRegular">
            <Link to={`/auth/phone-verification?page=${numberVerificationEnum.INPUT_NUMBER}`}>
              <Text
                as="span"
                variant="contentButton"
                css={{
                  margin: 0,
                  color: '$textGreen',
                }}
              >
                {t('verify.recheck', 'Mohon cek ulang nomor Whatsapp.')}
              </Text>
            </Link>
          </Paragraph>
        )}
      </Box>
    </Box>
  );
};

FormOTP.propTypes = {
  onSuccess: PropTypes.func.isRequired,
  onErrors: PropTypes.bool,
  resendCode: PropTypes.func.isRequired,
  phone: PropTypes.string.isRequired,
  duration: PropTypes.shape({ current: PropTypes.number }).isRequired,
  resendLimit: PropTypes.shape({}).isRequired,
};

FormOTP.defaultProps = {
  onErrors: false,
};

export default FormOTP;
