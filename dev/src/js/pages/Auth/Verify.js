import React, { useCallback, useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Box, Heading, Paragraph, Text,
  ToastContext,
  Flex,
  Image,
  ResendOTP,
  Separator,
  Button,
} from '@majoo-ui/react';
import { useTranslation } from 'react-i18next';
import { isEmpty } from 'lodash';
import { Link, useHistory } from 'react-router-dom';
import { activateUser, resendActivationCode, resendActivationCodeV2 } from '../../data/users';
import { Footer, WelcomeBackground } from './component';
import { setConfigStorage, logout } from '../../services/session';
import CoreHOC from '../../core/CoreHOC';
import logoMajoo from '../../../assets/images/majoo-logo-sm.svg';
import { colors, styled } from '../../stitches.config';
import Loading from '../../components/Loading';
import { getErrorCode, hideEmail } from '../../utils/helper';
import { useTabsCounter } from '../../utils/useTabsCounter';

const ResendContainer = styled(Flex, {
  flexDirection: 'column',
  justifyContent: 'center',
  width: '100%',
  userSelect: 'none',
  variants: {
    disabled: {
      true: {
        cursor: 'not-allowed',
        color: `${colors.textSecondary} !important`,
      },
      false: {
        cursor: 'pointer',
        color: `${colors.textGreen} !important`,
      },
    },
  },
});

const ResendButton = styled(Paragraph, {
  textDecoration: 'underline',
});

const toTimeString = value => (value < 10 ? `0${value}` : value);

const Login = (props) => {
  const browserHistory = useHistory();
  useTabsCounter();
  const { location: { query }, router } = props;
  const [{ email, code }, setVerificationData] = useState({
    email: undefined,
    code: undefined,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [resendClickCount, setResendClickCount] = useState(0);
  const [resendOtpKey, setResendOtpKey] = useState(0);
  const { addToast } = React.useContext(ToastContext);

  const { t } = useTranslation(['Auth/verify', 'translation']);
  
  const handleBack = () => {
    logout();
    browserHistory.replace('/auth/login');
  };  

  const renderResendButton = ({ disabled, remainingTime, onClick }) => {
    const minutes = Math.floor(remainingTime / 60);
    const seconds = remainingTime - minutes * 60;
    return (
      <Flex direction="column" align="center" gap={5} css={{ width: '100%' }}>
        <ResendContainer gap={2} disabled={disabled} align="center">
          <Flex align="center" gap={3}>
            <Paragraph color="primary" align="center">
              {t('notReceive', 'Belum menerima kode verifikasi? ')}
            </Paragraph>
            <ResendButton onClick={() => (!disabled ? onClick() : {})}>{t('resend', 'Kirim Ulang')}</ResendButton>
          </Flex>
          <Paragraph>
            {remainingTime > 0 ? `(${toTimeString(minutes)}:${toTimeString(seconds)})` : ''}
          </Paragraph>
        </ResendContainer>
        {(remainingTime <= 0 && resendClickCount >= 4) ? (
          <Flex direction="column" align="center" gap={5} css={{ width: '100%' }}>
            <Separator />
            <Flex direction="column" align="center">
              <Paragraph>{t('somethingWrong', 'Mengalami kesulitan ?')}</Paragraph>
              <Paragraph>{t('checkYourEmail', 'Mungkin anda perlu periksa kembali email anda')}</Paragraph>
            </Flex>
            <Link to={`/auth/register?email=${email}`} style={{ textDecoration: 'unset' }}>
              <Button size="sm" buttonType="secondary">
                {t('checkEmailButton', 'Periksa Email')}
              </Button>
            </Link>
          </Flex>
        ) : (
          <Flex direction="column" align="center" gap={5} css={{ width: '100%' }}>
            <Separator />
            <Flex direction="column" align="center">
              <Paragraph>{t('haveAccount', 'Sudah punya akun ?')}</Paragraph>
              <Paragraph>{t('backToLogin', 'Kembali ke halaman awal untuk login')}</Paragraph>
            </Flex>
            <Button size="sm" buttonType="secondary" onClick={handleBack}>
              {t('label.back', 'Kembali', { ns: 'translation' })}
            </Button>
          </Flex>
        )}
      </Flex>
    );
  };

  const submitCode = async () => {
    setIsLoading(true);
    const activationResult = await activateUser({ email, kode_verifikasi: code });
    if (activationResult && activationResult.status) {
      // TODO: refresh_token
      if (activationResult && activationResult.token) {
        setConfigStorage({
          token: activationResult.token,
          id_permission: 1,
          status_user: 0,
          journey: { web: '0' },
          is_verified: true,
        });
      }
      router.replace('/businessInfo');
    } else {
      addToast({
        id: 'ver-error', variant: 'failed', title: t('toast.success', { ns: 'translation' }), description: t('errors.invalid'), preventDuplicate: true,
      });
      // setError('code', { type: 'wrong', message: 'invalid' });
    }
    setIsLoading(false);
  };

  const resendCode = async () => {
    try {
      const resendCodeResult = await resendActivationCodeV2({ email });
      if (resendCodeResult.status.code === 2010001) {
        addToast({
          id: 'ver-success', variant: 'success', title: 'Success!', description: t('resendSuccess'), preventDuplicate: true,
        });
      } else {
        addToast({
          id: 'ver-error', variant: 'failed', title: 'Error!', description: t(resendCodeResult.status.code === 4220001 ? 'wait30sec' : 'wait10min'), preventDuplicate: true,
        });
      }
    } catch (e) {
      addToast({
        id: 'ver-error', variant: 'failed', title: 'Error!', description: t(getErrorCode(e) === 4220001 ? 'wait30sec' : 'wait10min'), preventDuplicate: true,
      });
    }
  };

  const getTimer = () => {
    let timer = resendClickCount < 3 ? 60 : 600;

    const storeTimer = localStorage.getItem('timerVerification');
    if (storeTimer && JSON.parse(storeTimer).email === query.email) {
      timer = Number(JSON.parse(storeTimer).time);
    }

    return timer;
  };

  const intervalId = useRef(null);
  const [timer, setTimer] = useState(getTimer());
  const startInterval = useCallback(() => {
    intervalId.current = setInterval(() => {
      if (timer > 0) {
        setTimer(prev => prev - 1);
      } else {
        clearInterval(intervalId.current);
      }
    }, 1000);
  }, [timer]);

  useEffect(() => {
    if (timer >= 0) localStorage.setItem('timerVerification', JSON.stringify({ email: query.email, time: timer }));
  }, [timer]);

  useEffect(() => {
    if (query) {
      setVerificationData({
        email: query.email,
        code: query.code,
      });
    }
    startInterval();
    return () => clearInterval(intervalId.current);
  }, []);

  useEffect(() => {
    if (!isEmpty(code)) {
      submitCode();
    }
  }, [code]);

  return (
    <React.Fragment>
      <Box
        css={{
          display: 'flex',
          backgroundColor: '$gray50',
          flexDirection: 'column',
          '@lg': {
            flexDirection: 'row',
          },
        }}
      >
        <WelcomeBackground />
        <Box
          css={{
            display: 'flex',
            justifyContent: 'flex-start',
            flexDirection: 'column',
            alignItems: 'center',
            flex: 1,
            mx: '$compact',
            marginTop: 40,
            '@md': {
              justifyContent: 'center',
              alignItems: 'center',
              mx: 'auto',
              my: '$compact',
            },
            '@lg': {
              minWidth: 'unset',
              maxWidth: '40vw',
              mx: '$spacing-04',
            },
          }}
        >
          <Image
            css={{
              width: 149,
              height: 48,
              marginBottom: '28px',
              display: 'block',
              '@md': {
                width: 186, height: 60, margin: 0, display: 'none',
              },
            }}
            src={logoMajoo}
            alt="Logo Majoo"
          />
          <Box
            css={{
              display: 'flex',
              flexDirection: 'column',
              gap: '$cozy',
              boxShadow: '$small',
              borderRadius: '$lg',
              backgroundColor: '$white',
              padding: '24px 16px 40px',
              width: '100%',
              maxWidth: 486,
              '@md': {
                minWidth: 330, padding: '36px 40px 36px', mt: 'auto',
              },
            }}
          >
            <Heading as="h1" heading="h3" align="center">{t('title', 'Verifikasi Email')}</Heading>
            <Paragraph color="primary" align="center">
              {`${t('description1', 'Silakan verifikasi akun dengan klik tautan')}`}
              <br />
              {`${t('description2', 'yang telah dikirimkan ke email')}`}
              {' '}
              <span
                style={{
                  color: colors.textGreen,
                  textDecoration: 'underline',
                }}
              >
                {email}
              </span>
            </Paragraph>
            <Paragraph color="red" align="center" css={{ marginTop: '-20px' }}>
              {`${t('description3', '(Cek folder Inbox atau Spam)')}`}
            </Paragraph>
            <ResendOTP
              key={resendOtpKey}
              maxTime={getTimer}
              renderButton={renderResendButton}
              renderTime={() => <React.Fragment />}
              onResendClick={() => {
                localStorage.removeItem('timerVerification');
                resendCode();
                if (resendClickCount >= 4) {
                  setResendClickCount(1);
                  setResendOtpKey(current => current + 1);
                } else {
                  setResendClickCount(current => current + 1);
                }
              }}
            />
          </Box>
          <Footer />
        </Box>
      </Box>
      {isLoading && (
        <Loading showLoading={isLoading} />
      )}
    </React.Fragment>
  );
};

Login.propTypes = {
  location: PropTypes.shape({
    query: PropTypes.shape({
      email: PropTypes.string,
      code: PropTypes.string,
    }),
  }),
  router: PropTypes.shape({
    push: PropTypes.func,
    replace: PropTypes.func,
  }).isRequired,
};

Login.defaultProps = {
  location: {
    query: {},
  },
};

export default CoreHOC(Login);
