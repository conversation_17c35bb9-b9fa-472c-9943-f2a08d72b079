import React from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import CoreHOC from '../../core/CoreHOC';
import { validateTokenChangeEmail } from '../../data/auth';

export default
@connect(state => ({}))
@CoreHOC
class ChangeEmail extends React.Component {
    static propTypes = {
        router: PropTypes.shape({
            push: PropTypes.func,
        }),
        location: PropTypes.shape({
            query: PropTypes.shape({
                token: PropTypes.string,
            }),
        }),
    };

    static defaultProps = {
        router: {
            push: () => { },
        },
        location: {
            query: {
                token: '',
            },
        },
    }

    constructor(props) {
        super(props);

        const { location } = this.props;
        this.state = {
            token: location.query.token,
            email: '',
            isTokenValid: true,
        };
    }

    componentWillMount() {
        const { token } = this.state;
        validateTokenChangeEmail({
            token,
        }).then(res => this.setState({
            isTokenValid: res.status,
            email: res.email,
        }));
    }

    render() {
        const {
            isTokenValid, email,
        } = this.state;

        if (isTokenValid) {
            return (
                <div className="panel panel-sign">
                    <div className="panel-body">
                        <h1 className="text-success">Permintaan Ubah Email Berhasil</h1>
                        <p className="alert alert-success mb-1">
                            Selamat, email anda berhasil diubah ke
                            {' '}
                            <strong>
                                {email}
                            </strong>
                            {' '}
                            . Silahkan gunakan email tersebut untuk masuk kedalam sistem.
                        </p>
                        <div className="row">
                            <div className="col-12 text-center">
                                <Link to="/auth/login">
                                    Menuju halaman login
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        return (
            <div className="panel panel-sign">
                <div className="panel-body">
                    <h1 className="text-danger">Terjadi Kegagalan</h1>
                    <p className="alert alert-danger mb-1">
                        Terjadi kegagalan dalam proses perubahan email anda.
                        Harap periksa kembali tautan anda, atau tautan yang anda tuju sudah tidak aktif.
                    </p>
                    <div className="row">
                        <div className="col-12 text-center">
                            <Link to="/auth/login">Menuju halaman Login</Link>
                        </div>
                    </div>
                </div>
            </div>
        );
    }
}
