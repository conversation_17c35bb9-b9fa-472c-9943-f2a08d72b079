import React, { useEffect, useState } from 'react';
import {
    Box, Button, Flex, FormHelper, FormLabel, Grid, GridItem, Heading, Image, InputSelect, InputText, Paragraph, Text, ToastContext,
} from '@majoo-ui/react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { styled } from '@stitches/react';
import { uniqBy } from 'lodash';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useTranslation } from 'react-i18next';
import { getAnalytics, logEvent } from '@firebase/analytics';
import tokenUtils from '../../utils/token.util';
import logoMajoo from '../../../assets/images/majoo-logo-sm.svg';
import {
    getDataKecamatan, getDataKota, getWizzardData, newUpdateUsahaUser,
} from '../../data/users';
import CoreHOC from '../../core/CoreHOC';
import { fetchJenisUsaha } from '../../actions/usahaActions';
import { lamaUsahaOptions } from './setting/section';
import Loading from '../../components/Loading';
import { setConfigStorage } from '../../services/session';
import initSociomile from '../../utils/sociomile';
import { useMediaQuery } from '../../utils/useMediaQuery';
import { sortArr } from '../../utils/helper';
import { getContentTranslation } from '../LayoutV2/utils';

const EllipseBackground = styled('div', {
    position: 'absolute',
    background: '$bgBorder',
    width: '100vw',
    height: '100vh',
    clipPath: 'ellipse(60% 55% at 50% 100%)',
    zIndex: -1,
});

const Container = styled(Flex, {
    width: '100vw',
    minHeight: '100vh',
    padding: '60px 0',
    variants: {
        isMobile: {
            true: {
                padding: '$spacing-08 $spacing-05',
            },
        },
    },
});

const FormContainer = styled(Flex, {
    width: '720px',
    minHeight: '540px',
    padding: '36px $spacing-08',
    backgroundColor: '$white',
    flexDirection: 'column',
    gap: '28px',
    variants: {
        isMobile: {
            true: {
                width: '100%',
                padding: '24px $spacing-05',
            },
        },
    },
});

const InputContainer = styled(GridItem, {
    display: 'flex',
    flexDirection: 'column',
    gap: '$spacing-03',
});

const schema = yup.object({
    username: yup.string().required('required'),
    usaha_name: yup.string().nullable().required('required'),
    jenis_usaha: yup.string().required('required'),
    lama_beroperasi: yup.string().required('required'),
    id_provinsi: yup.string().required('required'),
    id_kota: yup.string().required('required'),
    id_kecamatan: yup.string().required('required'),
}).required();

function BusinessInfo(props) {
    const {
        getJenisUsaha, jenisUsaha, userStrap, router, contentTranslations,
    } = props;
    const {
        register, handleSubmit, formState: { errors, isSubmitting }, setValue, getValues, clearErrors, watch,
    } = useForm({ resolver: yupResolver(schema) });

    const [dataKota, setDataKota] = useState([]);
    const [dataKecamatan, setDataKecamatan] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const isMobile = useMediaQuery('(max-width: 768px)');

    const { t, i18n } = useTranslation(['Auth/step', 'translation']);

    const provinceId = watch('id_provinsi', '');
    const cityId = watch('id_kota', '');

    const { addToast } = React.useContext(ToastContext);
    const unknownError = (e) => {
        addToast({ title: 'Error!', variant: 'failed', description: e });
    };

    const getPreWizardData = async () => {
        let idUser = null;

        idUser = tokenUtils.getTokenPayload('id');
        if (!idUser) {
            unknownError('Data wizard could be anomaly.');
        }

        try {
            const result = await getWizzardData({ id: idUser });
            if (result.status) {
                setValue('usaha_name', result.data.nama_usaha, { shouldValidate: true });
            }
        } catch (e) {
            unknownError(e.message);
        }
    };

    const getCityData = async () => {
        const params = {
            limit: 1000,
        };

        const response = await getDataKota(params);
        if (response.status) {
            setDataKota(response.data);
        }
    };

    const getKecamatanData = async () => {
        const params = {
            limit: 1000,
        };

        if (!cityId) {
            setDataKecamatan([]);
            return;
        }

        const response = await getDataKecamatan(params, {
            id_kota: cityId,
        });

        if (response.status) {
            setDataKecamatan(response.data);
        }
    };

    const handleJenisUsahaOption = () => {
        if (jenisUsaha && jenisUsaha.length > 0) {
            return jenisUsaha
                .map(item => (
                    {
                        name: getContentTranslation(contentTranslations, i18n.language, item.business_type_name || item.jenis_usaha_name),
                        value: item.id_jenis_usaha,
                        group: getContentTranslation(contentTranslations, i18n.language, item.jenis_outlet),
                    }));
        }
        return [];
    };

    const provinceOptions = sortArr(uniqBy(dataKota.map(data => ({
        value: data.id_provinsi,
        name: data.provinsi_name,
    })), 'value'), 'name');

    const cityOptions = sortArr(dataKota.filter(data => data.id_provinsi === provinceId).map(data => ({
        value: data.id_kota,
        name: data.kota_name,
    })), 'name');

    const subDistrictOptions = sortArr(dataKecamatan.map(data => ({
        value: data.id_kecamatan,
        name: data.kecamatan_name,
    })), 'name');

    const onSubmit = async (data) => {
        setIsLoading(true);
        const payload = {
            ...data,
            is_cms: 1,
            is_retina: 1,
        };

        try {
            const result = await newUpdateUsahaUser(payload);
            if (result && result.status) {
                const { id_cabang: branchId, support, redirect } = result.data;

                const analytics = getAnalytics();
                const currentPathname = window.location.pathname;

                logEvent(analytics, 'Registrasi_Informasi_Usaha', {
                    page_referrer: currentPathname,
                    Registrasi_Informasi_Usaha_email: userStrap.email,
                });

                // TODO: perlu set storage yang lainnya ? coba cek tokennya juga beda ga?
                const configStorage = {
                    id_cabang: branchId,
                    status_user: 3,
                    journey: { web: '0' },
                    support: support || [
                        {
                            name: 'TRIAL',
                            support_exp_date: '2100-10-13 23:59:59',
                            id_support: '6',
                            id_cabang: branchId,
                            is_grace_periode: '0',
                            grace_exp_date: '2100-10-13 23:59:59',
                        },
                    ],
                    outlet_exp: support ? {
                        [branchId]: support[0].support_exp_date,
                    } : {
                        [branchId]: '2100-10-13 23:59:59',
                    },
                    redirect: redirect || '/sales-dashboard',
                };
                setConfigStorage(configStorage);
                router.replace(redirect || '/sales-dashboard');
                initSociomile();
            } else {
                throw result.msg;
            }
        } catch (e) {
            let errMessage = 'Something went wrong';

            if (typeof e === 'string') {
                errMessage = e;
            }
            unknownError(errMessage);
        }
        setIsLoading(false);
    };

    useEffect(() => {
        setValue('id_kota', undefined);
        setValue('id_kecamatan', undefined);
    }, [provinceId]);

    useEffect(() => {
        setValue('id_kecamatan', undefined);
        getKecamatanData();
    }, [cityId]);

    useEffect(() => {
        if (userStrap && userStrap.token) {
            getJenisUsaha();
        }
        getPreWizardData();
        getCityData();
    }, []);

    return (
        <React.Fragment>
            <Box>
                {!isMobile && <EllipseBackground />}
                <Container direction="column" gap={isMobile ? 6 : 8} align="center" isMobile={isMobile}>
                    <Image css={{ width: 149, height: 'auto', '@md': { width: 154 } }} src={logoMajoo} alt="Logo Majoo" />
                    <FormContainer isMobile={isMobile}>
                        <Heading heading="h3" align="center">
                            {t('step1.title', 'Informasi Usaha')}
                        </Heading>
                        <Grid
                            columns={isMobile ? 1 : 2}
                            flow="row"
                            gap={3}
                        >
                            <InputContainer>
                                <FormLabel
                                    htmlFor="username"
                                    css={{ color: '$textPrimary' }}
                                >
                                    {t('step1.owner.label', 'Nama Pemilik Usaha')}
                                </FormLabel>
                                <InputText
                                    id="username"
                                    name="username"
                                    type="text"
                                    placeholder={t('step1.owner.placeholder', 'Contoh: Edi Hartanto')}
                                    {...register('username')}
                                    isInvalid={!!errors.username}
                                />
                                {errors.username && (
                                    <FormHelper
                                        error
                                    >
                                        {t(`step1.owner.errors.${errors.username.message}`)}
                                    </FormHelper>
                                )}
                            </InputContainer>
                            <InputContainer>
                                <FormLabel
                                    htmlFor="usaha_name"
                                    css={{ color: '$textPrimary' }}
                                >
                                    {t('step1.businessName.label', 'Nama Usaha')}
                                </FormLabel>
                                <InputText
                                    id="usaha_name"
                                    name="usaha_name"
                                    type="text"
                                    placeholder={t('step1.businessName.placeholder', 'Contoh: Toko Sahaja')}
                                    {...register('usaha_name')}
                                    isInvalid={!!errors.usaha_name}
                                />
                                {errors.usaha_name && (
                                    <FormHelper
                                        error
                                    >
                                        {t(`step1.businessName.errors.${errors.usaha_name.message}`)}
                                    </FormHelper>
                                )}
                            </InputContainer>
                            <InputContainer>
                                <FormLabel
                                    htmlFor="jenis_usaha"
                                    css={{ color: '$textPrimary' }}
                                >
                                    {t('step1.businessType.label', 'Bidang Usaha')}
                                </FormLabel>
                                <InputSelect
                                    id="jenis_usaha"
                                    name="jenis_usaha"
                                    placeholder={t('placeholder.select', { ns: 'translation', defaultValue: 'Pilih' })}
                                    search={!isMobile}
                                    option={handleJenisUsahaOption()}
                                    value={handleJenisUsahaOption().find(x => x.value === watch('jenis_usaha'))}
                                    onChange={(option) => {
                                        setValue('jenis_usaha', option.value, { shouldValidate: true });
                                    }}
                                    isInvalid={!!errors.jenis_usaha}
                                    optionCss={{
                                        '& p': {
                                            color: '$textPrimary',
                                        },
                                    }}
                                    isMobile={isMobile}
                                    mobileTitle={t('step1.businessType.label', 'Bidang Usaha')}
                                />
                                {errors.jenis_usaha && (
                                    <FormHelper
                                        error
                                    >
                                        {t(`step1.businessType.errors.${errors.jenis_usaha.message}`)}
                                    </FormHelper>
                                )}
                            </InputContainer>
                            <InputContainer>
                                <FormLabel
                                    htmlFor="lama_beroperasi"
                                    css={{ color: '$textPrimary' }}
                                >
                                    {t('step1.operationPeriod.label', 'Lama Beroperasi')}
                                </FormLabel>
                                <InputSelect
                                    id="lama_beroperasi"
                                    name="lama_beroperasi"
                                    placeholder={t('placeholder.select', { ns: 'translation', defaultValue: 'Pilih' })}
                                    search={!isMobile}
                                    option={lamaUsahaOptions(t)}
                                    onChange={(option) => {
                                        setValue('lama_beroperasi', option.value, { shouldValidate: true });
                                    }}
                                    value={lamaUsahaOptions(t).find(x => x.value === watch('lama_beroperasi'))}
                                    isInvalid={!!errors.lama_beroperasi}
                                    isMobile={isMobile}
                                    mobileTitle={t('step1.operationPeriod.label', 'Lama Beroperasi')}
                                />
                                {errors.lama_beroperasi && (
                                    <FormHelper
                                        error
                                    >
                                        {t(`step1.operationPeriod.errors.${errors.lama_beroperasi.message}`)}
                                    </FormHelper>
                                )}
                            </InputContainer>
                            <InputContainer>
                                <FormLabel
                                    htmlFor="id_provinsi"
                                    css={{ color: '$textPrimary' }}
                                >
                                    {t('step1.location.province.label', 'Provinsi')}
                                </FormLabel>
                                <InputSelect
                                    id="id_provinsi"
                                    name="id_provinsi"
                                    placeholder={t('placeholder.select', { ns: 'translation', defaultValue: 'Pilih' })}
                                    search={!isMobile}
                                    option={provinceOptions}
                                    onChange={(option) => {
                                        setValue('id_provinsi', option.value, { shouldValidate: true });
                                    }}
                                    value={provinceOptions.find(x => x.value === watch('id_provinsi'))}
                                    isInvalid={!!errors.id_provinsi}
                                    isMobile={isMobile}
                                    mobileTitle={t('step1.location.province.label', 'Provinsi')}
                                />
                                {errors.id_provinsi && (
                                    <FormHelper
                                        error
                                    >
                                        {t(`step1.location.province.errors.${errors.id_provinsi.message}`)}
                                    </FormHelper>
                                )}
                            </InputContainer>
                            <InputContainer>
                                <FormLabel
                                    htmlFor="id_kota"
                                    css={{ color: '$textPrimary' }}
                                >
                                    {t('step1.location.city.label', 'Kota')}
                                </FormLabel>
                                <InputSelect
                                    key={provinceId}
                                    id="id_kota"
                                    name="id_kota"
                                    placeholder={t('placeholder.select', { ns: 'translation', defaultValue: 'Pilih' })}
                                    search={!isMobile}
                                    emptyDataMessage={!provinceId ? t('step1.location.city.emptyDataMessage') : undefined}
                                    option={cityOptions}
                                    onChange={(option) => {
                                        setValue('id_kota', option.value, { shouldValidate: true });
                                    }}
                                    value={cityOptions.find(x => x.value === watch('id_kota'))}
                                    isInvalid={!!errors.id_kota}
                                    isMobile={isMobile}
                                    mobileTitle={t('step1.location.city.label', 'Kota')}
                                />
                                {errors.id_kota && (
                                    <FormHelper
                                        error
                                    >
                                        {t(`step1.location.city.errors.${errors.id_kota.message}`)}
                                    </FormHelper>
                                )}
                            </InputContainer>
                            <InputContainer>
                                <FormLabel
                                    htmlFor="id_kecamatan"
                                    css={{ color: '$textPrimary' }}
                                >
                                    {t('step1.location.subdistrict.label', 'Kecamatan')}
                                </FormLabel>
                                <InputSelect
                                    key={`${provinceId}-${cityId}`}
                                    id="id_kecamatan"
                                    name="id_kecamatan"
                                    placeholder={t('placeholder.select', { ns: 'translation', defaultValue: 'Pilih' })}
                                    search={!isMobile}
                                    emptyDataMessage={!cityId ? t('step1.location.subdistrict.emptyDataMessage') : undefined}
                                    option={subDistrictOptions}
                                    onChange={(option) => {
                                        setValue('id_kecamatan', option.value, { shouldValidate: true });
                                    }}
                                    value={subDistrictOptions.find(x => x.value === watch('id_kecamatan'))}
                                    isInvalid={!!errors.id_kecamatan}
                                    isMobile={isMobile}
                                    mobileTitle={t('step1.location.subdistrict.label', 'Kecamatan')}
                                />
                                {errors.id_kecamatan && (
                                    <FormHelper
                                        error
                                    >
                                        {t(`step1.location.subdistrict.errors.${errors.id_kecamatan.message}`)}
                                    </FormHelper>
                                )}
                            </InputContainer>
                        </Grid>
                        <Button
                            css={{
                                alignSelf: 'end',
                                width: '100%',
                                height: '48px',
                                '@md': {
                                    width: 'unset',
                                    height: '40px',
                                },
                            }}
                            onClick={handleSubmit(onSubmit)}
                        >
                            {t('label.save', { ns: 'translation', defaultValue: 'Simpan' })}
                        </Button>
                    </FormContainer>
                    <Flex direction="column" align="center" gap={isMobile ? 3 : 6}>
                        <Flex gap={6}>
                            {['Privacy Policy', 'Terms and Condition'].map(label => (isMobile ? (
                                <Text variant="label">
                                    {label}
                                </Text>
                            ) : (
                                <Paragraph>
                                    {label}
                                </Paragraph>
                            )))}
                        </Flex>
                        <Text variant="label">2023. PT  Majoo Teknologi Indonesia</Text>
                    </Flex>
                </Container>
            </Box>
            {isLoading && (
                <Loading showLoading={isLoading} />
            )}
        </React.Fragment>
    );
}

export default connect(
    state => ({
        jenisUsaha: state.usaha.jenisUsaha,
        userStrap: state.users.strap,
        contentTranslations: state.translations.contentTranslations,
    }),
    dispatch => ({
        getJenisUsaha: () => {
            dispatch(fetchJenisUsaha());
        },
    }),
)(BusinessInfo);
