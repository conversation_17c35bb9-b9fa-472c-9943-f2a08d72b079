import React, { useState } from 'react';
import { PlusOutline, LogOutline } from '@majoo-ui/icons';
import {
  Box, Button, Flex, Heading, IconButton, NotificationBadge,
} from '@majoo-ui/react';
import { foundations } from '@majoo-ui/core';
import PropTypes from 'prop-types';
import { FavoriteWrapper, TooltipGuidance } from '../../../../components/retina';
import LogActivityDialog from './LogActivity';

const { colors } = foundations;

const TitleSection = ({ title, onClickButton, addLabel, translator, isMobile, selectedBranch, isCanCreate }) => {
  const [logActivityState, setLogActivityState] = useState({
    openDialog: false,
    selectedDepositNo: '',
  });

  // render
  return (
    <Flex
      css={{
        '@md': { padding: '0px $spacing-03' },
        justifyContent: 'space-between',
      }}
    >
      <Box>
        <FavoriteWrapper css={{ '@md': { marginBottom: '$spacing-03' } }}>
          <Heading
            as="h1"
            heading="pageTitle"
          >
            {title}
          </Heading>
          <TooltipGuidance />
        </FavoriteWrapper>
      </Box>
      <Flex gap={4}>
        {
          logActivityState.openDialog && (
            <LogActivityDialog
              state={logActivityState}
              isMobile={isMobile}
              onOpenChange={open => setLogActivityState(current => ({
                ...current,
                openDialog: open,
              }))}
              translator={translator}
              selectedBranch={selectedBranch}
            />
          )
        }
        <Box onClick={() => setLogActivityState({ ...logActivityState, openDialog: true })} css={{ paddingTop: '4px' }}>
          <IconButton>
            <NotificationBadge
              variant="important"
              // TODO: if has clicked dot are gone
              // dot
              css={{
                top: '10%',
                right: '20%',
                zIndex: 0,
                backgroundColor: '$red500 !important',
                borderWidth: '1px !important',
                padding: '0px !important',
              }}
            >
              <LogOutline size={20} color={colors.iconGreen} />
            </NotificationBadge>
          </IconButton>
        </Box>
        {isCanCreate && (
          <Button
            onClick={onClickButton}
            size="sm"
            leftIcon={<PlusOutline color="currentColor" />}
            css={{
              fontWeight: 600,
              '@sm': { display: 'none' },
              '@md': { display: 'inline-flex' },
            }}
          >
            {addLabel}
          </Button>
        )}
      </Flex>
    </Flex>
  );
};

TitleSection.propTypes = {
  title: PropTypes.string.isRequired,
  onClickButton: PropTypes.func,
};

TitleSection.defaultProps = {
  onClickButton: undefined,
};

export default TitleSection;
