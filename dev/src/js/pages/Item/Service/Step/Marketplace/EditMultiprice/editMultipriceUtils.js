import React, { useState } from 'react';
import {
  EditableTable,
  RowSelectionColumn,
  Flex,
  InputNumber,
  Box,
  Text,
} from '@majoo-ui/react';

import { currency, ishasProperty } from '../../../../../../utils/helper';

const PRESET_TYPE = {
  MARKUP_PERCENT: '1',
  MARKUP_NOMINAL: '2',
  MARKDOWN_PERCENT: '3',
  MARKDOWN_NOMINAL: '4',
  PRESET_MANUAL: '5',
};

// DETAIL WEB ORDER
const generateFormData = (data = null, { outletId }) => {
  let preset = {
    type: PRESET_TYPE.PRESET_MANUAL,
    value: 0,
  };

  if (data && data.preset) {
    preset = {
      type: String(data.preset),
      value: Number(data.preset_value),
    };
  }

  return {
    id: data ? data.id : 0,
    outletId: data ? data.outletId : outletId,
    activeStatus: data ? data.status : true,
    typeName: data ? data.tipe : '',
    readOnlyTypeName: data ? data.tipe : '',
    description: data ? data.deskripsi : '',
    customerGroups: data ? data.pelanggan : [],
    products: data ? data.produk : [],
    preset,
    isPermanent: data ? data.isPermanent : true,
  };
};

const handleValidateNominalProduct = ({ products, typeName }) => {
  let retval = {
    status: true,
    message: '',
  };

  if (typeName.toLowerCase() !== 'web order') return retval;

  const isProductInvalid = products.some(
    detail => Number(detail.customPrice) < 100
  );

  const allProductCustomPriceFilled = products.every(product => Boolean(product.customPrice));

  if (isProductInvalid) {
    retval = {
      status: false,
      message: 'Terdapat nominal produk yang kurang dari Rp 100',
    };
  } else if (!allProductCustomPriceFilled) {
    retval = {
      status: false,
      message: 'Terdapat nominal produk yang dikosongkan',
    };
  }

  return retval;
};

const generatePayload = (formData) => {
  const checkNominalProductValid = handleValidateNominalProduct(formData);
  
  if (!checkNominalProductValid.status) {
    return {
      ...checkNominalProduct, // ini darimana ya?
      payload: {},
    };
  }

  const items = formData.products.map(x => ({
    id_item: Number(x.id),
    item_price: x.customPrice,
  }));

  return {
    status: true,
    message: '',
    payload: {
      ...(formData.id ? { id: formData.id } : {}),
      type: formData.typeName,
      desc: formData.description,
      customers_categories: formData.customerGroups.map(x => x.id),
      items,
      id_outlet: Number(formData.outletId),
      active_status: formData.activeStatus ? 1 : 0,
      preset: Number(formData.preset.type),
      preset_value: Number(formData.preset.value),
    },
  };
};

const StyledFlex = ({ css, children }) => (
  <Flex
    css={{
      ...css,
      flexDirection: 'column',
      gap: 16,
      '@md': {
        flexDirection: 'row',
        gap: 32,
        alignItems: 'flex-start',
      },
    }}
  >
    {children}
  </Flex>
);

const StyledLabelBox = ({ children }) => (
  <Box
    css={{
      width: '100%',
      '@md': {
        width: '35%',
      },
    }}
  >
    {children}
  </Box>
);

const StyledFieldBox = ({ children }) => (
  <Box
    css={{
      width: '100%',
      '@md': {
        width: '65%',
      },
    }}
  >
    {children}
  </Box>
);

const StyledHeaderLabelText = ({ children }) => (
  <Text color="primary" css={{ fontSize: 14, fontWeight: 600 }}>
    {children}
  </Text>
);

// PRODUCT AND PRICE
const tableColumnsProductAndPrice = (
  handleUpdateCustomPriceProduct,
  handleRemoveProduct,
) => [
  {
    Header: 'PRODUK',
    accessor: 'name',
    isMobileHeader: true,
    Cell: ({ value }) => <Box css={{ wordBreak: 'break-word' }}>{value}</Box>,
    width: 210,
  },
  {
    Header: 'SATUAN',
    accessor: 'satuan',
    width: 140,
  },
  {
    Header: 'HARGA NORMAL',
    accessor: 'price',
    Cell: ({ value }) => currency({ value }),
    width: 200,
  },
  {
    Header: 'ATUR HARGA',
    accessor: 'customPrice',
    width: 200,
    Cell: ({ value, row: { index } }) => {
      const [inputValue, setInputValue] = useState(value);

      return (
        <InputNumber
          css={{
            width: '70%',
            '@md': {
              width: '100%',
            },
          }}
          size="sm"
          prefix="Rp "
          allowEmptyFormatting
          isNumericString
          value={inputValue}
          onValueChange={({ value }) => setInputValue(`${value}`)}
          onBlur={() => handleUpdateCustomPriceProduct(index, inputValue)}
        />
      );
    },
  },
  {
    Header: '',
    accessor: 'remove',
    width: 80,
    Cell: ({ row: { index } }) => (
      <EditableTable.Remove
        css={{ marginLeft: 25 }}
        onRemove={() => handleRemoveProduct(index)}
      />
    ),
  },
];

const tableColumnsAddProduct = () => [
  RowSelectionColumn,
  {
    Header: 'Nama Produk',
    accessor: 'name',
    isMobileHeader: true,
    unsortable: true,
  },
  {
    Header: 'Kategori',
    accessor: 'categories',
    Cell: ({ value }) => `${value.map(item => item.name).join(', ')}`,
    unsortable: true,
  },
  {
    Header: 'SKU',
    accessor: 'sku',
    unsortable: true,
  },
  {
    Header: 'Satuan',
    accessor: 'unit_name',
    unsortable: true,
  },
  {
    Header: 'Harga Jual',
    accessor: 'selling_price',
    Cell: ({ value }) => currency({ value }),
    unsortable: true,
  },
];

const formatProductPrice = (type, obj) => {
  let retval = 0;
  switch (type) {
    case 'price':
      retval = obj.price;
      if (ishasProperty(obj, 'selling_price')) {
        retval = obj.selling_price;
      }
      break;
    case 'customPrice':
      retval = obj.customPrice;
      if (ishasProperty(obj, 'selling_price')) {
        retval = obj.selling_price;
      }
      break;
    default:
      retval = 0;
  }

  return String(retval);
};

const StyledEditableTable = ({ columns, data }) => (
  <EditableTable
    css={{
      marginTop: 16,
      '@md': {
        marginTop: 28,
        maxHeight: 450,
        overflow: 'auto',
      },
      '& thead': { zIndex: 1 },
      '& td': {
        maxWidth: 'unset !important',
        height: 'auto',
        '@md': { height: 80 },
      },
      '& td > div > div > div > div': {
        minHeight: 56,
        backgroundColor: '$gray50',
        padding: '0px 8px',
        justifyContent: 'center',
      },
      '& td > div': { padding: 0, display: 'flex', alignItems: 'center' },
      '& h1': { color: '$textSecondary' },
      '& input': { backgroundColor: 'white', width: '100%' },
    }}
    columns={columns}
    data={data}
  />
);

export {
  PRESET_TYPE,
  generateFormData,
  generatePayload,
  StyledFlex,
  StyledLabelBox,
  StyledFieldBox,
  StyledHeaderLabelText,
  tableColumnsProductAndPrice,
  tableColumnsAddProduct,
  formatProductPrice,
  StyledEditableTable,
};
