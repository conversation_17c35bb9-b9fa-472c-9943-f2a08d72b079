import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
    ModalDialog,
    ModalDialogTitle,
    ModalDialogContent,
    ModalDialogFooter,
    DialogClose,
    Button,
    Box,
    Banner,
    BannerDescription,
    InputResourceList,
} from '@majoo-ui/react';

const ProductModal = ({
    isOpen,
    onOpenChange,
    isMobile,
    productList,
    formCategory,
    onSubmit,
    fetchProductList,
    pageIndex,
    isLoadProduct,
    isMoreProduct,
    handleInitForm,
    t,
}) => {
    const [disabledItems, setDisabledItems] = useState([]);
    const [formData, setFormData] = useState({
        id: '',
        name: '',
        order: '',
        id_department: '',
        isShown: false,
        outlets: [],
        products: [],
        id_products: [],
        itemAdd: [],
        itemDelete: [],
    });

    const getDisabledProducts = useCallback(() => {
        // Products that already have a category (except "Tanpa Kategori" status 4) should be disabled
        const disabledProductIds = productList
            .filter(item => item.category !== null && item.status !== '4')
            .map(item => item.value);
        setDisabledItems(disabledProductIds);
    }, [productList]);

    const fetchData = useCallback(
        ({ query, pageIndex: index }) => {
            fetchProductList({ query, index });
        },
        [fetchProductList],
    );

    // Handle InputResourceList onChange - this replaces the old onItemCheck/onItemUncheck pattern
    const handleProductSelection = useCallback((selectedItems) => {
        // selectedItems will be an array of IResource objects (since value prop contains objects)
        const currentProducts = formData.products || [];
        const currentProductValues = currentProducts.map(item => item.value);
        const selectedValues = selectedItems.map(item => item.value);

        // Find newly added items
        const newItems = selectedItems.filter(item => !currentProductValues.includes(item.value));
        // Find removed items
        const removedValues = currentProductValues.filter(value => !selectedValues.includes(value));

        setFormData(prev => ({
            ...prev,
            products: selectedItems,
            itemAdd: [...(prev.itemAdd || []), ...newItems],
            itemDelete: [...(prev.itemDelete || []), ...removedValues],
        }));
    }, [formData.products]);

    useEffect(() => {
        fetchData({ pageIndex: 0 });
    }, []);

    useEffect(() => {
        setFormData(prev => ({ ...prev, ...formCategory }));
    }, [formCategory]);

    useEffect(() => {
        getDisabledProducts();
    }, [getDisabledProducts]);

    return (
        <ModalDialog modal layer="$modal" open={isOpen} onOpenChange={onOpenChange} isMobile={isMobile}>
            <ModalDialogTitle>{t('form.product.title')}</ModalDialogTitle>
            <ModalDialogContent>
                <Box>
                    <Box css={{ marginBottom: '20px' }}>
                        <Banner variant="warning">
                            <Box css={{ '& p': { whiteSpace: 'normal' } }}>
                                <BannerDescription>{t('form.product.description')}</BannerDescription>
                            </Box>
                        </Banner>
                    </Box>
                    <Box>
                        <InputResourceList
                            searchPlaceholder={t('placeholder.search', { ns: 'translation' })}
                            counterFooterText={t('form.product.selectProduct')}
                            isLoading={isLoadProduct}
                            data={productList}
                            disabledItems={disabledItems}
                            disabled={false}
                            value={formData.products || []}
                            onChange={handleProductSelection}
                            fetchData={fetchData}
                            onQueryChange={query => fetchData({ query, pageIndex: 0 })}
                        />
                    </Box>
                </Box>
            </ModalDialogContent>
            <ModalDialogFooter css={{ display: 'flex', gap: '$compact' }}>
                <DialogClose asChild>
                    <Button
                        aria-label="Close"
                        buttonType="ghost"
                        size="sm"
                        onClick={() => {
                            handleInitForm();
                            fetchData({ query: '', pageIndex: 0 });
                        }}
                        css={{ width: isMobile && '50%' }}
                    >
                        {t('label.cancel', { ns: 'translation' })}
                    </Button>
                </DialogClose>
                <Button size="sm" onClick={() => onSubmit(formData)} css={{ width: isMobile && '50%' }}>
                    {t('placeholder.select', { ns: 'translation' })}
                </Button>
            </ModalDialogFooter>
        </ModalDialog>
    );
};

ProductModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onOpenChange: PropTypes.func.isRequired,
    isMobile: PropTypes.bool.isRequired,
    productList: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
    formCategory: PropTypes.oneOfType([PropTypes.object]).isRequired,
    onSubmit: PropTypes.func.isRequired,
    fetchProductList: PropTypes.func.isRequired,
    pageIndex: PropTypes.number.isRequired,
    isLoadProduct: PropTypes.bool.isRequired,
    isMoreProduct: PropTypes.bool.isRequired,
    handleInitForm: PropTypes.func.isRequired,
    t: PropTypes.func.isRequired,
};

export default ProductModal;
