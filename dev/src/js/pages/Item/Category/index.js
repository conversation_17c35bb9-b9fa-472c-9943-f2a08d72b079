import React, {
  useEffect, useState, useContext, Fragment,
} from 'react';
import PropTypes from 'prop-types';
import { get } from 'lodash';
import {
  Flex, Paper, Box, Heading, Button, Separator, Table, ToastContext,
} from '@majoo-ui/react';
import { connect } from 'react-redux';
import { PlusOutline } from '@majoo-ui/icons';
import { Trans, useTranslation } from 'react-i18next';
import { tableColumn, ACTIVE_MENU_STATUS, USER_ACCESS } from './settings/utils';
import { useMediaQuery } from '../../../utils/useMediaQuery';
import { getDepartemen } from '../../../data/department';
import * as OutletApi from '../../../data/outlets';
import * as productApi from '../../../data/product';
import * as marketPlace from '../../../data/marketplace';
import DetailForm from './components/form';
import FilterSection from './components/filterSection';
import DeleteModal from './components/deleteModal';
import ProductModal from './components/productModal';
import CoreHOC from '../../../core/CoreHOC';
import FailedDeleteModal from './components/failedDeleteModal';
import { catchError } from '../../../utils/helper';
import { FavoriteWrapper, TooltipGuidance, BannerText } from '../../../components/retina';
import { MARKETPLACE_PROVIDER_ID } from './utils';

const DaftarKategori = ({
  filterBranch, showProgress, hideProgress, idCabang, userAccess, menuPrivilege
}) => {
  const { isCanCreate, isCanUpdate, isCanView, isWhitelistMerchant } = menuPrivilege;
  const isMobile = useMediaQuery('(max-width: 1024px)');
  const { addToast } = useContext(ToastContext);
  const [isLoading, setLoading] = useState(true);
  const [isTableLoading, setTableLoading] = useState(true);
  const [tableData, setTableData] = useState([]);
  const [categoryName, setCategoryName] = useState('');
  const [filteredTable, setFilteredTable] = useState([]);
  const [filterValue, setFilterValue] = useState('');
  const [outletList, setOutletList] = useState([]);
  const [departmentList, setDepartmentList] = useState([]);
  const [productList, setProductList] = useState([]);
  const [type, setType] = useState('add');
  const [activeMenuStatus, setActiveMenuStatus] = useState('11');
  const [isOpen, setOpen] = useState(false);
  const [isDeleteModal, setDeleteModal] = useState(false);
  const [isFailedDeleteModal, setIsFailedDeleteModal] = useState(false);
  const [isProductModal, setProductModal] = useState(false);
  const [isMoreProduct, setMoreProduct] = useState(true);
  const [isLoadProduct, setLoadProduct] = useState(false);
  const [page, setPage] = useState(0);
  const [form, setForm] = useState({
    id: '',
    name: '',
    order: '',
    status_category_item: '',
    id_department: undefined,
    isShown: true,
    outlets: [],
    products: [],
    id_products: [],
    itemAdd: [],
    itemDelete: [],
  });
  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [tempResetKey, setTempResetKey] = useState(0);
  const [totalTableData, setTotalTableData] = useState(0);

  const { t, i18n } = useTranslation(['Penjualan/Produk/category', 'translation']); // folder location -> dev\src\assets\locales
  const currentLang = i18n.language;

  const initForm = {
    id: '',
    name: '',
    order: '',
    status_category_item: '',
    id_department: '',
    isShown: true,
    outlets: [],
    products: [],
    id_products: [],
    itemAdd: [],
    itemDelete: [],
  };

  const fetchOutletData = async () => {
    const payload = { is_cms: 1 };
    const fetchOutlet = OutletApi.getOutlet(payload);
    const response = await fetchOutlet;
    const list = [];
    response.data.map(item => list.push({
      id: item.id_cabang,
      name: item.cabang_name,
    }));
    return list;
  };

  const fetchDepartmentList = async () => {
    const response = await getDepartemen();
    const initList = [{ value: '', name: t('placeholder.select', { ns: 'translation' }) + ' ' + t('label.department', { ns: 'translation' }) }];
    const list = [];
    response.data.map(item => list.push({
      value: item.id,
      name: item.name,
    }));
    const newList = [...initList, ...list];
    return newList;
  };

  const fetchCategoryList = async ({
    pageIndex: mainPageIndex, pageSize: mainPageSize, sortAccessor, sortDirection,
  }) => {
    setTableLoading(true);

    try {
      const payload = {
        limit: mainPageSize,
        page: mainPageIndex + 1,
        active_menu_status: activeMenuStatus || '11',
        tanpa_kategori_order: 'DESC',
        search: filterValue,
      }

      if (Boolean(sortAccessor) && Boolean(sortDirection)) {
        payload.order_by = sortAccessor;
        payload.order_type = sortDirection;
      }

      if (Boolean(filterBranch)) {
        payload.id_outlet = filterBranch;
      }

      const { data: categoryList, meta: { total } } = await productApi.getKategoriProdukV11(payload);

      setTableData(categoryList);
      setFilteredTable(categoryList);
      setTotalTableData(total);
      setPageIndex(mainPageIndex);
      if (mainPageSize) setPageSize(mainPageSize);
    } catch (err) {
      addToast({
        title: t('toast.error', { ns: 'translation' }),
        description: catchError(err),
        variant: 'failed',
        position: 'top-right',
        dismissAfter: 3000,
      });
    } finally {
      setTableLoading(false);
    }
  };

  const refetchTable = () => {
    fetchCategoryList({ pageIndex, pageSize });
  };

  const fetchData = async () => {
    try {
      const outletData = await fetchOutletData();
      const departmentData = await fetchDepartmentList();

      setOutletList(outletData);
      setDepartmentList(departmentData);
    } catch (e) {
      addToast({
        title: t('toast.error', { ns: 'translation' }),
        description: t('toast.data.error'),
        variant: 'failed',
        position: 'top-right',
      });
    } finally {
      setTableLoading(false);
      setLoading(false);
    }
  };

  const getSelectedOutlet = () => {
    const dataOutlet = filterBranch === '' ? idCabang : filterBranch;
    const filterOutlet = outletList.filter(x => x.id === dataOutlet);
    const selected = filterOutlet.map(x => x.id);
    setForm({ ...form, outlets: selected });
  };

  const fetchProductList = async ({ query, index }) => {
    const payload = {
      per_page: 10,
      page: index !== undefined ? index : page,
      item_type: 1,
      path: ['stock', 'multiunit', 'detail'],
      default_unit: 0,
    };

    if (query) {
      Object.assign(payload, { search: query });
    }

    if (!isMoreProduct && index === undefined) return;

    setLoadProduct(true);
    const response = await productApi.getProduct(payload);
    let products = [];
    if (query !== undefined && (index === 0 || page === 0)) {
      products = response.data.map(item => ({
        value: item.id,
        label: item.name,
        variants: item.variants,
        category: item.categories.length > 0 ? item.categories[0].id : null,
        status: item.categories.length > 0 ? item.categories[0].status : null,
      }));
    } else {
      products = [
        ...productList,
        ...response.data.map(item => ({
          value: item.id,
          label: item.name,
          variants: item.variants,
          category: item.categories.length > 0 ? item.categories[0].id : null,
          status: item.categories.length > 0 ? item.categories[0].status : null,
        })),
      ];
    }
    setProductList(products);
    setPage(response.meta.page.current_page + 1);
    setMoreProduct(products.length < response.meta.page.total);
    setLoadProduct(false);
  };

  const remormatSelectedItems = (selectedItems) => {
    let dataItem = [];
    dataItem = selectedItems.length > 0
      ? selectedItems.map(item => ({
        value: item.id,
        label: item.name,
        variants: item.variants !== undefined || item.variants !== null ? item.variants : [],
      }))
      : [];
    return [...dataItem];
  };

  const fetchDetailCategory = async (id) => {
    const payload = { category_id: id };
    if (!!filterBranch && !Number.isNaN(filterBranch)) {
      Object.assign(payload, { outlet_id: filterBranch });
    }

    try {
      const { data: categoryDetail } = await productApi.getDetailKategoriV11(payload);
      const selectedProducts = remormatSelectedItems(categoryDetail.selected_items);
      const idProducts = selectedProducts.length > 0
        ? selectedProducts.map(item => item.value)
        : [];

      setForm({
        ...form,
        id: categoryDetail.id,
        name: categoryDetail.name,
        order: categoryDetail.sequence,
        status_category_item: categoryDetail.active_status,
        id_department: categoryDetail.departement_id === null ? '' : categoryDetail.departement_id,
        isShown: String(categoryDetail.status) === '1',
        outlets: categoryDetail.outlets.map(item => item.outlet_id),
        id_products: idProducts,
        products: selectedProducts,
      });
    } catch (error) {
      addToast({
        title: t('toast.error', { ns: 'translation' }),
        description: t('toast.data.error'),
        variant: 'failed',
        position: 'top-right',
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchMarketplaceAggregator = async (data, providerId) => {
    const payload = {
      name: data.name,
      sequence: Number(data.order),
      majoo_category_id: Number(data.id),
      provider_category_id: null,
      majoo_outlet_id: data.outlets.map(item => Number(item)),
      provider_id: providerId,
    };
    await marketPlace.putCategory(payload);
  };

  const fetchUpdateCategory = async (data) => {
    const payload = {
      id_category_item: data.id ? data.id : form.id,
      category_item_name: data.name,
      category_item_sequence: data.order,
      departement: data.id_department,
      active_menu_status: data.isShown ? 1 : 0,
      outlets: userAccess === 'Manager' && type === 'edit' ? [filterBranch] : data.outlets,
    };
    setLoading(true);
    try {
      if (type === 'add') {
        const createCategory = await productApi.createKategoriProduk(payload);
        if (createCategory.status) {
          addToast({
            title: t('toast.success', { ns: 'translation' }),
            description: (
              <Trans t={t} i18nKey="toast.save.successAdd">
                Kategori
                {' '}
                <b>{{ category: data.name }}</b>
                {' '}
                berhasil ditambakan
              </Trans>
            ),
            variant: 'success',
            position: 'top-right',
          });
        } else {
          addToast({
            title: t('toast.error', { ns: 'translation' }),
            description: (
              <Fragment>
                {createCategory.msg}
              </Fragment>
            ),
            variant: 'failed',
            position: 'top-right',
          });
        }
      } else if (type === 'edit') {
        const updateCategory = await productApi.updateKategoriProduk(payload);
        if (updateCategory.status) {
          await fetchMarketplaceAggregator(data, MARKETPLACE_PROVIDER_ID.GRAB);
          await fetchMarketplaceAggregator(data, MARKETPLACE_PROVIDER_ID.GOFOOD);
          addToast({
            title: t('toast.success', { ns: 'translation' }),
            description: updateCategory.msg,
            variant: 'success',
            position: 'top-right',
          });
        } else {
          addToast({
            title: t('toast.error', { ns: 'translation' }),
            description: updateCategory.msg,
            variant: 'failed',
            position: 'top-right',
          });
        }
      } else if (type === 'product') {
        const newItem = data.itemAdd.length > 0 ? data.itemAdd.map(item => item.value) : [];
        const idItemAdd = [...data.id_products, ...newItem];
        const payloadItem = {
          id_item_add: idItemAdd.length > 0 ? idItemAdd.join(',') : '0',
          id_item_delete: data.itemDelete.length > 0 ? data.itemDelete.join(',') : '0',
        };
        const payloadProduct = { ...payload, ...payloadItem };
        const updateItem = await productApi.updateKategoriProdukItem(
          payloadProduct,
        );
        if (updateItem.status) {
          addToast({
            title: t('toast.success', { ns: 'translation' }),
            description: (
              <Trans t={t} i18nKey="toast.save.successEdit">
                Kategori
                {' '}
                <b>{{ category: data.name }}</b>
                {' '}
                berhasil diubah
              </Trans>
            ),
            variant: 'success',
            position: 'top-right',
          });
        } else {
          addToast({
            title: t('toast.error', { ns: 'translation' }),
            description: (
              <Trans t={t} i18nKey="toast.save.errorEdit">
                Kategori
                {' '}
                <b>{data.name}</b>
                {' '}
                gagal diubah
              </Trans>
            ),
            variant: 'failed',
            position: 'top-right',
          });
        }
      }
    } catch (error) {
      addToast({
        title: t('toast.error', { ns: 'translation' }),
        description:
          type === 'add'
            ? (
              <Trans t={t} i18nKey="toast.save.errorAdd">
                Kategori
                {' '}
                <b>{data.name}</b>
                {' '}
                gagal ditambakan
              </Trans>
            )
            : (
              <Trans t={t} i18nKey="toast.save.errorEdit">
                Kategori
                {' '}
                <b>{data.name}</b>
                {' '}
                gagal diubah
              </Trans>
            ),
        variant: 'failed',
        position: 'top-right',
      });
    } finally {
      setLoading(false);
      setOpen(false);
      setProductModal(false);
      fetchData();
      fetchProductList({ query: '', index: 0 });
      setForm(initForm);
      refetchTable();
    }
  };

  const fetchDeleteCategory = async (id, name) => {
    setLoading(true);
    const payload = { id_category_item: id };
    try {
      const deleteCategory = productApi.deleteKategoriProduk(payload);
      const response = await deleteCategory;
      if (response.status) {
        fetchData();
        addToast({
          title: t('toast.success', { ns: 'translation' }),
          description: response.msg,
          variant: 'success',
          position: 'top-right',
        });
      } else {
        setIsFailedDeleteModal(true);
      }
    } catch (error) {
      addToast({
        title: t('toast.error', { ns: 'translation' }),
        description: catchError(error),
        variant: 'failed',
        position: 'top-right',
      });
    } finally {
      setLoading(false);
      setForm(initForm);
      setDeleteModal(false);
      refetchTable();
    }
  };

  const handleChangeModal = (action) => {
    if (action === 'edit') {
      setOpen(true);
    } else if (action === 'product') {
      setProductModal(true);
    } else if (action === 'delete') {
      setDeleteModal(true);
    }
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      fetchCategoryList({ pageIndex, pageSize });
    }, 500);
    return () => clearTimeout(timer);
  }, [filterValue, activeMenuStatus, filterBranch]);

  useEffect(() => {
    if (isLoading) {
      showProgress();
    } else {
      hideProgress();
    }
  }, [isLoading]);

  useEffect(() => {
    if (form.id !== '') handleChangeModal(type);
  }, [form.id, type]);

  useEffect(() => {
    fetchData();
  }, [currentLang])

  const onChangeRowMenu = (id, action) => {
    if (action === 'edit') {
      setType(action);
      fetchDetailCategory(id);
      setLoading(true);
    } else if (action === 'delete') {
      if (userAccess === USER_ACCESS.MANAGER) {
        addToast({
          title: t('toast.error', { ns: 'translation' }),
          description: t('toast.delete.errorUser'),
          variant: 'failed',
          position: 'top-right',
        });
      } else {
        setType(action);
        const findCategory = tableData.find(item => item.id === id);
        setForm({ ...form, id, name: get(findCategory, 'name') });
      }
    } else if (action === 'product') {
      setType(action);
      fetchDetailCategory(id);
    }
  };

  const handleTableSearch = (value) => {
    setFilterValue(value);
  };

  const onChangeTableStatus = (data) => {
    switch (data) {
      case 'all':
        setActiveMenuStatus(ACTIVE_MENU_STATUS.ALL);
        break;
      case '1':
        setActiveMenuStatus(ACTIVE_MENU_STATUS.TAMPIL);
        break;
      case '0':
        setActiveMenuStatus(ACTIVE_MENU_STATUS.TIDAK_TAMPIL);
        break;
      default:
        return;
    }
  };

  const handleInitForm = () => {
    setForm(initForm);
  };

  const showModal = (action) => {
    if (action === 'delete') {
      setDeleteModal(!isDeleteModal);
    } else if (action === 'product') {
      setProductModal(!isProductModal);
      handleInitForm();
    }
  };

  const handleShowForm = (isCreate) => {
    if (isCreate) {
      setType('add');
      getSelectedOutlet();
      if (form.outlets) setOpen(true);
    } else {
      setOpen(!isOpen);
      handleInitForm();
    }
  };

  const handleSubmit = (data) => {
    fetchUpdateCategory(data);
  };

  const handleDelete = () => {
    setCategoryName(form.name);
    fetchDeleteCategory(form.id, form.name);
  };

  const handleFailedDelete = () => {
    setIsFailedDeleteModal(false);
  };

  const responsivePaper = {
    '@sm': {
      padding: 0,
      backgroundColor: 'transparent',
      boxShadow: 'none',
    },
    '@md': {
      padding: '$spacing-05',
      backgroundColor: '$white',
      boxShadow: '0px 2px 12px #00000014',
    },
  };

  return (
    <Flex
      direction="column"
      css={{ gap: '$comfortable', '@md': { marginBottom: '28px' } }}
    >
      <Paper css={responsivePaper}>
        <Box css={{ '@sm': { height: 'auto' }, '@md': { height: 50 } }}>
          <Flex justify="between" css={{ '@md': { padding: '$spacing-03 0' } }}>
            <FavoriteWrapper css={{ '@md': { marginBottom: '$spacing-03' } }}>
              <Heading
                heading="pageTitle"
              >
                {t('header.title', 'Daftar Kategori')}
              </Heading>
              <TooltipGuidance />
            </FavoriteWrapper>
            {(isCanCreate || (isCanView && !isWhitelistMerchant)) && !isMobile && (
              <Button
                leftIcon={<PlusOutline color="currentColor" />}
                size="sm"
                onClick={() => handleShowForm(true)}
              >
                {t('header.add', 'Tambah Kategori')}
              </Button>
            )}
          </Flex>
        </Box>
        <BannerText />
        <Separator
          css={{ '@sm': { display: 'none' }, '@md': { display: 'block', mt: '$spacing-06' } }}
        />
        <FilterSection
          isMobile={isMobile}
          onChangeTableStatus={onChangeTableStatus}
          handleTableSearch={handleTableSearch}
          handleShowForm={() => handleShowForm(true)}
          t={t}
          isCanCreate={(isCanCreate || (isCanView && !isWhitelistMerchant))}
        />
        <Separator css={{ display: 'block' }} />
        <Box css={{ mt: '$spacing-05' }}>
          <Table
            key={tempResetKey}
            fetchData={fetchCategoryList}
            columns={tableColumn((id, action) => onChangeRowMenu(id, action), t, menuPrivilege)}
            data={filteredTable}
            isLoading={isTableLoading}
            totalData={totalTableData}
            searchQuery={filterValue}
          />
        </Box>
        {
          isOpen
          && (
            <DetailForm
              isOpen={isOpen}
              type={type}
              onOpenChange={handleShowForm}
              isMobile={isMobile}
              outletList={outletList}
              departmentList={departmentList}
              formCategory={form}
              onSubmit={data => handleSubmit(data)}
              handleInitForm={handleInitForm}
              t={t}
              isCanCreate={isCanCreate || isCanUpdate || (isCanView && !isWhitelistMerchant)}
            />
          )
        }
        <DeleteModal
          isOpen={isDeleteModal}
          onOpenChange={() => showModal('delete')}
          onDelete={handleDelete}
          isMobile={isMobile}
          formCategory={form}
          handleInitForm={handleInitForm}
          t={t}
        />
        <FailedDeleteModal
          isOpen={isFailedDeleteModal}
          onDelete={handleFailedDelete}
          isMobile={isMobile}
          categoryName={categoryName}
          handleInitForm={handleInitForm}
          t={t}
        />
        <ProductModal
          isOpen={isProductModal}
          onOpenChange={() => showModal('product')}
          isMobile={isMobile}
          productList={productList}
          formCategory={form}
          onSubmit={data => handleSubmit(data)}
          fetchProductList={fetchProductList}
          pageIndex={page}
          isLoadProduct={isLoadProduct}
          isMoreProduct={isMoreProduct}
          handleInitForm={handleInitForm}
          t={t}
        />
      </Paper>
    </Flex>
  );
};

DaftarKategori.propTypes = {
  filterBranch: PropTypes.string,
  showProgress: PropTypes.func.isRequired,
  hideProgress: PropTypes.func.isRequired,
  idCabang: PropTypes.string,
  userAccess: PropTypes.string,
  menuPrivilege: PropTypes.shape({
    isCanCreate: PropTypes.bool.isRequired,
    isCanDelete: PropTypes.bool.isRequired,
    isCanUpdate: PropTypes.bool.isRequired,
    isCanView: PropTypes.bool.isRequired,
    isCanVoid: PropTypes.bool.isRequired,
    isWhitelistMerchant: PropTypes.bool.isRequired,
  }).isRequired,
};

DaftarKategori.defaultProps = {
  filterBranch: '',
  idCabang: '',
  userAccess: '',
};

const mapStateToProps = state => ({
  userAccess: state.accountInfo.accountInfoResult.hak_akses,
  menuPrivilege: { ...state.layouts.detailPrivilege, isWhitelistMerchant: state.layouts.isWhitelistMerchant },
});

export default connect(mapStateToProps)(
  CoreHOC(DaftarKategori),
);
