import React, { useContext } from 'react';
import {
    <PERSON><PERSON>, <PERSON>H<PERSON>per, Heading,
} from '@majoo-ui/react';
import update from 'immutability-helper';
import { <PERSON><PERSON><PERSON>, LabelWrapper, ContentWrapper } from '../Form.styles';
import RowList from './RowList';
import { createDefaultProductSubExtra, FormContext } from '../utils';
import { useTranslationHook } from '../../lang.utils';

const ProductSubExtra = ({ formData, onChange, parentScrollContainerRef }) => {
    const { LANG_DATA } = useTranslationHook();

    const { errors } = useContext(FormContext);
    const errorData = (errors && errors.productSubExtras && !Array.isArray(errors.productSubExtras)) ? errors.productSubExtras : null;

    const handleAddData = () => {
        const { productSubExtras } = formData;

        onChange([...productSubExtras, createDefaultProductSubExtra()]);
    };

    const handleChangeRowData = ({ key, value, index }) => {
        const { productSubExtras } = formData;

        let newValue = { [key]: { $set: value } };

        if (key === 'materialData') {
            newValue = {
                itemId: { $set: value.id },
                capitalPrice: { $set: String(value.basicPrice) },
            };
        } else if (key === 'type') {
            newValue = {
                $set: {
                    ...createDefaultProductSubExtra(),
                    type: value,
                },
            };
        }

        const newData = update(productSubExtras, {
            [index]: newValue,
        });

        onChange(newData);
    };

    const handleDeleteRowData = (index) => {
        const { productSubExtras } = formData;

        const newData = productSubExtras.filter((x, i) => (i !== index));

        onChange(newData);
    };

    return (
        <RowBox>
            <LabelWrapper css={{ mb: '$spacing-03' }}>
                <Heading
                    css={{ display: 'flex' }}
                    className="form-label"
                    htmlFor="productSubExtras"
                    id="productSubExtras"
                    variant="required"
                    heading="sectionSubTitle"
                >
                    {LANG_DATA.FORM_PRODUCT_SUB_EXTRA_LABEL}
                    {' '}
                    <p style={{ color: 'red' }}>*</p>
                </Heading>
            </LabelWrapper>
        <ContentWrapper css={{ background: '$bgGray', padding: '$spacing-05' }}>
            {errorData && (
                <FormHelper
                    error
                    css={{ marginBottom: '$spacing-03 !important' }}
                >
                    {errorData.message}
                </FormHelper>
            )}
            {parentScrollContainerRef.current && (
                <RowList {...{ formData, parentScrollContainerRef }} onChangeRowData={handleChangeRowData} onDeleteRowData={handleDeleteRowData} />
            )}
            <Button
                size="md"
                type="button"
                buttonType="secondary"
                onClick={() => { handleAddData(); }}
                css={{ width: '100%' }}
            >
                {LANG_DATA.FORM_PRODUCT_SUB_EXTRA_ADD_LABEL}
            </Button>
        </ContentWrapper>
        </RowBox>
    );
};

export default ProductSubExtra;
