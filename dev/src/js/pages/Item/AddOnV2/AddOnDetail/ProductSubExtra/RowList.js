import React, {
    useMemo, useContext, useCallback, useState, useRef,
} from 'react';
import {
    Accordion, AccordionContent, AccordionItem, AccordionTrigger, Card,
    Box, Flex, InputRadio, InputRadioGroup, Paragraph, Separator, FormHelper, Tooltip,
} from '@majoo-ui/react';
import { foundations } from '@majoo-ui/core';
import { RemoveOutline, CircleInfoOutline } from '@majoo-ui/icons';
import { get } from 'lodash';
import {
    List, AutoSizer, WindowScroller, CellMeasurer, CellMeasurerCache,
} from 'react-virtualized';
import { createStaticDataProductSubExtraType, FormContext, PRODUCT_SUB_EXTRA_TYPE } from '../utils';
import { isMobile as MobileView } from '../../../../../config/config';
import { ConditionalWrapper } from '../../../../../components/wrapper/ConditionalWrapper';
import CreateNew from './CreateNew';
import SelectFromInventory from './SelectFromInventory';
import { useTranslationHook } from '../../lang.utils';
import { colors } from '../../../../../stitches.config';

const { iconPrimary } = foundations.colors;

const RemoveButton = React.memo(({ item, onDeleteRowData }) => (
    <Box
        css={{
            cursor: 'pointer',
            flex: '0 1 0%',
            height: '28px',
            marginLeft: 4,
            marginTop: 10,
            textAlign: 'right',
            '@md': {
                marginTop: 'unset',
                textAlign: 'unset',
                position: 'unset',
                right: 'unset',
                bottom: 'unset',
            },
        }}
        onClick={() => { onDeleteRowData(item); }}
    >
        <RemoveOutline size={24} color={iconPrimary} />
    </Box>
));

const RowList = ({
    formData, onChangeRowData, onDeleteRowData, parentScrollContainerRef,
}) => {
    const { LANG_DATA } = useTranslationHook();
    const isMobile = MobileView.matches;
    const productSubExtras = useMemo(() => formData.productSubExtras, [formData.productSubExtras]);

    const formContext = useContext(FormContext);
    const errors = formContext ? formContext.errors : null;
    const errorData = errors ? get(errors, 'productSubExtras') : null;
    const [isScrolling, setIsScrolling] = useState(false);
    const [accordionOpen, setAccordionOpen] = useState('item-0');
    const staticDataProductSubExtraType = useMemo(() => createStaticDataProductSubExtraType(LANG_DATA), [LANG_DATA]);
    const listRef = useRef(null);
    const cache = useMemo(() => new CellMeasurerCache({
        fixedWidth: true,
        defaultHeight: 221,
        minHeight: 50,
        keyMapper: index => productSubExtras[index].id,
    }), [productSubExtras]);

    const updateRowHeight = useCallback((index) => {
        cache.clear(index, 0);
        if (listRef.current) {
            listRef.current.recomputeRowHeights(index);
        }
    }, [cache]);

    // TODO: move handler for onChangeRowData to this functions instead of function createion in props
    // const _onChangeRawData = () => {
    // }

    const rowRenderer = useCallback(({
        key, index, parent, style,
    }) => (
        <CellMeasurer
            key={key}
            cache={cache}
            parent={parent}
            columnIndex={0}
            rowIndex={index}
        >
            {({ measure, registerChild }) => (
                <div ref={registerChild} style={style}>
                    {isScrolling ? (
                        <div style={{ height: cache.getHeight(index, 0) }}>Loading...</div>
                    ) : (
                    <ConditionalWrapper
                        condition={isMobile}
                        wrapper={child => (
                            <Accordion
                                onValueChange={(val) => {
                                    const accValue = accordionOpen ? accordionOpen.split('-')[1] : null;
                                    setAccordionOpen(val);
                                    setTimeout(() => {
                                        if(accValue) updateRowHeight(accValue);
                                        updateRowHeight(index);
                                    }, 0);
                                }}
                                value={accordionOpen}
                                key={productSubExtras[index].id}
                                type="single"
                            >
                                <AccordionItem value={`item-${index}`}>
                                    <AccordionTrigger hasContent arrowColor="#404545">
                                        <Flex direction="column" css={{ gap: 10, padding: '18px 16px' }}>
                                            <Paragraph color="primary" paragraph="shortContentBold">
                                                Sub Ekstra
                                                {' '}
                                                {index + 1}
                                            </Paragraph>
                                            <FormHelper error>
                                                {errorData && errorData[index] && `Mohon lengkapi data Sub Ekstra ${index + 1}`}
                                            </FormHelper>
                                        </Flex>
                                    </AccordionTrigger>
                                    <Separator />
                                    <AccordionContent>
                                        <Box css={{ mt: 20, padding: '0 12px' }}>
                                            {child}
                                        </Box>
                                    </AccordionContent>
                                </AccordionItem>
                            </Accordion>
                        )}
                        // TODO: need to revisit if in future needs
                        // onLoad={() => setTimeout(() => updateRowHeight(index), 0)}
                    >
                        <Box key={productSubExtras[index].id}>
                            <Box css={{
                                    display: 'flex',
                                    width: '100%',
                                    alignItems: 'center',
                                    marginBottom: '1em',
                                    position: 'relative',
                                    '@md': {
                                        position: 'unset',
                                    },
                                }}
                            >
                                <InputRadioGroup
                                    direction={{ '@sm': 'column', '@md': 'row' }}
                                    outlined
                                    gap={{ '@sm': 3, '@md': 5 }}
                                    defaultValue={productSubExtras[index].type}
                                    css={{ flex: 1, '& > label': { display: 'contents' } }}
                                    onValueChange={(val) => { onChangeRowData({ key: 'type', value: val, index }); }}
                                >
                                    {staticDataProductSubExtraType.map(staticData => (
                                        <InputRadio
                                            key={staticData.id}
                                            css={{ flex: 1 }}
                                            value={staticData.id}
                                            label={(
                                                <Flex
                                                    gap={3}
                                                    align="center"
                                                    css={{
                                                        '& svg': {
                                                            marginTop: '$spacing-01',
                                                        },
                                                    }}
                                                >
                                                    {staticData.label}
                                                    <Tooltip
                                                        side="top"
                                                        label={staticData.tooltip}
                                                        css={{ maxWidth: '200px' }}
                                                    >
                                                        <CircleInfoOutline size={14} color={colors.gray700} />
                                                    </Tooltip>
                                                </Flex>
                                            )}
                                        />
                                    ))}
                                </InputRadioGroup>
                                {!isMobile && <RemoveButton item={index} onDeleteRowData={onDeleteRowData} />}
                            </Box>
                            {productSubExtras[index].type === PRODUCT_SUB_EXTRA_TYPE.CREATE_NEW
                                ? (
                                    <CreateNew
                                        data={productSubExtras[index]}
                                        index={index}
                                        onChange={(keyV, val, idx) => { onChangeRowData({ key: keyV, value: val, index: idx }); }}
                                    />
                                )
                                : null}
                            {productSubExtras[index].type === PRODUCT_SUB_EXTRA_TYPE.SELECT_FROM_INVENTORY
                                ? (
                                    <SelectFromInventory
                                        data={productSubExtras[index]}
                                        extrasData={productSubExtras}
                                        index={index}
                                        onChange={(keyV, val, idx) => { onChangeRowData({ key: keyV, value: val, index: idx }); }}
                                    />
                                )
                                : null}
                            {isMobile && <RemoveButton item={index} onDeleteRowData={onDeleteRowData} />}
                            <Separator css={{ display: 'block', marginTop: '2em', '@md': { marginBottom: '2em' } }} />
                        </Box>
                    </ConditionalWrapper>
                    )}
                </div>
            )}
        </CellMeasurer>
    ), [productSubExtras, errorData, staticDataProductSubExtraType, isMobile, onChangeRowData, onDeleteRowData, isScrolling, cache]);


    return (
        <React.Fragment>
            <ConditionalWrapper
                condition={isMobile}
                wrapper={child => (
                    <Card color="dark" css={{ color: '$text-primary', padding: 'unset', mb: '$spacing-05' }}>
                        {child}
                    </Card>
                )}
            >
                <WindowScroller scrollElement={parentScrollContainerRef.current}>
                    {({
                        height, isScrolling: windowIsScrolling, scrollTop, onChildScroll, registerChild,
                    }) => (
                        <AutoSizer disableHeight>
                            {({ width }) => (
                                <Box ref={registerChild}>
                                    <List
                                        ref={listRef}
                                        autoHeight
                                        rowCount={productSubExtras.length}
                                        height={height}
                                        width={width}
                                        overscanRowCount={2}
                                        rowRenderer={rowRenderer}
                                        scrollTop={scrollTop}
                                        deferredMeasurementCache={cache}
                                        rowHeight={cache.rowHeight}
                                        isScrolling={windowIsScrolling}
                                        onScroll={onChildScroll}
                                        onScrollStart={() => setIsScrolling(true)}
                                        onScrollEnd={() => setIsScrolling(false)}
                                        // TODO: need to revisit if in future needs, currently causing heavy processing
                                        // onRowsRendered={({ startIndex, stopIndex }) => {
                                        //     for (let i = startIndex; i <= stopIndex; i += 1) {
                                        //         cache.clear(i, 0);
                                        //     }
                                        // }}
                                    />
                                </Box>
                            )}
                        </AutoSizer>
                    )}
                </WindowScroller>
            </ConditionalWrapper>
        </React.Fragment>
    );
};

export default React.memo(RowList);
