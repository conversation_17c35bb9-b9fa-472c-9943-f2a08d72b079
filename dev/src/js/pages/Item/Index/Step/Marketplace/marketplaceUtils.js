import React from 'react';
import {
  Box, Flex, Text, Tooltip,
} from '@majoo-ui/react';
import { CircleInfoOutline } from '@majoo-ui/icons';
import { foundations } from '@majoo-ui/core';
import { EditorState, ContentState } from 'draft-js';
import htmlToDraft from 'html-to-draftjs';

const { gray150, gray400 } = foundations.colors;

const FORM_ID = 'marketplace-page';

const generateFormData = (formData = null) => ({
  id: formData && formData.aggregator.detail.length ? formData.aggregator.detail[0].id : null,
  price: formData && formData.aggregator.detail.length ? (formData.aggregator.detail[0].price || 0) : 0, // default price from step price and unit,
  is_favourite: formData && formData.aggregator.detail.length ? formData.aggregator.detail[0].is_favourite : 0,
  seq: formData && formData.aggregator.detail.length ? formData.aggregator.detail[0].seq : 0, // temp,
  status: formData && formData.aggregator.detail.length ? formData.aggregator.detail[0].status : false,
  minimum_stock: formData && formData.aggregator.detail.length ? formData.aggregator.detail[0].minimum_stock : 0,
  weight: formData && formData.aggregator.detail.length ? formData.aggregator.detail[0].weight : 1000, // base on PRD
  weight_unit: formData && formData.aggregator.detail.length ? formData.aggregator.detail[0].weight_unit : "gram", // base on PRD
  is_manual_price: formData && formData.aggregator.detail.length ? formData.aggregator.detail[0].is_manual_price : 0,
  is_minimum_stock: formData && formData.aggregator.detail.length ? formData.aggregator.detail[0].minimum_stock === 0 ? 0 : 1 : 0,
  item_description_long: formData && formData.aggregator.detail.length ? formData.aggregator.detail[0].item_description_long : null,
});

const StyledFlex = ({ css, children }) => (
  <Flex
    css={{
      ...css,
      '@sm': {
        marginTop: 16,
        flexDirection: 'column',
        gap: 16,
      },
      '@md': {
        marginTop: 24,
        flexDirection: 'row',
        gap: 0,
      },
    }}
  >
    {children}
  </Flex>
);

const StyledLabelBox = ({ children }) => (
  <Box
    css={{
      width: '100%',
      '@md': {
        width: '25%',
      },
    }}
  >
    {children}
  </Box>
);

const StyledFieldBox = ({ children }) => (
  <Box
    css={{
      width: '100%',
      '@md': {
        width: '75%',
      },
    }}
  >
    {children}
  </Box>
);

const StyledHeaderLabelText = ({ children }) => (
  <Text color="primary" css={{ fontSize: 14, fontWeight: 600 }}>
    {children}
  </Text>
);

const StyledTooltipWithIcon = ({ label, withClick }) => (
  <Tooltip
    label={label}
    withClick={withClick}
  >
    <CircleInfoOutline
      size={24}
      color={gray400}
    />
  </Tooltip>
);

const Divider = ({ mdMargin }) => (
  <Box
    css={{
      diplay: 'none',
      '@md': {
        display: 'block',
        borderTop: `1px solid ${gray150}`,
        width: '100%',
        ...mdMargin,
      },
    }}
  />
);

const LONG_DESCRIPTION_MAX_LENGTH = 5000;

const convertToEditorState = (_content) => {
  if (_content === null || _content === '' || _content === undefined) {
    return EditorState.createEmpty();
  } else {
    const blockContent = htmlToDraft(_content);
    const contentState = ContentState.createFromBlockArray(blockContent.contentBlocks);
    return EditorState.createWithContent(contentState);
  }
};

export {
  StyledFlex,
  StyledLabelBox,
  StyledFieldBox,
  StyledHeaderLabelText,
  StyledTooltipWithIcon,
  convertToEditorState,
  LONG_DESCRIPTION_MAX_LENGTH,
  Divider,
  FORM_ID,
  generateFormData,
};
