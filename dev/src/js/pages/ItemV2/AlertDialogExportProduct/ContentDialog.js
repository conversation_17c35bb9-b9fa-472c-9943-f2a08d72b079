import React, { useContext } from 'react';
import {
    AlertDialog,
} from '@majoo-ui/react';
import PageContext from '../PageContext';

export const EXPORT_ALERT_DIALOG_TYPE = {
    EXPORT_PRODUCT: 'EXPORT_PRODUCT',
    EXPORT_RECIPE: 'EXPORT_RECIPE',
};

const ContentDialog = ({ onClose, isOpen, type }) => {
    const { onExportProduct, isInMobileView, translationData, onExportRecipe } = useContext(PageContext);
    const { LANG_DATA, TransComponent } = translationData;

    const isExportProduct = type === EXPORT_ALERT_DIALOG_TYPE.EXPORT_PRODUCT;

    return (
        <AlertDialog
            onCancel={onClose}
            isMobile={isInMobileView}
            onConfirm={() => {
                if (isExportProduct) {
                    onExportProduct();
                } else {
                    onExportRecipe();
                }
                onClose();
            }}
            open={isOpen}
            title={isExportProduct ? LANG_DATA.MAIN_DIALOG_EXPORT_TITLE : LANG_DATA.MAIN_DIALOG_EXPORT_RECIPE_TITLE}
            description={(
                <TransComponent i18nKey={isExportProduct ? 'main.dialog.exportDialog.description' : 'main.dialog.exportRecipeDialog.description'}>
                    {' '}
                    <b>{{ type: LANG_DATA.MAIN_TITLE }}</b>
                    {' '}
                    akan diekspor dan disimpan di perangkat yang digunakan. Lanjutkan?
                </TransComponent>
            )}
            css={{
                width: isInMobileView ? '100%' : '422px',
            }}
        />
    );
};

export default ContentDialog;
