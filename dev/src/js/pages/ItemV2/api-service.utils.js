import React from 'react';
import queryString from 'query-string';
import {
    uploadForNewAndUpdateProductBulk,
    uploadForNewAndUpdateProductBulkV2,
    uploadProductRecipes,
} from '~/data/upload';
import { ACTIVITY_LOG_TYPE } from '~/components/activityLogDialog/config';
import * as productMultiOutlet from '../../data/multioutlet';
import * as productApi from '../../data/product';
import * as productSelectors from '../../data/product/selectors';
import * as multipriceApi from '../../data/multiprice';
import * as inventoryApi from '../../data/inventories';
import { ITEM_TYPE } from '../../data/product/enum';
import { catchError, currency, getMatchingGroups, ishasProperty } from '../../utils/helper';
import { DISPLAY_FILTER_TYPE, IMPORT_PRODUCT_TYPE } from './enums';
import * as outletApi from '../../data/outlets/index';
import outletSelectors from '../../data/outlets/selectors';
import * as settingApi from '../../data/settings';
import {
    buildGrouppedOutletRecipes,
    buildGrouppedOutletUnits,
    buildGrouppedOutletVariants,
    getOutletName,
} from './utils';
import { getSubscriptionType, isPrimeSubscription } from '../../v2-utils';
import { SUBSCRIPTION_TYPE } from '../../v2-enum';
import DEFAULT_VALUE from './default-value-multi-lang';
import ToastTitle from '../../v2-components/ToastTitle';
import { PRICE_SETTING } from '../Settings/ProductInventory/PurchasePriceSettings';
import { EXPORT_ALERT_DIALOG_TYPE } from './AlertDialogExportProduct/ContentDialog';

export const defaultStateFetchProducts = {
    displayed: DISPLAY_FILTER_TYPE.ALL,
    categoryId: 0,
    pageSize: null,
    pageIndex: 0,
    sortDirection: '',
    sortAccessor: '',
    filterOutlet: '',
    filterKeyword: '',
};

export const defaultRecipeMasterSetting = {
    use_master_recipe: 0,
    use_raw_material_recipe: 1,
};

export const handleFetchProducts = async ({
    addToast,
    setState,
    filterData,
    filterBranch,
    tableState,
    translationData,
}) => {
    const { LANG_DATA } = translationData;
    let product = {
        data: [],
        totalData: 0,
        pageIndex: 0,
    };

    try {
        let state = tableState;

        if (filterData) {
            state = {
                ...state,
                displayed: filterData.display,
                categoryId: filterData.categoryId,
                filterKeyword: filterData.keyword,
                filterOutlet: filterBranch,
            };
        }

        const payload = {
            displayed: state.displayed,
            item_type: ITEM_TYPE.FINISHED,
            category_id: state.categoryId,
            per_page: state.pageLimit || state.pageSize || 10,
            page: state.pageIndex + 1,
            product_type: '0',
            ...(state.sortDirection && {
                order: state.sortDirection,
            }),
            ...(state.sortAccessor && {
                sort: state.sortAccessor,
            }),
            ...(state.filterOutlet && {
                id_outlet: state.filterOutlet,
            }),
            ...(state.filterKeyword && {
                search: state.filterKeyword,
            }),
        };

        const apiResponse = await productApi.getProductV15(payload);
        const {
            data,
            meta: { total, current_page: currentPage },
        } = apiResponse;

        const reformatData = data.map(x => {
            let categoryName = LANG_DATA.LABEL_NO_CATEGORY;

            if (x.categories && x.categories.length > 0) categoryName = x.categories[0].name;

            return {
                ...x,
                categoryName,
                formattedAveragePrice: x.is_variant ? '-' : currency({ value: x.average_price, decimal: true }),
                formattedPurchasePrice: x.is_variant ? '-' : currency({ value: x.purchase_price, decimal: true }),
                formattedSellingPrice: x.is_variant ? '-' : currency({ value: x.selling_price }),
            };
        });

        product = {
            data: reformatData,
            totalData: total,
            pageIndex: currentPage > 0 ? currentPage - 1 : 0,
        };
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: LANG_DATA.MAIN_TOAST_FAILED_GETPRODUCT,
            variant: 'failed',
        });
    } finally {
        setState({
            product,
            loading: false,
        });
    }
};

export const handleFetchCategories = async ({ addToast, setState, lastSavedCategory, LANG_DATA = DEFAULT_VALUE }) => {
    const defaultValue = { name: LANG_DATA.STATUS_ALL_CATEGORY, value: 0 };
    let newState = { categories: [defaultValue] };

    try {
        const res = await productApi.getKategoriProduk({ tanpa_kategori_order: 'asc' });
        let data = [];

        if (res && res.data) {
            data = res.data.map(x => ({
                ...x,
                name: x.category_item_name,
                value: x.id_category_item,
                status: x.active_status,
            }));
        }

        const categories = [defaultValue, ...data];

        newState = { categories };

        if (lastSavedCategory) {
            newState = {
                lastSavedCategory,
                categories,
            };
        }
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: LANG_DATA.MAIN_TOAST_FAILED_GET_CATEGORY,
            variant: 'failed',
        });
    } finally {
        setState(newState);
    }
};

export const handleFetchOutlets = async ({ addToast, setState }) => {
    try {
        const res = await outletApi.getOutletV3({
            is_cms: 1,
        });

        const data = outletSelectors.getOutletOpts(res.data);

        setState({ outlets: data });
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

export const handleFetchProductsGroupParents = async ({ addToast, setState, translationData }) => {
    try {
        const res = await productApi.getProductV13({
            item_type: ITEM_TYPE.PARENT_ONLY,
            per_page: -1,
        });

        const data = productSelectors.reformatGroupParentOptionsV2(res.data, translationData.t);

        setState({ groupParents: data });
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

export const handleFetchUnits = async ({ addToast, setState }) => {
    try {
        const res = await settingApi.fetchUnit();
        if (res.data) {
            const data = res.data.map(x => ({ ...x, value: x.id_satuan, name: x.satuan_name }));
            setState({ units: data });
        }
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

export const handleFetchProductExtra = async ({ addToast, setState, filterBranch, idCabang }) => {
    try {
        let outletId = idCabang;

        if (filterBranch) outletId = filterBranch;

        const res = await productApi.getAddons({ id_outlet: outletId });
        const data = productSelectors.toListObjectAddons(res.data);

        setState({ productExtras: data });
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

export const handleFetchProductExtraV2 = async ({ addToast, setState, productExtras, filterExtra, index, query }) => {
    const payload = {
        ...(query && {
            search: query,
        }),
        per_page: filterExtra.pageSize || 20,
        page: index !== undefined ? index + 1 : filterExtra.pageIndex + 1,
    };

    setState({ loadingExtra: true });

    try {
        if (!filterExtra.hasMoreItems && index === undefined) return;

        const res = await productApi.getAddonsV2(payload);
        const data = productSelectors.toListObjectAddonsV2(res.data);
        const dataExtra = index === 0 || filterExtra.pageIndex === 0 ? data : [...productExtras, ...data];

        setState({
            productExtras: dataExtra,
            filterExtra: {
                ...filterExtra,
                hasMoreItems: dataExtra.length < res.meta.total,
                pageIndex: res.meta.current_page || 0,
                pageSize: res.meta.total,
                totalData: res.meta.total,
            },
        });
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    } finally {
        setState({ loadingExtra: false });
    }
};

export const handleFetchRawMaterials = async ({
    addToast,
    setState,
    filterBranch,
    idCabang,
    showProgress,
    hideProgress,
}) => {
    try {
        showProgress();
        let outletId = idCabang;

        if (filterBranch) outletId = filterBranch;

        const res = await productApi.getProductV13({
            item_type: ITEM_TYPE.RAW_MATERIAL,
            path: 'multiunit',
            per_page: -1,
            id_outlet: outletId,
        });

        let data = productSelectors.reformatRawMaterialOptionsV2(res.data);
        data = data.map(x => ({ ...x, value: x.id }));

        setState({ rawMaterials: data });
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const handleCreateRawMaterial = async ({
    payload,
    addToast,
    createRawMaterialDialogRef,
    onFetchRawMaterials,
}) => {
    try {
        const res = await productApi.createProduct(payload);

        if (!res.status) throw Error(res.msg);

        await onFetchRawMaterials();

        createRawMaterialDialogRef.current.handleHideDialog();
        addToast({
            title: <ToastTitle type="success" />,
            description: (
                <span>
                    Bahan baku <strong>{payload.name}</strong> berhasil ditambah
                </span>
            ),
            variant: 'success',
            position: 'top-right',
            dismissAfter: 3000,
        });
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
            position: 'top-right',
            dismissAfter: 3000,
        });
    }
};

export const handleFetchMasterRecipeDetail = async ({ id, addToast, onSuccess }) => {
    try {
        const res = await inventoryApi.getRecipeMasterDetail(id);
        if (res.data) {
            onSuccess(res.data);
        }
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

export const handleCreateCategory = async ({ payload, createCategoryDialogRef, onFetchCategories, addToast }) => {
    try {
        const { name, categorySequence, outletIds } = payload;
        const createCategoryPayload = {
            category_item_name: name,
            category_item_sequence: categorySequence,
            active_menu_status: 1,
            outlets: outletIds,
        };
        const createCategory = await productApi.createKategoriProduk(createCategoryPayload);

        if (createCategory.status) {
            const lastSavedCategory = createCategory.data.id_category_item;
            await onFetchCategories(lastSavedCategory);
            createCategoryDialogRef.current.handleHideDialog();
        }
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

export const handleFetchMarketplaceProviders = async ({ addToast, setState }) => {
    try {
        const res = await productApi.getMarketplaceProvider();
        if ('data' in res) {
            setState({ marketplaceProviders: res.data });
        }
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

export const handleFetchMarketplaceOutlets = async ({ addToast, setState }) => {
    try {
        const res = await productApi.getOutletMarketplace();
        if ('data' in res) {
            setState({ marketplaceOutlets: res.data });
        }
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

export const handleFetchWebOrderDetail = async ({ outletId, vendor, addToast, setState }) => {
    const payload = {
        id_outlet: outletId,
        vendor,
    };

    let retval = null;

    try {
        const res = await multipriceApi.getMultiPriceDetail(payload);
        if (res.data) {
            retval = { ...res.data, payloadOutletId: outletId };

            setState({ webOrderDetail: retval });
        }
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }

    return retval;
};

export const handleUpdateWebOrder = async ({ addToast, payload, multiPriceDetailRef, setState, vendor }) => {
    try {
        const res = await multipriceApi.updateMultiprice(payload);

        if (!res.status) throw new Error(res.msg);

        const resDetail = await multipriceApi.getMultiPriceDetail({
            id_outlet: payload.id_outlet,
            vendor,
        });

        setState({ webOrderDetail: resDetail.data }, () => {
            multiPriceDetailRef.current.handleHideDialog();

            addToast({
                title: <ToastTitle type="success" />,
                description: res.msg,
                variant: 'success',
                position: 'top-right',
                dismissAfter: 3000,
            });
        });
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

export const handleSaveProduct = async (
    { payload: rawPayload, outlets, addToast, currentProductDetailData, translationData },
    onSuccessCallbackFn = () => {},
) => {
    const { LANG_DATA, TransComponent } = translationData;
    const { formValidation, ...restPayload } = rawPayload;
    try {
        const payload = restPayload;

        const resValidation = await productApi.validateEntryProduct(formValidation);

        if (!resValidation.status && resValidation.data.length > 0) {
            const invalidData = resValidation.data[0];
            let errorMsg = '';

            if (invalidData.validation_errors.length > 0) {
                const error = invalidData.validation_errors[0];
                errorMsg = error.message;
            }

            if (errorMsg) throw errorMsg;
        }

        if (!payload.id) {
            await productMultiOutlet.createMultiOutletProduct(payload);
        } else {
            if (!currentProductDetailData) throw new Error('product detail is required');

            const { id: itemId, ...rest } = payload;
            const newPayload = {
                ...rest,
                id_item: +itemId,
                item_no: currentProductDetailData.no,
            };
            await productMultiOutlet.updateMultiOutletProduct(itemId, newPayload);
        }

        const outletIds = JSON.parse(payload.id_outlet);
        const infoOutletMessage = outletIds.length > 0 ? getOutletName(outlets, outletIds[0]) : '';

        let description = (
            <TransComponent
                i18nKey="toast.activityLog.add.description"
                values={{
                    product_name: payload.name,
                    main_outlet: infoOutletMessage,
                    multiple_outlet_length:
                        outletIds.length > 1 ? LANG_DATA.TOAST_MULTIPLE_SENTENCE(outletIds.length - 1) : '',
                }}
                components={{
                    strong: <b />,
                }}
            />
        );

        if (payload.id) {
            description = (
                <TransComponent
                    i18nKey="toast.activityLog.edit.description"
                    values={{
                        product_name: payload.name,
                        main_outlet: infoOutletMessage,
                        multiple_outlet_length:
                            outletIds.length > 1 ? LANG_DATA.TOAST_MULTIPLE_SENTENCE(outletIds.length - 1) : '',
                    }}
                    components={{
                        strong: <b />,
                    }}
                />
            );
        }

        addToast({
            title: !payload.id ? LANG_DATA.TOAST_ADD_TITLE : LANG_DATA.TOAST_EDIT_TITLE,
            description,
            variant: 'pending',
            position: 'top-right',
            dismissAfter: 3000,
        });

        onSuccessCallbackFn();
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
            values: {
                ...(catchError(err) === 'sku already use' && {
                    duplicate_SKU: formValidation.datas[0].form.multiunits[0].unit_sku,
                }),
            },
        });
    }
};

const processCheckingTransactionSNBN = async itemId => {
    const checkTransactionPayload = {
        item_id: itemId,
    };
    const response = await productApi.checkTransaction(checkTransactionPayload);

    return response.data;
};

export const handleCheckTransactionSNBN = async ({ itemId, addToast, setState }) => {
    try {
        const SNBNTransactionData = await processCheckingTransactionSNBN(itemId);

        setState({
            SNBNTransactionData,
        });
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: `Check Transaction SN BN - ${catchError(err)}`,
            variant: 'failed',
            dismissAfter: 3000,
        });
    }
};

const processFetchingMarketplaceProducts = async itemId => {
    const payload = {
        item_id: itemId,
    };

    const res = await productApi.getMarketplaceV15(payload);

    let marketplaceProducts = [];
    if (res.data) {
        if (!Array.isArray(res.data.detail) && ishasProperty(res.data.detail, 'weborder')) {
            marketplaceProducts = res.data.detail.weborder;
        }
    }

    return marketplaceProducts;
};

export const handleFetchProductDetail = async ({ productId, addToast, filterBranch }, onSuccessCallbackFn) => {
    try {
        const responses = await Promise.all([
            productApi.getProductDetailV15(productId, {
                ...(filterBranch && { id_outlet: filterBranch }),
            }),
            processFetchingMarketplaceProducts(productId),
            processCheckingTransactionSNBN(productId),
        ]);

        const [resProduct, marketplaceProducts, SNBNTransactionData] = responses;

        onSuccessCallbackFn({
            rawProductData: resProduct.data,
            marketplaceProducts,
            SNBNTransactionData,
        });
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

const getOutletGroupSettingDetail = async itemId => {
    const response = await productApi.getOutletGroupSettingDetail(itemId);
    return response.data;
};

export const handleFetchPerOutletSettingDetail = async ({ productId, addToast }, onSuccessCallbackFn) => {
    try {
        const resData = await getOutletGroupSettingDetail(productId);
        onSuccessCallbackFn(resData);
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

export const handleDeleteProduct = async ({ productId, productName, addToast, onSuccessfully, translationData }) => {
    const { TransComponent } = translationData;
    try {
        const res = await productApi.deleteProduct(Number(productId));

        if (!res.status) throw res.msg;

        addToast({
            title: <ToastTitle type="success" />,
            description: (
                <TransComponent i18nKey="main.toast.succeedDeleteProduct" values={{ name: productName }}>
                    Produk <b>{productName}</b> berhasil dihapus
                </TransComponent>
            ),
            variant: 'success',
            position: 'top-right',
            dismissAfter: 3000,
        });

        onSuccessfully();
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

export const handleExportProduct = async ({
    filterData,
    filterBranch,
    addToast,
    idCabang,
    alertDialogInfoExport,
    lang,
    handleSetExportBannerState,
}) => {
    try {
        const payload = {
            displayed: filterData.display,
            outlet_id: !filterBranch ? Number(idCabang) : Number(filterBranch),
            search: filterData.keyword,
            ...(!filterData.categoryId ? {} : { category_id: filterData.categoryId }),
            use_variants: Boolean(getSubscriptionType() === SUBSCRIPTION_TYPE.ENTERPRISE),
            lang,
        };

        const params = `?${queryString.stringify(payload)}`;

        await productApi.productsDownloadV3(params);
        alertDialogInfoExport.current.handleShowDialog();
        handleSetExportBannerState({ open: true });
    } catch (error) {
        if (error.cause && error.cause.status.code) {
            if (error.cause.status.code === 42200001) {
                alertDialogInfoExport.current.handleShowDialog(error.cause.status.message);
                return;
            }
        }

        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(error),
            variant: 'failed',
        });
    }
};

export const handleExportRecipe = async ({
    filterBranch,
    addToast,
    idCabang,
    alertDialogInfoExport,
    handleSetExportBannerState,
    t,
}) => {
    try {
        const payload = {
            outlet_id: !filterBranch ? Number(idCabang) : Number(filterBranch),
        };

        await productApi.exportRecipe(payload);
        alertDialogInfoExport.current.handleShowDialog('', EXPORT_ALERT_DIALOG_TYPE.EXPORT_RECIPE);
        handleSetExportBannerState({ open: true, customName: t('form.stepFour.heading') });
    } catch (error) {
        if (error.cause && error.cause.status && error.cause.status.code) {
            if (error.cause.status.code === 42200001) {
                alertDialogInfoExport.current.handleShowDialog(
                    error.cause.status.message,
                    EXPORT_ALERT_DIALOG_TYPE.EXPORT_RECIPE,
                );
                return;
            }
        }

        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(error),
            variant: 'failed',
        });
    }
};

const handlePrepareImportData = ({
    outlets,
    selectedOutletIds,
    userAccess,
    idCabang,
    importProductType,
    filterBranch,
}) => {
    let outletId = idCabang;
    let downloadType = outlets.length === selectedOutletIds.length ? 'insert-all-outlet' : 'insert-per-outlet';

    if (outlets.length === 1 && userAccess !== 'Admin') {
        downloadType = 'insert-per-outlet';
    }

    if (filterBranch) {
        outletId = filterBranch;
    }

    let isGroupOutlet = false;
    let branchId = outletId;
    let assignedBranchId = selectedOutletIds
        .map(id => {
            if (id.includes('-')) {
                isGroupOutlet = true;
                return id
                    .split('-')[1]
                    .split(',')
                    .map(item => Number(item));
            }
            return Number.isNaN(Number(id)) ? id : Number(id);
        })
        .flat();

    if (importProductType === IMPORT_PRODUCT_TYPE.UPDATE_PRODUCT) {
        downloadType = 'edit-per-outlet';
        if (!isGroupOutlet) {
            const selectedOutletId = assignedBranchId[0];

            branchId = selectedOutletId;
            assignedBranchId = [+selectedOutletId];

            if (selectedOutletId === 'ALL') {
                branchId = outletId;
                assignedBranchId = outlets.map(item => +item.id);
                downloadType = 'edit-all-outlet';
            }
        }
        if (outlets.length === 1) {
            downloadType = 'edit-per-outlet';
            if (userAccess === 'Admin') {
                downloadType = 'edit-all-outlet';
            }
        }
    }

    return { downloadType, branchId: +branchId, assignedBranchId };
};

export const handleDownloadTemplateImportProduct = async (
    {
        showProgress,
        hideProgress,
        addToast,
        importProductType,
        selectedOutletIds,
        outlets,
        userAccess,
        idCabang,
        filterBranch,
        lang,
    },
    onSuccessCallbackFn = () => {},
) => {
    try {
        showProgress();

        const { downloadType, branchId, assignedBranchId } = handlePrepareImportData({
            outlets,
            selectedOutletIds,
            userAccess,
            idCabang,
            importProductType,
            filterBranch,
        });

        let isAsync = false;
        let isQueued = false;

        if (importProductType !== IMPORT_PRODUCT_TYPE.UPDATE_RECIPE) {
            const payload = {
                type: downloadType,
                branch_id: branchId,
                ...(assignedBranchId && {
                    assigned_branch_id: assignedBranchId.filter(id => !!id).join(','),
                }),
                ...(isPrimeSubscription()
                    ? {
                          use_wholesale_prices: true,
                          use_serial_batch_number: true,
                      }
                    : {}),
                lang,
            };

            let res = null;

            if (importProductType === IMPORT_PRODUCT_TYPE.NEW_PRODUCT_VARIANT) {
                res = await productApi.exportTemplateV2(payload);
            } else {
                res = await productApi.exportTemplate(payload);
            }

            isAsync = res.data.is_async ? res.data.is_async : false;
            isQueued = res.data.is_queued ? res.data.is_queued : false;

            if (!isAsync && !isQueued) {
                window.open(res.data.file_url, '_self');
            }
        } else {
            isAsync = true;
            isQueued = true;
            try {
                const payload = {
                    outlet_id: !filterBranch ? Number(idCabang) : Number(filterBranch),
                    is_template: true,
                };

                await productApi.exportRecipe(payload);
            } catch (error) {
                if (error.cause && error.cause.status && error.cause.status.code) {
                    if (error.cause.status.code === 42200001) {
                        isQueued = false;
                        return;
                    }
                }

                addToast({
                    title: <ToastTitle type="failed" />,
                    description: catchError(error),
                    variant: 'failed',
                });
            }
        }

        onSuccessCallbackFn({ isAsync, isQueued });
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export async function handleImportProduct(
    {
        selectedOutletIds,
        fileToImport,
        importProductType,
        addToast,
        outlets,
        userAccess,
        idCabang,
        showProgress,
        hideProgress,
        filterBranch,
    },
    onSuccessCallbackFn = () => {},
) {
    try {
        showProgress();

        const { downloadType, branchId, assignedBranchId } = handlePrepareImportData({
            outlets,
            selectedOutletIds,
            userAccess,
            idCabang,
            importProductType,
            filterBranch,
        });

        const formData = new FormData();
        formData.append('file', fileToImport);
        formData.append('type', downloadType);
        formData.append('branch_id', branchId);
        if (assignedBranchId) {
            formData.append(
                'assigned_branch_id',
                assignedBranchId.filter(id => !!id),
            );
        }

        if (isPrimeSubscription()) {
            formData.append('use_wholesale_prices', true);
            formData.append('use_serial_batch_number', true);
        }

        let uploadRes = null;

        if (importProductType === IMPORT_PRODUCT_TYPE.NEW_PRODUCT_VARIANT) {
            uploadRes = await uploadForNewAndUpdateProductBulkV2(formData);
        } else if (importProductType === IMPORT_PRODUCT_TYPE.UPDATE_RECIPE) {
            uploadRes = await uploadProductRecipes(formData);
        } else {
            uploadRes = await uploadForNewAndUpdateProductBulk(formData);
        }

        if (!uploadRes.data)
            throw uploadRes.message || uploadRes.msg || 'Telah terjadi kesalahan. Silahkan coba kembali.';

        const payload = {
            file_url: uploadRes.data.file_url,
            type: downloadType,
            branch_id: parseInt(branchId, 10),
            ...((!downloadType.includes('all') || importProductType !== IMPORT_PRODUCT_TYPE.UPDATE_RECIPE) &&
                !!assignedBranchId && {
                    assigned_branch_id: assignedBranchId.filter(id => !!id),
                }),
            ...(isPrimeSubscription() && {
                use_wholesale_prices: true,
                use_serial_batch_number: true,
            }),
        };

        if (importProductType === IMPORT_PRODUCT_TYPE.NEW_PRODUCT_VARIANT) {
            await productApi.importAndUpdateBulkProductV2(payload, 'post');
        } else if (importProductType === IMPORT_PRODUCT_TYPE.UPDATE_RECIPE) {
            await productApi.importRecipe(payload);
        } else {
            let apiMethod = 'post';

            if (importProductType === IMPORT_PRODUCT_TYPE.UPDATE_PRODUCT) apiMethod = 'put';

            await productApi.importAndUpdateBulkProduct(payload, apiMethod);
        }
        onSuccessCallbackFn();
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
}

export const handleApplyMassUpdate = async (
    { selectedProductIds, action, filterBranch, idCabang, addToast, translationData },
    onSuccessCallbackFn = () => {},
) => {
    let outletId = idCabang;
    const { LANG_DATA } = translationData;
    if (filterBranch) outletId = filterBranch;

    const payload = {
        id_items: JSON.stringify(selectedProductIds),
        action,
        id_outlet: outletId,
    };

    try {
        const res = await productApi.updateBulkProduct(payload);

        if (!res.status) throw res.msg;

        addToast({
            title: <ToastTitle type="success" />,
            description:
                selectedProductIds.length > 1
                    ? `${selectedProductIds.length} ${LANG_DATA.MAIN_TOAST_SUCCEED_EDIT_MULTIPLE}`
                    : LANG_DATA.MAIN_TOAST_SUCCEED_EDIT,
            variant: 'success',
            dismissAfter: 3000,
        });
        onSuccessCallbackFn();
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

export const handleMassDelete = async (
    { selectedProductIds, addToast, translationData },
    onSuccessCallbackFn = () => {},
) => {
    const { TransComponent } = translationData;
    try {
        const res = await productApi.deleteBulkProductV15(selectedProductIds.map(x => Number(x.id)));

        if (!res.status) throw new Error('false');

        const resData = res.data;

        let failedDeletedStocks = [];
        const isFailed = resData && ishasProperty(resData, 'failed') && +resData.failed > 0;

        if (!isFailed) {
            addToast({
                title: <ToastTitle type="success" />,
                description: (
                    <TransComponent
                        i18nKey="main.toast.succeedDeleteBulkProduct"
                        values={{ dataSuccess: selectedProductIds.length }}
                    >
                        <b>{`${selectedProductIds.length} Produk`}</b> berhasil dihapus
                    </TransComponent>
                ),
                variant: 'success',
                dismissAfter: 3000,
            });
        } else {
            resData.stock.map(outlet => {
                outlet.data.map(item => {
                    failedDeletedStocks = [...failedDeletedStocks, item];
                    return item;
                });

                return outlet;
            });
        }

        onSuccessCallbackFn(isFailed ? { ...resData, failedDeletedStocks } : null);
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

export const handleCheckRunningProcess = async ({ setState, addToast }, onSuccessCallbackFn) => {
    try {
        const response = await productApi.checRunningProcess();
        const isProcessing = response.data.background_process;
        const runningBackgroundProcessData = response.data;

        setState({ runningBackgroundProcessData }, () => {
            onSuccessCallbackFn(isProcessing);
        });
    } catch (error) {
        setState({
            runningBackgroundProcessData: {
                background_process: false,
                is_create: null,
                is_variant: null,
                all_active_branch: [],
            },
        });
        addToast({
            title: <ToastTitle type="failed" />,
            description: `Running Process - ${catchError(error)}`,
            variant: 'failed',
            dismissAfter: 3000,
        });
    }
};

export const handleFetchPriceSettings = async ({ setState }) => {
    try {
        const res = await settingApi.getPurchasePriceSetting();
        if (res.data) {
            setState({ priceSetting: res.data.value || PRICE_SETTING.PURCHASE_PRICE });
        }
    } catch (err) {
        console.error(err);
    }
};

export const handleFetchRecipeMasterSettings = async ({ setState }) => {
    try {
        const res = await settingApi.getRecipeMasterSetting();
        if (res.data) {
            setState({
                recipeMasterSetting: res.data || defaultRecipeMasterSetting,
            });
        } else {
            setState({
                recipeMasterSetting: {
                    id: 1,
                    ...defaultRecipeMasterSetting,
                },
            });
        }
    } catch (err) {
        console.error(err);
        setState({
            recipeMasterSetting: {
                id: 1,
                ...defaultRecipeMasterSetting,
            },
        });
    }
};

export const handleUpdateOutletGroupSettings = async (
    { payload, addToast, translationData },
    onSuccessCallbackFn = () => {},
) => {
    const { LANG_DATA, TransComponent } = translationData;
    try {
        const resValidation = await productApi.updateOutletGroup(payload.id, payload);

        if (!resValidation.status && resValidation.data.length > 0) {
            const invalidData = resValidation.data[0];
            let errorMsg = '';

            if (invalidData.validation_errors.length > 0) {
                const error = invalidData.validation_errors[0];
                errorMsg = error.message;
            }

            if (errorMsg) throw errorMsg;
        }

        addToast({
            title: LANG_DATA.TOAST_EDIT_TITLE,
            description: (
                <TransComponent
                    i18nKey="toast.activityLog.edit.noOutletDescription"
                    values={{
                        product_name: payload.name,
                    }}
                    components={{
                        strong: <b />,
                    }}
                />
            ),
            variant: 'pending',
            position: 'top-right',
            dismissAfter: 3000,
        });

        onSuccessCallbackFn();
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};
