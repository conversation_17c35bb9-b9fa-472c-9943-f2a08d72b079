import React, { useContext, useMemo } from 'react';
import {
    Box, Heading, InputSwitch,
    InputText, InputTextArea, InputSelectTag,
} from '@majoo-ui/react';
import ResponsivePaper from '../../Inventory/StockEntryV2/StockEntryDetail/ResponsivePaper'; // TODO: akan dibuatkan global component di dev/src/js/v2-components
import SelectOutlet from '../components/SelectOutlet';
import FormContext from './FormContext';
import HorizontalForm from '../../../v2-components/HorizontalForm';
import PageContext from '../PageContext';

const SectionOne = () => {
    const {
        onChange, formData,
    } = useContext(FormContext);
    const { translationData } = useContext(PageContext);
    const { LANG_DATA } = translationData;

    const customerGroupValues = useMemo(() => formData.customerGroups.map(item => item.id), [formData.customerGroups]);

    return (
        <ResponsivePaper css={{ gap: '$spacing-03', '@md': { gap: '$cozy' } }}>
            <Box css={{ '@md': { marginBottom: '$spacing-03' } }}>
                <Heading
                    as="h3"
                    heading="pageTitle"
                    css={{ marginBottom: 0, '@md': { marginBottom: '$spacing-03 !important' } }}
                >
                    {LANG_DATA.FORM_STEP_FIVE_SUBDIALOG_EDITMULTIPRICE_HEADING}
                </Heading>
            </Box>
            <HorizontalForm
                labelOptions={{
                    text: LANG_DATA.FORM_STEP_FIVE_SUBDIALOG_EDITMULTIPRICE_OUTLETLIST,
                }}
                render={() => (
                    <SelectOutlet
                        onChange={(val) => { onChange('outletId', val.value); }}
                        value={formData.outletId}
                        readOnly={false}
                        disabled
                    />
                )}
            />
            <HorizontalForm
                labelOptions={{
                    text: LANG_DATA.FORM_STEP_FIVE_SUBDIALOG_EDITMULTIPRICE_FIELDS_STATUS,
                }}
                render={() => (
                    <InputSwitch
                        dataOffLabel="OFF"
                        dataOnLabel="ON"
                        onCheckedChange={val => onChange('isActive', val)}
                        checked={formData.isActive}
                        disabled
                    />
                )}
            />
            <HorizontalForm
                labelOptions={{
                    text: LANG_DATA.FORM_STEP_FIVE_SUBDIALOG_EDITMULTIPRICE_FIELDS_TYPENAME,
                }}
                render={() => (
                    <InputText
                        value={formData.name}
                        onChange={(e) => { onChange('name', e.target.value); }}
                        disabled
                    />
                )}
            />
            <HorizontalForm
                labelOptions={{
                    text: LANG_DATA.FORM_STEP_FIVE_SUBDIALOG_EDITMULTIPRICE_FIELDS_DESCRIPTION,
                }}
                render={() => (
                    <InputTextArea
                        css={{ minHeight: 62 }}
                        value={formData.description}
                        onChange={(e) => { onChange('description', e.target.value); }}
                        disabled
                    />
                )}
            />
            <HorizontalForm
                labelOptions={{
                    text: LANG_DATA.FORM_STEP_FIVE_SUBDIALOG_EDITMULTIPRICE_FIELDS_CUSTOMERGROUP,
                }}
                render={() => (
                    <InputSelectTag
                        onChange={() => { }}
                        option={formData.customerGroups}
                        size="sm"
                        disabled
                        value={customerGroupValues}
                    />
                )}
            />
        </ResponsivePaper>
    );
};

export default SectionOne;
