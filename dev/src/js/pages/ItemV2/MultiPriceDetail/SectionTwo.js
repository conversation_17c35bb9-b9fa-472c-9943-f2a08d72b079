import React, { useContext } from 'react';
import {
    Box, Heading, Flex,
} from '@majoo-ui/react';
import ResponsivePaper from '../../Inventory/StockEntryV2/StockEntryDetail/ResponsivePaper'; // TODO: akan dibuatkan global component di dev/src/js/v2-components
import HorizontalForm from '../../../v2-components/HorizontalForm';
import ManagePriceList from './ManagePriceList';
import * as SectionTwoComponent from './section-two-components';
import { useTranslationHook } from '../lang.utils';
import PageContext from '../PageContext';

const SectionTwo = () => {
    const { translationData } = useContext(PageContext);
    const { LANG_DATA } = translationData;

    return (
        <ResponsivePaper css={{ gap: '$spacing-03', '@md': { gap: '$cozy' } }}>
            <Box css={{ '@md': { marginBottom: '$spacing-03' } }}>
                <Heading
                    as="h3"
                    heading="pageTitle"
                    css={{ marginBottom: 0, '@md': { marginBottom: '$spacing-03 !important' } }}
                >
                    {LANG_DATA.FORM_STEP_FIVE_SUBDIALOG_PRODUCTNPRICE_HEADING}
                </Heading>
            </Box>
            <HorizontalForm
                labelOptions={{
                    text: `${LANG_DATA.PLACEHOLDER_SELECT} ${LANG_DATA.LABEL_PRODUCT}`,
                    required: true,
                }}
                render={() => (
                    <Flex css={{ width: '100%', justifyContent: 'flex-end' }}>
                        <SectionTwoComponent.AddProduct />
                    </Flex>
                )}
            />
            <HorizontalForm
                labelOptions={{
                    text: LANG_DATA.FORM_STEP_FIVE_SUBDIALOG_PRODUCTNPRICE_PRICESETTING,
                    required: true,
                }}
                render={() => (
                    <Flex css={{ width: '100%', justifyContent: 'flex-end' }}>
                        <SectionTwoComponent.ShowPresetPrice />
                    </Flex>
                )}
            />
            <Box>
                <ManagePriceList />
            </Box>
        </ResponsivePaper>
    );
};

export default SectionTwo;
