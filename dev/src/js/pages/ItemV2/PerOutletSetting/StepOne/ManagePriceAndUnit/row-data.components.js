import React, { useContext, Fragment } from 'react';
import {
    Box,
    InputGroup,
    InputNumber,
    InputSuffix,
    InputText,
    InputPrefix,
    InputRightElement,
} from '@majoo-ui/react';
import { EditOutline, RemoveOutline } from '@majoo-ui/icons';
import { foundations } from '@majoo-ui/core';
import update from 'immutability-helper';
import FormContext from '../../FormContext';
import PageContext from '../../../PageContext';

const { iconSecondary } = foundations.colors;

const usePreviewMultiUnits = index => {
    const { unitPreviewRef, getValues } = useContext(FormContext);

    const handlePreviewMultiUnits = (key, val) => {
        let newData = getValues('units');
        newData = update(newData, {
            [index]: {
                [key]: {
                    $set: val,
                },
            },
        });

        unitPreviewRef.current.handlePreviewMultiUnits(newData);
    };

    return {
        onPreviewMultiUnits: handlePreviewMultiUnits,
    };
};

export const UnitName = ({ index }) => {
    const { Controller, control, setModalAddUnitState } = useContext(FormContext);

    return (
        <Controller
            control={control}
            name={`units.${index}.unitName`}
            render={({ field: { value } }) => (
                <InputGroup readOnly>
                    <InputText value={value} css={{ '& input': { color: '$textPrimary' } }} />
                    <InputRightElement
                        css={{ cursor: 'pointer' }}
                        onClick={() => setModalAddUnitState({ open: true, index, selectedUnit: value })}
                    >
                        <EditOutline />
                    </InputRightElement>
                </InputGroup>
            )}
        />
    );
};

export const Conversion = ({ errorReceiver, index, firstUnitName }) => {
    const { Controller, control } = useContext(FormContext);
    const { onPreviewMultiUnits } = usePreviewMultiUnits(index);

    return (
        <Controller
            control={control}
            name={`units.${index}.conversion`}
            render={({ fieldState, field: { onChange, value } }) => {
                const receivedError = errorReceiver(fieldState.error);

                return (
                    <Fragment>
                        <InputGroup disabled={index === 0}>
                            <InputNumber
                                size="lg"
                                thousandSeparator={false}
                                disabled={index === 0}
                                onValueChange={({ value: val }) => {
                                    onPreviewMultiUnits('conversion', val);
                                    onChange(val);
                                }}
                                defaultValue={value}
                                isNumericString
                                allowNegative={false}
                                isInvalid={!!receivedError}
                            />
                            <InputSuffix>{firstUnitName}</InputSuffix>
                        </InputGroup>
                        {receivedError}
                    </Fragment>
                );
            }}
        />
    );
};

export const Sku = ({ errorReceiver, index }) => {
    const { Controller, control, formData, getUserPermissionId } = useContext(FormContext);
    const { translationData } = useContext(PageContext);
    const { LANG_DATA } = translationData;
    const isNotAdmin = Number(getUserPermissionId) !== 1;

    return (
        <Controller
            control={control}
            name={`units.${index}.sku`}
            render={({ fieldState, field: { onChange, value } }) => {
                const receivedError = errorReceiver(fieldState.error);

                return (
                    <Fragment>
                        <InputText
                            onChange={event => {
                                onChange(event.target.value);
                            }}
                            defaultValue={value}
                            placeholder={`${LANG_DATA.PLACEHOLDER_EXAMPLE}S001`}
                            isInvalid={!!receivedError}
                            maxLength={40}
                            disabled={formData.id !== '' && isNotAdmin}
                        />
                        {receivedError}
                    </Fragment>
                );
            }}
        />
    );
};

export const DeleteRowButton = ({ index, onDeleteRow }) => {
    if (index === 0) return null;

    return (
        <Box
            css={{ cursor: 'pointer', '@md': { marginTop: '29px' } }}
            onClick={() => {
                onDeleteRow(index);
            }}
        >
            <RemoveOutline size={28} color={iconSecondary} />
        </Box>
    );
};

export const Price = ({ dataKey, index, errorReceiver, disabled, placeholder }) => {
    const { Controller, control } = useContext(FormContext);
    const allowDecimal = dataKey === 'average_price' || dataKey === 'purchasePrice';
    return (
        <Controller
            control={control}
            name={`units.${index}.${dataKey}`}
            render={({ fieldState, field: { onChange, value } }) => {
                const receivedError = errorReceiver(fieldState.error);

                return (
                    <Fragment>
                        <InputGroup isInvalid={!!receivedError} {...{ disabled }}>
                            <InputPrefix>Rp</InputPrefix>
                            <InputNumber
                                defaultValue={value}
                                isNumericString
                                allowNegative={false}
                                {...{ disabled }}
                                onValueChange={({ value: valueInput }) => {
                                    onChange(valueInput);
                                }}
                                placeholder={placeholder}
                                allowLeadingZeros={false}
                                maxLength={allowDecimal ? 15 : 11}
                                decimalScale={allowDecimal ? 2 : 0}
                                // fixedDecimalScale
                            />
                        </InputGroup>
                        {receivedError}
                    </Fragment>
                );
            }}
        />
    );
};

export const MinimumPurchase = ({ index, errorReceiver }) => {
    const { Controller, control } = useContext(FormContext);
    const { translationData } = useContext(PageContext);
    const { LANG_DATA } = translationData;

    return (
        <Controller
            control={control}
            name={`units.${index}.minimumPurchase`}
            render={({ fieldState, field: { onChange, value } }) => {
                const receivedError = errorReceiver(fieldState.error);

                return (
                    <Fragment>
                        <InputNumber
                            onValueChange={({ value }) => {
                                onChange(value);
                            }}
                            defaultValue={value}
                            placeholder={LANG_DATA.FORM_STEP_ONE_MINIMUM_PURCHASE}
                            allowNegative={false}
                            isInvalid={!!receivedError}
                            disabled={false}
                        />
                        {receivedError}
                    </Fragment>
                );
            }}
        />
    );
};

export const ProductDimensionVolume = ({ dataKey, index }) => {
    const { Controller, control } = useContext(FormContext);

    return (
        <Controller
            control={control}
            name={`units.${index}.${dataKey}`}
            render={({ field: { onChange, value } }) => (
                <InputNumber
                    onValueChange={({ value }) => {
                        onChange(value);
                    }}
                    defaultValue={value}
                    css={{ width: '30%', textAlign: 'center' }}
                    allowNegative={false}
                    isInvalid={false}
                    disabled={false}
                />
            )}
        />
    );
};

export const Weight = ({ index }) => {
    const { Controller, control } = useContext(FormContext);

    return (
        <Controller
            control={control}
            name={`units.${index}.weight`}
            render={({ field: { onChange, value } }) => (
                <InputGroup isInvalid={false}>
                    <InputNumber
                        onValueChange={({ floatValue }) => onChange(floatValue)}
                        defaultValue={value}
                        allowNegative={false}
                    />
                    <InputSuffix>gram</InputSuffix>
                </InputGroup>
            )}
        />
    );
};
