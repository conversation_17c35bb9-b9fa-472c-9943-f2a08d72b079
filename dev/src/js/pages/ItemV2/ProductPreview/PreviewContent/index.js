import React, { useContext, useEffect } from 'react';
import {
    Box, Heading,
} from '@majoo-ui/react';
import ResponsivePaper from '../../../Inventory/StockEntryV2/StockEntryDetail/ResponsivePaper'; // TODO: akan dibuatkan global component di dev/src/js/v2-components
import ProductInformation from './ProductInformation';
import PriceAndUnit from './PriceAndUnit';
import WebOrder from './WebOrder';
import WholesalePrices from './WholesalePrices';
import ProductExtra from './ProductExtra';
import Variants from './Variants';
import RawMaterials from './RawMaterials';
import PageContext from '../../PageContext';

const PreviewContent = ({ formData }) => {
    const { translationData, handleFetchRawMaterials } = useContext(PageContext);
    const { LANG_DATA } = translationData;
    useEffect(() => {
        handleFetchRawMaterials();
    }, []);
    return (
        <ResponsivePaper css={{ gap: '$spacing-03', '@md': { gap: '$cozy' } }}>
            <Box css={{ '@md': { marginBottom: '$spacing-03' } }}>
                <Heading
                    as="h3"
                    heading="pageTitle"
                    css={{ marginBottom: 0, '@md': { marginBottom: '$spacing-03 !important' } }}
                >
                    {LANG_DATA.MODAL_DETAIL_HEADING}
                </Heading>
            </Box>
            <ProductInformation {...{ formData }} />
            <PriceAndUnit {...{ formData }} />
            <WholesalePrices {...{ formData }} />
            <Variants {...{ formData }} />
            <ProductExtra {...{ formData }} />
            <RawMaterials {...{ formData }} />
            <WebOrder {...{ formData }} />
        </ResponsivePaper>
    );
};

export default PreviewContent;
