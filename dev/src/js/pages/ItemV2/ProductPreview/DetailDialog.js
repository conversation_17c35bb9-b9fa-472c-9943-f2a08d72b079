import React, { Fragment, useContext } from 'react';
import {
    <PERSON>Dialog, PageDialogT<PERSON>le,
    PageDialogContent,
    PageDialogFooter, Flex, Button, Box,
} from '@majoo-ui/react';
import CustomPageDialogFooter from '../../../v2-components/CustomPageDialogFooter';
import PreviewContent from './PreviewContent';
import { createPayloadFormValidation, createPayloadSaveProduct } from './utils';
import PageContext from '../PageContext';
import AlertDialogSaveConfirm from './AlertDialogSaveConfirm';
import { createFormData } from '../Detail/utils';
import { FORM_TYPE } from '../enums';
import { buildWebStoreData } from '../utils';

class DetailDialogComponent extends React.Component {
    state = {
        formData: null,
    }

    componentDidMount = () => {
        this.handleSetFormData();
    }

    componentDidUpdate = (prevProps) => {
        const { currentFormType, currentProductDetailData, formData } = this.props;

        if (currentFormType === FORM_TYPE.VIEW && (JSON.stringify(prevProps.currentProductDetailData) !== JSON.stringify(currentProductDetailData))) {
            this.handleSetFormData();
        } else if (currentFormType !== FORM_TYPE.VIEW && JSON.stringify(prevProps.formData) !== JSON.stringify(formData)) {
            this.handleSetFormData();
        }
    }

    handleSetFormData = () => {
        const {
            currentFormType, formData, currentProductDetailData, marketplaceProducts, rawMaterials, productExtras, listOutlet, filterBranch, outletGroupList, outlets
        } = this.props;

        if (currentFormType !== FORM_TYPE.VIEW) {
            this.setState({ formData });
        } else if (currentFormType === FORM_TYPE.VIEW) {
            this.setState({
                formData: createFormData(currentProductDetailData, {
                    marketplaceProducts, rawMaterials, productExtras, listOutlet, outlets, filterBranch, outletGroupList
                }),
            });
        }
    }

    handleCloseDialog = () => {
        const { onClose } = this.props;

        onClose();
    }

    handleSave = async () => {
        const {
            webOrderDetail, rawMaterials, currentProductDetailData,
            marketplaceProviders, idCabang, marketplaceProducts, showProgress, hideProgress, onFetchWebOrderDetail,
            marketplaceOutlets,
        } = this.props;
        const { formData } = this.state;

        let newWebOrderDetail = webOrderDetail;
        // TODO: harusnya tidak seperti ini, perlu di buat lebih clean lagi https://app.clickup.com/t/860rw3xch
        if (!webOrderDetail) {
            showProgress();
            const { integratedOutlet, marketplaceProvider } = buildWebStoreData({ formData, marketplaceOutlets, marketplaceProviders });

            if (integratedOutlet && marketplaceProvider) {
                newWebOrderDetail = await onFetchWebOrderDetail({ outletId: integratedOutlet.id, vendor: marketplaceProvider.id });
            }

            hideProgress();
        }
        const payload = createPayloadSaveProduct(formData, {
            webOrderDetail: newWebOrderDetail, rawMaterials, currentProductDetailData, marketplaceProviders, idCabang, marketplaceProducts,
        });

        const payloadFormValidation = createPayloadFormValidation({
            formData, currentProductDetailData, rawMaterials, payloadSaveProduct: payload,
        });

        this.alertDialogSaveConfirm.handleShowDialog({ ...payload, formValidation: payloadFormValidation });
    }

    handleOpenFormProductToEdit = () => {
        const { onOpenFormProductToEdit } = this.props;

        this.handleCloseDialog();
        onOpenFormProductToEdit();
    }

    render() {
        const { formData } = this.state;
        const { menuPrivilege: { isCanUpdate, isCanCreate, isCanView, isWhitelistMerchant }, isOpen, isInMobileView, currentFormType, translationData } = this.props;
        const { LANG_DATA } = translationData;

        if (!formData) return null;

        return (
            <Fragment>
                <PageDialog
                    open={isOpen}
                    defaultOpen={isOpen}
                    onOpenChange={this.handleCloseDialog}
                    modal
                    layer={1}
                    size="full"
                    isMobile={isInMobileView}
                >
                    <PageDialogTitle>
                        <Flex direction="row" align="center" gap={4}>
                            <Box>
                                {LANG_DATA.MODAL_DETAIL_HEADING}
                            </Box>
                        </Flex>
                    </PageDialogTitle>
                    <PageDialogContent wrapperSize="md">
                        <PreviewContent {...{ formData }} />
                    </PageDialogContent>
                    <PageDialogFooter>
                        <CustomPageDialogFooter
                            {...{ pageDialogContentHasIndicator: false }}
                            renderGroups={[
                                () => (
                                    <Flex
                                        gap={4}
                                        css={{
                                            justifyContent: 'end',
                                        }}
                                    >
                                        <Button
                                            type="button"
                                            buttonType="ghost"
                                            onClick={() => { this.handleCloseDialog(); }}
                                        >
                                            {LANG_DATA.LABEL_BACK}
                                        </Button>
                                        {currentFormType === FORM_TYPE.VIEW && (isCanUpdate || (isCanView && !isWhitelistMerchant)) ? (
                                            <Button
                                                type="button"
                                                onClick={this.handleOpenFormProductToEdit}
                                            >
                                                {LANG_DATA.LABEL_EDIT_PRODUCT}
                                            </Button>
                                        ) : (isCanUpdate || isCanCreate || (isCanView && !isWhitelistMerchant)) && (
                                            <Button
                                                type="button"
                                                onClick={this.handleSave}
                                            >
                                                {LANG_DATA.LABEL_SAVE}
                                            </Button>
                                        )}

                                    </Flex>
                                ),
                            ]}
                        />
                    </PageDialogFooter>
                </PageDialog>
                <AlertDialogSaveConfirm ref={(c) => { this.alertDialogSaveConfirm = c; }} />
            </Fragment>
        );
    }
}

const DetailDialog = (props) => {
    const {
        webOrderDetail, rawMaterials, isInMobileView, currentProductDetailData, currentFormType,
        onOpenFormProductToEdit, groupParents, marketplaceProviders, idCabang, marketplaceProducts, listOutlet,
        translationData, showProgress, hideProgress, onFetchWebOrderDetail, marketplaceOutlets, productExtras,
        filterBranch, menuPrivilege, outletGroupList, outlets,
    } = useContext(PageContext);

    return (
        <DetailDialogComponent
            {...props}
            {...{
                menuPrivilege,
                webOrderDetail,
                rawMaterials,
                isInMobileView,
                currentProductDetailData,
                currentFormType,
                onOpenFormProductToEdit,
                groupParents,
                marketplaceProviders,
                idCabang,
                marketplaceProducts,
                translationData,
                showProgress,
                hideProgress,
                onFetchWebOrderDetail,
                marketplaceOutlets,
                productExtras,
                listOutlet,
                filterBranch,
                outletGroupList,
                outlets,
            }}
        />
    );
};

export default DetailDialog;
