import _ from 'lodash';
import draftToHtml from 'draftjs-to-html';
import update from 'immutability-helper';
import { convertToRaw } from 'draft-js';
import { ITEM_TYPE } from '../../../v2-enum';
import { VARIANT_FLAG_TYPE } from '../enums';
import { ishasProperty } from '../../../utils/helper';
import { getFormattedOutletIds } from '../utils';

const buildPayloadRecipes = ({
    rawMaterials, selectedRawMaterials, hasVariantRecipe, variantList, hasVariant, hasRecipe,
}) => {
    let recipes = [];

    const reformatSelectedRawMaterial = (x, replaceVariants = []) => {
        const found = rawMaterials.find(rawMaterial => String(rawMaterial.value) === String(x.rawMaterialId));

        let variants = [];

        if (hasVariant && !hasVariantRecipe) variants = variantList.map(variant => ({ quantity: x.dose, sku: variant.sku }));
        if (hasVariant && hasVariantRecipe && replaceVariants.length > 0) variants = replaceVariants;

        let unitNo = '';

        if (ishasProperty(x, 'unitNo') && x.unitNo) {
            ({ unitNo } = x);
        } else if (found && found.units && found.units.length > 0) {
            unitNo = found.units[0].no;
        }

        return {
            item_id: x.rawMaterialId,
            item_no: found ? found.no : '',
            quantity: x.dose,
            unit_no: unitNo,
            ...!hasVariant ? {} : {
                variants,
            },
        };
    };

    if (!hasRecipe) return [];

    if (hasRecipe && (!hasVariantRecipe || _.isEmpty(variantList) || _.isEmpty(variantList[0].selectedRawMaterials))) {
        recipes = selectedRawMaterials.map(x => reformatSelectedRawMaterial(x));
    } else if (hasRecipe && hasVariantRecipe) {
        variantList.forEach((x) => {
            x.selectedRawMaterials.forEach((selectedRawMaterial) => {
                const indexFound = recipes.findIndex(recipe => String(recipe.item_id) === String(selectedRawMaterial.rawMaterialId));

                if (indexFound > -1) {
                    recipes = update(recipes, {
                        [indexFound]: {
                            variants: {
                                $push: [{ quantity: selectedRawMaterial.dose, sku: x.sku }],
                            },
                        },
                    });
                } else {
                    recipes = [...recipes, reformatSelectedRawMaterial(selectedRawMaterial, [{ quantity: selectedRawMaterial.dose, sku: x.sku }])];
                }
            });
        });
    }

    return recipes.filter(x => x.item_id);
};

const buildPayloadExtras = ({ selectedProductExtras, hasProductExtra }) => {
    let extras = {};
    if (hasProductExtra) {
        selectedProductExtras.forEach((x) => {
            extras = {
                ...extras,
                [x.productExtraId]: x.details.map(detail => ({
                    harga_jual: +detail.sellingPrice,
                    id_add_ons_detail: detail.subExtraId,
                    ingredients: detail.ingredients.map(material => ({
                        M_Item_id_item: material.item_id,
                        takaran: +material.measurement,
                    })),
                })),
            };
        });
    }
    return extras;
};

const buildPayloadAggregator = ({
    webOrderDetail, formData, marketplaceProviders, idCabang, marketplaceProducts, firstMultiUnit,
}) => {
    const rawDescription = formData.onlineStoreProductDescriptionRaw;
    let aggregator = {
        outlet: 0,
        detail: [],
    };

    if (!webOrderDetail) return aggregator;

    const getWebOrderDetailId = () => {
        const found = marketplaceProviders.find(x => x.alias === 'weborder');

        if (found) return found.id;

        return '';
    };

    let webOrderDetailId = getWebOrderDetailId();

    if (marketplaceProducts.length > 0) webOrderDetailId = marketplaceProducts[0].id;

    aggregator = {
        detail: [
            {
                id: webOrderDetailId,
                is_favourite: +formData.onlineStoreFavoriteProductChecked,
                is_manual_price: +formData.onlineStoreManualChecked,
                is_minimum_stock: +formData.onlineStoreMinimumStockChecked,
                item_description_long: encodeURIComponent(draftToHtml(convertToRaw(rawDescription.getCurrentContent()))),
                long_desc_length: rawDescription.getCurrentContent().getPlainText().length,
                minimum_stock: formData.onlineStoreMinimumStock,
                outlets: formData.onlineStoreOutletIds,
                price: formData.onlineStoreManualChecked ? formData.onlineStoreManualPrice : +firstMultiUnit.sellingPrice,
                seq: 0,
                status: formData.isWebOrderActive,
                weight: 1000,
                weight_unit: 'gram',
            },
        ],
        outlet: idCabang,
    };

    return aggregator;
};

// TODO: perlu dibikin lebih clean lagi
const buildPayloadVariants = ({ formData, currentProductDetailData }) => {
    if (!formData.hasVariant
        && (formData.initialStepFormData
            && formData.initialStepFormData.variantList
            && formData.initialStepFormData.variantList.length <= 0)) return [];

    const nonDeletedData = formData.variantList.map((x) => {
        if (!currentProductDetailData) {
            return {
                selling_price: x.sellingPrice,
                purchase_price: x.purchasePrice,
                variant_sku: x.sku,
                status: x.isActive,
                detail_variant: x.detail,
                detail_variant_string: JSON.stringify(x.detail),
                flag: VARIANT_FLAG_TYPE.NEW,
                master_recipe_id: x.masterRecipeId,
            };
        }

        const found = currentProductDetailData.variants ? currentProductDetailData.variants.find((variant) => {
            if (ishasProperty(x, 'initialDetail')) return _.isEqual(variant.variant_detail, x.initialDetail);

            return _.isEqual(variant.variant_detail, x.detail);
        }) : null;
        const isExisting = !!found;

        return {
            selling_price: x.sellingPrice,
            purchase_price: x.purchasePrice,
            variant_sku: x.sku,
            status: x.isActive,
            detail_variant: x.detail,
            detail_variant_string: JSON.stringify(x.detail),
            flag: !isExisting ? VARIANT_FLAG_TYPE.NEW : VARIANT_FLAG_TYPE.EXISTING,
            master_recipe_id: x.masterRecipeId,
            ...isExisting ? {
                id: found.variant_id,
                M_Item_item_no: found.variant_no,
            } : {},
        };
    });

    let deletedData = [];

    if (formData.id) {
        const { initialStepFormData } = formData;

        deletedData = initialStepFormData.variantList.filter(prevVariant => !formData.variantList.some(newVariant => String(newVariant.variant_id) === String(prevVariant.variant_id)));
        deletedData = deletedData.map((x) => {
            const found = currentProductDetailData.variants ? currentProductDetailData.variants.find((variant) => {
                if (ishasProperty(x, 'initialDetail')) return _.isEqual(variant.variant_detail, x.initialDetail);

                return _.isEqual(variant.variant_detail, x.detail);
            }) : null;
            const isExisting = !!found;

            return {
                selling_price: x.sellingPrice,
                purchase_price: x.purchasePrice,
                variant_sku: x.sku,
                status: x.isActive,
                detail_variant: x.detail,
                flag: VARIANT_FLAG_TYPE.DELETED,
                master_recipe_id: x.masterRecipeId,
                ...isExisting ? {
                    id: found.variant_id,
                    M_Item_item_no: found.variant_no,
                } : {},
            };
        });
    }

    return [...nonDeletedData, ...deletedData];
};

export const createPayloadSaveProduct = (formData, {
    webOrderDetail, rawMaterials, currentProductDetailData, marketplaceProviders, idCabang, marketplaceProducts,
}) => {
    const {
        selectedRawMaterials, hasVariantRecipe, variantList, hasVariant, hasRecipe,
        selectedProductExtras, hasProductExtra,
    } = formData;
    let recipes = buildPayloadRecipes({
        rawMaterials, selectedRawMaterials, hasVariantRecipe, variantList, hasVariant, hasRecipe,
    });
    const extras = buildPayloadExtras({ selectedProductExtras, hasProductExtra });
    const firstMultiUnit = formData.multiUnits[0];
    const variants = buildPayloadVariants({ formData, currentProductDetailData });
    const aggregator = buildPayloadAggregator({
        webOrderDetail, formData, marketplaceProviders, idCabang, marketplaceProducts, firstMultiUnit,
    });

    const wholesalePrices = formData.wholesalePrices.map(x => ({
        min_qty: x.minQty,
        selling_price: x.unitPrice,
        unit_name: firstMultiUnit.unitName,
        unit_no: '',
    }));

    let isRecipeAllVariants = !formData.hasRecipe ? false : (!formData.hasVariantRecipe);

    if (formData.isMonitorStock) {
        isRecipeAllVariants = false;
        recipes = [];
    }

    const formattedOutletId = getFormattedOutletIds(formData.outletIds);

    return {
        id: formData.id,
        name: formData.productName,
        description: formData.description,
        SKU: firstMultiUnit.sku,
        capital_price: !currentProductDetailData ? 0 : +currentProductDetailData.average_price,
        purchase_price: +firstMultiUnit.purchasePrice,
        selling_price: +firstMultiUnit.sellingPrice,
        min_selling_qty: +firstMultiUnit.minimumPurchase,
        is_favorite: +formData.isFavorite,
        is_displayed: +formData.showOnMenu,
        is_parent: +formData.setAsParent,
        use_cogs: +formData.isMonitorStock,
        use_serial_number: +formData.isSerialNumberActive,
        has_recipe: +formData.hasRecipe,
        stock_alert: formData.minimumStock,
        is_finished_material: ITEM_TYPE.FINISHED,
        id_outlet: JSON.stringify(formattedOutletId), // existing code: JSON.stringify(formData.outletIds)
        image_path: JSON.stringify(formData.productImages.map(x => x.url)),
        id_category: JSON.stringify([formData.categoryId]),
        extras,
        extra_change: formData.isProductExtraChangeable,
        id_item_parent: formData.setAsParent ? 0 : +formData.groupParentId,
        can_change_selling_status: formData.canChangeSellingStatus,
        recipes: JSON.stringify(recipes),
        is_recipe_all_variants: +isRecipeAllVariants,
        master_recipe_id: formData.masterRecipeId,
        changeable_price_percent: formData.isSellingPriceEditable ? formData.editableSellingPrice : 0,
        units: JSON.stringify(formData.multiUnits.map(x => ({
            capital_price: x.average_price || 0, // TODO:
            conversion: x.conversion,
            dimension: {
                height: x.volumeHeight,
                length: x.volumeLength,
                width: x.volumeWidth,
            },
            dimension_unit: 'cm',
            min_selling_qty: x.minimumPurchase,
            name: x.unitName,
            no: x.no || '',
            purchase_price: +x.purchasePrice,
            selling_price: +x.sellingPrice,
            sku: x.sku,
            weight: x.weight,
        }))),
        default_unit: JSON.stringify({
            sales_unit: firstMultiUnit.unitName,
            inventory_unit: firstMultiUnit.unitName,
            purchase_unit: firstMultiUnit.unitName,
        }),
        aggregator: JSON.stringify(aggregator),
        wholesale_prices: JSON.stringify(formData.isWholesalePriceActive ? wholesalePrices : []),
        is_wholesale_price_active: +formData.isWholesalePriceActive,
        is_variant: +formData.hasVariant,
        variants,
        has_serial_number: +formData.isSerialNumberActive,
        has_batch_number: +formData.isBatchNumberActive,
        has_expired_date: !formData.isMonitorStock ? 0 : +formData.hasExpiredDate,
        hasSNBNItemDrafted: {
            dataItem: [
            ],
            switchBNOrigin: false,
            switchSNOrigin: false,
        },
        is_validate_group_outlet: formData.outletIds && formData.outletIds.find(id => id.includes('-')) ? 1 : 0,
    };
};

export const createPayloadFormValidation = ({
    formData, currentProductDetailData, rawMaterials, payloadSaveProduct,
}) => {
    let datas = [];
    const recipes = payloadSaveProduct.recipes && JSON.parse(payloadSaveProduct.recipes);

    if (formData.multiUnits.length > 0) {
        datas = [...datas, {
            step: 'info-price-and-unit-page',
            form: {
                name: formData.productName,
                multiunits: formData.multiUnits.map(x => ({
                    unit_sku: x.sku,
                    weight: x.weight,
                })),
            },
        }];
    }

    if (formData.hasVariant) {
        datas = [...datas, {
            step: 'variant-page',
            form: {
                variant_fields: formData.variantTypes.map(x => ({
                    name: x.name,
                    options: x.options.map(option => option.value),
                })),
                variant_skus: payloadSaveProduct.variants.map(x => ({
                    variant_id_item: ishasProperty(x, 'id') ? x.id : null,
                    variant_item_no: ishasProperty(x, 'M_Item_item_no') ? x.M_Item_item_no : null,
                    variant_sku: x.variant_sku,
                    flag: x.flag,
                })),
            },
        }];
    }

    if (formData.hasProductExtra) {
        let addOns = [];

        formData.selectedProductExtras.forEach((x) => {
            x.details.forEach((detail) => {
                addOns = [
                    ...addOns,
                    {
                        id_add_on: detail.id_add_ons_detail,
                        add_on_name: detail.add_ons_detail_name,
                        qty: detail.dose,
                        M_Item_id_item: detail.M_Item_id_item,
                    },
                ];
            });
        });

        datas = [...datas, {
            step: 'extra-page',
            form: {
                add_ons: addOns,
            },
        }];
    }

    if (formData.hasRecipe) {
        datas = [...datas, {
            step: 'recipe-page',
            form: {
                recipes: recipes.map((x) => {
                    const found = rawMaterials.find(rawMaterial => String(rawMaterial.id) === String(x.item_id));
                    const materialName = found ? found.name : '';

                    return {
                        id_material: x.item_id,
                        material_name: materialName,
                        qty: x.quantity,
                        ...ishasProperty(x, 'variants') ? { variants: x.variants } : {},
                    };
                }),
            },
        }];
    }

    if (formData.isWebOrderActive) {
        datas = [...datas, {
            step: 'marketplace-page',
            form: {
                marketplace: {
                    is_manual_price: +formData.onlineStoreManualChecked,
                    manual_price: +formData.onlineStoreManualPrice,
                    minimum_stock: +formData.onlineStoreMinimumStock,
                },
            },
        }];
    }

    return {
        id_item: formData && formData.id ? formData.id : '',
        item_no: currentProductDetailData ? currentProductDetailData.no : '',
        is_create: !currentProductDetailData,
        datas,
    };
};
