import React from 'react';
import { useTranslation, Trans } from 'react-i18next';
import DEFAULT_VALUE from './default-value-multi-lang';

const defaultValueWithTransNS = val => ({ ns: 'translation', defaultValue: val });

const LANG_KEY = {
    ALERT_DIALOG_CANCEL_DESCRIPTION: 'alertDialogCancel.description',
    SUCCESS_EXPORT_PROCESS: 'translation:success.exportProcess',
    DIALOG_INFO_EXPORT_DESCRIPTION: 'dialogInfoExport.description',
    DIALOG_INFO_EXPORT_RECIPE_DESCRIPTION: 'dialogInfoExportRecipe.description',
    FORM_CONTACT_SUPPORT_BANNER: 'form.contactSupportBanner',
    MAIN_OUTLET_MAIN_OUTLET_DESCRIPTION: 'main.outlet.mainOutletDescription',
};

const TransComponent = ({ i18nKey, children, t, ...props }) => (
    <Trans t={t} i18nKey={i18nKey} {...props}>
        {children}
    </Trans>
);

export const useTranslationHook = () => {
    const { t, i18n } = useTranslation(['Penjualan/itemv2', 'translation']);

    const retval = {
        i18n,
        TransComponent: ({ i18nKey, children, ...props }) => (
            <TransComponent {...{ i18nKey, t, ...props }}>{children}</TransComponent>
        ),
        DEFAULT_VALUE,
        LANG_KEY,
        LANG_DATA: {
            MAIN_TITLE: t('main.title', DEFAULT_VALUE.MAIN_TITLE),
            LABEL_IMPORT_DATA: t('label.importData', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_IMPORT_DATA)),
            LABEL_EXPORT_DATA: t('label.exportData', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_EXPORT_DATA)),
            LABEL_ADD_PRODUCT: t('label.addProduct', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_ADD_PRODUCT)),
            LABEL_EDIT_PRODUCT: t('label.editProduct', defaultValueWithTransNS('')),
            LABEL_PRODUCT_NAME: t('label.productName', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_PRODUCT_NAME)),
            LABEL_CATEGORY: t('label.category', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_CATEGORY)),
            LABEL_BASIC_PRICE: t('label.averagePrice', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_BASIC_PRICE)),
            LABEL_PURCHASE_PRICE: t('label.purchasePrice', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_PURCHASE_PRICE)),
            LABEL_LAST_PURCHASE_PRICE: t(
                'label.lastPurchasePrice',
                defaultValueWithTransNS(DEFAULT_VALUE.LABEL_LAST_PURCHASE_PRICE),
            ),
            LABEL_SELLING_PRICE: t('label.sellingPrice', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_SELLING_PRICE)),
            STATUS_ALL: t('status.all', defaultValueWithTransNS(DEFAULT_VALUE.STATUS_ALL)),
            STATUS_SHOW_IN_MENU: t('status.showInMenu', defaultValueWithTransNS(DEFAULT_VALUE.STATUS_SHOW_IN_MENU)),
            STATUS_HIDE_IN_MENU: t('status.hideInMenu', defaultValueWithTransNS(DEFAULT_VALUE.STATUS_HIDE_IN_MENU)),
            STATUS_ALL_CATEGORY: t('status.allCategory', defaultValueWithTransNS(DEFAULT_VALUE.STATUS_ALL_CATEGORY)),
            PLACEHOLDER_SEARCH: t('placeholder.search', defaultValueWithTransNS(DEFAULT_VALUE.PLACEHOLDER_SEARCH)),
            LABEL_PRODUCTS: t('label.products', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_PRODUCTS)),
            LABEL_PRODUCT: t('label.product', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_PRODUCT)),
            LABEL_ACTIVE: t('label.active', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_ACTIVE)),
            LABEL_INACTIVE: t('label.inactive', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_INACTIVE)),
            LABEL_GROUP: t('label.group', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_GROUP)),
            LABEL_PRODUCT_CATEGORY: t(
                'label.productCategory',
                defaultValueWithTransNS(DEFAULT_VALUE.LABEL_PRODUCT_CATEGORY),
            ),
            LABEL_INFORMATION: t('label.information', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_INFORMATION)),
            LABEL_NEXT_OPTION: t('label.nextOption', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_NEXT_OPTION)),
            LABEL_PRICE: t('label.price', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_PRICE)),
            SUBSCRIPTION_UNAUTHORIZED_BLOCKER_AND: t(
                'subscription.unauthorizedBlocker.and',
                defaultValueWithTransNS(DEFAULT_VALUE.SUBSCRIPTION_UNAUTHORIZED_BLOCKER_AND),
            ),
            LABEL_UNIT: t('label.unit', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_UNIT)),
            LABEL_MAX: t('label.max', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_MAX)),
            LABEL_CANCEL: t('label.cancel', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_CANCEL)),
            LABEL_NEXT_THEN: t('label.nextThen', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_NEXT_THEN)),
            LABEL_SAVE: t('label.save', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_SAVE)),
            PLACEHOLDER_EXAMPLE: t('placeholder.example', defaultValueWithTransNS(DEFAULT_VALUE.PLACEHOLDER_EXAMPLE)),
            PLACEHOLDER_FOOD: t('form.stepOne.food'),
            PLACEHOLDER_SELECT: t('placeholder.select', defaultValueWithTransNS(DEFAULT_VALUE.PLACEHOLDER_SELECT)),
            LABEL_BACK: t('label.back', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_BACK)),
            LABEL_LEARN_MORE: t('label.learnMore', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_LEARN_MORE)),
            LABEL_LOADING: t('label.loading', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_LOADING)),
            LABEL_ADD: t('label.add', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_ADD)),
            LABEL_EDIT: t('label.edit', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_EDIT)),
            LABEL_DELETE: t('label.delete', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_DELETE)),
            LABEL_ATTENTION: t('label.attention', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_ATTENTION)),
            LABEL_YES: t('label.yes', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_YES)),
            LABEL_NO: t('label.no', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_NO)),
            LABEL_ADD_2: t('label.add2', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_ADD_2)),
            LABEL_WEIGHT: t('label.weight', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_WEIGHT)),
            LABEL_WAITING: t('label.waiting', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_WAITING)),
            LABEL_NORMAL_PRICE: t('label.normalPrice', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_NORMAL_PRICE)),
            LABEL_ADD_CATEGORY: t('label.addCategory', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_ADD_CATEGORY)),
            LABEL_CATEGORY_NAME: t('label.categoryName', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_CATEGORY_NAME)),
            LABEL_SEQUENCE: t('label.sequence', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_SEQUENCE)),
            LABEL_CONTINUE: t('label.continue', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_CONTINUE)),
            LABEL_CONTINUE_2: t('label.continue2', defaultValueWithTransNS('')),
            LABEL_CALL_US: t('label.callUs', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_CALL_US)),
            LABEL_UPGRADE_NOW: t('label.upgradeNow', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_UPGRADE_NOW)),
            LABEL_CONFIRM: t('label.confirm', defaultValueWithTransNS(DEFAULT_VALUE.LABEL_CONFIRM)),
            LABEL_CHOOSE_CATEGORY: t('label.chooseCategory', defaultValueWithTransNS('')),
            LABEL_NO_CATEGORY: t('label.noCategory', defaultValueWithTransNS('')),
            LABEL_CLOSE: t('translation:label.close'),
            TOAST_DISABLED: t('toast.disabled', defaultValueWithTransNS('')),
            LABEL_EXPORT: t('label.export', defaultValueWithTransNS('')),
            FORM_STEP_PRODUCT_INFORMATION: t('form.step.productInformation'),
            FORM_STEP_VARIANT: t('form.step.variant'),
            FORM_STEP_EXTRA: t('form.step.extra'),
            FORM_STEP_RECIPE: t('form.step.recipe'),
            FORM_STEP_ONLINE_STORE: t('form.step.onlineStore'),
            FORM_STEP_ONE_OUTLET_LIST: t('form.stepOne.outletList'),
            FORM_STEP_ONE_PRODUCT_DESC: t('form.stepOne.productDesc'),
            FORM_STEP_ONE_PRODUCT_PHOTO: t('form.stepOne.productPhoto'),
            FORM_STEP_ONE_PRODUCT_PHOTO_DESC: t('form.stepOne.productPhotoDesc'),
            FORM_STEP_ONE_INVENTORY_MONITOR: t(
                'form.stepOne.inventoryMonitor',
                DEFAULT_VALUE.FORM_STEP_ONE_INVENTORY_MONITOR,
            ),
            FORM_STEP_ONE_SERIAL_NUMBER_DESC: t('form.stepOne.serialNumberDesc'),
            FORM_STEP_ONE_ACTIVATE_PRODUCT_HAS: t('form.stepOne.activateProductHas'),
            FORM_STEP_ONE_SET_AS_PARENT: t('form.stepOne.setAsParent'),
            FORM_STEP_ONE_PRODUCT_HAS_TRANSACTION: t('form.stepOne.productHasTransaction'),
            FORM_STEP_ONE_CONVERSION: t('form.stepOne.conversion'),
            FORM_STEP_ONE_MINIMUM_PURCHASE: t('form.stepOne.minimumPurchase'),
            FORM_STEP_ONE_PRODUCT_DIMENSION: t('form.stepOne.productDimension'),
            FORM_STEP_ONE_PRODUCT_DIMENSION_VOLUME: t('form.stepOne.productDimensionVolume'),
            FORM_STEP_ONE_EDIT_SELLING_PRICE_DESC: t('form.stepOne.editSellingPriceDesc'),
            FORM_STEP_ONE_EDIT_SELLING_PRICE_INFO: t('form.stepOne.editSellingPriceInfo'),
            FORM_STEP_ONE_WHOLESALE_PRICE: t('form.stepOne.wholesalePrice'),
            FORM_STEP_ONE_WHOLESALE_PRICE_DESC: t('form.stepOne.wholesalePriceDesc'),
            FORM_STEP_ONE_CATEGORY_NOT_AVAILABLE: t('form.stepOne.categoryNotAvailable'),
            FORM_STEP_ONE_CREATE_NEW_CATEGORY: t('form.stepOne.createNewCategory'),
            FORM_STEP_ONE_FAVORITE_PRODUCT: t('form.stepOne.favoriteProduct'),
            FORM_STEP_ONE_INVENTORY_MONITOR_DESC: t('form.stepOne.inventoryMonitorDesc'),
            FORM_STEP_ONE_ALERT_STOCK_DESC: t('form.stepOne.alertStockDesc'),
            FORM_STEP_ONE_PRODUCT_MINIMUM_STOCK: t('form.stepOne.productMinimumStock'),
            FORM_STEP_ONE_HAS_EXPIRED_DATE: t('form.stepOne.hasExpiredDate'),
            FORM_STEP_ONE_NOT_FOR_SALE_ACCESS: t('form.stepOne.notForSaleAccess'),
            FORM_STEP_ONE_NOT_FOR_SALE_ACCESS_DESC: t('form.stepOne.notForSaleAccessDesc'),
            FORM_DISCOUNT_PRODUCT_BANNER: t('form.discountProductBanner'),
            FORM_CONTACT_SUPPORT_BANNER: t('form.contactSupportBanner'),
            FORM_STEP_ONE_MINIMUM_QTY: t('form.stepOne.minimumQty'),
            FORM_STEP_ONE_UNIT_PRICE: t('form.stepOne.unitPrice'),
            IMPORTEXPORT_TOAST_ONPROCESS: t('importExportProduct.toast.onProcess'),
            IMPORTEXPORT_BUTTON_IMPORT_NEW: t('importExportProduct.mainButton.importNewProduct'),
            IMPORTEXPORT_BUTTON_IMPORT_CHANGE: t('importExportProduct.mainButton.importChangeProduct'),
            IMPORTEXPORT_BUTTON_IMPORT_NEW_VARIANT: t('importExportProduct.mainButton.importVariantNewProduct'),
            IMPORTEXPORT_BUTTON_IMPORT_CHANGE_RECIPE: t('importExportProduct.mainButton.importChangeRecipe'),
            IMPORTEXPORT_BUTTON_EXPORT_PRODUCT: t('importExportProduct.mainButton.exportProduct'),
            IMPORTEXPORT_BUTTON_EXPORT_RECIPE: t('importExportProduct.mainButton.exportRecipe'),
            IMPORTEXPORT_BANNER_FRONTDESCRIPTION: t('importExportProduct.banner.frontDescription'),
            IMPORTEXPORT_BANNER_BACKDESCRIPTION: t('importExportProduct.banner.backDescription'),
            IMPORTEXPORT_BANNER_ISBEINGPROCESSED: t('importExportProduct.banner.isBeingProcessed'),
            IMPORTEXPORT_VALIDATION_WRONGFORMAT: t('importExportProduct.validation.wrongFormat'),
            IMPORTEXPORT_VALIDATION_TEMPLATENOTMATCH: t('importExportProduct.validation.templateNotMatch'),
            IMPORTEXPORT_VALIDATION_FILESIZE: t('importExportProduct.validation.fileSize'),
            IMPORTEXPORT_VALIDATION_SELECTANOUTLET: t('importExportProduct.validation.selectAnOutlet'),
            IMPORTEXPORT_VALIDATION_IMPORTFILEFIRST: t('importExportProduct.validation.importFileFirst'),
            IMPORTEXPORT_IMPORT_HEADING: t('importExportProduct.import.heading'),
            IMPORTEXPORT_IMPORT_TITLE: t('importExportProduct.import.title'),
            IMPORTEXPORT_IMPORT_PROCESS: t('importExportProduct.import.process'),
            IMPORTEXPORT_IMPORT_CANCEL: t('importExportProduct.import.cancel'),
            IMPORTEXPORT_IMPORT_CANCEL_FRONTDESCRIPTION: t('importExportProduct.import.cancelFrontDescription'),
            IMPORTEXPORT_IMPORT_CANCEL_BACKDESCRIPTION: t('importExportProduct.import.cancelBackDescription'),
            IMPORTEXPORT_IMPORT_SUCCESS_DESCRIPTION: t('importExportProduct.import.successDescription'),
            IMPORTEXPORT_IMPORT_DESCRIPTION: t('importExportProduct.import.description'),
            IMPORTEXPORT_IMPORT_SELECT: t('importExportProduct.import.select'),
            IMPORTEXPORT_IMPORT_SELECT_FILE: t('importExportProduct.import.selectFile'),
            IMPORTEXPORT_IMPORT_CHANGE_FILE: t('importExportProduct.import.changeFile'),
            IMPORTEXPORT_IMPORT_UPLOAD_FILE_PLACEHOLDER: t('importExportProduct.import.uploadFilePlaceHolder'),
            IMPORTEXPORT_IMPORT_ALERTDIALOG_SUCCESS_FRONT: t(
                'importExportProduct.import.alertDialogSuccessFrontDescription',
            ),
            IMPORTEXPORT_IMPORT_ALERTDIALOG_SUCCESS_BACK: t(
                'importExportProduct.import.alertDialogSuccessBackDescription',
            ),
            IMPORTEXPORT_IMPORT_NEW_PRODUCTIMPORTS: t('importExportProduct.import.newProductImports'),
            IMPORTEXPORT_IMPORT_ASYNC_EXPORTTEMPLATE_SUCCESS: t(
                'importExportProduct.import.asyncExportTemplateSuccess',
            ),
            IMPORTEXPORT_IMPORT_ASYNC_EXPORTTEMPLATE_PROCESSED: t(
                'importExportProduct.import.asyncExportTemplateProcessed',
            ),
            IMPORTEXPORT_EXPORT_HEADING: t('importExportProduct.export.heading'),
            IMPORTEXPORT_EXPORT_SELECTOUTLET: t('importExportProduct.export.selectOutlet'),
            IMPORTEXPORT_EXPORT_SELECTOUTLET_DESCRIPTION: t('importExportProduct.export.selectOutletDescription'),
            IMPORTEXPORT_EXPORT_SELECTOUTLET_PLACEHOLDER: t('importExportProduct.export.selectOutletPlaceHolder'),
            IMPORTEXPORT_EXPORT_SELECTALLOUTLET_PLACEHOLDER: t('importExportProduct.export.selectAllOutletPlaceHolder'),
            IMPORTEXPORT_EXPORT_TEMPLATE_DESCRIPTION: t('importExportProduct.export.templateDescription'),
            IMPORTEXPORT_EXPORT_DONTHAVE_TEMPLATE: t('importExportProduct.export.dontHaveATemplate'),
            IMPORTEXPORT_EXPORT_DONTHAVE_TEMPLATE_DESCRIPTION: t(
                'importExportProduct.export.dontHaveATemplateDescription',
            ),
            IMPORTEXPORT_EXPORT_ALREADYHAVE_TEMPLATE: t('importExportProduct.export.alreadyHaveATemplate'),
            IMPORTEXPORT_EXPORT_ALREADYHAVE_TEMPLATE_DESCRIPTION: t(
                'importExportProduct.export.alreadyHaveATemplateDescription',
            ),
            IMPORTEXPORT_EXPORT_ATTENTION: t('importExportProduct.export.attention'),
            IMPORTEXPORT_EXPORT_ALLOUTLET: t('importExportProduct.export.allOutlet'),
            IMPORTEXPORT_EXPORT_RULES_FIRST: t('importExportProduct.export.importChangeProductRules.first'),
            IMPORTEXPORT_EXPORT_RULES_SECOND: t('importExportProduct.export.importChangeProductRules.second'),
            IMPORTEXPORT_EXPORT_RULES_THIRD: t('importExportProduct.export.importChangeProductRules.third'),
            IMPORTEXPORT_EXPORT_RULES_FOURTH: t('importExportProduct.export.importChangeProductRules.fourth'),
            IMPORTEXPORT_EXPORT_RULES_FIFTH: t('importExportProduct.export.importChangeProductRules.fifth'),
            IMPORTEXPORT_EXPORT_RULES_SIXTH: t('importExportProduct.export.importChangeProductRules.sixth'),
            IMPORTEXPORT_EXPORT_RULES_SEVENTH: t('importExportProduct.export.importChangeProductRules.seventh'),
            IMPORTEXPORT_EXPORT_RULES_EIGHTH: t('importExportProduct.export.importChangeProductRules.eighth'),
            IMPORTEXPORT_EXPORT_NEWRULES_FIRST: t('importExportProduct.export.importNewProductRules.first'),
            IMPORTEXPORT_EXPORT_NEWRULES_SECOND: t('importExportProduct.export.importNewProductRules.second'),
            IMPORTEXPORT_EXPORT_NEWRULES_THIRD: t('importExportProduct.export.importNewProductRules.third'),
            IMPORTEXPORT_EXPORT_NEWRULES_FOURTH: t('importExportProduct.export.importNewProductRules.fourth'),
            IMPORTEXPORT_EXPORT_NEWRULES_FIFTH: t('importExportProduct.export.importNewProductRules.fifth'),
            IMPORTEXPORT_EXPORT_NEWRULES_SIXTH: t('importExportProduct.export.importNewProductRules.sixth'),
            IMPORTEXPORT_EXPORT_NEWRULES_SEVENTH: t('importExportProduct.export.importNewProductRules.seventh'),
            IMPORTEXPORT_EXPORT_NEWRULES_EIGHTH: t('importExportProduct.export.importNewProductRules.eighth'),
            IMPORTEXPORT_EXPORT_NEWRULES_NINTH: t('importExportProduct.export.importNewProductRules.ninth'),
            IMPORTEXPORT_EXPORT_NEWRULES_TENTH: t('importExportProduct.export.importNewProductRules.tenth'),
            IMPORTEXPORT_IMPORT_RECIPERULES_FIRST: t('importExportProduct.export.importChangeRecipe.first'),
            IMPORTEXPORT_IMPORT_RECIPERULES_SECOND: t('importExportProduct.export.importChangeRecipe.second'),
            IMPORTEXPORT_IMPORT_RECIPERULES_THIRD: t('importExportProduct.export.importChangeRecipe.third'),
            IMPORTEXPORT_IMPORT_RECIPERULES_THIRD1: t('importExportProduct.export.importChangeRecipe.third1'),
            IMPORTEXPORT_IMPORT_RECIPERULES_THIRD2: t('importExportProduct.export.importChangeRecipe.third2'),
            IMPORTEXPORT_IMPORT_RECIPERULES_THIRD3: t('importExportProduct.export.importChangeRecipe.third3'),
            IMPORTEXPORT_IMPORT_RECIPERULES_FOURTH: t('importExportProduct.export.importChangeRecipe.fourth'),
            IMPORTEXPORT_IMPORT_RECIPERULES_FOURTH1: t('importExportProduct.export.importChangeRecipe.fourth1'),
            IMPORTEXPORT_IMPORT_RECIPERULES_FOURTH2: t('importExportProduct.export.importChangeRecipe.fourth2'),
            IMPORTEXPORT_IMPORT_RECIPERULES_FIFTH: t('importExportProduct.export.importChangeRecipe.fifth'),
            IMPORTEXPORT_IMPORT_RECIPERULES_SIXTH: t('importExportProduct.export.importChangeRecipe.sixth'),
            IMPORTEXPORT_IMPORT_RECIPERULES_SEVENTH: t('importExportProduct.export.importChangeRecipe.seventh'),
            IMPORTEXPORT_IMPORT_RECIPERULES_EIGHTH: t('importExportProduct.export.importChangeRecipe.eighth'),
            MAIN_DIALOG_EXPORT_TITLE: t('main.dialog.exportDialog.title'),
            MAIN_DIALOG_EXPORT_DESCRIPTION: t('main.dialog.exportDialog.description'),
            MAIN_DIALOG_EXPORT_RECIPE_TITLE: t('main.dialog.exportRecipeDialog.title'),
            MAIN_DIALOG_EXPORT_RECIPE_DESCRIPTION: t('main.dialog.exportRecipeDialog.description'),
            MAIN_DIALOG_FILTER_TITLE: t('main.dialog.filterDialog.title'),
            MAIN_DIALOG_FILTER_TITLE_LISTPRODUCT: t('main.dialog.filterDialog.titleListProduct'),
            MAIN_DIALOG_FILTER_SHOW_ALL_MENU: t('main.dialog.filterDialog.showAllOnMenu'),
            MAIN_DIALOG_FILTER_SHOW_ON_MENU_LABEL: t('main.dialog.filterDialog.showOnMenuLabel'),
            MAIN_DIALOG_FILTERDISPLAY_LABEL: t('main.dialog.filterDialog.filterDisplay.label'),
            MAIN_DIALOG_FILTERDISPLAY_OPTION_00: t('main.dialog.filterDialog.filterDisplay.option.00'),
            MAIN_DIALOG_FILTERDISPLAY_OPTION_10: t('main.dialog.filterDialog.filterDisplay.option.10'),
            MAIN_DIALOG_FILTERDISPLAY_OPTION_01: t('main.dialog.filterDialog.filterDisplay.option.01'),
            MAIN_DIALOG_DETAIL_PRODUCT_NAME: t('main.dialog.detailDialog.productName'),
            MAIN_DIALOG_SAVE_CONFIRM_MONITORING_TITLE: t('main.dialog.saveConfirmDialog.inventoryMonitoring.title'),
            MAIN_DIALOG_SAVE_CONFIRM_MONITORING_ACTIVATE: t(
                'main.dialog.saveConfirmDialog.inventoryMonitoring.activateMessage',
            ),
            MAIN_DIALOG_SAVE_CONFIRM_MONITORING_ZERO_STOCK: t(
                'main.dialog.saveConfirmDialog.inventoryMonitoring.zeroStockProductMessage',
            ),
            MAIN_DIALOG_SAVE_CONFIRM_MONITORING_ON_RECIPE: t(
                'main.dialog.saveConfirmDialog.inventoryMonitoring.dialogOnMonitoringRecipe',
            ),
            MAIN_DIALOG_DELETE_TITLE: t('main.dialog.deleteDialog.title'),
            MAIN_DIALOG_DELETE_SINGLE: t('main.dialog.deleteDialog.descriptionSingle'),
            MAIN_DIALOG_DELETE_MULTIPLE: t('main.dialog.deleteDialog.descriptionMultiple'),
            MAIN_DIALOG_IMPORT_OUTLETCHOOSED: t('main.dialog.importDialog.outletChoosed'),
            MAIN_DIALOG_IMPORT_CUSTOMCAPTION_IMPORT_MESSAGE: t(
                'main.dialog.importDialog.customCaptionMsg.importMaterialData.message',
            ),
            MAIN_DIALOG_IMPORT_CUSTOMCAPTION_IMPORT_WARNING: t(
                'main.dialog.importDialog.customCaptionMsg.importMaterialData.warning',
            ),
            MAIN_TOAST_SUCCEED_EXPORT: t('main.toast.succeedProductListExport'),
            MAIN_TOAST_SUCCEED_IMPORT: t('main.toast.succeedProductListImport'),
            MAIN_TOAST_SUCCEED_DELETE: t('main.toast.succeedDeleteProduct'),
            MAIN_TOAST_SUCCEED_DELETE_BULK: t('main.toast.succeedDeleteBulkProduct'),
            MAIN_TOAST_SUCCEED_EDIT: t('main.toast.succeedEditProduct'),
            MAIN_TOAST_SUCCEED_EDIT_MULTIPLE: t('main.toast.succeedEditProductMultiple'),
            MAIN_TOAST_FAILED_EDIT: t('main.toast.failedEditProduct'),
            MAIN_TOAST_FAILED_GETPRODUCT: t('main.toast.failedGetProducts'),
            MAIN_TOAST_FAILED_OPENADD: t('main.toast.failedOpenAddProduct'),
            MAIN_TOAST_FAILED_OPENEDIT: t('main.toast.failedOpenEditProduct'),
            MAIN_TOAST_FAILED_OPENPRODUCT: t('main.toast.failedOpenProductDetail'),
            MAIN_TOAST_FAILED_OPENPREVIEW: t('main.toast.failedOpenPreviewProduct'),
            MAIN_TOAST_FAILED_EXPORTDATA_JWT: t('main.toast.failedExportDataCausedJWT'),
            MAIN_TOAST_FAILED_EXPORT: t('main.toast.failedExportData'),
            MAIN_TOAST_FAILED_DUPLICATE_SKU: t('main.toast.failedDuplicateProductSKU'),
            MAIN_TOAST_FAILED_GET_CATEGORY: t('main.toast.failedGetCategoryData'),
            MAIN_SELECTMODE_STATUS_SETTING: t('main.selectMode.statusSettings'),
            MAIN_SELECTMODE_STATUSACTION_SHOW: t('main.selectMode.statusActionOption.show'),
            MAIN_SELECTMODE_STATUSACTION_UNSHOW: t('main.selectMode.statusActionOption.unshow'),
            MAIN_SELECTMODE_STATUSACTION_FAVORITE: t('main.selectMode.statusActionOption.favorite'),
            MAIN_SELECTMODE_STATUSACTION_UNFAVORITE: t('main.selectMode.statusActionOption.unfavorite'),
            MAIN_SELECTMODE_STATUSACTION_COGS: t('main.selectMode.statusActionOption.cogs'),
            MAIN_SELECTMODE_STATUSACTION_NONCOGS: t('main.selectMode.statusActionOption.noncogs'),
            MAIN_SELECTMODE_STATUSOPERATION_EDIT: t('main.selectMode.statusOperationLabel.edit'),
            MAIN_SELECTMODE_STATUSOPERATION_DELETE: t('main.selectMode.statusOperationLabel.delete'),
            MAIN_SELECTMODE_STATUSOPERATION_APPLY: t('main.selectMode.statusOperationLabel.apply'),
            MAIN_OUTLET_ALL_OUTLET: t('main.outlet.allOutlet', 'Semua Outlet'),
            MAIN_OUTLET_PER_OUTLET_SETTING: t('main.outlet.perOutletSetting'),
            MODAL_DETAIL_HEADING: t('modal.detailProduct.heading'),
            MODAL_DETAIL_INFO_TITLE: t('modal.detailProduct.productInformation.title'),
            MODAL_DETAIL_INFO_OUTLETLIST: t('modal.detailProduct.productInformation.outletList'),
            MODAL_DETAIL_INFO_PRODUCTNAME: t('modal.detailProduct.productInformation.productName'),
            MODAL_DETAIL_INFO_DESCRIPTION: t('modal.detailProduct.productInformation.description'),
            MODAL_DETAIL_INFO_CATEGORY: t('modal.detailProduct.productInformation.category'),
            MODAL_DETAIL_INFO_PRODUCTIMAGES: t('modal.detailProduct.productInformation.productImages'),
            MODAL_DETAIL_INFO_GROUPPARENT: t('modal.detailProduct.productInformation.groupParent'),
            MODAL_DETAIL_PRICEUNIT_TITLE: t('modal.detailProduct.priceAndUnit.title'),
            MODAL_DETAIL_PRICEUNIT_TABLE_UNIT: t('modal.detailProduct.priceAndUnit.table.unit'),
            MODAL_DETAIL_PRICEUNIT_TABLE_MODALPRICE: t('modal.detailProduct.priceAndUnit.table.averagePrice'),
            MODAL_DETAIL_PRICEUNIT_TABLE_PURCHASEPRICE: t('modal.detailProduct.priceAndUnit.table.purchasePrice'),
            MODAL_DETAIL_PRICEUNIT_TABLE_LAST_PURCHASEPRICE: t(
                'modal.detailProduct.priceAndUnit.table.lastPurchasePrice',
            ),
            MODAL_DETAIL_PRICEUNIT_TABLE_SELLINGPRICE: t('modal.detailProduct.priceAndUnit.table.sellingPrice'),
            MODAL_DETAIL_PRICEUNIT_TABLE_SKU: t('modal.detailProduct.priceAndUnit.table.sku'),
            MODAL_DETAIL_PRICEUNIT_TABLE_VOLUME: t('modal.detailProduct.priceAndUnit.table.volume'),
            MODAL_DETAIL_PRICEUNIT_TABLE_WEIGHT: t('modal.detailProduct.priceAndUnit.table.weight'),
            MODAL_DETAIL_PRICEUNIT_TABLE_MINPURCHASE: t('modal.detailProduct.priceAndUnit.table.minPurchase'),
            MODAL_DETAIL_WHOLESALE_TITLE: t('modal.detailProduct.wholesalePrice.title'),
            MODAL_DETAIL_WHOLESALE_TABLE_MINQTY: t('modal.detailProduct.wholesalePrice.table.minQty'),
            MODAL_DETAIL_WHOLESALE_TABLE_UNITPRICE: t('modal.detailProduct.wholesalePrice.table.unitPrice'),
            MODAL_DETAIL_VARIANT_TITLE: t('modal.detailProduct.variant.title'),
            MODAL_DETAIL_VARIANT_TABLE_VARIANT: t('modal.detailProduct.variant.table.variant'),
            MODAL_DETAIL_VARIANT_TABLE_CAPITALPRICE: t('modal.detailProduct.variant.table.capitalPrice'),
            MODAL_DETAIL_VARIANT_TABLE_PURCHASEPRICE: t('modal.detailProduct.variant.table.purchasePrice'),
            MODAL_DETAIL_VARIANT_TABLE_SELLINGPRICE: t('modal.detailProduct.variant.table.sellingPrice'),
            MODAL_DETAIL_VARIANT_TABLE_SKU: t('modal.detailProduct.variant.table.sku'),
            MODAL_DETAIL_VARIANT_TABLE_STATUS: t('modal.detailProduct.variant.table.status'),
            MODAL_DETAIL_EKSTRA_TITLE: t('modal.detailProduct.ekstra.title'),
            MODAL_DETAIL_EKSTRA_TABLE_EXTRANAME: t('modal.detailProduct.ekstra.table.extraName'),
            MODAL_DETAIL_EKSTRA_TABLE_OPTION: t('modal.detailProduct.ekstra.table.option'),
            MODAL_DETAIL_EKSTRA_TABLE_QUANTITY: t('modal.detailProduct.ekstra.table.quantity'),
            MODAL_DETAIL_EKSTRA_TABLE_CAPITALPRICE: t('modal.detailProduct.ekstra.table.capitalPrice'),
            MODAL_DETAIL_RECIPE_TITLE: t('modal.detailProduct.recipe.title'),
            MODAL_DETAIL_RECIPE_TABLE_INGREDIENT: t('modal.detailProduct.recipe.table.ingredients'),
            MODAL_DETAIL_RECIPE_TABLE_QUANTITY: t('modal.detailProduct.recipe.table.quantity'),
            MODAL_DETAIL_RECIPE_TABLE_UNIT: t('modal.detailProduct.recipe.table.unit'),
            MODAL_DETAIL_WEBORDER_TITLE: t('modal.detailProduct.weborder.title'),
            MODAL_DETAIL_WEBORDER_PRODUCTSPEC: t('modal.detailProduct.weborder.productSpecifications'),
            FORM_STEP_ONE_MINIMUMSTOCK_TOOLTIP: t('form.stepOne.minimumStock.tooltip'),
            FORM_STEP_ONE_MINIMUMSTOCK_PLACEHOLDER: t('form.stepOne.minimumStock.placeholder'),
            FORM_STEP_ONE_SNBN_SN_PRIME: t('form.stepOne.snbnContent.serialNumber.prime'),
            FORM_STEP_ONE_SNBN_SN_TOOLTIP: t('form.stepOne.snbnContent.serialNumber.tooltip'),
            FORM_STEP_ONE_SNBN_SN_TITLE: t('form.stepOne.snbnContent.serialNumber.title'),
            FORM_STEP_ONE_SNBN_SN_CAPTION: t('form.stepOne.snbnContent.serialNumber.caption'),
            FORM_STEP_ONE_SNBN_SN_SWITCH: t('form.stepOne.snbnContent.serialNumber.snSwitch'),
            FORM_STEP_ONE_SNBN_SN_DISABLE: t('form.stepOne.snbnContent.serialNumber.disableSnWhenBnEnabled'),
            FORM_STEP_ONE_SNBN_BN_PRIME: t('form.stepOne.snbnContent.batchNumber.prime'),
            FORM_STEP_ONE_SNBN_BN_TOOLTIP: t('form.stepOne.snbnContent.batchNumber.tooltip'),
            FORM_STEP_ONE_SNBN_BN_TITLE: t('form.stepOne.snbnContent.batchNumber.title'),
            FORM_STEP_ONE_SNBN_BN_EXPIRED: t('form.stepOne.snbnContent.batchNumber.hasExpiredDate'),
            FORM_STEP_ONE_SNBN_BN_SWITCH: t('form.stepOne.snbnContent.batchNumber.bnSwitch'),
            FORM_STEP_ONE_SNBN_BN_DISABLE: t('form.stepOne.snbnContent.batchNumber.disableBnWhenSnEnabled'),
            FORM_STEP_ONE_HASRECIPE: t('form.stepOne.snbnContent.hasRecipe'),
            FORM_STEP_ONE_SUBTITLE: t('form.stepOne.snbnContent.subtitle'),
            FORM_STEP_ONE_NOTE: t('form.stepOne.snbnContent.note'),
            FORM_STEP_ONE_CREATE_STOCKOPNAME: t('form.stepOne.snbnContent.createStockOpname'),
            FORM_STEP_ONE_UPGRADENOW: t('form.stepOne.snbnContent.upgradeNow'),
            FORM_STEP_ONE_PRODUCTGROUP_LABEL: t('form.stepOne.productGroup.label'),
            FORM_STEP_ONE_PRODUCTGROUP_SETPARENT: t('form.stepOne.productGroup.setAsParentCheckbox'),
            FORM_STEP_ONE_PRODUCTTAX_LABEL: t('form.stepOne.productTax.label'),
            FORM_STEP_ONE_PRODUCTTAX_DESCRIPTION: t('form.stepOne.productTax.description'),
            FORM_STEP_ONE_PRODUCTTAX_LINK: t('form.stepOne.productTax.link'),
            FORM_STEP_ONE_PRODUCTTAX_NOTAXSUBJECT: t('form.stepOne.productTax.noTaxSubject'),
            FORM_STEP_ONE_WHOLESALE_TITLE: t('form.stepOne.wholesalePriceSection.title'),
            FORM_STEP_ONE_WHOLESALE_NOTE: t('form.stepOne.wholesalePriceSection.note'),
            FORM_STEP_ONE_WHOLESALE_MAX: t('form.stepOne.wholesalePriceSection.maxWholesalePrice'),
            FORM_STEP_ONE_WHOLESALE_UNITPRICE: t('form.stepOne.wholesalePriceSection.unitPrice'),
            FORM_STEP_ONE_WHOLESALE_ADDPRICE: t('form.stepOne.wholesalePriceSection.addWholesalePrice'),
            FORM_STEP_ONE_WHOLESALE_ISVARIANT: t('form.stepOne.wholesalePriceSection.isHaveVariant'),
            FORM_STEP_ONE_WHOLESALE_STARTERUPGRADE: t('form.stepOne.wholesalePriceSection.starterAdvanceUpgrade'),
            FORM_STEP_ONE_WHOLESALE_MINQTY_LABEL: t('form.stepOne.wholesalePriceSection.minQty.label'),
            FORM_STEP_ONE_WHOLESALE_MINQTY_PLACEHOLDER: t('form.stepOne.wholesalePriceSection.minQty.placeholder'),
            FORM_STEP_ONE_GROUPDEFAULT: t('form.stepOne.groupDefault'),
            FORM_STEP_ONE_WEIGHT_TOOLTIP: t('form.stepOne.weightTooltip'),
            FORM_STEP_ONE_DIMENSION_TOOLTIP: t('form.stepOne.dimensionTooltip'),
            FORM_STEP_TWO_HEADING: t('form.stepTwo.heading'),
            FORM_STEP_TWO_SCHEMA_FIELDS_EMPTYNAME: t('form.stepTwo.validationSchema.variantFields.name.emptyName'),
            FORM_STEP_TWO_SCHEMA_FIELDS_OPTION_EMPTY: t(
                'form.stepTwo.validationSchema.variantFields.option.emptyOption',
            ),
            FORM_STEP_TWO_SCHEMA_FIELDS_OPTION_EMPTYNAME: t(
                'form.stepTwo.validationSchema.variantFields.option.emptyOptionName',
            ),
            FORM_STEP_TWO_SCHEMA_FIELDS_HAVECOMMA: t('form.stepTwo.validationSchema.variantFields.haveNameWithComma'),
            FORM_STEP_TWO_SCHEMA_FIELDS_ADDOPTION: t('form.stepTwo.validationSchema.variantFields.addVariantOption'),
            FORM_STEP_TWO_VARIANTS_PRICE_EMPTYPRICE: t('form.stepTwo.validationSchema.variants.price.emptyPrice'),
            FORM_STEP_TWO_VARIANTS_PRICE_EMPTYCAPITAL: t('form.stepTwo.validationSchema.variants.price.emptyCapital'),
            FORM_STEP_TWO_VARIANTS_PRICE_EMPTYPURCHASE: t('form.stepTwo.validationSchema.variants.price.emptyPurchase'),
            FORM_STEP_TWO_VARIANTS_SKU_EMPTY: t('form.stepTwo.validationSchema.variants.sku.emptySKU'),
            FORM_STEP_TWO_ERROR_DUPLICATESKU_MOBILE: t('form.stepTwo.validationSchema.errorSetter.duplicateSKU.mobile'),
            FORM_STEP_TWO_ERROR_DUPLICATESKU_DESKTOP: t(
                'form.stepTwo.validationSchema.errorSetter.duplicateSKU.desktop',
            ),
            FORM_STEP_TWO_FORM_SWITCH_LABEL: t('form.stepTwo.form.variantSwitch.label'),
            FORM_STEP_TWO_FORM_SWITCH_CAPTION: t('form.stepTwo.form.variantSwitch.caption'),
            FORM_STEP_TWO_FORM_SWITCH_HASWEBORDER: t('form.stepTwo.form.variantSwitch.hasWebOrder'),
            FORM_STEP_TWO_FORM_TYPE_LABEL: t('form.stepTwo.form.variantType.label'),
            FORM_STEP_TWO_FORM_TYPE_NAME_LABEL: t('form.stepTwo.form.variantType.name.label'),
            FORM_STEP_TWO_FORM_TYPE_NAME_PLACEHOLDER: t('form.stepTwo.form.variantType.name.placeholder'),
            FORM_STEP_TWO_FORM_TYPE_OPTION_LABEL: t('form.stepTwo.form.variantType.option.label'),
            FORM_STEP_TWO_FORM_TYPE_OPTION_PLACEHOLDER: t('form.stepTwo.form.variantType.option.placeholder'),
            FORM_STEP_TWO_FORM_TYPE_OPTION_INSTRUCTION: t('form.stepTwo.form.variantType.option.instruction'),
            FORM_STEP_TWO_FORM_TYPE_OPTION_ERROR: t('form.stepTwo.form.variantType.option.error.maximumVariantOption'),
            FORM_STEP_TWO_FORM_ADDVARIANT_TYPE: t('form.stepTwo.form.addVariantTypeButton'),
            FORM_STEP_TWO_FORM_VARIANTLIST_TITLE: t('form.stepTwo.form.variantList.title'),
            FORM_STEP_TWO_FORM_VARIANTLIST_LABEL: t('form.stepTwo.form.variantList.label'),
            FORM_STEP_TWO_FORM_VARIANTLIST_DESC: t('form.stepTwo.form.variantList.description'),
            FORM_STEP_TWO_FORM_VARIANTLIST_OPTION: t('form.stepTwo.form.variantList.variantOption'),
            FORM_STEP_TWO_FORM_VARIANTLIST_PURCHASEPRICE: t('form.stepTwo.form.variantList.purchasePrice'),
            FORM_STEP_TWO_FORM_VARIANTLIST_SELLINGPRICE: t('form.stepTwo.form.variantList.sellingPrice'),
            FORM_STEP_TWO_FORM_VARIANTLIST_CAPITALPRICE: t('form.stepTwo.form.variantList.capitalPrice'),
            FORM_STEP_TWO_FORM_VARIANTLIST_SKU: t('form.stepTwo.form.variantList.sku'),
            FORM_STEP_TWO_FORM_VARIANTLIST_SHOWONMENU: t('form.stepTwo.form.variantList.showOnMenu'),
            FORM_STEP_TWO_FORM_VARIANTLIST_EMPTYVARIANT: t('form.stepTwo.form.variantList.emptyVariants'),
            FORM_STEP_TWO_FORM_TOAST_DUPLICATE_OPTION: t('form.stepTwo.form.toast.duplicateVariantOption'),
            FORM_STEP_TWO_FORM_TOAST_DUPLICATE_NAME: t('form.stepTwo.form.toast.duplicateVariantName'),
            FORM_STEP_TWO_SUBDIALOG_WARNING_TITLE: t('form.stepTwo.subDialog.warningVariantActive.title'),
            FORM_STEP_TWO_SUBDIALOG_WARNING_DESC_WARNING: t(
                'form.stepTwo.subDialog.warningVariantActive.description.warning',
            ),
            FORM_STEP_TWO_SUBDIALOG_WARNING_DESC_INFO: t(
                'form.stepTwo.subDialog.warningVariantActive.description.info',
            ),
            FORM_STEP_TWO_SUBDIALOG_DELETE_TITLE: t('form.stepTwo.subDialog.deleteVariant.title'),
            FORM_STEP_TWO_SUBDIALOG_DELETE_STILLHAVE: t('form.stepTwo.subDialog.deleteVariant.stillHaveVariant'),
            FORM_STEP_TWO_SUBDIALOG_DELETE_LASTVARIANT: t('form.stepTwo.subDialog.deleteVariant.lastVariant'),
            FORM_STEP_TWO_SUBDIALOG_FAILDELETE_TITLE: t('form.stepTwo.subDialog.failDeleteVariant.title'),
            FORM_STEP_TWO_SUBDIALOG_FAILDELETE_DESC: t('form.stepTwo.subDialog.failDeleteVariant.description'),
            FORM_STEP_TWO_SUBDIALOG_DISABLEVARIANT_TITLE: t('form.stepTwo.subDialog.disabledVariant.title'),
            FORM_STEP_TWO_SUBDIALOG_DISABLEVARIANT_DESC: t('form.stepTwo.subDialog.disabledVariant.description'),
            FORM_STEP_TWO_SUBDIALOG_OUTLETALERT_TITLE: t('form.stepTwo.subDialog.outletAlert.title'),
            FORM_STEP_TWO_SUBDIALOG_OUTLETALERT_DESC: t('form.stepTwo.subDialog.outletAlert.description'),
            FORM_STEP_TWO_CANTACTIVATE_STOCK_NOTZERO: t('form.stepTwo.cantActivateVariantStockNotZero'),
            FORM_STEP_TWO_HAVE_TRANSACTION: t('form.stepTwo.variantHaveTransaction'),
            FORM_STEP_TWO_STARTER_UPGRADE: t('form.stepTwo.starterAdvanceUpgrade'),
            FORM_STEP_TWO_ALL_VARIANTS: t('form.stepTwo.allVariants'),
            FORM_STEP_ONE_SCHEMA_CATEGORYNAME: t('form.stepOne.validationSchema.categoryName'),
            FORM_STEP_ONE_SCHEMA_PRODUCTNAME: t('form.stepOne.validationSchema.productName'),
            FORM_STEP_ONE_SCHEMA_OUTLETNAME: t('form.stepOne.validationSchema.outletName'),
            FORM_STEP_ONE_SCHEMA_CATEGORY: t('form.stepOne.validationSchema.category'),
            FORM_STEP_ONE_SCHEMA_MINSTOCK: t('form.stepOne.validationSchema.minimumStock'),
            FORM_STEP_ONE_SCHEMA_SKU: t('form.stepOne.validationSchema.sku'),
            FORM_STEP_ONE_SCHEMA_UNITNAME: t('form.stepOne.validationSchema.unitName'),
            FORM_STEP_ONE_SCHEMA_PURCHASEPRICE: t('form.stepOne.validationSchema.purchasePrice'),
            FORM_STEP_ONE_SCHEMA_SELLINGPRICE: t('form.stepOne.validationSchema.sellingPrice'),
            FORM_STEP_ONE_SCHEMA_SELLINGPRICE_LESS_THAN_PROMO: t(
                'form.stepOne.validationSchema.sellingPriceLessThanPromo',
            ),
            FORM_STEP_ONE_SCHEMA_MINPURCHASE: t('form.stepOne.validationSchema.minimumPurchase'),
            FORM_STEP_ONE_SCHEMA_UNITPRICE: t('form.stepOne.validationSchema.unitPrice'),
            FORM_STEP_ONE_SCHEMA_MINQTY: t('form.stepOne.validationSchema.minQty'),
            FORM_STEP_ONE_SCHEMA_SAMEUNIT: t('form.stepOne.validationSchema.sameUnitName'),
            FORM_STEP_ONE_SCHEMA_SAMESKU: t('form.stepOne.validationSchema.sameSkuName'),
            FORM_STEP_ONE_SCHEMA_CONVERSION: t('form.stepOne.validationSchema.conversion'),
            FORM_STEP_ONE_SCHEMA_LESSSELLING: t('form.stepOne.validationSchema.lessSellingPrice'),
            FORM_STEP_ONE_SCHEMA_MOREMINPURCHASE: t('form.stepOne.validationSchema.moreMinPurchase'),
            FORM_STEP_THREE_HEADING: t('form.stepThree.heading'),
            FORM_STEP_THREE_EKSTRAINFO: t('form.stepThree.ekstraInfo'),
            FORM_STEP_THREE_SCHEMA_EXTRAUNCHOOSED: t('form.stepThree.validationSchema.ekstraUnchoosed'),
            FORM_STEP_THREE_SCHEMA_EXTRANOTADDYET: t('form.stepThree.validationSchema.ekstraNotAddedYet'),
            FORM_STEP_THREE_SCHEMA_EXTRAQTYEMPTY: t('form.stepThree.validationSchema.ekstraQtyEmpty'),
            FORM_STEP_THREE_SCHEMA_EXTRAQTYUNDERSIZE: t('form.stepThree.validationSchema.ekstraQtyUnderSize'),
            FORM_STEP_THREE_SCHEMA_EXTRASELLINGPRICE_EMPTY: t(
                'form.stepThree.validationSchema.ekstraSellingPriceEmpty',
            ),
            FORM_STEP_THREE_SCHEMA_EXTRASAMENAME: t('form.stepThree.validationSchema.ekstraSameName'),
            FORM_STEP_THREE_FORM_SWITCH_LABEL: t('form.stepThree.form.ekstraSwitch.label'),
            FORM_STEP_THREE_FORM_SWITCH_TOOLTIP: t('form.stepThree.form.ekstraSwitch.tooltip'),
            FORM_STEP_THREE_FORM_SWITCH_MOBILEINFO: t('form.stepThree.form.ekstraSwitch.mobileInfo'),
            FORM_STEP_THREE_FORM_SWITCH_ALERTHASEXTRA: t('form.stepThree.form.ekstraSwitch.alertSwitchOffHasExtra'),
            FORM_STEP_THREE_FORM_FIELD_LABEL: t('form.stepThree.form.ekstraField.label'),
            FORM_STEP_THREE_FORM_FIELD_FIELDS_TITLE: t('form.stepThree.form.ekstraField.fields.title'),
            FORM_STEP_THREE_FORM_FIELD_FIELDS_NAME_LABEL: t('form.stepThree.form.ekstraField.fields.extraName.label'),
            FORM_STEP_THREE_FORM_FIELD_FIELDS_NAME_PLACEHOLDER: t(
                'form.stepThree.form.ekstraField.fields.extraName.placeholder',
            ),
            FORM_STEP_THREE_FORM_FIELD_FIELDS_OPTIONNAME: t('form.stepThree.form.ekstraField.fields.extraOptionName'),
            FORM_STEP_THREE_FORM_FIELD_FIELDS_MEASURE: t('form.stepThree.form.ekstraField.fields.extraMeasure'),
            FORM_STEP_THREE_FORM_FIELD_FIELDS_CAPITALPRICE: t(
                'form.stepThree.form.ekstraField.fields.extraCapitalPrice',
            ),
            FORM_STEP_THREE_FORM_FIELD_FIELDS_SELLINGPRICE: t(
                'form.stepThree.form.ekstraField.fields.extraSellingPrice',
            ),
            FORM_STEP_THREE_FORM_FIELD_FIELDS_ADDBUTTON: t('form.stepThree.form.ekstraField.addExtraButton'),
            FORM_STEP_THREE_FORM_FIELD_FIELDS_EDITDATA: t('form.stepThree.form.ekstraField.editData'),
            FORM_STEP_THREE_FORM_FIELD_FIELDS_EDITTOOLTIP: t('form.stepThree.form.ekstraField.editDataTooltip'),
            FORM_STEP_THREE_FORM_FIELD_FIELDS_SAMEWITHMASTER: t(
                'form.stepThree.form.ekstraField.exactSameValueWithMaster',
            ),
            FORM_STEP_THREE_FORM_FIELD_FIELDS_SUB_EXTRA: t('form.stepThree.form.ekstraField.subExtra'),
            FORM_STEP_THREE_FORM_FIELD_FIELDS_INGREDIENT: t('form.stepThree.form.ekstraField.ingredient'),
            FORM_STEP_THREE_FORM_FIELD_FIELDS_INGREDIENT_NAME: t(
                'form.stepThree.form.ekstraField.fields.extraIngredient',
            ),
            FORM_STEP_THREE_FORM_FIELD_FIELDS_UNIT: t('form.stepThree.form.ekstraField.fields.extraUnit'),
            FORM_STEP_THREE_FORM_BANNER_DESC: t('form.stepThree.form.extraBanner.desc'),
            FORM_STEP_THREE_FORM_BANNER_LINK: t('form.stepThree.form.extraBanner.link'),
            FORM_STEP_THREE_FORM_EMPTY_INGREDIENT_TITLE: t(
                'form.stepThree.form.ekstraField.fields.emptyIngredientTitle',
            ),
            FORM_STEP_THREE_FORM_EMPTY_INGREDIENT_DESC: t('form.stepThree.form.ekstraField.fields.emptyIngredientDesc'),
            FORM_STEP_FOUR_HEADING: t('form.stepFour.heading'),
            FORM_STEP_FOUR_SCHEMA_UNCHOOSED: t('form.stepFour.validationSchema.unchoosedRawIngredient'),
            FORM_STEP_FOUR_SCHEMA_EMPTYMEASURE: t('form.stepFour.validationSchema.emptyRawIngredientMeasure'),
            FORM_STEP_FOUR_SCHEMA_ZEROQTY: t('form.stepFour.validationSchema.zeroRawIngredientQuantity'),
            FORM_STEP_FOUR_SCHEMA_NOTADDRAWYET: t('form.stepFour.validationSchema.notAddRawIngredientYet'),
            FORM_STEP_FOUR_SCHEMA_CANTSAME: t('form.stepFour.validationSchema.ingredientCannotBeTheSame'),
            FORM_STEP_FOUR_SWITCH_LABEL: t('form.stepFour.form.recipeSwitch.label'),
            FORM_STEP_FOUR_SWITCH_CAPTION: t('form.stepFour.form.recipeSwitch.caption'),
            FORM_STEP_FOUR_FIELD_TITLE: t('form.stepFour.form.recipeField.title'),
            FORM_STEP_FOUR_FIELD_LABEL: t('form.stepFour.form.recipeField.label'),
            FORM_STEP_FOUR_FIELD_FIELDS_RAWINGREDIENT: t('form.stepFour.form.recipeField.fields.recipeRawIngredient'),
            FORM_STEP_FOUR_FIELD_FIELDS_MAKERECIPEINGREDIENT: t(
                'form.stepFour.form.recipeField.fields.makeRecipeRawIngredient',
            ),
            FORM_STEP_FOUR_FIELD_FIELDS_CAPITALPRICE: t('form.stepFour.form.recipeField.fields.capitalPriceRecipe'),
            FORM_STEP_FOUR_FIELD_FIELDS_MEASURE: t('form.stepFour.form.recipeField.fields.recipeMeasure'),
            FORM_STEP_FOUR_FIELD_FIELDS_UNIT: t('form.stepFour.form.recipeField.fields.recipeUnit'),
            FORM_STEP_FOUR_FIELD_FIELDS_ADDBUTTON: t('form.stepFour.form.addRecipeButton'),
            FORM_STEP_FOUR_MONITORINGMUSTOFF: t('form.stepFour.monitoringMustOffAlert'),
            FORM_STEP_FOUR_MONITORINGMUSTOFF_NOTHAVEVARIANT: t('form.stepFour.monitoringMustOffAndNotHaveVariantAlert'),
            FORM_STEP_FOUR_VARIANTACTIVE: t('form.stepFour.isVariantActive'),
            FORM_STEP_FOUR_ALERTOFF_TITLE: t('form.stepFour.subDialog.activeRecipeAlertOff.title'),
            FORM_STEP_FOUR_ALERTOFF_DESC: t('form.stepFour.subDialog.activeRecipeAlertOff.description'),
            FORM_STEP_FOUR_ALERTON_TITLE: t('form.stepFour.subDialog.activeRecipeAlertOn.title'),
            FORM_STEP_FOUR_ALERTON_DESC: t('form.stepFour.subDialog.activeRecipeAlertOn.description'),
            FORM_STEP_FOUR_CREATEINGREDIENT_TITLE: t('form.stepFour.subDialog.createRawIngredient.title'),
            FORM_STEP_FOUR_CREATEINGREDIENT_NAME_LABEL: t(
                'form.stepFour.subDialog.createRawIngredient.rawMaterialName.label',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_NAME_PLACEHOLDER: t(
                'form.stepFour.subDialog.createRawIngredient.rawMaterialName.placeholder',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_RAWMONITORING: t(
                'form.stepFour.subDialog.createRawIngredient.rawMaterialMonitoring',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_RAWMINSTOCK_LABEL: t(
                'form.stepFour.subDialog.createRawIngredient.rawMaterialMinimumStock.label',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_RAWMINSTOCK_TOOLTIP: t(
                'form.stepFour.subDialog.createRawIngredient.rawMaterialMinimumStock.tooltip',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_SCHEMA_NAME: t(
                'form.stepFour.subDialog.createRawIngredient.validationSchema.name',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_SCHEMA_STOCKALERT: t(
                'form.stepFour.subDialog.createRawIngredient.validationSchema.stockAlert',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_SCHEMA_UNITNAME: t(
                'form.stepFour.subDialog.createRawIngredient.validationSchema.multiunit.unitName',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_SCHEMA_UNITSKU: t(
                'form.stepFour.subDialog.createRawIngredient.validationSchema.multiunit.unitSKU',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_SCHEMA_UNITPURCHASEPRICE: t(
                'form.stepFour.subDialog.createRawIngredient.validationSchema.multiunit.unitPurchasePrice',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_FIELD_UNIT_LABEL: t(
                'form.stepFour.subDialog.createRawIngredient.rawMaterialField.unit.label',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_FIELD_UNIT_PLACEHOLDER: t(
                'form.stepFour.subDialog.createRawIngredient.rawMaterialField.unit.placeholder',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_FIELD_CONVERSION: t(
                'form.stepFour.subDialog.createRawIngredient.rawMaterialField.conversion',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_FIELD_PURCHASEPRICE: t(
                'form.stepFour.subDialog.createRawIngredient.rawMaterialField.purchasePrice',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_FIELD_SKU_LABEL: t(
                'form.stepFour.subDialog.createRawIngredient.rawMaterialField.sku.label',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_FIELD_SKU_PLACEHOLDER: t(
                'form.stepFour.subDialog.createRawIngredient.rawMaterialField.sku.placeholder',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_FIELD_DEFAULTUNIT: t(
                'form.stepFour.subDialog.createRawIngredient.rawMaterialField.defaultUnit',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_FIELD_PURCHASEUNIT: t(
                'form.stepFour.subDialog.createRawIngredient.rawMaterialField.purchaseUnit',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_FIELD_ADDUNIT: t(
                'form.stepFour.subDialog.createRawIngredient.rawMaterialField.addUnit',
            ),
            FORM_STEP_FOUR_CREATEINGREDIENT_SUCCESSADD: t(
                'form.stepFour.subDialog.createRawIngredient.successAddMaterialToast',
            ),
            FORM_STEP_FOUR_PRODUCTHASVARIANT: t('form.stepFour.productHasVariants'),
            FORM_STEP_FOUR_SETTINGVARIANTRECIPES: t('form.stepFour.settingVariantRecipes'),
            FORM_STEP_FIVE_HEADING: t('form.stepFifth.heading'),
            FORM_STEP_FIVE_SCHEMA_LONGDESC: t('form.stepFifth.validationSchema.longDescMoreFiveThousand'),
            FORM_STEP_FIVE_SCHEMA_PRICEPERCENTAGE: t('form.stepFifth.validationSchema.pricePercentageMoreThanHundred'),
            FORM_STEP_FIVE_SCHEMA_ZERORAW: t('form.stepFifth.validationSchema.zeroRawIngredientQuantity'),
            FORM_STEP_FIVE_SCHEMA_EMPTYMINSTOCK: t('form.stepFifth.validationSchema.emptyMinimumStock'),
            FORM_STEP_FIVE_SCHEMA_OUTLET: t('form.stepFifth.validationSchema.outletNotChoosed'),
            FORM_STEP_FIVE_BANNER_TITLE: t('form.stepFifth.banner.title'),
            FORM_STEP_FIVE_BANNER_DESCRIPTION: t('form.stepFifth.banner.description'),
            FORM_STEP_FIVE_FORM_OUTLETLIST: t('form.stepFifth.form.outletList'),
            FORM_STEP_FIVE_FORM_UNINTEGRATED_NOTE: t('form.stepFifth.form.unintegrated.note'),
            FORM_STEP_FIVE_FORM_UNINTEGRATED_ACTION: t('form.stepFifth.form.unintegrated.action'),
            FORM_STEP_FIVE_FORM_SWITCH_LABEL: t('form.stepFifth.form.weborderSwitch.label'),
            FORM_STEP_FIVE_FORM_SWITCH_CAPTION: t('form.stepFifth.form.weborderSwitch.caption'),
            FORM_STEP_FIVE_FORM_CHECKBOX_LABEL: t('form.stepFifth.form.manualPriceCheckbox.label'),
            FORM_STEP_FIVE_FORM_CHECKBOX_CAPTION: t('form.stepFifth.form.manualPriceCheckbox.caption'),
            FORM_STEP_FIVE_FORM_CHECKBOX_NOTE: t('form.stepFifth.form.manualPriceCheckbox.note'),
            FORM_STEP_FIVE_FORM_PRESETBUTTON: t('form.stepFifth.form.presetButton'),
            FORM_STEP_FIVE_FORM_FAVORITE_LABEL: t('form.stepFifth.form.favoriteProductCheckbox.label'),
            FORM_STEP_FIVE_FORM_FAVORITE_TOOLTIP: t('form.stepFifth.form.favoriteProductCheckbox.tooltip'),
            FORM_STEP_FIVE_FORM_MINSTOCK_LABEL: t('form.stepFifth.form.minimumStockCheckbox.label'),
            FORM_STEP_FIVE_FORM_MINSTOCK_TOOLTIP: t('form.stepFifth.form.minimumStockCheckbox.tooltip'),
            FORM_STEP_FIVE_FORM_PRODUCTSPECS: t('form.stepFifth.form.productSpecs'),
            FORM_STEP_FIVE_SUBDIALOG_EDITMULTIPRICE_TITLE: t('form.stepFifth.subDialog.editMultipricePage.title'),
            FORM_STEP_FIVE_SUBDIALOG_EDITMULTIPRICE_HEADING: t('form.stepFifth.subDialog.editMultipricePage.heading'),
            FORM_STEP_FIVE_SUBDIALOG_EDITMULTIPRICE_OUTLETLIST: t(
                'form.stepFifth.subDialog.editMultipricePage.fields.outletList',
            ),
            FORM_STEP_FIVE_SUBDIALOG_EDITMULTIPRICE_FIELDS_STATUS: t(
                'form.stepFifth.subDialog.editMultipricePage.fields.statusSwitch',
            ),
            FORM_STEP_FIVE_SUBDIALOG_EDITMULTIPRICE_FIELDS_TYPENAME: t(
                'form.stepFifth.subDialog.editMultipricePage.fields.typeName',
            ),
            FORM_STEP_FIVE_SUBDIALOG_EDITMULTIPRICE_FIELDS_DESCRIPTION: t(
                'form.stepFifth.subDialog.editMultipricePage.fields.description',
            ),
            FORM_STEP_FIVE_SUBDIALOG_EDITMULTIPRICE_FIELDS_CUSTOMERGROUP: t(
                'form.stepFifth.subDialog.editMultipricePage.fields.customerGroup',
            ),
            FORM_STEP_FIVE_SUBDIALOG_PRODUCTNPRICE_HEADING: t('form.stepFifth.subDialog.productAndPrice.heading'),
            FORM_STEP_FIVE_SUBDIALOG_PRODUCTNPRICE_SUBTITLE: t('form.stepFifth.subDialog.productAndPrice.subtitle'),
            FORM_STEP_FIVE_SUBDIALOG_PRODUCTNPRICE_ADDPRODUCT_BUTTON: t(
                'form.stepFifth.subDialog.productAndPrice.addProductButton',
            ),
            FORM_STEP_FIVE_SUBDIALOG_PRODUCTNPRICE_PRICESETTING: t(
                'form.stepFifth.subDialog.productAndPrice.priceSetting',
            ),
            FORM_STEP_FIVE_SUBDIALOG_PRODUCTNPRICE_PRESETBUTTON: t(
                'form.stepFifth.subDialog.productAndPrice.productPricePresetButton',
            ),
            FORM_STEP_FIVE_SUBDIALOG_PRODUCTNPRICE_EDITABLETABLE_PRODUCT: t(
                'form.stepFifth.subDialog.productAndPrice.editableTableSelectedList.product',
            ),
            FORM_STEP_FIVE_SUBDIALOG_PRODUCTNPRICE_EDITABLETABLE_UNIT: t(
                'form.stepFifth.subDialog.productAndPrice.editableTableSelectedList.unit',
            ),
            FORM_STEP_FIVE_SUBDIALOG_PRODUCTNPRICE_EDITABLETABLE_PRICE: t(
                'form.stepFifth.subDialog.productAndPrice.editableTableSelectedList.price',
            ),
            FORM_STEP_FIVE_SUBDIALOG_PRODUCTNPRICE_EDITABLETABLE_CUSTOMPRICE: t(
                'form.stepFifth.subDialog.productAndPrice.editableTableSelectedList.customPrice',
            ),
            FORM_STEP_FIVE_SUBDIALOG_PRESETPRICE_TITLE: t('form.stepFifth.subDialog.presetPrice.title'),
            FORM_STEP_FIVE_SUBDIALOG_PRESETPRICE_MARKUPPRICE: t('form.stepFifth.subDialog.presetPrice.markupPrice'),
            FORM_STEP_FIVE_SUBDIALOG_PRESETPRICE_MARKDOWNPRICE: t('form.stepFifth.subDialog.presetPrice.markdownPrice'),
            FORM_STEP_FIVE_SUBDIALOG_PRESETPRICE_RESETTOMANUAL: t(
                'form.stepFifth.subDialog.presetPrice.resetToManualRadio',
            ),
            FORM_STEP_FIVE_SUBDIALOG_ADDPRODUCT_TITLE: t('form.stepFifth.subDialog.addWeborderProduct.title'),
            FORM_STEP_FIVE_SUBDIALOG_TABLE_PRODUCTNAME: t('form.stepFifth.subDialog.table.productName'),
            FORM_STEP_FIVE_SUBDIALOG_TABLE_CATEGORY: t('form.stepFifth.subDialog.table.category'),
            FORM_STEP_FIVE_SUBDIALOG_TABLE_SKU: t('form.stepFifth.subDialog.table.sku'),
            FORM_STEP_FIVE_SUBDIALOG_TABLE_UNIT: t('form.stepFifth.subDialog.table.unit'),
            FORM_STEP_FIVE_SUBDIALOG_TABLE_SELLINGPRICE: t('form.stepFifth.subDialog.table.sellingPrice'),
            FORM_STEP_FIVE_TOAST_ERROR_GETDATA: t('form.stepFifth.toast.errorGetOnlineShopData'),
            FORM_STEP_FIVE_TOAST_ERROR_GETPROVIDER: t('form.stepFifth.toast.errorGetOnlineShopProviderData'),
            FORM_STEP_FIVE_TOAST_FAILED_GETMULTIPRICE: t('form.stepFifth.toast.failedToGetMultipriceData'),
            FORM_PRICE_UNIT_WHOLESALEPRICE_MINIMUMSALEVALUE: t(
                'form.priceUnitStep.validationSchema.wholeSalePrice.minimumWholeSaleValue',
            ),
            FORM_PRICE_UNIT_WHOLESALEPRICE_EMPTYSALEVALUE: t(
                'form.priceUnitStep.validationSchema.wholeSalePrice.emptyWholeSaleValue',
            ),
            FORM_PRICE_UNIT_WHOLESALEPRICE_SELLINGPRICEMOREZERO: t(
                'form.priceUnitStep.validationSchema.wholeSalePrice.sellingPriceMoreThanEqualZero',
            ),
            FORM_PRICE_UNIT_WHOLESALEPRICE_EMPTYSELLINGPRICE: t(
                'form.priceUnitStep.validationSchema.wholeSalePrice.emptySellingPrice',
            ),
            FORM_PRICE_UNIT_WHOLESALEPRICE_ZEROSELLINGPRICE: t(
                'form.priceUnitStep.validationSchema.wholeSalePrice.zeroSellingPrice',
            ),
            FORM_PRICE_UNIT_WHOLESALEPRICE_EMPTYPERCENTAGEVALUE: t(
                'form.priceUnitStep.validationSchema.wholeSalePrice.emptyPercentageValue',
            ),
            FORM_PRICE_UNIT_WHOLESALEPRICE_PERCENTMINVALUE: t(
                'form.priceUnitStep.validationSchema.wholeSalePrice.percentageMinimumValue',
            ),
            FORM_PRICE_UNIT_WHOLESALEPRICE_PERCENTMAXVALUE: t(
                'form.priceUnitStep.validationSchema.wholeSalePrice.percentageMaximumValue',
            ),
            FORM_PRICE_UNIT_WHOLESALEPRICE_MINPURCHASEMOREWHOLESALE: t(
                'form.priceUnitStep.validationSchema.wholeSalePrice.minimumPurchaseMoreOrEqualWithWholeSalePrice',
            ),
            FORM_PRICE_UNIT_WHOLESALEPRICE_SELLINGPRICELESSWHOLESALE: t(
                'form.priceUnitStep.validationSchema.wholeSalePrice.sellingPriceForbidLessThanWholeSalePrice',
            ),
            FORM_PRICE_UNIT_WHOLESALEPRICE_MINQTYMOREPREV: t(
                'form.priceUnitStep.validationSchema.wholeSalePrice.minimumQuantityMoreThanPrevious',
            ),
            FORM_PRICE_UNIT_WHOLESALEPRICE_UNITPRICECHEAPERPREV: t(
                'form.priceUnitStep.validationSchema.wholeSalePrice.unitPriceMustCheaperThanPrevious',
            ),
            FORM_PRICE_UNIT_WHOLESALEPRICE_MINQTYMOREMINORDER: t(
                'form.priceUnitStep.validationSchema.wholeSalePrice.minimumQuantityMustMoreThanMinimumOrder',
            ),
            FORM_PRICE_UNIT_WHOLESALEPRICE_UNITPRICECHEAPERSELLINGPRICE: t(
                'form.priceUnitStep.validationSchema.wholeSalePrice.unitPriceMustCheaperThanSellingPrice',
            ),
            TOAST_ADD_TITLE: t('toast.activityLog.add.title'),
            TOAST_EDIT_TITLE: t('toast.activityLog.edit.title'),
            TOAST_MULTIPLE_SENTENCE: value => t('toast.activityLog.multipleSentence', { outlet_length: value }),
            DIALOG_INFO_EXPORT_TITLE: t('dialogInfoExport.title'),
            DIALOG_INFO_EXPORT_DESCRIPTION: t('dialogInfoExport.description'),
            DIALOG_INFO_EXPORT_RECIPE_TITLE: t('dialogInfoExportRecipe.title'),
            DIALOG_INFO_EXPORT_RECIPE_DESCRIPTION: t('dialogInfoExportRecipe.description'),
            SUCCESS_EXPORT_PROCESS: t('translation:success.exportProcess'),
            COACHMARK_SKIP: t('coachMark.skip', 'Lewati'),
            COACHMARK_NEXT: t('coachMark.next', 'Lanjut'),
            COACHMARK_OK: t('coachMark.ok', 'Oke'),
            COACHMARK_MODAL_TITLE: t('coachMark.modal.title', 'Panduan Selesai!'),
            COACHMARK_MODAL_DESCRIPTION: t(
                'coachMark.modal.description',
                'Anda telah menyelesaikan panduan pembuatan produk. Sudah siap untuk membuat produk anda sendiri?',
            ),
            COACHMARK_CANCEL_LABEL: t('coachMark.modal.cancelLabel', 'Ulangi Panduan'),
            COACHMARK_OUTLET_LIST_TITLE: t('coachMark.outletList.title', 'Daftar Outlet'),
            COACHMARK_OUTLET_LIST_CONTENT: t(
                'coachMark.outletList.content',
                'Tambahkan produk pada outlet yang diinginkan',
            ),
            COACHMARK_PRODUCT_INFORMATION_TITLE: t('coachMark.productInformation.title', 'Informasi Produk'),
            COACHMARK_PRODUCT_CATEGORY_TITLE: t('coachMark.productCategory.title', 'Kategori Produk'),
            COACHMARK_PRODUCT_CATEGORY_CONTENT: t(
                'coachMark.productCategory.content',
                'Buat kategori untuk mengelompokan produk-produk yang serupa',
            ),
            COACHMARK_NEXT_OPTION_TITLE: t('coachMark.nextOption.title', 'Opsi Lanjutan'),
            COACHMARK_MONITOR_INVENTORY_TITLE: t('coachMark.monitorInventory.title', 'Monitor Persediaan'),
            COACHMARK_MONITOR_INVENTORY_CONTENT: t(
                'coachMark.monitorInventory.content',
                'Aktifkan monitor persediaan untuk memantau kuantitas produk',
            ),
            COACHMARK_UNIT_AND_CONVERSION_TITLE: t('coachMark.unitAndConversion.title', 'Satuan & Konversi'),
            COACHMARK_SELLING_PRICE_TITLE: t('coachMark.sellingPrice.title', 'Harga Jual'),
            COACHMARK_PRODUCT_DIMENSION_TITLE: t('coachMark.productDimension.title', 'Dimensi Produk'),
            COACHMARK_PRODUCT_DIMENSION_CONTENT: t(
                'coachMark.productDimension.content',
                'Atur dimensi untuk produk yang dapat dikirim menggunakan jasa ekspedisi',
            ),
            COACHMARK_VARIANT_PRODUCT_TITLE: t('coachMark.variantProduct.title', 'Varian Produk'),
            COACHMARK_ADD_SELECT_VARIANT_TITLE: t('coachMark.addSelectVariant.title', 'Tambah Pilihan Varian'),
            COACHMARK_ADD_SELECT_VARIANT_CONTENT: t(
                'coachMark.addSelectVariant.content',
                'Klik "Tambah" jika varian memiliki lebih dari 1 pilihan',
            ),
            COACHMARK_VARIANT_LIST_TITLE: t('coachMark.variantList.title', 'Daftar varian'),
            COACHMARK_VARIANT_LIST_CONTENT: t(
                'coachMark.variantList.content',
                'Atur harga, SKU dan status pilihan varian pada daftar varian',
            ),
            COACHMARK_EXTRA_SECTION_TITLE: t('coachMark.extraSection.title', 'Ekstra'),
            COACHMARK_EXTRA_PRODUCT_LABEL: t('coachMark.extraSection.productLabel', 'Produk'),
            COACHMARK_EXTRA_VARIANT_LABEL: t('coachMark.extraSection.variantLabel', 'Varian'),
            COACHMARK_EXTRA_LABEL: t('coachMark.extraSection.extraLabel', 'Ekstra'),
            COACHMARK_EXTRA_COLOR_LABEL: t('coachMark.extraSection.colorLabel', 'Warna'),
            COACHMARK_EXTRA_WHITE_LABEL: t('coachMark.extraSection.whiteLabel', 'Putih'),
            COACHMARK_EXTRA_SAUCE_LABEL: t('coachMark.extraSection.sauceLabel', 'Saus'),
            COACHMARK_RECIPE_SECTION_TITLE: t('coachMark.recipeSection.title', 'Resep'),
            COACHMARK_RECIPE_SECTION_CONTENT: t(
                'coachMark.recipeSection.content',
                'Resep digunakan untuk produk yang diracik dan tidak dapat aktif dengan monitor persediaan',
            ),
            COACHMARK_MARKETPLACE_SECTION_TITLE: t('coachMark.marketplaceStep.title', 'Toko Online'),
            COACHMARK_MARKETPLACE_SECTION_CONTENT: t(
                'coachMark.marketplaceStep.content',
                'Jika produk dijual di Toko Online, aktifkan opsi ini untuk mengintegrasikan seluruh pesanan yang masuk secara langsung',
            ),
            PER_OUTLET_SETTING_TITLE: t('perOutletSettingForm.title'),
            PER_OUTLET_SETTING_PRICE_UNIT_TITLE: t('perOutletSettingForm.step.priceUnit'),
            PER_OUTLET_SETTING_VARIANT_TITLE: t('perOutletSettingForm.step.variant'),
            PER_OUTLET_SETTING_RECIPE_TITLE: t('perOutletSettingForm.step.recipe'),
            PER_OUTLET_SETTING_NEXT_OPTION_TITLE: t('perOutletSettingForm.step.nextOption'),
            PER_OUTLET_SETTING_WHOLESALE_PRICE_TITLE: t('perOutletSettingForm.step.wholesalePrice'),
            PER_OUTLET_SETTING_EXTRA_TITLE: t('perOutletSettingForm.step.extra'),
            PER_OUTLET_SETTING_SEARCH_OUTLET_PLACEHOLDER: t('perOutletSettingForm.searchOutletPlaceholder'),
            OUTLET_GROUP: t('perOutletSettingForm.outletGroup'),
            CURRENT_DATA: t('perOutletSettingForm.currentData'),
            OUTLET_GROUP_BANNER_TITLE: t('perOutletSettingForm.outletGroupBanner.title'),
            OUTLET_GROUP_BANNER_DESCRIPTION: t('perOutletSettingForm.outletGroupBanner.description'),
            OUTLET_GROUP_BANNER_LINK_TEXT: t('perOutletSettingForm.outletGroupBanner.linkText'),
            MANUAL_RECIPE_BANNER_DESCRIPTION: t('perOutletSettingForm.manualRecipeBanner.description'),
            EDIT_VARIANT_BANNER_DESCRIPTION: t('perOutletSettingForm.editVariantBanner.description'),
            NOT_SELLABEL_PRODUCT: t('perOutletSettingForm.notSellabelProduct'),
            MAX_CHANGEABLE_PRICE: t('perOutletSettingForm.maxChangeablePrice'),
            PLEASE_FILL_RECIPE: t('perOutletSettingForm.pleaseFillRecipe'),
            NO_RECIPE: t('perOutletSettingForm.noRecipe'),
            PER_VARIANT_RECIPE: t('perOutletSettingForm.perVariantRecipe'),
            SELECT_RECIPE: t('perOutletSettingForm.selectRecipe'),
            EQUALIZED: t('perOutletSettingForm.equalized'),
            SELECT_REFERENCE_RECIPE: t('perOutletSettingForm.selectReferenceRecipe'),
            RECIPE_MASTER: t('perOutletSettingForm.recipeMaster'),
            RECIPE_MASTER_CODE: t('perOutletSettingForm.recipeMasterCode'),
            RECIPE_MASTER_NAME: t('perOutletSettingForm.recipeMasterName'),
            MATERIAL_COUNT: t('perOutletSettingForm.materialCount'),
        },
        t,
    };

    return retval;
};
