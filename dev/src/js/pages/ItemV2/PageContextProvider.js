import React, { useContext, useMemo } from 'react';
import { Flex, ToastContext } from '@majoo-ui/react';
import PropTypes from 'prop-types';
import PageContext from './PageContext';
import * as apiServiceUtility from './api-service.utils';
import { DISPLAY_FILTER_TYPE, FORM_TYPE, IMPORT_PRODUCT_TYPE, ROLES_TYPE } from './enums';
import { isPrimeSubscription, matchMediaChecker, isStarterBasicSubscription } from '../../v2-utils';
import { MATCH_MEDIA_TYPE } from '../../v2-enum';
import { useTranslationHook } from './lang.utils';
import { updateOnboardingTask } from '../../services/session';
import userUtil from '../../utils/user.util';
import { createExportType } from './AlertDialogProcessExportTemplate/ContentDialog';
import { PRICE_SETTING } from '../Settings/ProductInventory/PurchasePriceSettings';
import { EXPORT_ALERT_DIALOG_TYPE } from './AlertDialogExportProduct/ContentDialog';
import { GROUP_TYPE } from '../Settings/BranchGroup/utils';
import ActivityLogDialog from '../../components/activityLogDialog/ActivityLogDialog';
import { ACTIVITY_LOG_TYPE } from '~/components/activityLogDialog/config';

let intervalCheckRunningProcess = null;

class PageContextProvider extends React.Component {
    defaultStateProduct = {
        data: [],
        totalData: 0,
        pageIndex: 0,
    };

    state = {
        SNBNTransactionData: null,
        webOrderDetail: null,
        loading: false, // loading table
        lastSavedCategory: '',
        categories: [],
        marketplaceOutlets: [],
        outlets: [],
        groupParents: [],
        units: [],
        productExtras: [],
        rawMaterials: [],
        marketplaceProviders: [],
        marketplaceProducts: [],
        filterData: {
            categoryId: 0,
            keyword: '',
            display: DISPLAY_FILTER_TYPE.ALL,
        },
        product: this.defaultStateProduct,
        selectedProductIds: [],
        selectedMassApplyOption: '',
        currentProductDetailStep: 1,
        currentFormType: FORM_TYPE.CREATE,
        currentProductDetailData: null, // data dari API product detail
        stepWizard: {
            previousStep: () => {},
            nextStep: () => {},
        },
        runningBackgroundProcessData: {
            background_process: false,
            is_create: false,
            is_variant: false,
            all_active_branch: [],
        },
        latestMassDeletedProduct: null,
        deletedProductIds: [],
        tableKey: 0,
        coachMarkProduct: true,
        loadingExtra: false,
        filterExtra: {
            hasMoreItems: false,
            pageSize: 20,
            totalData: 0,
            pageIndex: 0,
            keyword: '',
        },
        unauthorizedBlocker: false,
        pageLimit: 10,
        priceSetting: PRICE_SETTING.PURCHASE_PRICE,
        recipeMasterSetting: apiServiceUtility.defaultRecipeMasterSetting,
        exportBannerState: {
            open: false,
            customName: '',
        },
        showOutletGroupBanner: true,
        showActivityLog: false,
    };

    setState = this.setState.bind(this);

    componentDidMount = () => {
        this.initialFetching();
    };

    componentDidUpdate = prevProps => {
        const { filterBranch, translationData, selectOutlet, outletList, userAccess } = this.props;

        if (filterBranch !== prevProps.filterBranch) {
            this.handleRefetchDataAfterUpdateFilterBranch();
        }

        if (prevProps.translationData.LANG_DATA.MAIN_TITLE !== translationData.LANG_DATA.MAIN_TITLE) {
            this.handleFetchCategories();
        }

        if (
            outletList &&
            outletList.length > 0 &&
            JSON.stringify(prevProps.outletList) !== JSON.stringify(outletList)
        ) {
            const primaryOutlet = outletList.find(outlet => outlet.cabang_is_primary === '1');
            if (userAccess === 'Manager') {
                selectOutlet(String(outletList[1].id_cabang));
            } else if (outletList.length === 1) {
                selectOutlet(String(outletList[0].id_cabang));
            } else if (primaryOutlet) {
                selectOutlet(String(primaryOutlet.id_cabang));
            }
        }
    };

    componentWillUnmount = () => {
        if (intervalCheckRunningProcess) clearInterval(intervalCheckRunningProcess);
    };

    handleSetLoading = loading => {
        this.setState({ loading });
    };

    handleSetCoachMark = coachMarkProduct => {
        this.setState({ coachMarkProduct });
    };

    doShowingToast = val => {
        const { addToast } = this.props;

        addToast(val);
    };

    initialFetching = async () => {
        const { hideProgress, translationData, location, outletList, selectOutlet, userAccess, filterBranch } =
            this.props;

        await Promise.all([
            this.handleFetchMarketplaceOutlets(),
            this.handleFetchMarketplaceProviders(),
            this.handleFetchOutlets(),
            this.handleFetchCategories(),
            this.handleFetchProducts(),
            this.handleFetchProductsGroupParents(translationData),
            this.handleFetchUnits(),
            // this.handleFetchProductExtra(),
            this.handleFetchProductExtraV2({ index: 0 }),
            // this.handleFetchRawMaterials(),
            this.handleCheckRunningProcess(),
            this.handleFetchPriceSettings(),
        ]);

        hideProgress();

        // TODO: need review in future relate to card #86et4e5bf #86erp0cj9
        if (outletList && outletList.length > 0 && filterBranch === '') {
            const primaryOutlet = outletList.find(outlet => outlet.cabang_is_primary === '1');
            if (userAccess === 'Manager') {
                selectOutlet(String(outletList[1].id_cabang));
            } else if (outletList.length === 1) {
                selectOutlet(String(outletList[0].id_cabang));
            } else if (primaryOutlet) {
                selectOutlet(String(primaryOutlet.id_cabang));
            }
        }

        if (location.state) this.handleLocationState();
    };

    incrementTableKey = () => {
        this.setState(prevState => ({
            tableKey: prevState.tableKey + 1,
        }));
    };

    handleRefetchDataAfterUpdateFilterBranch = async () => {
        const { showProgress, hideProgress } = this.props;

        showProgress();

        await Promise.all([
            this.handleFetchProducts(),
            // this.handleFetchProductExtra(),
            // this.handleFetchRawMaterials(),
        ]);

        hideProgress();
    };

    handleFetchMarketplaceOutlets = async () => {
        const { translationData } = this.props;
        await apiServiceUtility.handleFetchMarketplaceOutlets({
            addToast: this.doShowingToast,
            setState: this.setState,
            translationData,
        });
    };

    handleFetchMarketplaceProviders = async () => {
        const { translationData } = this.props;
        await apiServiceUtility.handleFetchMarketplaceProviders({
            addToast: this.doShowingToast,
            setState: this.setState,
            translationData,
        });
    };

    handleFetchRawMaterials = async () => {
        const { filterBranch, idCabang, translationData, showProgress, hideProgress } = this.props;

        await apiServiceUtility.handleFetchRawMaterials({
            addToast: this.doShowingToast,
            setState: this.setState,
            filterBranch,
            idCabang,
            translationData,
            showProgress,
            hideProgress,
        });
    };

    handleFetchMasterRecipeDetail = async (id, onSuccess) => {
        const { showProgress, hideProgress } = this.props;
        showProgress();
        await apiServiceUtility.handleFetchMasterRecipeDetail({
            id,
            addToast: this.doShowingToast,
            onSuccess,
        });
        hideProgress();
    };

    handleFetchProductExtra = async () => {
        const { filterBranch, idCabang, translationData } = this.props;

        await apiServiceUtility.handleFetchProductExtra({
            addToast: this.doShowingToast,
            setState: this.setState,
            filterBranch,
            idCabang,
            translationData,
        });
    };

    handleFetchProductExtraV2 = async ({ query, index }) => {
        const { translationData } = this.props;
        const { productExtras, filterExtra } = this.state;

        if (!filterExtra.hasMoreItems && index === undefined) return;

        await apiServiceUtility.handleFetchProductExtraV2({
            addToast: this.doShowingToast,
            setState: this.setState,
            productExtras,
            filterExtra,
            query,
            index,
            translationData,
        });
    };

    handleFetchUnits = async () => {
        const { translationData } = this.props;
        await apiServiceUtility.handleFetchUnits({
            addToast: this.doShowingToast,
            setState: this.setState,
            translationData,
        });
    };

    handleFetchProductsGroupParents = async () => {
        const { translationData } = this.props;
        await apiServiceUtility.handleFetchProductsGroupParents({
            addToast: this.doShowingToast,
            setState: this.setState,
            translationData,
        });
    };

    handleFetchOutlets = async () => {
        const { translationData } = this.props;
        await apiServiceUtility.handleFetchOutlets({
            addToast: this.doShowingToast,
            setState: this.setState,
            translationData,
        });
    };

    handleFetchProducts = async tableStateProps => {
        const { filterBranch, translationData } = this.props;
        const { filterData, pageLimit, product } = this.state;

        const tableState = tableStateProps ?? {
            ...apiServiceUtility.defaultStateFetchProducts,
            pageIndex: product.pageIndex,
        };

        this.handleSetLoading(true);
        const cb = async (isPageSize = false) => {
            await apiServiceUtility.handleFetchProducts({
                addToast: this.doShowingToast,
                setState: this.setState,
                filterData,
                filterBranch,
                tableState: { ...tableState, ...(!isPageSize && { pageLimit }) },
                translationData,
            });
        };

        if (tableState.pageSize) {
            this.setState({ pageLimit: tableState.pageSize }, () => {
                cb(true);
            });
            return;
        }
        cb();
    };

    handleFetchCategories = async (lastSavedCategory = '') => {
        const { translationData } = this.props;
        const { LANG_DATA } = translationData;

        await apiServiceUtility.handleFetchCategories({
            addToast: this.doShowingToast,
            setState: this.setState,
            lastSavedCategory,
            LANG_DATA,
        });
    };

    handleFetchWebOrderDetail = async ({ outletId, vendor }) => {
        const { translationData } = this.props;
        const res = await apiServiceUtility.handleFetchWebOrderDetail({
            outletId,
            vendor,
            addToast: this.doShowingToast,
            setState: this.setState,
            translationData,
        });

        return res;
    };

    handleCreateCategory = async payload => {
        const {
            refCollections: { createCategoryDialogRef },
            translationData,
        } = this.props;

        await apiServiceUtility.handleCreateCategory({
            payload,
            createCategoryDialogRef,
            onFetchCategories: this.handleFetchCategories,
            addToast: this.doShowingToast,
            translationData,
        });
    };

    handleCreateRawMaterial = async payload => {
        const {
            refCollections: { createRawMaterialDialogRef },
            translationData,
        } = this.props;

        await apiServiceUtility.handleCreateRawMaterial({
            payload,
            onFetchRawMaterials: this.handleFetchRawMaterials,
            addToast: this.doShowingToast,
            createRawMaterialDialogRef,
            translationData,
        });
    };

    handleUpdateWebOrder = async payload => {
        const { marketplaceProviders } = this.state;
        const {
            refCollections: { multiPriceDetailRef },
            showProgress,
            hideProgress,
            translationData,
        } = this.props;

        showProgress();

        const marketplaceProvider = marketplaceProviders.find(x => x.alias === 'weborder');

        await apiServiceUtility.handleUpdateWebOrder({
            addToast: this.doShowingToast,
            payload,
            multiPriceDetailRef,
            vendor: marketplaceProvider.id,
            setState: this.setState,
            translationData,
        });

        hideProgress();
    };

    handleFetchProductDetail = async (productId, formType) => {
        const {
            filterBranch,
            refCollections: { productDetailRef, productPreviewRef },
            showProgress,
            hideProgress,
        } = this.props;

        showProgress();

        await apiServiceUtility.handleFetchProductDetail(
            {
                productId,
                addToast: this.doShowingToast,
                filterBranch,
            },
            ({ rawProductData, marketplaceProducts, SNBNTransactionData }) => {
                this.setState(
                    {
                        marketplaceProducts,
                        currentProductDetailData: rawProductData,
                        SNBNTransactionData,
                    },
                    () => {
                        if (formType === FORM_TYPE.EDIT) {
                            productDetailRef.current.handleShowDialog();
                        } else if (formType === FORM_TYPE.VIEW) {
                            productPreviewRef.current.handleShowDialog();
                        }
                    },
                );
            },
        );

        hideProgress();
    };

    handleFetchPerOutletSettingDetail = async productId => {
        const {
            refCollections: { perOutletSettingRef },
            showProgress,
            hideProgress,
        } = this.props;

        showProgress();

        await apiServiceUtility.handleFetchPerOutletSettingDetail(
            {
                productId,
                addToast: this.doShowingToast,
            },
            outletGroupSettingDetail => {
                this.setState(
                    {
                        outletGroupSettingDetail,
                    },
                    () => {
                        perOutletSettingRef.current.handleShowDialog();
                    },
                );
            },
        );

        hideProgress();
    };

    handleOpenFormProductToEdit = () => {
        const {
            refCollections: { productDetailRef },
        } = this.props;

        this.setState(
            {
                lastSavedCategory: '',
                currentFormType: FORM_TYPE.EDIT,
            },
            () => {
                productDetailRef.current.handleShowDialog();
            },
        );
    };

    handleSaveProduct = async payload => {
        const {
            refCollections: { productPreviewRef, productDetailRef },
            translationData,
        } = this.props;
        const { outlets, currentProductDetailData } = this.state;

        await apiServiceUtility.handleSaveProduct(
            {
                payload,
                outlets,
                addToast: this.doShowingToast,
                currentProductDetailData,
                translationData,
            },
            () => {
                this.setState({ webOrderDetail: null });
                productPreviewRef.current.handleHideDialog();
                productDetailRef.current.handleHideDialog();
                this.handleFetchProducts();
                updateOnboardingTask({
                    productPage: 1,
                });
            },
        );
    };

    handleDeleteProduct = async ({ productId, productName }) => {
        const { deletedProductIds } = this.state;
        const {
            showProgress,
            hideProgress,
            translationData,
            refCollections: { productDetailRef },
        } = this.props;
        const onSuccessfully = () => {
            this.setState(
                {
                    deletedProductIds: [...deletedProductIds, productId],
                },
                () => {
                    this.handleSetSelectedProductIds([]);
                    productDetailRef.current.handleHideDialog();
                    this.handleFetchProducts();
                    this.incrementTableKey();
                },
            );
        };

        showProgress();

        await apiServiceUtility.handleDeleteProduct({
            productId,
            productName,
            addToast: this.doShowingToast,
            onSuccessfully,
            translationData,
        });

        hideProgress();
    };

    handleRefetchProductsAfterUpdateFilterData = () => {
        const { filterData: updatedFilterData } = this.state;
        const fetchState = {
            ...apiServiceUtility.defaultStateFetchProducts,
            displayed: updatedFilterData.display,
            categoryId: updatedFilterData.categoryId,
            filterKeyword: updatedFilterData.keyword,
        };

        this.handleFetchProducts(fetchState);
    };

    handleChangeFilterState = (key, val) => {
        const { filterData, product } = this.state;

        let newFilterData = filterData;

        if (key === 'mobile_filter') {
            newFilterData = {
                ...filterData,
                ...val,
            };
        } else {
            newFilterData = {
                ...filterData,
                [key]: val,
            };
        }

        this.setState(
            {
                filterData: newFilterData,
                product: {
                    ...this.defaultStateProduct,
                    data: product.data,
                },
            },
            () => {
                this.handleRefetchProductsAfterUpdateFilterData();
            },
        );
    };

    handleOpenImportProductDialog = (importProductType = IMPORT_PRODUCT_TYPE.NEW_PRODUCT) => {
        const {
            refCollections: { importProductRef },
        } = this.props;
        importProductRef.current.handleShowDialog(importProductType, this.handleShowImportOutletsDialog);
    };

    handleCloseImportProductDialog = () => {
        const {
            refCollections: { importProductRef },
        } = this.props;
        importProductRef.current.handleHideDialog();
    };

    handleOpenCreateCategoryDialog = (selectedOutlets = []) => {
        const {
            refCollections: { createCategoryDialogRef },
        } = this.props;
        createCategoryDialogRef.current.handleShowDialog(selectedOutlets);
    };

    handleOpenCreateRawMaterialDialog = selectedOutlets => {
        const {
            refCollections: { createRawMaterialDialogRef },
        } = this.props;
        createRawMaterialDialogRef.current.handleShowDialog(selectedOutlets);
    };

    handleShowDeleteConfirmationDialog = (products = []) => {
        const {
            refCollections: { alertDialogDeleteConfirmRef },
        } = this.props;

        alertDialogDeleteConfirmRef.current.handleShowDialog(products);
    };

    handleOpenFormProduct = ({ formType, data }) => {
        const {
            refCollections: { productDetailRef },
        } = this.props;

        this.setState(
            {
                lastSavedCategory: '',
                currentFormType: formType,
                currentProductDetailData: null,
            },
            () => {
                if (formType === FORM_TYPE.DELETE) {
                    this.handleShowDeleteConfirmationDialog([data]);
                } else if (formType === FORM_TYPE.CREATE) {
                    productDetailRef.current.handleShowDialog();
                } else if (formType === FORM_TYPE.PER_OUTLET_SETTING) {
                    this.handleFetchPerOutletSettingDetail(data.id);
                } else {
                    this.handleFetchProductDetail(data.id, formType);
                }
            },
        );
    };

    handleOpenMultiPriceDetail = () => {
        const {
            refCollections: { multiPriceDetailRef },
        } = this.props;
        multiPriceDetailRef.current.handleShowDialog();
    };

    handleOpenActivityLog = () => {
        this.setState({ showActivityLog: true });
    };

    handleOpenProductPreview = formData => {
        const {
            refCollections: { productPreviewRef },
        } = this.props;
        productPreviewRef.current.handleShowDialog(formData);
    };

    handleSetSelectedProductIds = val => {
        const { deletedProductIds } = this.state;

        const isDeleted = selectedProductId =>
            deletedProductIds.some(deletedProductId => +deletedProductId === +selectedProductId);
        const filteredSelectedProductIds = val.filter(selectedProductId => !isDeleted(selectedProductId));

        this.setState({ selectedProductIds: filteredSelectedProductIds });
    };

    handleSetSelectedMassApplyOption = val => {
        this.setState({ selectedMassApplyOption: val });
    };

    handleShowProcessingInfoDialog = (isShowing = false) => {
        const {
            refCollections: { processingInfoDialogRef },
        } = this.props;

        if (isShowing) {
            processingInfoDialogRef.current.handleShowDialog();
        } else {
            processingInfoDialogRef.current.handleHideDialog();
        }
    };

    handleExportProduct = async () => {
        const { filterData } = this.state;
        const {
            filterBranch,
            idCabang,
            showProgress,
            hideProgress,
            refCollections: { alertDialogInfoExport },
            translationData: { i18n },
        } = this.props;

        showProgress();

        await apiServiceUtility.handleExportProduct({
            filterData,
            filterBranch,
            idCabang,
            addToast: this.doShowingToast,
            alertDialogInfoExport,
            lang: i18n.language,
            handleSetExportBannerState: this.handleSetExportBannerState,
        });

        hideProgress();
    };

    handleExportRecipe = async () => {
        const { filterData } = this.state;
        const {
            filterBranch,
            idCabang,
            showProgress,
            hideProgress,
            refCollections: { alertDialogInfoExport },
            translationData: { t, i18n },
        } = this.props;

        showProgress();

        await apiServiceUtility.handleExportRecipe({
            filterBranch,
            idCabang,
            addToast: this.doShowingToast,
            alertDialogInfoExport,
            handleSetExportBannerState: this.handleSetExportBannerState,
            t,
        });

        hideProgress();
    };

    handleOpenExportProductDialog = () => {
        const {
            refCollections: { alertDialogExportProductRef },
        } = this.props;

        alertDialogExportProductRef.current.handleShowDialog(EXPORT_ALERT_DIALOG_TYPE.EXPORT_PRODUCT);
    };

    handleOpenExportRecipeDialog = () => {
        const {
            refCollections: { alertDialogExportProductRef },
        } = this.props;

        alertDialogExportProductRef.current.handleShowDialog(EXPORT_ALERT_DIALOG_TYPE.EXPORT_RECIPE);
    };

    handleDownloadTemplateImportProduct = async ({ importProductType, selectedOutletIds }) => {
        const {
            userAccess,
            idCabang,
            showProgress,
            hideProgress,
            filterBranch,
            translationData: { LANG_DATA, i18n },
        } = this.props;
        const { outlets } = this.state;

        await apiServiceUtility.handleDownloadTemplateImportProduct(
            {
                showProgress,
                hideProgress,
                addToast: this.doShowingToast,
                importProductType,
                selectedOutletIds,
                outlets,
                userAccess,
                idCabang,
                filterBranch,
                lang: i18n.language,
            },
            ({ isAsync, isQueued }) => {
                if (!!isAsync && !!isQueued) this.handleShowProcessingExportTemplateDialog({ importProductType });
                if (!!isAsync && !isQueued) {
                    this.doShowingToast({
                        title: LANG_DATA.IMPORTEXPORT_IMPORT_PROCESS,
                        description: `${LANG_DATA.LABEL_EXPORT} ${createExportType({
                            importProductType,
                            LANG_DATA,
                        }).toLowerCase()} ${LANG_DATA.IMPORTEXPORT_IMPORT_ASYNC_EXPORTTEMPLATE_PROCESSED}`,
                        variant: 'pending',
                    });
                }
            },
        );
    };

    handleShowInfoExportTemplateDialog = ({ importProductType, selectedOutletIds }) => {
        const {
            refCollections: { alertDialogInfoExportTemplateRef },
        } = this.props;

        alertDialogInfoExportTemplateRef.current.handleShowDialog(importProductType, selectedOutletIds);
    };

    handleShowProcessingExportTemplateDialog = ({ importProductType }) => {
        const {
            refCollections: { alertDialogProcessExportTemplateRef },
        } = this.props;

        alertDialogProcessExportTemplateRef.current.handleShowDialog(importProductType);
    };

    handleShowProcessingImportProductDialog = ({ importProductType }) => {
        const {
            refCollections: { alertDialogProcessImportProductRef },
        } = this.props;

        alertDialogProcessImportProductRef.current.handleShowDialog(importProductType);
    };

    handleShowImportOutletsDialog = ({ importProductType, selectedOutletIds, onConfirm }) => {
        const {
            refCollections: { alertDialogRecipeImportOutletsRef },
        } = this.props;

        alertDialogRecipeImportOutletsRef.current.handleShowDialog(importProductType, selectedOutletIds, onConfirm);
    };

    handleImportProduct = async ({ importProductType, selectedOutletIds, fileToImport }) => {
        const { userAccess, idCabang, showProgress, hideProgress, filterBranch } = this.props;
        const { outlets } = this.state;

        await apiServiceUtility.handleImportProduct(
            {
                selectedOutletIds,
                fileToImport,
                importProductType,
                addToast: this.doShowingToast,
                outlets,
                userAccess,
                idCabang,
                showProgress,
                hideProgress,
                filterBranch,
            },
            () => {
                this.handleShowProcessingImportProductDialog({ importProductType });
                this.handleCheckRunningProcess();
            },
        );
    };

    handleApplyMassUpdate = async ({ action }) => {
        const { selectedProductIds } = this.state;
        const { idCabang, filterBranch, showProgress, hideProgress, translationData } = this.props;

        showProgress();

        await apiServiceUtility.handleApplyMassUpdate(
            {
                selectedProductIds,
                action,
                idCabang,
                filterBranch,
                addToast: this.doShowingToast,
                translationData,
            },
            () => {
                this.handleSetSelectedProductIds([]);
                this.handleFetchProducts();
            },
        );

        hideProgress();
    };

    handleMassDelete = selectedProductIds => {
        const { deletedProductIds } = this.state;

        const isDeleted = selectedProductId =>
            deletedProductIds.some(deletedProductId => +deletedProductId === +selectedProductId);
        const filteredSelectedProductIds = selectedProductIds.filter(
            selectedProductId => !isDeleted(selectedProductId),
        );

        this.setState(
            {
                latestMassDeletedProduct: null,
            },
            () => {
                this.processingMassDelete(filteredSelectedProductIds);
            },
        );
    };

    handleClearLatestMassDeletedProduct = () => {
        this.setState({
            latestMassDeletedProduct: null,
        });
    };

    processingMassDelete = async selectedProductIds => {
        const { deletedProductIds } = this.state;
        const { showProgress, hideProgress, translationData } = this.props;
        showProgress();

        await apiServiceUtility.handleMassDelete(
            {
                selectedProductIds,
                addToast: this.doShowingToast,
                translationData,
            },
            resData => {
                let failedDeletedStocks = [];

                if (resData) ({ failedDeletedStocks } = resData);

                const isFailedDeleted = selectedProductId =>
                    failedDeletedStocks.some(failedDeletedStock => +failedDeletedStock.id === +selectedProductId);

                this.setState(
                    {
                        deletedProductIds: [
                            ...deletedProductIds,
                            ...selectedProductIds
                                .map(x => Number(x.id))
                                .filter(selectedProductId => !isFailedDeleted(selectedProductId)),
                        ],
                        latestMassDeletedProduct: resData,
                    },
                    () => {
                        this.handleSetSelectedProductIds([]);
                        this.handleFetchProducts();
                        this.incrementTableKey();
                    },
                );
            },
        );

        hideProgress();
    };

    handleCheckRunningProcess = async () => {
        const { translationData } = this.props;
        await apiServiceUtility.handleCheckRunningProcess(
            {
                setState: this.setState,
                addToast: this.doShowingToast,
                translationData,
            },
            isProcessing => {
                if (isProcessing) {
                    if (intervalCheckRunningProcess) clearInterval(intervalCheckRunningProcess);
                    intervalCheckRunningProcess = setInterval(() => {
                        this.handleCheckRunningProcess();
                    }, 10000);
                } else if (intervalCheckRunningProcess) {
                    clearInterval(intervalCheckRunningProcess);
                }
            },
        );
    };

    handleShowMassMonitorStockDialog = selectedMassApplyOption => {
        const { refCollections } = this.props;

        refCollections.alertDialogMassMonitorStockRef.current.handleShowDialog(selectedMassApplyOption);
    };

    handleCheckTransactionSNBN = async itemId => {
        const { translationData } = this.props;
        await apiServiceUtility.handleCheckTransactionSNBN({
            itemId,
            addToast: this.doShowingToast,
            setState: this.setState,
            translationData,
        });
    };

    handleShowHasSNBNTransactionDialog = dataToSwitch => {
        const { refCollections } = this.props;

        refCollections.hasSNBNTransactionDialogRef.current.handleShowDialog(dataToSwitch);
    };

    handleShowFailedDeleteProductInformationDialog = () => {
        const { refCollections } = this.props;

        refCollections.failedDeleteProductInformationDialogRef.current.handleShowDialog();
    };

    handleShowMonitoringOffDialog = () => {
        const { refCollections } = this.props;

        refCollections.monitoringOffDialogRef.current.handleShowDialog();
    };

    handleLocationState = () => {
        const {
            location: { state },
        } = this.props;
        if (state.fromFtue) {
            this.handleOpenFormProduct({ formType: FORM_TYPE.CREATE });
        }
        window.history.replaceState({}, document.title);
    };

    isNotAdmin = () => {
        const { listOutlet } = this.props;
        const getUserPermissionId = userUtil.getLocalConfigByKey('permissionId');
        let isNotAdmin = ROLES_TYPE.ADMIN !== String(getUserPermissionId);
        const hasMultiOutlet = userUtil.getLocalConfigByKey('isMultiOutlet');
        if (ROLES_TYPE.MANAGER === String(getUserPermissionId)) {
            const isSingleOutlet = hasMultiOutlet ? hasMultiOutlet === 0 : listOutlet.length === 1;
            isNotAdmin = !isSingleOutlet;
        }
        return isNotAdmin;
    };

    handleFetchPriceSettings = async () => {
        await apiServiceUtility.handleFetchPriceSettings({
            addToast: this.doShowingToast,
            setState: this.setState,
        });
    };

    handleFetchRecipeMasterSettings = async () => {
        await apiServiceUtility.handleFetchRecipeMasterSettings({
            addToast: this.doShowingToast,
            setState: this.setState,
        });
    };

    handleSetExportBannerState = ({ open, customName }) => {
        this.setState({ exportBannerState: { open, customName } });
    };

    handleUpdateOutletGroupSettings = async payload => {
        const { showProgress, hideProgress } = this.props;
        showProgress();
        const {
            refCollections: { perOutletSettingRef },
            translationData,
        } = this.props;

        await apiServiceUtility.handleUpdateOutletGroupSettings(
            {
                payload,
                addToast: this.doShowingToast,
                translationData,
            },
            () => {
                this.setState({ webOrderDetail: null });
                perOutletSettingRef.current.handleHideDialog();
                this.handleFetchProducts();
                hideProgress();
            },
        );
    };

    handleShowOutletGroupBanner = show => {
        this.setState({ showOutletGroupBanner: show });
    };

    createContextValue = () => ({
        ...this.props,
        ...this.state,
        onChangeFilterState: this.handleChangeFilterState,
        onFetchProducts: this.handleFetchProducts,
        onSetCurrentProductDetailStep: currentProductDetailStep => {
            this.setState({ currentProductDetailStep });
        },
        onSetStepWizard: stepWizard => {
            this.setState({ stepWizard });
        },
        onOpenFormProduct: this.handleOpenFormProduct,
        onOpenImportProductDialog: this.handleOpenImportProductDialog,
        onCloseImportProductDialog: this.handleCloseImportProductDialog,
        onOpenCreateCategoryDialog: this.handleOpenCreateCategoryDialog,
        onOpenCreateRawMaterialDialog: this.handleOpenCreateRawMaterialDialog,
        onCreateCategory: this.handleCreateCategory,
        onCreateRawMaterial: this.handleCreateRawMaterial,
        onUpdateWebOrder: this.handleUpdateWebOrder,
        onOpenMultiPriceDetail: this.handleOpenMultiPriceDetail,
        onFetchWebOrderDetail: this.handleFetchWebOrderDetail,
        onOpenActivityLog: this.handleOpenActivityLog,
        onOpenProductPreview: this.handleOpenProductPreview,
        onSetSelectedProductIds: this.handleSetSelectedProductIds,
        onSetSelectedMassApplyOption: this.handleSetSelectedMassApplyOption,
        onSaveProduct: this.handleSaveProduct,
        onShowProcessingInfoDialog: this.handleShowProcessingInfoDialog,
        onDeleteProduct: this.handleDeleteProduct,
        onOpenFormProductToEdit: this.handleOpenFormProductToEdit,
        onOpenExportProductDialog: this.handleOpenExportProductDialog,
        onOpenExportRecipeDialog: this.handleOpenExportRecipeDialog,
        onExportProduct: this.handleExportProduct,
        onExportRecipe: this.handleExportRecipe,
        onDownloadTemplateImportProduct: this.handleDownloadTemplateImportProduct,
        onImportProduct: this.handleImportProduct,
        onApplyMassUpdate: this.handleApplyMassUpdate,
        onShowMassMonitorStockDialog: this.handleShowMassMonitorStockDialog,
        onMassDeleteProduct: this.handleMassDelete,
        onShowDeleteConfirmationDialog: this.handleShowDeleteConfirmationDialog,
        onCheckRunningProcess: this.handleCheckRunningProcess,
        onCheckTransactionSNBN: this.handleCheckTransactionSNBN,
        onShowHasSNBNTransactionDialog: this.handleShowHasSNBNTransactionDialog,
        onClearLatestMassDeletedProduct: this.handleClearLatestMassDeletedProduct,
        onShowFailedDeleteProductInformationDialog: this.handleShowFailedDeleteProductInformationDialog,
        onUpdateCoachMark: this.handleSetCoachMark,
        onFetchProductsExtra: this.handleFetchProductExtraV2,
        onChangeUnauthorizedBlocker: unauthorizedBlocker => {
            this.setState({ unauthorizedBlocker });
        },
        onShowMonitoringOffDialog: this.handleShowMonitoringOffDialog,
        onShowInfoExportTemplate: this.handleShowInfoExportTemplateDialog,
        onShowProcessingExportTemplate: this.handleShowProcessingExportTemplateDialog,
        isNotAdmin: this.isNotAdmin(),
        handleFetchRawMaterials: this.handleFetchRawMaterials,
        handleFetchPriceSettings: this.handleFetchPriceSettings,
        handleFetchMasterRecipeDetail: this.handleFetchMasterRecipeDetail,
        handleFetchRecipeMasterSettings: this.handleFetchRecipeMasterSettings,
        handleSetExportBannerState: this.handleSetExportBannerState,
        onShowImportOutletsDialog: this.handleShowImportOutletsDialog,
        handleUpdateOutletGroupSettings: this.handleUpdateOutletGroupSettings,
        handleShowOutletGroupBanner: this.handleShowOutletGroupBanner,
    });

    render() {
        const { children, filterBranch, idCabang } = this.props;
        const { showActivityLog } = this.state;

        const contextValue = this.createContextValue();

        return (
            <PageContext.Provider value={contextValue}>
                {children}
                {showActivityLog && (
                    <ActivityLogDialog
                        isOpen={showActivityLog}
                        onClose={() => {
                            this.setState({ showActivityLog: false });
                        }}
                        filterBranch={filterBranch}
                        idCabang={idCabang}
                        logType={ACTIVITY_LOG_TYPE.PRODUCT}
                    />
                )}
            </PageContext.Provider>
        );
    }
}

PageContextProvider.propTypes = {
    children: PropTypes.node.isRequired,
    filterBranch: PropTypes.string.isRequired,
    idCabang: PropTypes.string.isRequired,
    addToast: PropTypes.func.isRequired,
    showProgress: PropTypes.func.isRequired,
    hideProgress: PropTypes.func.isRequired,
    refCollections: PropTypes.shape({
        multiPriceDetailRef: PropTypes.shape({
            current: PropTypes.shape({
                handleShowDialog: PropTypes.func,
            }),
        }),
        productDetailRef: PropTypes.shape({
            current: PropTypes.shape({
                handleShowDialog: PropTypes.func,
            }),
        }),
        productPreviewRef: PropTypes.shape({
            current: PropTypes.shape({
                handleShowDialog: PropTypes.func,
            }),
        }),
        perOutletSettingRef: PropTypes.shape({
            current: PropTypes.shape({
                handleShowDialog: PropTypes.func,
            }),
        }),
        createCategoryDialogRef: PropTypes.shape({
            current: PropTypes.shape({
                handleShowDialog: PropTypes.func,
            }),
        }),
        createRawMaterialDialogRef: PropTypes.shape({
            current: PropTypes.shape({
                handleShowDialog: PropTypes.func,
            }),
        }),
    }).isRequired,
    translationData: PropTypes.shape({
        i18n: PropTypes.shape({
            language: PropTypes.string,
        }),
    }).isRequired,
    location: PropTypes.shape({
        state: PropTypes.shape({
            fromFtue: PropTypes.bool,
        }).isRequired,
    }).isRequired,
    listOutlet: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
    menuPrivilege: PropTypes.shape({
        isCanCreate: PropTypes.bool.isRequired,
        isCanDelete: PropTypes.bool.isRequired,
        isCanUpdate: PropTypes.bool.isRequired,
        isCanView: PropTypes.bool.isRequired,
        isCanVoid: PropTypes.bool.isRequired,
        isWhitelistMerchant: PropTypes.bool.isRequired,
    }).isRequired,
    selectOutlet: PropTypes.func.isRequired,
    outletList: PropTypes.arrayOf(
        PropTypes.shape({
            id_cabang: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
            cabang_is_primary: PropTypes.string,
        }),
    ).isRequired,
    userAccess: PropTypes.string.isRequired,
};

const ContextProvider = ({ children, parentProps, refCollections }) => {
    const { addToast } = useContext(ToastContext);
    const isInMobileView = useMemo(() => !matchMediaChecker(MATCH_MEDIA_TYPE.MD), []);
    const translationData = useTranslationHook();
    const outletGroups = (parentProps.outletGroupList || []).filter(group => group.type === GROUP_TYPE.PRODUCT);

    return (
        <PageContextProvider
            {...parentProps}
            {...{
                addToast,
                refCollections,
                isPrimeSubscription: isPrimeSubscription(),
                isInMobileView,
                translationData,
                isStarterBasicSubscription: isStarterBasicSubscription(),
                outletGroupList: outletGroups,
                outletGroupOptions: outletGroups.map(outletGroup => ({
                    id: outletGroup.id_cabangs,
                    value: outletGroup.id_cabangs,
                    name: outletGroup.group_name,
                    render: () => (
                        <Flex css={{ width: '100%' }} justify="between" align="center">
                            <Flex>{outletGroup.group_name}</Flex>
                            <Flex css={{ color: '$textSecondary', fontSize: 'small' }}>
                                {(outletGroup.cabang_details || []).length} Outlet
                            </Flex>{' '}
                        </Flex>
                    ),
                })),
            }}
        >
            {children}
        </PageContextProvider>
    );
};

ContextProvider.propTypes = {
    children: PropTypes.node.isRequired,
    parentProps: PropTypes.shape({
        filterBranch: PropTypes.string,
        idCabang: PropTypes.string,
        outletGroupList: PropTypes.arrayOf(PropTypes.shape({})),
    }).isRequired,
    refCollections: PropTypes.shape({}).isRequired,
};

export default ContextProvider;
