// TODO: dibikin lebih clean lagi
import React, { useContext } from 'react';
import { AlertDialog, Box } from '@majoo-ui/react';
import { foundations } from '@majoo-ui/core';
import PageContext from '../PageContext';

const { textRed, primary500 } = foundations.colors;

const ContentDialog = ({
    isOpen, onClose
}) => {
    const { isInMobileView, latestMassDeletedProduct } = useContext(PageContext);

    if (!latestMassDeletedProduct) return null;

    return (
        <AlertDialog
            isMobile={isInMobileView}
            onConfirm={onClose}
            onCancel={onClose}
            open={isOpen}
            singleButton
            labelConfirm="Kembali"
            css={{ width: isInMobileView ? 'unset' : '422px' }}
            title="Informasi Gagal Hapus"
            description={(
                <Box css={{ overflowY: 'auto', maxHeight: '360px' }}>
                    <span>
                        Produk berhasil dihapus:
                        <b style={{ color: primary500 }}>
                            {' '}
                            {latestMassDeletedProduct.success}
                        </b>
                    </span>

                    <p>
                        Produk gagal dihapus:
                        <b style={{ color: textRed }}>
                            {' '}
                            {latestMassDeletedProduct.failed}
                        </b>
                        <br />
                    </p>

                    <p>Penyebab gagal hapus:</p>

                    {latestMassDeletedProduct.stock.length ? (
                        <p>
                            <b>Stok akhir produk harus = 0</b>
                            <br />
                            <ol type="A">
                                {latestMassDeletedProduct.stock.map(outlet => (
                                    <li key={outlet.outlet_name}>
                                        {outlet.outlet_name}
                                        <ol>
                                            {outlet.data.map(item => (
                                                <li key={item.id}>
                                                    {item.name}
                                                </li>
                                            ))}
                                        </ol>
                                    </li>
                                ))}
                            </ol>
                        </p>
                    ) : ''}

                    {latestMassDeletedProduct.parent.length ? (
                        <p>
                            <b>Produk masih sebagai induk group:</b>
                            <br />
                            {latestMassDeletedProduct.parent.map((item, index) => (
                                <span style={{ paddingLeft: 15 }}>
                                    {index + 1}
                                    .
                                    {' '}
                                    {item.name}
                                    <br />
                                </span>
                            ))}
                        </p>
                    ) : ''}

                    {latestMassDeletedProduct.promo.length ? (
                        <p>
                            <b>Produk masih terdaftar pada promo:</b>
                            <br />
                            {latestMassDeletedProduct.promo.map((item, index) => (
                                <span style={{ paddingLeft: 15 }}>
                                    {index + 1}
                                    .
                                    {' '}
                                    {item.name}
                                    <br />
                                </span>
                            ))}
                        </p>
                    ) : ''}

                    {latestMassDeletedProduct.marketplace.length ? (
                        <p>
                            <b>Produk terkoneksi dengan Marketplace (Tokopedia/Shopee/Bukalapak) atau Order Online (GrabFood/Grabmart/GoFood)</b>
                            <br />
                            {latestMassDeletedProduct.marketplace.map((item, index) => (
                                <span style={{ paddingLeft: 15 }}>
                                    {index + 1}
                                    .
                                    {' '}
                                    {item.name}
                                    <br />
                                </span>
                            ))}
                        </p>
                    ) : ''}
                </Box>
            )}
        />
    );
};

export default ContentDialog;
