// TODO: remove this component after pengaturan promo per product harga coret release
import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { Banner, Box } from '@majoo-ui/react';
import { CircleInfoFilled } from '@majoo-ui/icons';
import PageContext from '../PageContext';

const MasterRecipeBanner = ({ onRemove }) => {
    const { translationData } = useContext(PageContext);
    const { LANG_DATA, LANG_KEY, TransComponent } = translationData;

    return (
        <Box css={{
            '> div > div': {
                width: '100%',
            },
            '> div > div > div': {
                width: '100%',
            },
            '> div > div > div > div:first-child': {
                flex: 1,
            }
        }}>
            <Banner
                title="Baru!"
                description="Gunakan Master resep untuk memilih resep yang sudah tersedia, khusus pengguna paket Advance, Prime dan Prime+"
                variant="info"
                icon={() => <CircleInfoFilled />}
                linkText="Pengaturan Resep"
                link="/item/recipe"
                onRemove={onRemove}
            />
        </Box>
    );
};

MasterRecipeBanner.propTypes = {
    onRemove: PropTypes.func,
};

MasterRecipeBanner.defaultProps = {
    onRemove: () => {},
};

export default MasterRecipeBanner;
