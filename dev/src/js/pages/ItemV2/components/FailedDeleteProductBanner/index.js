import React, { useContext } from 'react';
import {
    Box,
    Flex,
    Banner,
    Heading,
    Paragraph,
    BannerLink,
    BannerClose,
} from '@majoo-ui/react';
import PageContext from '../../PageContext';

const FailedDeleteProductBanner = () => {
    const { latestMassDeletedProduct, onClearLatestMassDeletedProduct, onShowFailedDeleteProductInformationDialog } = useContext(PageContext);

    if (!latestMassDeletedProduct) return null;

    return (
        <Box>
            <Banner variant="failed" bannerBlock onRemove={() => { onClearLatestMassDeletedProduct(); }} css={{ marginBottom: '18px' }}>
                <Flex direction="row" align="center" justify="between" wrap="wrap" css={{ flex: 1 }}>
                    <Flex direction="column" wrap="wrap" css={{ width: '100%' }}>
                        <Box>
                            <Flex justify="between" css={{ paddingBottom: 4 }}>
                                <Heading heading="sectionTitle" css={{ color: '#272A2A', fontWeight: 600, width: '-webkit-fill-available' }}>
                                    {latestMassDeletedProduct.success === 0 ? 'Gagal Hapus Seluruhnya' : 'Gagal Hapus Sebagian'}
                                </Heading>
                                <Box css={{ textAlign: '-webkit-right', width: 'auto' }}>
                                    <BannerClose />
                                </Box>
                            </Flex>
                            <Paragraph css={{ fontSize: 14 }}>
                                Terdapat
                                {' '}
                                <b>{latestMassDeletedProduct.failed}</b>
                                {' '}
                                produk yang gagal dihapus karena adanya kesalahan. Silakan klik tombol Lihat Detail untuk mengetahui daftar produk dan penyebab kegagalan
                            </Paragraph>
                        </Box>
                    </Flex>
                    <Box
                        direction="row"
                        css={{ width: '-webkit-fill-available', textAlign: 'end' }}
                        onClick={onShowFailedDeleteProductInformationDialog}
                    >
                        <BannerLink>
                            Lihat Detail
                        </BannerLink>
                    </Box>
                </Flex>
            </Banner>
        </Box>
    );
};

export default FailedDeleteProductBanner;
