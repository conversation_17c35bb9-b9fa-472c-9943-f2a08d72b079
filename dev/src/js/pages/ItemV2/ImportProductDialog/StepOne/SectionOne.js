import React, { useContext } from 'react';
import {
    Box, Heading, Text,
    InputRadioGroup, InputRadio,
    Flex,
} from '@majoo-ui/react';
import { CircleInfoOutline } from '@majoo-ui/icons';
import ResponsivePaper from '../../../Inventory/StockEntryV2/StockEntryDetail/ResponsivePaper'; // TODO: akan dibuatkan global component di dev/src/js/v2-components
import FormContext from '../FormContext';
import { ANSWER_TYPE, IMPORT_PRODUCT_TYPE } from '../../enums';
import HorizontalForm from '../../../../v2-components/HorizontalForm';
import HorizontalLine from '../../../../v2-components/HorizontalLine';
import * as SectionOneComponent from './section-one-components';
import PageContext from '../../PageContext';

const SectionOne = () => {
    const {
        formData, onChange: onChangeFormData
    } = useContext(FormContext);
    const { translationData, outlets, filterBranch, idCabang } = useContext(PageContext);
    const { LANG_DATA, t } = translationData;

    const currentBranch = (outlets || []).find(x => String(x.value) === String(!filterBranch ? Number(idCabang) : Number(filterBranch)));

    return (
        <ResponsivePaper css={{ gap: '$spacing-03', '@md': { gap: '$cozy' } }}>
            <Box css={{ '@md': { marginBottom: '$spacing-03' } }}>
                <Heading
                    as="h3"
                    heading="pageTitle"
                    css={{ marginBottom: 0, '@md': { marginBottom: '$spacing-03 !important' } }}
                >
                    {LANG_DATA.IMPORTEXPORT_EXPORT_HEADING}
                </Heading>
            </Box>
            <HorizontalForm
                labelOptions={{
                    text: LANG_DATA.IMPORTEXPORT_EXPORT_SELECTOUTLET,
                    description: LANG_DATA.IMPORTEXPORT_EXPORT_SELECTOUTLET_DESCRIPTION,
                    required: true,
                }}
                render={({ errorReceiver }) => (<SectionOneComponent.ChooseOutlet {...{ errorReceiver, disabled: false }} />)}
            />
            <HorizontalLine />
            <HorizontalForm
                labelOptions={{
                    text: 'Template',
                    description: LANG_DATA.IMPORTEXPORT_EXPORT_TEMPLATE_DESCRIPTION,
                    required: true,
                }}
                render={() => (
                    <InputRadioGroup value={formData.alreadyHaveTemplate} disabled={formData.outletIds.length === 0} direction="column" gap={5} onValueChange={val => onChangeFormData('alreadyHaveTemplate', val)}>
                        <Box
                            css={{
                                border: '1px solid #DADDDD',
                                padding: '8px',
                                borderRadius: '8px',
                                flex: 1,
                            }}
                        >
                            <InputRadio id={ANSWER_TYPE.NO} value={ANSWER_TYPE.NO} label={LANG_DATA.IMPORTEXPORT_EXPORT_DONTHAVE_TEMPLATE} />
                            <Text
                                align="justify"
                                color="primary"
                                variant="label"
                                css={{ padding: '10px 0px' }}
                            >
                                {LANG_DATA.IMPORTEXPORT_EXPORT_DONTHAVE_TEMPLATE_DESCRIPTION}
                            </Text>
                            <SectionOneComponent.ExportTemplateButton
                                {...{
                                    outletId: formData.outletId, outletIds: formData.outletIds, importProductType: formData.importProductType, alreadyHaveTemplate: formData.alreadyHaveTemplate,
                                }}
                            />
                            {formData.importProductType === IMPORT_PRODUCT_TYPE.UPDATE_RECIPE && (
                                <Flex align="center" gap={3} mt={3}>
                                    <CircleInfoOutline size={20} />
                                    <Text color="secondary" variant="caption">{t('importExportProduct.exportRecipeTemplateDesc')} <b>{currentBranch ? currentBranch.name : 'kaksjd'}</b></Text>
                                </Flex>
                            )}
                        </Box>
                        <Box
                            css={{
                                border: '1px solid #DADDDD',
                                padding: '8px',
                                borderRadius: '8px',
                                flex: 1,
                            }}
                        >
                            <InputRadio id={ANSWER_TYPE.YES} value={ANSWER_TYPE.YES} label={LANG_DATA.IMPORTEXPORT_EXPORT_ALREADYHAVE_TEMPLATE} />
                            <Text
                                variant="label"
                                css={{ paddingTop: 10 }}
                            >
                                {LANG_DATA.IMPORTEXPORT_EXPORT_ALREADYHAVE_TEMPLATE_DESCRIPTION}
                            </Text>
                        </Box>
                    </InputRadioGroup>
                )}
            />
            <HorizontalLine />
            <SectionOneComponent.InformationNotes {...{ importProductType: formData.importProductType }} />
        </ResponsivePaper>
    );
};

export default SectionOne;
