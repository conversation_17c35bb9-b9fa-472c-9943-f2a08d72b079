import React, { useContext } from 'react';
import { Box, Heading, Text } from '@majoo-ui/react';
import ResponsivePaper from '../../../Inventory/StockEntryV2/StockEntryDetail/ResponsivePaper'; // TODO: akan dibuatkan global component di dev/src/js/v2-components
import HorizontalForm from '../../../../v2-components/HorizontalForm';
import * as SectionOneComponent from './section-one-components';
import PageContext from '../../PageContext';
import { IMPORT_PRODUCT_TYPE } from '../../enums';
import FormContext from '../FormContext';

const SectionOne = () => {
    const {
        formData
    } = useContext(FormContext);
    const { translationData } = useContext(PageContext);
    const { LANG_DATA, t } = translationData;
    return (
        <ResponsivePaper css={{ gap: '$spacing-03', '@md': { gap: '$cozy' } }}>
            <Box css={{ '@md': { marginBottom: '$spacing-03' } }}>
                <Heading
                    as="h3"
                    heading="pageTitle"
                    css={{ marginBottom: 0, '@md': { marginBottom: '$spacing-03 !important' } }}
                >
                    {LANG_DATA.IMPORTEXPORT_IMPORT_TITLE}
                </Heading>
            </Box>
            <Text align="justify" color="primary" variant="helper">
                {LANG_DATA.IMPORTEXPORT_IMPORT_DESCRIPTION}
            </Text>
            <HorizontalForm
                labelOptions={{
                    text: 'Outlet',
                }}
                render={({ errorReceiver }) => (
                    <SectionOneComponent.ChooseOutlet {...{ errorReceiver, disabled: true }} />
                )}
            />
            {formData.importProductType === IMPORT_PRODUCT_TYPE.UPDATE_RECIPE ? (
                <HorizontalForm
                    labelOptions={{
                        text: t('importExportProduct.uploadLabel'),
                        required: true,
                    }}
                    render={() => (
                        <SectionOneComponent.UploadTemplateFile withLabel />
                    )}
                />
            ) : (
                <Box
                    css={{
                        textAlign: 'center',
                    }}
                >
                    <SectionOneComponent.UploadTemplateFile />
                </Box>
            )}
        </ResponsivePaper >
    );
};

export default SectionOne;
