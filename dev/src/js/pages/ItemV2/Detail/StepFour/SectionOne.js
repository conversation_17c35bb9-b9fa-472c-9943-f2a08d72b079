import React, { Fragment, useContext, useEffect, useState } from 'react';
import {
    Box,
    Heading,
    Flex,
    InputSwitch,
    Text,
    Banner,
    BannerDescription,
    InputRadioGroup,
    Paragraph,
    InputRadio,
} from '@majoo-ui/react';
import { RECIPE_MASTER_SETTING } from '~/pages/Settings/ProductInventory/RecipeMasterSettings';
import { isNullOrUndefined } from '~/utils/helper';
import ResponsivePaper from '../../../Inventory/StockEntryV2/StockEntryDetail/ResponsivePaper'; // TODO: akan dibuatkan global component di dev/src/js/v2-components
import FormContext from '../FormContext';
import RawMaterials from './RawMaterials';
import VariantRawMaterials from './VariantRawMaterials';
import HorizontalForm from '../../../../v2-components/HorizontalForm';
import StarterBasicAuthorize from '../../components/StarterBasicAuthorize';
import PageContext from '../../PageContext';
import { CoachMarkFragment } from '../../CoachMark';
import VariantMasterRecipes from './VariantMasterRecipes';
import MasterRecipe from './MasterRecipe';
import MasterRecipeBanner from '../../components/MasterRecipeBanner';

const SectionOne = () => {
    const { formData, onChange, handleChangeValueSimultaneously } = useContext(FormContext);
    const { translationData, recipeMasterSetting } = useContext(PageContext);
    const { LANG_DATA, TransComponent } = translationData;

    const [recipeSetting, setRecipeSetting] = useState();
    const [showMasterRecipeBanner, setShowMasterRecipeBanner] = useState(true);

    useEffect(() => {
        if (
            formData.hasRecipe &&
            (formData.hasVariantRecipe ||
                (formData.selectedRawMaterials &&
                    formData.selectedRawMaterials.filter(material => material.rawMaterialId).length > 0) ||
                formData.masterRecipeId)
        ) {
            setRecipeSetting(
                (
                    formData.hasVariantRecipe
                        ? formData.variantList &&
                        formData.variantList.length > 0 &&
                        formData.variantList[0].masterRecipeId
                        : formData.masterRecipeId
                )
                    ? RECIPE_MASTER_SETTING.USE_MASTER_RECIPE
                    : RECIPE_MASTER_SETTING.USE_RAW_MATERIAL_RECIPE,
            );
        }
    }, [formData.hasRecipe]);

    useEffect(() => {
        if (formData.hasRecipe && !isNullOrUndefined(recipeMasterSetting.id)) {
            if (recipeMasterSetting.use_raw_material_recipe === 1 && recipeMasterSetting.use_master_recipe === 0) {
                setRecipeSetting(RECIPE_MASTER_SETTING.USE_RAW_MATERIAL_RECIPE);
            }
            if (recipeMasterSetting.use_raw_material_recipe === 0 && recipeMasterSetting.use_master_recipe === 1) {
                setRecipeSetting(RECIPE_MASTER_SETTING.USE_MASTER_RECIPE);
            }
        }
    }, [recipeMasterSetting, formData.hasRecipe])

    return (
        <ResponsivePaper>
            <Box css={{ '@md': { marginBottom: '$spacing-03' } }}>
                <Heading
                    as="h3"
                    heading="pageTitle"
                    css={{ marginBottom: 0, '@md': { marginBottom: '$spacing-03 !important' } }}
                >
                    {LANG_DATA.FORM_STEP_FOUR_HEADING}
                </Heading>
            </Box>
            <StarterBasicAuthorize
                featureName="FORM_STEP_RECIPE"
                render={() => (
                    <Fragment>
                        {showMasterRecipeBanner && (
                            <MasterRecipeBanner onRemove={() => setShowMasterRecipeBanner(false)} />
                        )}
                        <CoachMarkFragment className="recipe-section">
                            <HorizontalForm
                                labelOptions={{
                                    text: LANG_DATA.FORM_STEP_FOUR_SWITCH_LABEL,
                                }}
                                render={() => (
                                    <Flex gap={4}>
                                        <InputSwitch
                                            dataOffLabel="OFF"
                                            dataOnLabel="ON"
                                            onCheckedChange={val => {
                                                handleChangeValueSimultaneously({
                                                    hasRecipe: val,
                                                    masterRecipeId: null,
                                                    masterRecipeName: null,
                                                    selectedRawMaterials: [],
                                                });
                                                setRecipeSetting(undefined);
                                            }}
                                            checked={formData.hasRecipe}
                                            disabled={formData.isMonitorStock}
                                        />
                                        <Text color="secondary" css={{ fontSize: 12, fontWeight: 500 }}>
                                            {LANG_DATA.FORM_STEP_FOUR_SWITCH_CAPTION}
                                        </Text>
                                    </Flex>
                                )}
                            />
                        </CoachMarkFragment>
                        {formData.isMonitorStock && (
                            <Banner variant="failed">
                                <Box>
                                    <BannerDescription css={{ whiteSpace: 'normal' }}>
                                        <TransComponent i18nKey="form.stepFour.isVariantActive">
                                            Resep hanya berlaku untuk produk dengan <b>Monitoring persediaan OFF</b>
                                        </TransComponent>
                                    </BannerDescription>
                                </Box>
                            </Banner>
                        )}
                        {formData.hasRecipe &&
                            recipeMasterSetting.use_master_recipe === 1 &&
                            recipeMasterSetting.use_raw_material_recipe === 1 && (
                                <HorizontalForm
                                    labelOptions={{
                                        text: 'Pengaturan Resep Produk',
                                    }}
                                    render={() => (
                                        <InputRadioGroup
                                            value={recipeSetting}
                                            css={{
                                                display: 'flex',
                                                width: '100%',
                                                flex: 1,
                                                flexDirection: 'column',
                                                gap: '10px',
                                                '& > label': {
                                                    width: '100%',
                                                },
                                            }}
                                            onValueChange={val => {
                                                if (val === RECIPE_MASTER_SETTING.USE_RAW_MATERIAL_RECIPE) {
                                                    onChange('masterRecipeId', null);
                                                    onChange('masterRecipeName', null);
                                                } else {
                                                    onChange('selectedRawMaterials', []);
                                                }
                                                setRecipeSetting(val);
                                            }}
                                        >
                                            <Flex gap={5} css={{ width: '100%' }}>
                                                <Box
                                                    css={{
                                                        display: 'flex',
                                                        justifyContent: 'space-between',
                                                        flex: 1,
                                                        border: `1px solid ${recipeSetting ===
                                                            RECIPE_MASTER_SETTING.USE_RAW_MATERIAL_RECIPE
                                                            ? '$primary500'
                                                            : '$gray300'
                                                            }`,
                                                        padding: '8px',
                                                        borderRadius: '8px',
                                                    }}
                                                >
                                                    <InputRadio
                                                        id={RECIPE_MASTER_SETTING.USE_RAW_MATERIAL_RECIPE}
                                                        value={RECIPE_MASTER_SETTING.USE_RAW_MATERIAL_RECIPE}
                                                        label={
                                                            <Flex
                                                                direction="column"
                                                                gap={2}
                                                                css={{ width: '100%', flex: 1 }}
                                                            >
                                                                <Paragraph paragraph="longContentRegular">
                                                                    Resep Manual Bahan Baku
                                                                </Paragraph>
                                                                <Text variant="caption" color="secondary">
                                                                    Pada resep manual bahan baku, Anda akan dapat
                                                                    menentukan sendiri resep dengan bahan baku yang ada
                                                                </Text>
                                                            </Flex>
                                                        }
                                                        css={{
                                                            width: '100%',
                                                            '& > div': {
                                                                alignItems: 'start',
                                                            },
                                                            '& button': {
                                                                marginTop: '5px',
                                                            },
                                                        }}
                                                    />
                                                </Box>
                                                <Box
                                                    css={{
                                                        display: 'flex',
                                                        justifyContent: 'space-between',
                                                        flex: 1,
                                                        border: `1px solid ${recipeSetting === RECIPE_MASTER_SETTING.USE_MASTER_RECIPE
                                                            ? '$primary500'
                                                            : '$gray300'
                                                            }`,
                                                        padding: '8px',
                                                        borderRadius: '8px',
                                                    }}
                                                >
                                                    <InputRadio
                                                        id={RECIPE_MASTER_SETTING.USE_MASTER_RECIPE}
                                                        value={RECIPE_MASTER_SETTING.USE_MASTER_RECIPE}
                                                        label={
                                                            <Flex
                                                                direction="column"
                                                                gap={2}
                                                                css={{ width: '100%', flex: 1 }}
                                                            >
                                                                <Paragraph paragraph="longContentRegular">
                                                                    Resep Menggunakan Master Resep
                                                                </Paragraph>
                                                                <Text variant="caption" color="secondary">
                                                                    Pada resep menggunakan master resep, Anda akan dapat
                                                                    memilih resep yang sudah tersedia pada master resep
                                                                </Text>
                                                            </Flex>
                                                        }
                                                        css={{
                                                            width: '100%',
                                                            '& > div': {
                                                                alignItems: 'start',
                                                            },
                                                            '& button': {
                                                                marginTop: '5px',
                                                            },
                                                        }}
                                                    />
                                                </Box>
                                            </Flex>
                                        </InputRadioGroup>
                                    )}
                                />
                            )}
                        {recipeSetting === RECIPE_MASTER_SETTING.USE_MASTER_RECIPE && (
                            <React.Fragment>
                                {formData.hasVariant ? <VariantMasterRecipes /> : <MasterRecipe />}
                            </React.Fragment>
                        )}
                        {recipeSetting === RECIPE_MASTER_SETTING.USE_RAW_MATERIAL_RECIPE && (
                            <HorizontalForm
                                labelOptions={{
                                    text: LANG_DATA.FORM_STEP_FOUR_FIELD_LABEL,
                                }}
                                render={() => {
                                    if (formData.hasVariant) return <VariantRawMaterials />;

                                    return <RawMaterials />;
                                }}
                            />
                        )}
                    </Fragment>
                )}
            />
        </ResponsivePaper>
    );
};

export default SectionOne;
