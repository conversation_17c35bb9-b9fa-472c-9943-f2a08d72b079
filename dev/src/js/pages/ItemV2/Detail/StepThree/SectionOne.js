import React, { Fragment, useContext } from 'react';
import {
    Box, Heading, Flex,
    Text, InputSwitch,
    Tooltip,
} from '@majoo-ui/react';
import { foundations } from '@majoo-ui/core';
import { CircleInfoOutline } from '@majoo-ui/icons';
import FormContext from '../FormContext';
import ResponsivePaper from '../../../Inventory/StockEntryV2/StockEntryDetail/ResponsivePaper'; // TODO: akan dibuatkan global component di dev/src/js/v2-components
import ManageProductExtra from './ManageProductExtra';
import HorizontalForm from '../../../../v2-components/HorizontalForm';
import { isSelectedProductExtrasDetailModified, ALLOWED_ACCOUNT } from './utils';
import StarterBasicAuthorize from '../../components/StarterBasicAuthorize';
import PageContext from '../../PageContext';
import { CoachMarkFragment } from '../../CoachMark';
import { getSubscriptionName } from '../../../Support/buy/retina/utils';
import { useAccountType } from '../../../LayoutV2/hooks';

const { gray400, iconSecondary } = foundations.colors;

const SectionOne = () => {
    const {
        onChange, formData, getValues, onShowAlertDialogDeactivateProductExtraChangeable,
    } = useContext(FormContext);
    const { translationData, onChangeUnauthorizedBlocker } = useContext(PageContext);
    const { LANG_DATA, TransComponent } = translationData;
    const accountType = useAccountType();

    return (
        <ResponsivePaper>
            <Box css={{ '@md': { marginBottom: '$spacing-03' } }}>
                <Flex
                    gap={3}
                    css={{
                        flexDirection: 'column',
                        '@md': {
                            flexDirection: 'row',
                        },
                    }}
                >
                    <Heading
                        as="h3"
                        heading="pageTitle"
                    >
                        {LANG_DATA.FORM_STEP_THREE_HEADING}
                    </Heading>
                </Flex>
            </Box>
            <StarterBasicAuthorize
                featureName="FORM_STEP_EXTRA"
                render={() => (
                    <Fragment>
                        <CoachMarkFragment className="extra-section">
                            <HorizontalForm
                                labelOptions={{
                                    text: LANG_DATA.FORM_STEP_THREE_FORM_SWITCH_LABEL,
                                }}
                                render={() => (
                                    <Flex align="center">
                                        <InputSwitch
                                            dataOffLabel="OFF"
                                            dataOnLabel="ON"
                                            disabled={false}
                                            checked={formData.hasProductExtra}
                                            onCheckedChange={(val) => { onChange('hasProductExtra', val); }}
                                            css={{ marginRight: 12 }}
                                        />
                                        <Tooltip
                                            align="start"
                                            label={(
                                                <TransComponent i18nKey="form.stepThree.form.ekstraSwitch.tooltip">
                                                    Mengubah data Ekstra pada produk ini tidak
                                                    <br />
                                                    mempengaruhi pengaturan awal data Ekstra
                                                </TransComponent>
                                            )}
                                            side="right"
                                        >
                                            <CircleInfoOutline size={24} color={iconSecondary} />
                                        </Tooltip>
                                    </Flex>
                                )}
                            />
                        </CoachMarkFragment>
                        {formData.hasProductExtra && (
                            <Fragment>
                                <HorizontalForm
                                    labelOptions={{
                                        text: LANG_DATA.FORM_STEP_THREE_FORM_FIELD_FIELDS_EDITDATA,
                                    }}
                                    render={() => (
                                        <React.Fragment>
                                            <Flex align="center">
                                                <InputSwitch
                                                    dataOffLabel="OFF"
                                                    dataOnLabel="ON"
                                                    disabled={false}
                                                    checked={formData.isProductExtraChangeable}
                                                    onCheckedChange={(val) => {
                                                        if (!val && isSelectedProductExtrasDetailModified(getValues('selectedProductExtras'))) {
                                                            onShowAlertDialogDeactivateProductExtraChangeable();
                                                        } else if (!ALLOWED_ACCOUNT.includes(getSubscriptionName(accountType))) {
                                                            onChangeUnauthorizedBlocker(true);
                                                        } else {
                                                            onChange('isProductExtraChangeable', val);
                                                        }
                                                    }}
                                                    css={{ marginRight: 12 }}
                                                />
                                                <Tooltip
                                                    css={{ maxWidth: 220 }}
                                                    align="start"
                                                    label={(
                                                        <span>
                                                            {LANG_DATA.FORM_STEP_THREE_FORM_FIELD_FIELDS_EDITTOOLTIP}
                                                        </span>
                                                    )}
                                                    side="right"
                                                >
                                                    <CircleInfoOutline size={24} color={iconSecondary} />
                                                </Tooltip>
                                            </Flex>
                                        </React.Fragment>
                                    )}
                                />
                                <HorizontalForm
                                    labelOptions={{
                                        text: LANG_DATA.FORM_STEP_THREE_FORM_FIELD_LABEL,
                                    }}
                                    render={() => (
                                        <ManageProductExtra />
                                    )}
                                />
                            </Fragment>
                        )}
                    </Fragment>
                )}
            />
        </ResponsivePaper>
    );
};

export default SectionOne;
