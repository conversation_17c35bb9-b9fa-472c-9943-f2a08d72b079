import React, { Fragment, useContext, useRef } from 'react';
import {
    Box, Heading, Flex, Text, InputSwitch,
} from '@majoo-ui/react';
import ResponsivePaper from '../../../Inventory/StockEntryV2/StockEntryDetail/ResponsivePaper'; // TODO: akan dibuatkan global component di dev/src/js/v2-components
import VariantList from './VariantList';
import FormContext from '../FormContext';
import AddVariant from './AddVariant';
import HorizontalForm from '../../../../v2-components/HorizontalForm';
import HorizontalLine from '../../../../v2-components/HorizontalLine';
import AlertDialogVariant from './AlertDialogVariant';
import AlertDialogInactiveVariant from './AlertDialogInactiveVariant';
import OnlyAvailablePrime from '../../components/OnlyAvailablePrime';
import PageContext from '../../PageContext';
import { checkAllOutletStockIsZero } from './utils';
import { CoachMarkFragment } from '../../CoachMark';
import { ishasProperty } from '../../../../utils/helper';

const SectionOne = () => {
    const {
        isInMobileView, currentProductDetailData, addToast, translationData,
    } = useContext(PageContext);
    const { LANG_DATA, TransComponent } = translationData;
    const { formData, fieldArray: { variantListFieldArray } } = useContext(FormContext);
    const alertDialogVariant = useRef(null);
    const alertDialogInactiveVariant = useRef(null);

    let switchDisabled = false;

    if (formData.id && formData.variantTypes.length > 0) {
        switchDisabled = variantListFieldArray.fields.some(x => ishasProperty(x, 'variant_id') && x.variant_id);
    }

    return (
        <ResponsivePaper>
            <Box css={{ '@md': { marginBottom: '$spacing-03' } }}>
                <Heading
                    as="h3"
                    heading="pageTitle"
                    css={{ marginBottom: 0, '@md': { marginBottom: '$spacing-03 !important' } }}
                >
                    {LANG_DATA.FORM_STEP_TWO_HEADING}
                </Heading>
            </Box>
            <OnlyAvailablePrime
                bannerOptions={{
                    renderCustomText: () => (
                        <TransComponent i18nKey="form.stepTwo.starterAdvanceUpgrade">
                            Fitur Varian memudahkan Anda untuk menambahkan produk yang memiliki beragam variasi (ukuran, warna, rasa, dll). Silahkan upgrade ke paket
                            {' '}
                            <b>Prime</b>
                            {' '}
                            untuk menggunakan fitur ini.
                        </TransComponent>
                    ),
                }}
                render={() => (
                    <Fragment>
                        <CoachMarkFragment className="variant-product">
                            <Flex direction="column" css={{ gap: '$compact', '@md': { gap: '$cozy' } }}>
                                <HorizontalForm
                                    labelOptions={{
                                        text: LANG_DATA.FORM_STEP_TWO_FORM_SWITCH_LABEL,
                                    }}
                                    render={() => (
                                        <Flex gap={4}>
                                            <InputSwitch
                                                dataOffLabel="OFF"
                                                dataOnLabel="ON"
                                                onCheckedChange={(val) => {
                                                    let isAbleToChange = true;

                                                    if (currentProductDetailData) isAbleToChange = !formData.isMonitorStock ? true : checkAllOutletStockIsZero({ currentProductDetailData });

                                                    if (isAbleToChange) {
                                                        if (!val) alertDialogInactiveVariant.current.handleShowDialog();
                                                        if (val) alertDialogVariant.current.handleShowDialog();
                                                    } else {
                                                        addToast({
                                                            title: 'Gagal!',
                                                            description: 'Tidak dapat mengaktifkan varian karna stok tidak sama dengan 0',
                                                            variant: 'failed',
                                                            position: 'top-right',
                                                            dismissAfter: 3000,
                                                        });
                                                    }
                                                }}
                                                checked={formData.hasVariant}
                                                disabled={switchDisabled}
                                            />
                                            <Text color="secondary" css={{ fontSize: 12, fontWeight: 500 }}>
                                                {LANG_DATA.FORM_STEP_TWO_FORM_SWITCH_CAPTION}
                                            </Text>
                                        </Flex>
                                    )}
                                />
                            </Flex>
                        </CoachMarkFragment>
                        {formData.hasVariant && (
                            <Fragment>
                                <HorizontalForm
                                    labelOptions={{
                                        text: LANG_DATA.FORM_STEP_TWO_FORM_TYPE_LABEL,
                                    }}
                                    render={() => (
                                        <AddVariant />
                                    )}
                                />
                                {!isInMobileView && <HorizontalLine />}
                                <CoachMarkFragment className="variant-list" asDiv>
                                    <Flex direction="column" gap={3}>
                                        <Text
                                            variant="label"
                                            color="primary"
                                            css={{
                                                fontSize: '14px',
                                                fontWeight: '600',
                                            }}
                                        >
                                            {LANG_DATA.FORM_STEP_TWO_FORM_VARIANTLIST_LABEL}
                                        </Text>
                                        <Text variant="helper" color="secondary">{LANG_DATA.FORM_STEP_TWO_FORM_VARIANTLIST_DESC}</Text>
                                    </Flex>
                                    <VariantList />
                                </CoachMarkFragment>
                            </Fragment>
                        )}
                        <AlertDialogVariant ref={alertDialogVariant} />
                        <AlertDialogInactiveVariant ref={alertDialogInactiveVariant} />
                    </Fragment>
                )}
            />
        </ResponsivePaper>
    );
};

export default SectionOne;
