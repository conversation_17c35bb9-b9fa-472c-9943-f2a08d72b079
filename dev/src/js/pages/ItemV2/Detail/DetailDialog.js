import React, { Fragment, useContext } from 'react';
import {
    PageDialog, PageDialogTitle,
    PageDialogContent,
    Flex, Button, Box, PageDialogFooter,
} from '@majoo-ui/react';
import { EllipsisHorizontalOutline, RemoveOutline } from '@majoo-ui/icons';
import { createFormData, stepNavigationContents } from './utils';
import StepIndicator from '../components/StepIndicator';
import Form from './Form';
import PageContext from '../PageContext';
import * as DetailDialogComponent from './detail-dialog.components';
import CustomPageDialogFooter from '../../../v2-components/CustomPageDialogFooter';
import AlertDialogCancel from './AlertDialogCancel';
import { SUBMIT_TYPE } from '../enums';
import AdvancedOptionsDialog from './AdvancedOptionsDialog';
import { scrollIntoErrorMessage } from '../../../v2-utils';

class DetailDialog extends React.Component {
    goToStep = 1; // untuk kebutuhan stepWizard.goToStep setelah validasi submit

    defaultFormData = {
        formData: createFormData(),
        /**
         * initialStepFormData adalah state yang berisi `formData`
         * ketika pertama kali render step,
         * Jadi ketika klik button `Kembali`, `formData` nya kembali seperti
         * pertama kali render step, data ini adalah read-only
         */
        initialStepFormData: createFormData(),
    }

    state = {
        ...this.defaultFormData,
        submitType: SUBMIT_TYPE.NEXT,
    }

    componentDidMount = () => {
        this.handleSetFormData();
    }

    componentDidUpdate = (prevProps) => {
        const { rawProductData, isOpen } = this.props;

        if (JSON.stringify(prevProps.rawProductData) !== JSON.stringify(rawProductData)) {
            this.handleSetFormData();
        }

        if (prevProps.isOpen !== isOpen && !isOpen) {
            this.handleSetFormData();
        }
    }

    handleCreateStateInitialStepFormData = newData => ({
        formData: newData,
        initialStepFormData: newData,
    })

    handleUpdateStateFormData = newData => ({
        formData: newData,
    })

    handleSetFormData = (callbackAfterSetState = () => { }) => {
        const {
            idCabang, rawProductData, rawMaterials, marketplaceProducts, outlets, productExtras, listOutlet, filterBranch, outletGroupList,
        } = this.props;
        this.setState(this.handleCreateStateInitialStepFormData(createFormData(rawProductData, {
            idCabang, rawMaterials, marketplaceProducts, outlets, productExtras, listOutlet, filterBranch, outletGroupList,
        })), () => {
            callbackAfterSetState();
        });
    }

    handleChangeFormData = (key, value) => {
        const { formData } = this.state;

        this.setState({
            formData: {
                ...formData, [key]: value,
            },
        });
    };

    handleChangeFormDataSimultaneously = (newFormData) => {
        const { formData } = this.state;
        this.setState({
            formData: {
                ...formData, ...newFormData,
            },
        });
    };

    handleSubmit = (submittedData) => {
        const { stepWizard, onOpenProductPreview } = this.props;
        const { submitType, initialStepFormData } = this.state;

        this.setState(submittedData.id ? this.handleUpdateStateFormData(submittedData) : this.handleCreateStateInitialStepFormData(submittedData), () => {
            if (submitType === SUBMIT_TYPE.GO_TO_STEP) {
                stepWizard.goToStep(this.goToStep);
            } else if (submitType === SUBMIT_TYPE.NEXT) {
                stepWizard.nextStep();
            } else if (submitType === SUBMIT_TYPE.SAVE) {
                onOpenProductPreview({ ...submittedData, initialStepFormData });
            }
        });
    };

    handlePreviousStep = () => {
        const { initialStepFormData } = this.state;
        const { stepWizard } = this.props;

        this.setState({
            formData: initialStepFormData,
        }, () => {
            stepWizard.previousStep();
        });
    }

    handleCloseDialog = () => {
        const { onClose } = this.props;

        this.handleSetFormData(() => {
            onClose();
        });
    }

    handleShowCancelConfirmation = () => {
        this.alertDialogCancel.handleShowDialog();
    }

    handleTriggerSubmit = (submitType = SUBMIT_TYPE.NEXT) => {
        this.setState({ submitType }, () => {
            this.form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
            scrollIntoErrorMessage();
        });
    };

    handleScrollToTop = () => {
        this.pageDialogContent.scrollTo(0, 0);
    }

    handleStepNavigation = (newStep) => {
        this.goToStep = newStep;
        this.handleTriggerSubmit(SUBMIT_TYPE.GO_TO_STEP);
    }

    handleShowDeleteConfirmationDialog = () => {
        const { onShowDeleteConfirmationDialog } = this.props;
        const { formData: { id, productName } } = this.state;

        onShowDeleteConfirmationDialog([{ id, name: productName }]);
    }

    render() {
        const { initialStepFormData, formData, submitType } = this.state;
        const { isOpen, isInMobileView, translationData, menuPrivilege: { isCanDelete, isCanCreate, isCanUpdate, isCanView, isWhitelistMerchant } } = this.props;
        const { LANG_DATA } = translationData;

        return (
            <Fragment>
                <PageDialog
                    modal
                    layer={1}
                    size="full"
                    open={isOpen}
                    defaultOpen={false}
                    onOpenChange={this.handleShowCancelConfirmation}
                    isMobile={isInMobileView}
                >
                    <PageDialogTitle>
                        <Flex direction="row" align="center" gap={4}>
                            <Box>
                                {!formData.id ? `${LANG_DATA.LABEL_ADD} ${LANG_DATA.LABEL_PRODUCT}` : `${LANG_DATA.LABEL_EDIT} ${LANG_DATA.LABEL_PRODUCT}`}
                            </Box>
                        </Flex>
                    </PageDialogTitle>
                    <PageDialogContent ref={(c) => { this.pageDialogContent = c; }} wrapperSize="lg">
                        <DetailDialogComponent.ScrollContainer>
                            <StepIndicator stepNavigationContents={stepNavigationContents({ LANG_DATA })} onStepNavigation={this.handleStepNavigation} />
                            <Form
                                {...{
                                    initialStepFormData,
                                    formData,
                                    submitType,
                                }}
                                stepDataRef={(c) => {
                                    this.stepData = c;
                                }}
                                formRef={(c) => {
                                    this.form = c;
                                }}
                                onChange={this.handleChangeFormData}
                                onChangeSimultaneously={this.handleChangeFormDataSimultaneously}
                                onSubmit={this.handleSubmit}
                                onScrollToTop={this.handleScrollToTop}
                            />
                        </DetailDialogComponent.ScrollContainer>
                    </PageDialogContent>
                    <PageDialogFooter
                        css={{
                            padding: '16px',
                            '@md': {
                                padding: '16px 24px',
                            },
                        }}
                    >
                        <CustomPageDialogFooter
                            {...{ pageDialogContentHasIndicator: true }}
                            renderGroups={[
                                () => (
                                    <Box>
                                        {isInMobileView ? (
                                            <Button
                                                buttonType="ghost"
                                                onClick={() => { this.advancedOptionsDialog.showPopup(); }}
                                                css={{
                                                    width: 50,
                                                    minWidth: 50,
                                                }}
                                            >
                                                <EllipsisHorizontalOutline />
                                            </Button>
                                        ) : (
                                            <Flex gap={4}>
                                                {(formData.id && (isCanDelete || (isCanView && !isWhitelistMerchant))) ? (
                                                    <Button
                                                        type="button"
                                                        buttonType="negative-secondary"
                                                        onClick={this.handleShowDeleteConfirmationDialog}
                                                    >
                                                        <RemoveOutline color="currentColor" />
                                                    </Button>
                                                ) : null}
                                                <Button
                                                    type="button"
                                                    buttonType="ghost"
                                                    onClick={this.handleShowCancelConfirmation}
                                                >
                                                    {LANG_DATA.LABEL_CANCEL}
                                                </Button>
                                            </Flex>
                                        )}
                                    </Box>
                                ),
                                () => (
                                    <Flex
                                        direction="row"
                                        gap={4}
                                        css={{
                                            justifyContent: 'flex-end',
                                        }}
                                    >
                                        {!isInMobileView && (<DetailDialogComponent.BackButton onClick={() => { this.handlePreviousStep(); }} />)}
                                        <DetailDialogComponent.NextButton onClick={() => { this.handleTriggerSubmit(SUBMIT_TYPE.NEXT); }} />
                                        {(isCanUpdate || isCanCreate || (isCanView && !isWhitelistMerchant)) && (
                                            <Button
                                                type="button"
                                                onClick={() => { this.handleTriggerSubmit(SUBMIT_TYPE.SAVE); }}
                                            >
                                                {LANG_DATA.LABEL_SAVE}
                                            </Button>
                                        )}
                                    </Flex>
                                ),
                            ]}
                        />
                    </PageDialogFooter>
                </PageDialog>
                <AlertDialogCancel
                    ref={(c) => {
                        this.alertDialogCancel = c;
                    }}
                    onConfirm={this.handleCloseDialog}
                />
                {isInMobileView && (
                    <AdvancedOptionsDialog
                        ref={(c) => {
                            this.advancedOptionsDialog = c;
                        }}
                        onCancel={this.handleShowCancelConfirmation}
                        onPrevious={() => { this.handlePreviousStep(); }}
                        onDelete={this.handleShowDeleteConfirmationDialog}
                        isEdit={!!formData.id}
                    />
                )}
            </Fragment>
        );
    }
}

const DetailDialogWithContext = (props) => {
    const {
        idCabang, stepWizard, onOpenProductPreview, isInMobileView, currentProductDetailData, listOutlet,
        rawMaterials, marketplaceProducts, SNBNTransactionData, translationData, outlets, productExtras,
        onShowDeleteConfirmationDialog, filterBranch, menuPrivilege, outletGroupList,
    } = useContext(PageContext);

    return (
        <DetailDialog
            {...props}
            {...{
                menuPrivilege,
                idCabang,
                stepWizard,
                onOpenProductPreview,
                isInMobileView,
                rawProductData: currentProductDetailData,
                rawMaterials,
                marketplaceProducts,
                SNBNTransactionData,
                translationData,
                outlets,
                productExtras,
                onShowDeleteConfirmationDialog,
                listOutlet,
                filterBranch,
                outletGroupList,
            }}
        />
    );
};

export default DetailDialogWithContext;
