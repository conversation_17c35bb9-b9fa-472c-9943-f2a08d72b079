import React, { Fragment, useContext } from 'react';
import {
    Box,
    Heading,
    InputSwitch,
    Text,
    InputTextArea,
    Flex,
    InputCheckbox,
    InputRadioGroup,
    InputRadio,
    FormLabel,
    InputSelectTag,
} from '@majoo-ui/react';
import ResponsivePaper from '../../../Inventory/StockEntryV2/StockEntryDetail/ResponsivePaper'; // TODO: akan dibuatkan global component di dev/src/js/v2-components
import FormContext from '../FormContext';
import PageContext from '../../PageContext';
import ImageUpload from '../../components/ImageUpload';
import SelectData from '../../../../v2-components/SelectData';
import StockMonitor from './StockMonitor';
import HorizontalForm from '../../../../v2-components/HorizontalForm';
import ManageCategory from './ManageCategory';
import { ANSWER_TYPE } from '../../enums';
import * as OnlyAvailablePrimeComponent from './only-available-prime.components';
import OnlyAvailablePrime from '../../components/OnlyAvailablePrime';
import { CoachMarkFragment } from '../../CoachMark';

const SectionOne = () => {
    const {
        formData,
        errors,
        control,
        onChange: onChangeFormData,
        Controller,
        refCollections,
    } = useContext(FormContext);
    const { outlets, groupParents, isInMobileView, translationData, listOutlet, isNotAdmin, outletGroupOptions } =
        useContext(PageContext);
    const { LANG_DATA } = translationData;

    const matchedOutlets = outlets.filter(
        outlet =>
            listOutlet.map(val => String(val[0])).includes(String(outlet.value)) &&
            !outletGroupOptions.map(group => String(group.value)).some(val => val.includes(String(outlet.value))),
    );

    const outletOptions = [...outletGroupOptions, ...matchedOutlets];

    const isSerialNumberOrBatchNumberActive = formData.isSerialNumberActive || formData.isBatchNumberActive;

    return (
        <ResponsivePaper>
            <Box css={{ '@md': { marginBottom: '$spacing-03' } }}>
                <Heading
                    as="h3"
                    heading="pageTitle"
                    css={{ marginBottom: 0, '@md': { marginBottom: '$spacing-03 !important' } }}
                >
                    {LANG_DATA.FORM_STEP_PRODUCT_INFORMATION}
                </Heading>
            </Box>
            <CoachMarkFragment className="outlet-list">
                <HorizontalForm
                    labelOptions={{
                        text: LANG_DATA.FORM_STEP_ONE_OUTLET_LIST,
                        required: true,
                    }}
                    render={({ errorReceiver }) => (
                        <Fragment>
                            <Controller
                                control={control}
                                name="outletIds"
                                render={({ fieldState, field: { onChange, value } }) => (
                                    <InputSelectTag
                                        onChange={(values) => { onChange(values); }}
                                        option={outletOptions}
                                        placeholder={LANG_DATA.PLACEHOLDER_SELECT}
                                        value={value}
                                        isInvalid={!!fieldState.error}
                                    />
                                )}
                            />
                            {errorReceiver(errors.outletIds)}
                        </Fragment>
                    )}
                />
            </CoachMarkFragment>
            <CoachMarkFragment className="product-information" asDiv>
                <Flex css={{ gap: '$compact' }} direction="column">
                    <Box
                        css={{
                            '& .horizontal-form-label-box': {
                                display: 'none',
                                '@md': {
                                    paddingTop: '10px',
                                    display: 'unset',
                                },
                            },
                        }}
                    >
                        <HorizontalForm
                            labelOptions={{
                                text: LANG_DATA.LABEL_PRODUCT_NAME,
                                required: true,
                            }}
                            render={({ errorReceiver }) => (
                                <Fragment>
                                    <Controller
                                        control={control}
                                        name="productName"
                                        render={({ fieldState, field: { onChange, value } }) => (
                                            <Fragment>
                                                <FormLabel
                                                    {...(isInMobileView ? { variant: 'required' } : {})}
                                                    count={String(value).length}
                                                    maxCount={255}
                                                    css={{
                                                        marginBottom: 8,
                                                        '& .label-text': {
                                                            color: '$textPrimary',
                                                        },
                                                    }}
                                                >
                                                    {isInMobileView ? (
                                                        <span className="label-text">
                                                            {LANG_DATA.LABEL_PRODUCT_NAME}
                                                        </span>
                                                    ) : (
                                                        <span>&nbsp;</span>
                                                    )}
                                                </FormLabel>
                                                <InputTextArea
                                                    placeholder={`${LANG_DATA.PLACEHOLDER_EXAMPLE}nasi padang`}
                                                    defaultValue={value}
                                                    onChange={e => {
                                                        onChange(e.target.value);
                                                    }}
                                                    isInvalid={!!fieldState.error}
                                                    maxLength={255}
                                                    disabled={formData.id !== '' && isNotAdmin}
                                                />
                                            </Fragment>
                                        )}
                                    />
                                    {errorReceiver(errors.productName)}
                                </Fragment>
                            )}
                        />
                    </Box>
                    <HorizontalForm
                        labelOptions={{ text: LANG_DATA.FORM_STEP_ONE_PRODUCT_DESC }}
                        render={() => (
                            <Controller
                                control={control}
                                name="description"
                                render={({ field: { onChange, value } }) => (
                                    <InputTextArea
                                        css={{ minHeight: 62 }}
                                        placeholder={`${LANG_DATA.PLACEHOLDER_EXAMPLE}yang best seller`}
                                        defaultValue={value}
                                        onChange={e => {
                                            onChange(e.target.value);
                                        }}
                                        disabled={false}
                                    />
                                )}
                            />
                        )}
                    />
                    <HorizontalForm
                        labelOptions={{
                            text: LANG_DATA.FORM_STEP_ONE_PRODUCT_PHOTO,
                            description: LANG_DATA.FORM_STEP_ONE_PRODUCT_PHOTO_DESC,
                        }}
                        render={() => (
                            <ImageUpload
                                fileList={formData.productImages}
                                onChange={val => {
                                    onChangeFormData('productImages', val);
                                }}
                            />
                        )}
                    />
                </Flex>
            </CoachMarkFragment>
            <CoachMarkFragment className="product-category">
                <HorizontalForm
                    labelOptions={{
                        text: LANG_DATA.LABEL_PRODUCT_CATEGORY,
                        required: true,
                    }}
                    render={({ errorReceiver }) => (
                        <Fragment>
                            <ManageCategory disabled={formData.id !== '' && isNotAdmin} />
                            {errorReceiver(errors.categoryId)}
                        </Fragment>
                    )}
                />
            </CoachMarkFragment>
            <CoachMarkFragment className="next-option">
                <HorizontalForm
                    labelOptions={{ text: LANG_DATA.LABEL_NEXT_OPTION }}
                    render={() => (
                        <Flex direction="column" gap={2}>
                            <InputCheckbox
                                label={LANG_DATA.FORM_STEP_ONE_FAVORITE_PRODUCT}
                                onCheckedChange={val => onChangeFormData('isFavorite', val)}
                                checked={formData.isFavorite}
                                disabled={false}
                            />
                            <InputCheckbox
                                label={LANG_DATA.STATUS_SHOW_IN_MENU}
                                onCheckedChange={val => onChangeFormData('showOnMenu', val)}
                                checked={formData.showOnMenu}
                                disabled={false}
                            />
                        </Flex>
                    )}
                />
            </CoachMarkFragment>
            <CoachMarkFragment className="monitor-inventory">
                <HorizontalForm
                    labelOptions={{ text: LANG_DATA.FORM_STEP_ONE_INVENTORY_MONITOR }}
                    render={({ errorReceiver }) => (
                        <Fragment>
                            <StockMonitor disabled={formData.id !== '' && isNotAdmin} refCollections={refCollections} />
                            {errorReceiver(errors.minimumStock)}
                        </Fragment>
                    )}
                />
            </CoachMarkFragment>
            <HorizontalForm
                labelOptions={{
                    text: 'Serial Number',
                    description: LANG_DATA.FORM_STEP_ONE_SERIAL_NUMBER_DESC,
                    tooltip: LANG_DATA.FORM_STEP_ONE_SNBN_SN_TOOLTIP,
                }}
                render={() => (
                    <OnlyAvailablePrime
                        bannerOptions={{
                            featureName: 'Serial Number',
                        }}
                        render={() => (
                            <OnlyAvailablePrimeComponent.SerialNumber
                                {...{ onChangeFormData, formData }}
                                disabled={formData.id !== '' && isNotAdmin}
                            />
                        )}
                    />
                )}
            />
            <HorizontalForm
                labelOptions={{
                    text: 'Batch Number',
                    tooltip: LANG_DATA.FORM_STEP_ONE_SNBN_BN_TOOLTIP,
                }}
                render={() => (
                    <OnlyAvailablePrime
                        bannerOptions={{
                            featureName: 'Batch Number',
                        }}
                        render={() => (
                            <OnlyAvailablePrimeComponent.BatchNumber
                                {...{ onChangeFormData, formData }}
                                disabled={formData.id !== '' && isNotAdmin}
                            />
                        )}
                    />
                )}
            />
            {formData.isBatchNumberActive && (
                <HorizontalForm
                    labelOptions={{
                        text: LANG_DATA.FORM_STEP_ONE_HAS_EXPIRED_DATE,
                        required: true,
                    }}
                    render={() => (
                        <InputRadioGroup
                            direction="column"
                            gap={3}
                            defaultValue={formData.hasExpiredDate ? ANSWER_TYPE.YES : ANSWER_TYPE.NO}
                            onValueChange={val => {
                                onChangeFormData('hasExpiredDate', val === ANSWER_TYPE.YES);
                            }}
                        >
                            <InputRadio id={ANSWER_TYPE.YES} value={ANSWER_TYPE.YES} label={LANG_DATA.LABEL_YES} />
                            <InputRadio id={ANSWER_TYPE.NO} value={ANSWER_TYPE.NO} label={LANG_DATA.LABEL_NO} />
                        </InputRadioGroup>
                    )}
                />
            )}
            <HorizontalForm
                labelOptions={{ text: LANG_DATA.LABEL_GROUP }}
                render={() => (
                    <Flex direction="column" gap={3}>
                        <SelectData
                            onChange={row => {
                                onChangeFormData('groupParentId', row.value);
                            }}
                            value={formData.groupParentId}
                            options={groupParents}
                            search
                            disabled={formData.setAsParent ? true : isSerialNumberOrBatchNumberActive}
                        />
                        <InputCheckbox
                            label={LANG_DATA.FORM_STEP_ONE_SET_AS_PARENT}
                            onCheckedChange={val => onChangeFormData('setAsParent', val)}
                            checked={formData.setAsParent}
                            disabled={isSerialNumberOrBatchNumberActive}
                        />
                    </Flex>
                )}
            />
            <HorizontalForm
                labelOptions={{ text: LANG_DATA.FORM_STEP_ONE_NOT_FOR_SALE_ACCESS }}
                render={() => (
                    <Flex direction="column" gap={4}>
                        <Flex gap={4} direction={isInMobileView ? 'column' : 'row'}>
                            <InputSwitch
                                dataOffLabel="OFF"
                                dataOnLabel="ON"
                                onCheckedChange={val => onChangeFormData('canChangeSellingStatus', val ? 1 : 0)}
                                checked={formData.canChangeSellingStatus === 1}
                                disabled={formData.id !== '' && isNotAdmin}
                            />
                            <Text color="secondary" css={{ fontSize: 12, fontWeight: 500 }}>
                                {LANG_DATA.FORM_STEP_ONE_NOT_FOR_SALE_ACCESS_DESC}
                            </Text>
                        </Flex>
                    </Flex>
                )}
            />
        </ResponsivePaper>
    );
};

export default SectionOne;
