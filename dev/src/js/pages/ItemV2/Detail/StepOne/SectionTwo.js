import React, { useContext } from 'react';
import {
    Box, Heading,
} from '@majoo-ui/react';
import HorizontalForm from '../../../../v2-components/HorizontalForm';
import ResponsivePaper from '../../../Inventory/StockEntryV2/StockEntryDetail/ResponsivePaper'; // TODO: akan dibuatkan global component di dev/src/js/v2-components
import ManageSellingPrice from './ManageSellingPrice';
import HorizontalLine from '../../../../v2-components/HorizontalLine';
import ManagePriceAndUnit from './ManagePriceAndUnit';
import * as OnlyAvailablePrimeComponent from './only-available-prime.components';
import OnlyAvailablePrime from '../../components/OnlyAvailablePrime';
import { useTranslationHook } from '../../lang.utils';
import PageContext from '../../PageContext';

const SectionTwo = () => {
    const { translationData } = useContext(PageContext);
    const { LANG_DATA } = translationData;

    return (
        <ResponsivePaper>
            <Box css={{ '@md': { marginBottom: '$spacing-03' } }}>
                <Heading
                    as="h3"
                    heading="pageTitle"
                    css={{ marginBottom: 0, '@md': { marginBottom: '$spacing-03 !important' } }}
                >
                    {`${LANG_DATA.LABEL_PRICE} ${LANG_DATA.SUBSCRIPTION_UNAUTHORIZED_BLOCKER_AND} ${LANG_DATA.LABEL_UNIT}`}
                </Heading>
            </Box>
            <ManagePriceAndUnit />
            <HorizontalLine />
            <HorizontalForm
                labelOptions={{
                    text: `${LANG_DATA.LABEL_EDIT} ${LANG_DATA.LABEL_SELLING_PRICE}`,
                }}
                render={() => (
                    <ManageSellingPrice />
                )}
            />
            <HorizontalForm
                labelOptions={{
                    text: LANG_DATA.FORM_STEP_ONE_WHOLESALE_PRICE,
                    description: LANG_DATA.FORM_STEP_ONE_WHOLESALE_PRICE_DESC,
                }}
                render={() => (
                    <OnlyAvailablePrime
                        bannerOptions={{
                            renderCustomText: () => (
                                <span>
                                    {LANG_DATA.FORM_STEP_ONE_WHOLESALE_STARTERUPGRADE}
                                </span>
                            ),
                        }}
                        render={() => (
                            <OnlyAvailablePrimeComponent.WholesalePrice />
                        )}
                    />
                )}
            />
        </ResponsivePaper>
    );
};

export default SectionTwo;
