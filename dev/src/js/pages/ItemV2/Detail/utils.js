import _ from 'lodash';
import { EditorState, ContentState } from 'draft-js';
import update from 'immutability-helper';
import htmlToDraft from 'html-to-draftjs';
import { getMatchingGroups, ishasProperty, randomize } from '../../../utils/helper';
import { GROUP_TYPE } from '~/pages/Settings/BranchGroup/utils';

const initialDefaultData = {
    idCabang: '',
    rawMaterials: [],
    marketplaceProducts: [],
    outlets: null,
    productExtras: [],
};

const createID = () => randomize(10, 'aA#');

export const createDefaultFormDataSelectedRawMaterials = (data = null) => ({
    id: createID(),
    rawMaterialId: data ? data.item_id : '',
    averagePrice: data ? data.averagePrice : 0, // data read only
    capitalPrice: data ? data.capitalPrice : 0, // untuk kalkulasi
    dose: data ? data.quantity : 0,
    unitName: data ? data.unitName : '',
    unitNo: data ? data.unit_no : '',
});

export const createDefaultFormDataMultiUnit = (data = null) => ({
    id: createID(),
    unitName: data ? data.name : '',
    conversion: data ? +data.conversion : 1,
    sku: data ? data.sku : '',
    averagePrice: data ? String(data.average_price || 0) : 0,
    purchasePrice: data ? data.purchase_price : 0,
    sellingPrice: data ? data.selling_price : 0,
    minimumPurchase: data ? data.minimum_selling_qty : 1,
    volumeLength: data && data.dimension ? data.dimension.length : 1,
    volumeWidth: data && data.dimension ? data.dimension.width : 1,
    volumeHeight: data && data.dimension ? data.dimension.height : 1,
    weight: data ? data.weight : 100,
});

export const createDefaultFormDataWholesalePrices = (data = null) => ({
    id: createID(),
    minQty: data ? data.min_qty : 0,
    unitPrice: data ? data.selling_price : 0,
});

export const createDefaultFormDataVariantTypeOptions = (data = null) => ({
    ...!data && { _isNewVariantTypeOption: true },
    id: createID(),
    value: data ? data.value : '',
});

export const createDefaultFormDataVariantTypes = (data = null) => ({
    id: createID(),
    name: data ? data.name : '',
    options: data ? data.options : [createDefaultFormDataVariantTypeOptions()],
});

export const createDefaultFormDataVariantListRow = (data = null, defaultData = initialDefaultData) => ({
    option: data ? data.option : '', // example value: Hitam, Manis
    /**
     * contoh value untuk kolom `detail`:
     * {
     *    "Warna": "Hitam",
     *    "Rasa": "Manis"
     * }
     */
    detail: data ? data.variant_detail : {},
    variant_capital_price: data ? +data.variant_capital_price : 0,
    purchasePrice: data ? +data.variant_purchase_price : 0,
    sellingPrice: data ? +data.variant_selling_price : 0,
    sku: data ? data.variant_sku : '',
    isActive: data ? !!data.variant_status : true,
    masterRecipeId: data ? data.master_recipe_id || '' : '',
    masterRecipeName: data ? data.master_recipe_name || '' : '',
    selectedRawMaterials: data ? data.recipes.map((x) => {
        const found = defaultData.rawMaterials.find(rawMaterial => String(rawMaterial.id) === String(x.item_id));

        let unitName = '';
        let averagePrice = 0;
        let capitalPrice = 0;

        if (found) {
            if (ishasProperty(x, 'average_price')) {
                averagePrice = x.average_price;
            } else {
                averagePrice = found.average_price;
            }

            capitalPrice = (averagePrice * x.quantity);
            unitName = found.unit_name;
        }


        return createDefaultFormDataSelectedRawMaterials({
            ...x, unitName, averagePrice, capitalPrice,
        });
    }) : [
        createDefaultFormDataSelectedRawMaterials(),
    ],
    ...data ? { initialDetail: data.variant_detail, initialOption: data.option } : {},
});

export const createDefaultFormDataSelectedProductExtras = (data = null) => ({
    id: createID(),
    productExtraId: data ? data.id : '',
    productExtraName: data ? data.name : '',
    details: data ? (data.detail || []).map(x => ({
        ...x,
        subExtraId: x.id_add_ons_detail ? x.id_add_ons_detail : '',
        subExtraName: x.add_ons_detail_name ? x.add_ons_detail_name : '',
        sellingPrice: x.add_ons_detail_harga_jual ? x.add_ons_detail_harga_jual : 0,
        ingredients: x.add_ons_detail_ingredients ? x.add_ons_detail_ingredients : [],
    })) : [],
});

const buildVariantTypesFromAPI = ({ variants }) => {
    let variantTypes = [];

    variants.forEach((variant) => {
        Object.keys(variant.variant_detail).forEach((key) => {
            const indexFound = variantTypes.findIndex(x => x.name === key);
            const hasAddedToVariantType = indexFound > -1;

            const newOption = {
                ...createDefaultFormDataVariantTypeOptions({ value: variant.variant_detail[key] }),
                initialValue: variant.variant_detail[key],
            };

            if (hasAddedToVariantType) {
                variantTypes = update(variantTypes, {
                    [indexFound]: {
                        options: {
                            $push: [newOption],
                        },
                    },
                });
            } else {
                variantTypes = [...variantTypes, createDefaultFormDataVariantTypes({
                    name: key,
                    options: [newOption],
                })];
            }
        });
    });

    variantTypes = variantTypes.map((x) => {
        const options = _.uniqBy(x.options, 'value');

        return {
            ...x,
            options,
        };
    });

    return variantTypes;
};

const buildSelectedProductExtras = (data, { productExtras }) => {
    if (!data) return [createDefaultFormDataSelectedProductExtras()];

    const addons = data.add_ons.map(x => createDefaultFormDataSelectedProductExtras(x));

    return addons;
};

const buildSelectedRawMaterials = ({ data, rawMaterials }) => {
    let results = [];

    if (((data.is_variant && !data.is_recipe_all_variants) || (!data.recipes || data.recipes.length === 0)) && data.variants) {
        data.variants.forEach((x) => {
            if (x.recipes) {
                results = [...results, ...x.recipes];
            }
        });

        results = _.uniqBy(results, 'item_id');
    } else {
        results = data.recipes;
    }

    return results.map((x) => {
        const found = rawMaterials.find(rawMaterial => String(rawMaterial.id) === String(x.item_id));

        let unitName = '';
        let averagePrice = 0;
        let capitalPrice = 0;


        if (ishasProperty(x, 'average_price')) {
            averagePrice = x.average_price;
        } else if (found) {
            averagePrice = found.average_price;
        }

        capitalPrice = (averagePrice * x.quantity);

        unitName = found ? found.unit_name : '';

        return createDefaultFormDataSelectedRawMaterials({
            ...x, unitName, averagePrice, capitalPrice,
        });
    });
};

const buildWebOrderFormData = (productData = null, defaultData = initialDefaultData, multiUnits = []) => {
    let data = null;
    let productDescFromAPI = '';
    let onlineStoreOutletIds = [defaultData.idCabang];
    const sellingPrice = multiUnits.length > 0 ? +multiUnits[0].sellingPrice : 0;

    if (defaultData.marketplaceProducts && defaultData.marketplaceProducts.length > 0) {
        onlineStoreOutletIds = defaultData.marketplaceProducts.map(x => x.outlet_id);
        if (productData && productData.outlets) onlineStoreOutletIds = onlineStoreOutletIds.filter(id => productData.outlets.map(x => String(x)).includes(String(id)));
        if (defaultData.filterBranch) {
            const marketplaceData = defaultData.marketplaceProducts.find(product => String(product.outlet_id) === String(defaultData.filterBranch));
            if (marketplaceData) {
                data = marketplaceData;
            }
        }
        if (!data && defaultData.idCabang) {
            const marketplaceData = defaultData.marketplaceProducts.find(product => String(product.outlet_id) === String(defaultData.idCabang));
            if (marketplaceData) {
                data = marketplaceData;
            }
        }
        if (!data) {
            ([data] = defaultData.marketplaceProducts);
        }
    }

    if (productData && productData.item_description_long && productData.item_description_long.length > 0) {
        let found = null;

        if (!data) {
            found = productData.item_description_long.find(x => String(x.id_outlet) === String(productData.outlets[0]));
        } else {
            const outletExistsInWebOrder = outletId => defaultData.marketplaceProducts.some(x => String(x.outlet_id) === String(outletId));

            found = productData.item_description_long.find(x => outletExistsInWebOrder(x.id_outlet));
        }

        if (found) productDescFromAPI = found.value;
    }

    const convertToEditorState = (val) => {
        const blockContent = htmlToDraft(val);
        const contentState = ContentState.createFromBlockArray(blockContent.contentBlocks);
        return EditorState.createWithContent(contentState);
    };

    const manualPrice = data ? +data.price : 0;

    return {
        onlineStoreOutletIds,
        onlineStoreManualChecked: data ? !!data.is_manual_price : false,
        onlineStoreManualPrice: (data && data.is_manual_price) ? manualPrice : sellingPrice,
        isWebOrderActive: data ? data.status : false,
        onlineStoreFavoriteProductChecked: data ? !!data.is_favourite : false,
        onlineStoreMinimumStockChecked: data ? !!data.minimum_stock : false,
        onlineStoreMinimumStock: data ? data.minimum_stock : 0,
        onlineStoreProductDescription: productDescFromAPI,
        onlineStoreProductDescriptionRaw: !productDescFromAPI ? EditorState.createEmpty() : convertToEditorState(productDescFromAPI),
    };
};

const filteringFormDataOutletIds = ({ outletIds, outlets, outletGroupList = [] }) => {
    if (!outlets) return outletIds;

    let filtered = outletIds.filter(outletId => outlets.some(outlet => +outlet.id === +outletId));
    const matchingGroupIds = getMatchingGroups(outletGroupList.map(group => group.id_cabangs), filtered);
    filtered = filtered.filter(outletId => !matchingGroupIds.some(group => group.includes(outletId)));
    return [...matchingGroupIds, ...filtered];
};

export const createFormData = (data = null, defaultData = initialDefaultData) => {
    let categoryId = '';
    let multiUnits = [
        createDefaultFormDataMultiUnit(),
    ];

    let variantTypes = [
        createDefaultFormDataVariantTypes(),
    ];
    let variantList = [];

    let wholesalePrices = [
        createDefaultFormDataWholesalePrices(),
    ];

    let selectedRawMaterials = [
        createDefaultFormDataSelectedRawMaterials(),
    ];

    if (data && data.categories && data.categories.length > 0) categoryId = data.categories[0].id;
    if (data && data.units && data.units.length > 0) {
        multiUnits = data.units.map(x => ({
            ...x,
            ...createDefaultFormDataMultiUnit(x),
        }));
    }

    if (data && data.variants) {
        variantTypes = buildVariantTypesFromAPI(data);
        variantList = data.variants.map((x) => {
            let options = [];

            Object.keys(x.variant_detail).forEach((key) => {
                options = [...options, x.variant_detail[key]];
            });

            return {
                ...x,
                masterRecipeId: x.master_recipe_id || x.variant_master_recipe_id,
                masterRecipeName: x.master_recipe_name || x.variant_master_recipe_name,
                ...createDefaultFormDataVariantListRow({ ...x, option: options.join(', ') }, defaultData),
            };
        });
    }

    if (data && data.wholesale_prices) {
        wholesalePrices = data.wholesale_prices.map(x => createDefaultFormDataWholesalePrices(x));
    }

    if (data) { selectedRawMaterials = buildSelectedRawMaterials({ data, rawMaterials: defaultData.rawMaterials }); }

    let outletIds = [String(defaultData.idCabang)];

    if (data) {
        const assignedOutletIds = defaultData.listOutlet ? defaultData.listOutlet.map(val => String(val[0])) : [];
        const filteredOutletIds = data.outlets ? data.outlets.filter(id => assignedOutletIds.includes(id)) : assignedOutletIds;
        const filteredOutlets = defaultData.outlets ? defaultData.outlets.filter(outlet => assignedOutletIds.includes(String(outlet.value))) : null;
        outletIds = filteringFormDataOutletIds({ outletIds: filteredOutletIds, outlets: filteredOutlets, outletGroupList: defaultData.outletGroupList });
    }

    const formData = {
        id: data ? data.id : '',
        productName: data ? String(data.name) : '',
        outletIds,
        description: data ? data.description : '',
        productImages: data ? data.images.map(x => ({ name: null, url: x })) : [],
        categoryId,
        hasExpiredDate: !!data && !!data.has_expired_date,
        isMonitorStock: !!data && !!data.use_cogs,
        isSerialNumberActive: !!data && !!data.has_serial_number,
        isBatchNumberActive: !!data && !!data.has_batch_number,
        minimumStock: data ? data.stock_alert : 0,
        groupParentId: data ? data.id_parent : '',
        setAsParent: !!data && !!data.is_parent,
        canChangeSellingStatus: data ? data.can_change_selling_status || 0 : 0,
        isFavorite: !!data && !!data.is_favorite,
        showOnMenu: data ? !!data.is_displayed : true,
        multiUnits,
        isSellingPriceEditable: !!data && data.changeable_price_percent > 0,
        editableSellingPrice: data ? data.changeable_price_percent : 0,
        isWholesalePriceActive: !!data && !!data.is_wholesale_price_active,
        wholesalePrices,
        variantTypes,
        variantList,
        hasVariant: !!data && !!data.is_variant,
        hasProductExtra: !!data && !!data.is_extra,
        isProductExtraChangeable: data ? data.add_ons_change : false,
        selectedProductExtras: buildSelectedProductExtras(data, defaultData),
        hasRecipe: !!data && !!data.has_recipe,
        hasVariantRecipe: !!data && !!data.has_recipe && !!data.is_variant && !data.is_recipe_all_variants,
        selectedRawMaterials,
        masterRecipeId: data ? data.master_recipe_id || '' : '',
        masterRecipeName: data ? data.master_recipe_name || '' : '',
        isHavingTransaction: data ? (+data.is_transaction === 1) : false,
        isValidateGroupOutlet: data && !!data.group_outlet_id ? 1 : 0,
        ...buildWebOrderFormData(data, defaultData, multiUnits),
    };

    return formData;
};

export const stepNavigationContents = ({ LANG_DATA }) => [
    {
        title: LANG_DATA.FORM_STEP_PRODUCT_INFORMATION,
    },
    {
        title: LANG_DATA.FORM_STEP_VARIANT,
    },
    {
        title: LANG_DATA.FORM_STEP_EXTRA,
    },
    {
        title: LANG_DATA.FORM_STEP_RECIPE,
    },
    {
        title: LANG_DATA.FORM_STEP_ONLINE_STORE,
    },
];
