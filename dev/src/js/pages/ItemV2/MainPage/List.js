import React, { useContext, useEffect, useMemo } from 'react';
import {
    Table,
} from '@majoo-ui/react';
import PageContext from '../PageContext';
import { tableColumns } from './utils';
import { useTranslationHook } from '../lang.utils';
import { PRICE_SETTING } from '../../Settings/ProductInventory/PurchasePriceSettings';

const handleReformatSelectedIds = ({ selectedProductIds }) => {
    let reformatSelectedProductIds = {};

    selectedProductIds.forEach((x) => {
        reformatSelectedProductIds = {
            ...reformatSelectedProductIds,
            [x]: true,
        };
    });

    return reformatSelectedProductIds;
};

const List = () => {
    const {
        loading, filterData, product, onFetchProducts, onOpenFormProduct,
        isInMobileView, onSetSelectedProductIds, selectedProductIds, translationData,
        tableKey, priceSetting,
    } = useContext(PageContext);
    const { LANG_DATA } = translationData;

    const isLastPurchasePrice = priceSetting === PRICE_SETTING.LAST_PURCHASE_PRICE;

    const columns = useMemo(() => tableColumns({ onClick: onOpenFormProduct, isInMobileView, LANG_DATA, isLastPurchasePrice }), [product.data, isInMobileView, LANG_DATA.MAIN_TITLE, isLastPurchasePrice]);
    const reformatSelectedProductIds = useMemo(() => handleReformatSelectedIds({ selectedProductIds }), [selectedProductIds]);

    return (
        <Table
            key={tableKey}
            selectedIds={reformatSelectedProductIds}
            columns={columns}
            data={product.data}
            totalData={product.totalData}
            pageIndex={product.pageIndex}
            fetchData={onFetchProducts}
            css={{ padding: 0 }}
            searchQuery={filterData.keyword}
            isLoading={loading}
            onSelectedChange={onSetSelectedProductIds}
        />
    );
};

export default List;
