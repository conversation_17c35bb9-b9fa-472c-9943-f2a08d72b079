import React, { useContext, useEffect, useState } from 'react';
import {
    Flex, Text, Box, Pagination,
    Image, Paragraph, Grid, GridItem,
} from '@majoo-ui/react';
import { Trans } from 'react-i18next';
import { useParams } from 'react-router-dom';
import ProductCard from '../componentV2/ProductCard';
import { getSuppliesProduct } from '../../../data/supplies';
import { selectorDataProduct } from '../selector';
import helper from '../helper';
import ProductEmptyBinoculars from '../../../../assets/images/doodle-not-found.png';
import SortProduct from '../componentV2/SortProduct';
import { SORT_CATEGORY, SORT_VALUE, SUPPLIES_CATEGORY } from '../componentV2/utils';
import { SuppliesContext } from '../context/SuppliesContext';
import ButtonBack from '../componentV2/ButtonBack';

const CategoryDetail = () => {
    const {
        hideProgress, filterBranch, showProgress,
        assignCustomBreadCrumbs, t,
        isCoverage, userOutlet, setBlock, keyword, isMobile,
    } = useContext(SuppliesContext);
    const notCategory = Object.values(SORT_CATEGORY);
    const params = useParams();
    const [title, setTitle] = useState('');
    const [products, setProducts] = useState([]);
    const [limitPage, setLimitPage] = useState(10);
    const [pageSelected, setPageSelected] = useState(1);
    const [totalPage, setTotalPage] = useState(0);
    const isCategory = !notCategory.includes(params.id);
    const isFranchise = params.id === SORT_CATEGORY.FRANCHISE;
    const [sort, setSort] = useState(([SORT_CATEGORY.NEW, SORT_CATEGORY.BEST_SELLER].includes(params.id)) ? params.id : SORT_CATEGORY.CHEAP);

    const fetchProduct = async () => {
        let payload = {
            per_page: limitPage,
            page: pageSelected,
            search: keyword || '',
            is_coverage: isFranchise ? false : isCoverage,
            id_outlet: userOutlet.id_cabang || '',
            status: 1,
            ...(sort ? SORT_VALUE[sort] : {}),
            ...(isFranchise ? { is_franchise: 1 } : {}),
        };
        try {
            showProgress();
            let result = [],
                resPage = 0,
                resTitle = title;
            if (isCategory) {
                payload = { ...payload, category_no: params.id };
            } else {
                payload = { ...payload, supplies_category: SUPPLIES_CATEGORY[params.id] };
            }
            const { data, meta } = await getSuppliesProduct(payload);
            result = selectorDataProduct(data.product);
            resPage = meta.total || 0;
            resTitle = data.category && data.category.name ? data.category.name : title;
            setProducts(result);
            setTotalPage(resPage);
            setTitle(resTitle);
        } catch (err) {
            const check = helper.checkingError(err);
            if (check) {
                setBlock(true);
            }
        } finally {
            hideProgress();
        }
    };

    useEffect(() => {
        setPageSelected(1);
    }, [keyword]);

    useEffect(() => {
        if (userOutlet) {
            fetchProduct();
            window.scrollTo(0, 0);
        }
    }, [pageSelected, limitPage, isCoverage, filterBranch, sort, userOutlet]);

    useEffect(() => {
        if (isCategory && title && title !== '') {
            const dataBreadCrumbs = [
                {
                    path: '/shopping/order',
                    label: 'Supplies',
                },
                {
                    path: '/shopping/order',
                    label: 'Belanja',
                },
                {
                    path: '/shopping/order/category/all',
                    label: 'Kategori',
                },
                {
                    path: `/shopping/order/category/${params.id}`,
                    label: title,
                    isCurrent: true,
                },
            ];
            assignCustomBreadCrumbs(dataBreadCrumbs);
        }
        return () => {
            assignCustomBreadCrumbs([]);
        };
    }, [title]);

    return (
        <Box>
            <ButtonBack />
            {isCategory && title !== '' && (
                <Box
                    css={{
                        background: 'linear-gradient(93.34deg, #13AB8A 16.72%, #70D0C1 55.2%, #27B495 100.69%);',
                        padding: '$spacing-05 $spacing-06',
                        borderRadius: '4px',
                        marginBottom: 10,
                    }}
                >
                    <Text css={{ color: '$white', fontSize: '$heading-3' }}>
                        {title}
                    </Text>
                </Box>
            )}
            <SortProduct t={t} sort={sort} setSort={setSort} />
            {products.length > 0 ? (
                <Grid
                    css={{
                        gridGap: '0.5em',
                        '@sm': {
                            gridTemplateColumns: 'repeat(2, 45vw)',
                        },
                        '@md': {
                            gridTemplateColumns: 'repeat(3, 1fr)',
                        },
                        '@lg': {
                            gridTemplateColumns: 'repeat(5, 1fr)',
                        },
                        marginBottom: '$cozy',
                    }}
                >
                    {products.map(data => (
                        <GridItem>
                            <ProductCard fullWidth data={data} key={data.id} />
                        </GridItem>
                    ))}
                </Grid>
            ) : (
                <Flex
                    justify="center"
                    gap={3}
                    wrap="wrap"
                    css={{ minHeight: '150px' }}
                    mb={8}
                >
                    <Flex css={{ gap: 20.8, '@md': { mb: 80 } }} align="center" direction="column">
                        <Image src={ProductEmptyBinoculars} height={200} />
                        <Flex gap={3} direction="column" align="center">
                            <Paragraph color="primary" paragraph="shortContentBold">
                                <Trans t={t} i18nKey="main.emptyData.notFound">
                                    {{ keyword }}
                                </Trans>
                            </Paragraph>
                            <Paragraph align="center" color="secondary">
                                {t('main.emptyData.desc', 'Belum ada data yang dapat ditampilkan di halaman ini')}
                            </Paragraph>
                        </Flex>
                    </Flex>
                </Flex>
            )}
            <Pagination
                limit={limitPage}
                currentPage={pageSelected}
                totalData={totalPage}
                onLimitChange={(val) => {
                    setLimitPage(val);
                    setPageSelected(1);
                }}
                onPageChange={val => setPageSelected(val)}
                rowsPerPageOptions={[10, 20, 30]}
            />
        </Box>
    );
};

export default CategoryDetail;
