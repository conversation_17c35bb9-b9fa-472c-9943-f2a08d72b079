import React, {
    useState, useEffect, useRef, useContext,
} from 'react';
import { useHistory, useParams } from 'react-router-dom';
import {
    Flex, Button, Box, Text,
    Separator, Heading,
    Banner, BannerDescription, Image, Skeleton,
} from '@majoo-ui/react';
import {
    TimeOutline, OutletOutline, CircleInfoFilled,
} from '@majoo-ui/icons';
import { foundations } from '@majoo-ui/core';
import moment from 'moment';
import { connect } from 'react-redux';
import { getSuppliesProductCourier, getSuppliesProductDestination, getSuppliesProductDetail } from '../../../data/supplies';
import cartHelper from '../helper';
import { catchError, currency } from '../../../utils/helper';
import { useMediaQuery } from '../../../utils/useMediaQuery';
import ProductImages from './components/ProductImage';
import ProductDescription from './components/ProductDescription';
import ModalSchedule from './components/ModalSchedule';
import ModalCourier from './components/ModalCourier';
import { styled } from '../../../stitches.config';
import ModalAddToCart from './components/ModalAddToCart';
import ButtonBack from '../componentV2/ButtonBack';
import { SuppliesContext } from '../context/SuppliesContext';
import QuantityButton from '../componentV2/QuantityButton';
import { KEY_RECAP_DATE } from './components/utils';
import FranchiseTag from '../componentV2/FranchiseTag';

const ProductDetail = ({ outletData }) => {
    const router = useHistory();
    const params = useParams();
    const isMobile = useMediaQuery('(max-width: 1024px)');
    const {
        hideProgress, showProgress, filterBranch, listBranch, outletDefault,
        assignCustomBreadCrumbs, setBlock, setWithSearch, t,
        addUserCart, updateUserCart, addToast, cartList, idCabang,
    } = useContext(SuppliesContext);

    const [loadingProduct, setLoadingProduct] = useState(false);
    const [product, setProduct] = useState({
        name: '',
        price: 0,
        qty: 0,
        desc: '',
        imageUrl: [],
        isEmpty: false,
        location: '',
        category: '',
        weight: 0,
    });
    const [qtyOnCart, setQtyOnCart] = useState(1);
    const [productRaw, setProductRaw] = useState(null);
    const [destination, setDestination] = useState({
        outlet_id: null,
        is_opend_hour: false,
        outlet_name: '',
        outlet_logo: '',
        schedule: [],
        latitude: '',
        longitude: '',
    });
    const [origin, setOrigin] = useState({
        outlet_id: null,
        is_opend_hour: false,
        outlet_name: '',
        outlet_logo: '',
        schedule: [],
        latitude: '',
        longitude: '',

    });
    const [courier, setCourier] = useState(null);
    const [courierText, setCourierText] = useState('');
    const [productInCart, setProductInCart] = useState();
    const [selectedOutlet, setSelectedOutlet] = useState({ id: '', longitude: '', latitude: '' });

    const modalScheduleRef = useRef();
    const modalCourierRef = useRef();
    const modalAddToCartRef = useRef();

    if (!params.merchantId || !params.outletId || !params.id) router.goBack();

    const TextLink = styled('span', {
        cursor: 'pointer',
        textDecoration: 'underline',
        fontWeight: 500,
    });

    const fetchCourier = async () => {
        if (
            !origin.outlet_latitude
            || !origin.outlet_longtitude
            || !destination.outlet_latitude
            || !destination.outlet_longtitude
        ) {
            console.log('das');
            setLoadingProduct(false);
            return;
        }
        console.log(destination);
        const payload = {
            origin: {
                outlet_id: origin.outlet_id,
                latitude: Number(origin.outlet_latitude),
                longitude: Number(origin.outlet_longtitude),
            },
            destination: {
                outlet_id: destination.outlet_id,
                latitude: Number(destination.outlet_latitude),
                longitude: Number(destination.outlet_longtitude),
            },
            items: [{
                value: productRaw.price,
                quantity: productRaw.min_order,
                weight: productRaw.weight,
                height: productRaw.volume.height,
                length: productRaw.volume.length,
                width: productRaw.volume.width,
            }],
        };
        try {
            const res = await getSuppliesProductCourier(payload);
            if (res && res.error) throw new Error(res.message);
            const { data: { outlet: outletCourier, shipper } } = res;
            const listShipper = shipper || [];
            if (!outletCourier.name && !listShipper.length) throw new Error('courier not found');
            setCourier({
                outlet: { ...outletCourier, ...cartHelper.reformatEstimate(outletCourier.shipment_duration) },
                shipper: listShipper.filter(x => x.service).map(x => ({
                    ...x,
                    service: x.service ? x.service.map(service => ({
                        ...service,
                        ...cartHelper.reformatEstimate(service.shipment_duration),
                    })) : [],
                })),
            });
            let tempCourier = [];
            if (outletCourier.name) {
                tempCourier.push({
                    price: outletCourier.price,
                    ...cartHelper.reformatEstimate(outletCourier.shipment_duration),
                });
            }
            listShipper.filter(x => x.service).forEach((x) => {
                const tempService = x.service.map(service => ({
                    price: service.price,
                    ...cartHelper.reformatEstimate(service.shipment_duration),
                }));
                tempCourier = [...tempCourier, ...tempService];
            });
            const isDays = tempCourier.some(x => x.key === 'days');
            if (isDays) tempCourier = await tempCourier.filter(x => x.key === 'days');
            const { cost, estimate } = await cartHelper.courierInfo(tempCourier);
            setCourierText(`${t('detailProduct.courierInfo', { cost, estimate, defaultValue: '' })} ${isDays ? t('detailProduct.day', 'Hari') : t('detailProduct.hour', 'Jam')}`);
        } catch (error) {
            setCourier(null);
        } finally {
            setLoadingProduct(false);
        }
    };

    const handleCheckCart = (data) => {
        const tempCartList = cartList || [];
        const result = tempCartList.find(x => x.inventoryId === data.inventory_id);
        setProductInCart(result);
        setQtyOnCart(result ? result.quantity : data.min_order);
    };

    const fetchProductDestination = async () => {
        const outlet = cartHelper.getUserOutlet(listBranch, outletDefault, filterBranch);
        const payload = {
            outlet_id: params.outletId,
            outlet_id_destination: outlet.id_cabang,
        };
        try {
            const res = await getSuppliesProductDestination(payload);
            if (res && res.error) throw new Error(res.message);
            setDestination(res.data.destination);
            setOrigin(res.data.outlet);
        } catch (error) {
            const check = cartHelper.checkingError(error);
            if (check) {
                setBlock(true);
            } else router.goBack();
        }
    };

    const fetchProductDetail = async () => {
        try {
            setLoadingProduct(true);
            const { data, error, message } = await getSuppliesProductDetail(params.merchantId, params.outletId, params.id);
            if (error) throw new Error(message);
            setProduct({
                name: data.name,
                price: data.price,
                qty: data.stock,
                desc: data.description,
                imageUrl: data.image,
                isEmpty: data.use_cogs && +data.stock <= 0,
                useCogs: data.use_cogs,
                location: data.coverage,
                sold: data.sold,
                category: data.category_name,
                weight: data.weight,
                idOutlet: data.outlet_id,
                merchantId: data.merchantId,
                id: data.inventory_id,
                isFranchise: data.is_franchise === 1,
            });
            setProductRaw(data);
        } catch (err) {
            const check = cartHelper.checkingError(err);
            if (check) {
                setBlock(true);
            } else router.goBack();
        }
    };

    const handleAddOnCart = (isRedirect = false) => {
        const isOnCart = Boolean(productInCart);
        const updateCart = isOnCart ? updateUserCart : addUserCart;
        const dataForCart = {
            name: productRaw.name,
            price: productRaw.price,
            image: productRaw.image,
            inventoryId: productRaw.inventory_id,
            quantity: qtyOnCart,
            total: (+qtyOnCart * +productRaw.price),
            stock: productRaw.stock,
            merchantId: productRaw.merchant_id,
            outletId: productRaw.outlet_id,
            weight: productRaw.weight,
            height: productRaw.volume.height,
            length: productRaw.volume.length,
            width: productRaw.volume.width,
            outletName: origin.outlet_name || '',
            isOpen: origin.is_opend_hour,
            useCogs: productRaw.use_cogs,
            minOrder: productRaw.min_order,
            isSelected: isRedirect,
            latitude: origin.outlet_latitude,
            longitude: origin.outlet_longtitude,
            ...(isOnCart && { cartId: productInCart.cartId }),
        };
        updateCart(dataForCart).then(() => {
            if (isRedirect) {
                router.push('/shopping/order/keranjang');
            }
            modalAddToCartRef.current.showPopup();
        }, (err) => {
            addToast({
                title: t('translation:toast.error', 'Gagal!'),
                description: catchError(err),
                variant: 'failed',
            });
        });
    };

    useEffect(() => {
        fetchProductDetail();
    }, []);

    useEffect(() => {
        if (listBranch.length) fetchProductDestination();
    }, [filterBranch, listBranch]);

    useEffect(() => {
        if (productRaw && origin.outlet_id && destination.outlet_id) {
            fetchCourier();
        } else if (productRaw && destination.outlet_id === 0) {
            setLoadingProduct(false);
        }
    }, [productRaw, origin, destination]);

    useEffect(() => {
        if (productRaw) handleCheckCart(productRaw);
    }, [productRaw, cartList]);

    useEffect(() => {
        if (product.name && product.name !== '') {
            const dataBreadCrumbs = [
                {
                    path: '/shopping/order',
                    label: 'Supplies',
                },
                {
                    path: '/shopping/order',
                    label: 'Belanja',
                },
                {
                    path: '/shopping/order/category/all',
                    label: 'Kategori',
                },
                {
                    path: `/shopping/order/product/${params.id}`,
                    label: product.name,
                    isCurrent: true,
                },
            ];
            assignCustomBreadCrumbs(dataBreadCrumbs);
        }
        return () => {
            assignCustomBreadCrumbs([]);
        };
    }, [product]);

    const isDisabled = product.isEmpty || courier === null;
    const isOverQuantity = product.useCogs && qtyOnCart > product.qty;

    const getLocation = (data) => {
        if (data && data.outlet_city_name && data.outlet_province_name) {
            return `${data.outlet_city_name}, ${data.outlet_province_name}`;
        }
        return '';
    };

    useEffect(() => {
        setWithSearch(false);
        return () => {
            setWithSearch(true);
        };
    }, []);

    useEffect(() => {
        if (loadingProduct) {
            showProgress();
        } else {
            hideProgress();
        }
    }, [loadingProduct]);

    const isClosedOutlet = () => {
        const dayNow = moment(new Date()).locale('id').format('dddd');
        const scheduleData = origin.schedule.find(s => s.date === KEY_RECAP_DATE[dayNow]);
        const todayData = scheduleData && Array.isArray(scheduleData.time) ? scheduleData.time : false;
        let isBetween = false;

        if (todayData) {
            const currentTime = moment();

            const targetTime = moment().set({ hour: +todayData[0].end.split(':')[0], minute: +todayData[0].end.split(':')[1], second: 0 });

            isBetween = targetTime.isBetween(currentTime);
        }

        return !isBetween && todayData;
    };
    useEffect(() => {
        if (Array.isArray(outletData)) {
            setSelectedOutlet(() => {
                const searchOutlet = outletData.filter(outlet => outlet.id_cabang !== '').find(outlet => outlet.id_cabang === (filterBranch || idCabang));

                if (searchOutlet) {
                    return {
                        id: searchOutlet.id_cabang,
                        latitude: searchOutlet.cabang_latitude,
                        longitude: searchOutlet.cabang_longitude,
                    };
                }

                return { id: '', longitude: '', latitude: '' };
            });
        }
    }, [idCabang, filterBranch, outletData]);

    return (loadingProduct) ? (
        <React.Fragment>
            <Skeleton varinat="single" />
            <Skeleton varinat="single" />
            <Skeleton varinat="single" />
        </React.Fragment>
    ) : (
        <Flex direction="column" gap={4}>
            <ButtonBack />
            {(!selectedOutlet.latitude || !selectedOutlet.longitude) && (
                <Banner variant="warning" bannerBlock>
                    <Flex css={{ gap: '8px', width: '100%' }}>
                        <CircleInfoFilled color="#404545" />
                        <Flex direction="column" css={{ gap: '4px', width: '100%' }}>
                            <Box css={{ mt: '2px' }}>
                                <Text color="primary" css={{ fontSize: '16px', fontWeight: 600 }}>Atur Titik Lokasi</Text>
                            </Box>
                            <Box>
                                <Text>
                                    Untuk melanjutkan proses transaksi, silakan atur titik lokasi pengiriman pada maps
                                </Text>
                            </Box>
                            <Flex justify="end">
                                <Text
                                    onClick={() => router.push(`/pengaturan-bisnis/cabang/detail/${selectedOutlet.id}?maps=supplies&redirect=/shopping/order`)}
                                    color="primary"
                                    css={{
                                        fontSize: '14px', fontWeight: 400, cursor: 'pointer', textDecoration: 'underline', textUnderlineOffset: 2,
                                    }}
                                >
                                    Atur Pin Point
                                </Text>
                            </Flex>
                        </Flex>
                    </Flex>
                </Banner>
            )}
            {(!origin.is_opend_hour) && (
                <Banner variant="warning" bannerBlock>
                    <Flex css={{ gap: '8px', width: '100%' }}>
                        <CircleInfoFilled color={foundations.colors.iconRed} size={20} />
                        <span style={{ width: '100%' }}>
                            {isClosedOutlet() ? 'Toko sedang tutup. Untuk melihat detail jadwal buka toko' : t('detailProduct.outletDayOffInfo', 'Toko sedang libur. Untuk melihat detail jadwal buka toko')}
                            {' '}
                            <TextLink onClick={() => modalScheduleRef.current.showPopup()}>
                                {t('detailProduct.clickHere', 'Klik di Sini')}
                            </TextLink>
                        </span>
                    </Flex>
                </Banner>
            )}
            {(product && product.isEmpty) && (
                <Banner variant="warning" bannerBlock>
                    <Flex css={{ gap: '8px', width: '100%' }}>
                        <CircleInfoFilled color={foundations.colors.iconRed} size={20} />
                        <span style={{ width: '100%' }}>
                            {t('detailProduct.stockEmpty', 'Stok sedang habis, silakan datang lagi saat produk kembali tersedia')}
                        </span>
                    </Flex>
                </Banner>
            )}
            {!courier && (
                <Banner variant="failed" bannerBlock>
                    <Flex css={{ gap: '8px', width: '100%' }}>
                        <CircleInfoFilled color={foundations.colors.iconRed} size={20} />
                        <span style={{ width: '100%' }}>
                            {t('detailProduct.noCourierInfo', 'Kurir tidak tersedia, pembelian tidak dapat diproses')}
                        </span>
                    </Flex>
                </Banner>
            )}
            <Flex
                gap={6}
                css={{
                    flexDirection: 'column',
                    '@lg': {
                        flexDirection: 'row',
                    }
                }}
            >
                <ProductImages images={product.imageUrl} isEmpty={product.isEmpty} t={t} />
                <Box css={{ width: '$full' }}>
                    {/* sementara di hide dulu karena ada request dari PO */}
                    {/* <Box css={{ marginBottom: '$spacing-03' }}>
                        <Text
                            css={{
                                fontSize: '$label', color: '$gray700',
                            }}
                        >
                            {t('main.sold', 'Terjual')}
                            {' '}
                            { product.sold }
                        </Text>
                    </Box> */}
                    <Flex
                        css={{
                            flexDirection: 'column-reverse',
                            marginBottom: '$spacing-03',
                            '@md': {
                                flexDirection: 'row',
                            }
                        }}
                        justify="between"
                        gap={3}
                    >
                        <Text
                            css={{
                                fontSize: '$page-title-2', fontWeight: '600', color: '$gray900', wordBreak: 'break-all',
                            }}
                        >
                            {product.name}
                        </Text>
                        {product.isFranchise && (<FranchiseTag />)}
                    </Flex>
                    <Box css={{ margin: '$spacing-05 0' }}>
                        <Text
                            css={{
                                fontSize: '$heading-2', fontWeight: '600', color: '$gray900',
                            }}
                        >
                            {currency({ value: product.price })}
                        </Text>
                    </Box>
                    {(origin.is_opend_hour) && (
                        <Flex direction="column" gap={3}>
                            <Flex gap={6}>
                                <Box css={{ minWidth: '100px' }}>
                                    <Text variant="contentButton" color="primary">{t('detailProduct.qty', 'Atur Jumlah')}</Text>
                                </Box>
                                <Flex direction="column" gap={4} css={{ width: '$full' }}>
                                    <Flex gap={3} align="baseline" css={{ flexDirection: 'column', '@md': { flexDirection: 'row' } }}>
                                        <QuantityButton
                                            qty={qtyOnCart}
                                            setQty={setQtyOnCart}
                                            hasLimit={product.useCogs}
                                            isEmpty={product.isEmpty}
                                            limit={product.qty}
                                            isDisabled={isDisabled}
                                        />
                                        {product.useCogs && (
                                            <Box css={{ minWidth: '100px', marginLeft: 0, '@md': { marginLeft: '$spacing-06' }  }}>
                                                <Flex gap={2}>
                                                    <Text>
                                                        {t('detailProduct.stock', 'Stok')}
                                                        :
                                                    </Text>
                                                    <Text css={{ fontWeight: '600', color: product.isEmpty ? '$textRed' : '$gray900' }}>
                                                        {product.isEmpty ? t('detailProduct.empty', 'Kosong') : product.qty}
                                                    </Text>
                                                </Flex>
                                            </Box>
                                        )}
                                    </Flex>
                                </Flex>
                            </Flex>
                            <Flex justify="between" gap={4} css={{ marginLeft: 0, '@md': { marginLeft: 124 } }}>
                                    <Button
                                        css={{ width: '$full' }}
                                        buttonType="secondary"
                                        onClick={() => handleAddOnCart(true)}
                                        disabled={isDisabled || +qtyOnCart <= 0 || isOverQuantity || qtyOnCart < productRaw.min_order}
                                    >
                                        {t('detailProduct.btnBuy', 'Beli Sekarang')}
                                    </Button>
                                    <Button
                                        css={{ width: '$full', minWidth: 'fit-content' }}
                                        onClick={() => handleAddOnCart(false)}
                                        disabled={isDisabled || +qtyOnCart <= 0 || isOverQuantity || qtyOnCart < productRaw.min_order}
                                    >
                                        {t('detailProduct.btnAddCart', 'Tambah ke Keranjang')}
                                    </Button>
                                </Flex>
                        </Flex>
                    )}
                    <Separator css={{ margin: '1em 0' }} />
                    <Flex direction="column" gap={6}>
                        <Flex direction="column" gap={4}>
                            <Heading heading="sectionTitle" css={{ marginBottom: '$spacing-04 !important' }}>{t('detailProduct.productInfo', 'Informasi Produk')}</Heading>
                            <Flex gap={6}>
                                <Text css={{ minWidth: 100 }}>{t('detailProduct.category', 'Kategori')}</Text>
                                <Text css={{ fontWeight: '600', color: '$gray900' }}>{product.category}</Text>
                            </Flex>
                            <Flex gap={6}>
                                <Text css={{ minWidth: 100 }}>{t('detailProduct.weight', 'Berat')}</Text>
                                <Text css={{ fontWeight: '600', color: '$gray900' }}>
                                    {product.weight}
                                    {' '}
                                    g
                                </Text>
                            </Flex>
                            <ProductDescription description={product.desc} t={t} />
                        </Flex>
                        <Flex direction="column" gap={4}>
                            <Heading heading="sectionTitle" css={{ marginBottom: '$spacing-04 !important' }}>{t('detailProduct.deliveryInfo', 'Informasi Pengiriman')}</Heading>
                            <Flex gap={6}>
                                <Text css={{ minWidth: '150px' }}>{t('detailProduct.shipped', 'Dikirim dari')}</Text>
                                <Text>{getLocation(origin)}</Text>
                            </Flex>
                            <Flex gap={6}>
                                <Text css={{ minWidth: '150px' }}>{t('detailProduct.shippedTo', 'Tujuan Pengiriman')}</Text>
                                <Text>{getLocation(destination)}</Text>
                            </Flex>
                            <Flex gap={6}>
                                <Text css={{ minWidth: '150px' }}>{t('detailProduct.deliveryMethod', 'Metode Pengiriman')}</Text>
                                {/* eslint-disable-next-line no-nested-ternary */}
                                {(!selectedOutlet.latitude || !selectedOutlet.longitude) ? (
                                    <Text color="red">Titik lokasi belum diatur, silakan atur lokasi untuk melihat opsi pengiriman</Text>

                                ) : courier ? (
                                    <Box css={{ fontWeight: '600', color: '$gray900' }}>
                                        <Text>{courierText}</Text>
                                        <Text css={{ marginTop: 8, cursor: 'pointer', color: '$textGreen' }} onClick={() => modalCourierRef.current.showPopup()}>
                                            {t('detailProduct.otherCourier', 'Daftar kurir lainnya')}
                                        </Text>
                                    </Box>
                                ) : (
                                    <Text color="red">{t('detailProduct.noCourier', 'Kurir tidak tersedia')}</Text>
                                )}

                            </Flex>
                        </Flex>
                    </Flex>
                    {origin && (
                        <Box css={{
                            marginTop: 20,
                            borderRadius: 8,
                            padding: '1em',
                            border: '1px solid $bgBorder',
                        }}
                        >
                            <Flex gap={6} align="center">
                                <Box css={{
                                    width: 56,
                                    height: 56,
                                    position: 'relative',
                                    background: '$bgBorder',
                                    borderRadius: '100%',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}
                                >
                                    {origin.outlet_logo ? (
                                        <Image objectFit="cover" src={origin.outlet_logo} width={56} height={56} rounded />
                                    ) : (
                                        <OutletOutline />
                                    )}

                                </Box>
                                <Flex direction="column" gap={2}>
                                    <Heading as="h6" heading="sectionSubTitle">{origin.outlet_name}</Heading>
                                    <Flex align="center" gap={2}>
                                        <TimeOutline color={origin.is_opend_hour ? '#404545' : '#DF2A36'} />
                                        <Text css={{
                                            color: origin.is_opend_hour ? '$textPrimary' : '$textRed',
                                        }}
                                        >
                                            {origin.is_opend_hour ? t('detailProduct.outletOpen', 'Buka') : isClosedOutlet() ? 'Toko Sedang Tutup' : t('detailProduct.outletDayOff', 'Toko Sedang Libur')}
                                        </Text>
                                    </Flex>
                                </Flex>
                            </Flex>
                        </Box>
                    )}
                </Box>
            </Flex>
            <ModalSchedule ref={modalScheduleRef} t={t} schedules={origin.schedule} isOpenHour={origin.is_opend_hour} />
            <ModalCourier ref={modalCourierRef} t={t} courier={courier} />
            <ModalAddToCart ref={modalAddToCartRef} t={t} router={router} product={{ ...product, qtyOnCart }} />
        </Flex>
    );
};

const mapStateToProps = state => ({
    outletData: state.branch.list,
});

export default connect(mapStateToProps)(ProductDetail);
