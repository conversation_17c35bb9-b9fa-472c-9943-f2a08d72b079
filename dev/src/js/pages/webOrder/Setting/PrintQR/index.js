import React from 'react';
import PropTypes from 'prop-types';
import QRCode from 'qrcode.react';
import { catchError } from '../../../../utils/helper';
import CoreHOC from '../../../../core/CoreHOC';
import { getQr } from '../../../../data/emenu';

import majooLogo from '../../../../../assets/images/majoo.png';
import titleImage from '../../../../../assets/images/emenuTitle.png';
import scanImage from '../../../../../assets/images/emenuScan.png';
import IconIG from '../image/wo-instagram.svg';
import './style.less';

export default
@CoreHOC
class Index extends React.Component {
    static propTypes = {
        params: PropTypes.shape({
            id: PropTypes.string,
        }).isRequired,
        router: PropTypes.shape({
            push: PropTypes.func,
        }),
    }

    static defaultProps = {
        router: {
            push: () => {},
        },
    }

    constructor(props) {
        super(props);

        this.state = {
            qrValue: '',
            outletLogo: '',
            sosmetLink: [],
        };
    }

    componentDidMount() {
        const {
            params,
        } = this.props;

        this.handleGetQR(params.id);
    }

    showingNotification = (val) => {
        const { addNotification } = this.props;
        addNotification(val);
    }

    handleGetQR = (idOutlet) => {
        const payload = {
            id_outlet: idOutlet,
        };

        getQr(payload).then((res) => {
            if (!res.status) throw new Error('Gagal mengambil detail QR');
            this.setState({
                qrValue: `${res.data.outlet_weblink_uri}/produk`,
                outletLogo: res.data.cabang_logo_path,
                sosmetLink: res.data.social_media,
            });
        }).catch((e) => {
            this.showingNotification({
                title: 'Terjadi Kesalahan',
                message: catchError(e),
                level: 'error',
            });
        });
    }

    printAction = () => {
        window.print();
    }

    render() {
        const { qrValue, outletLogo, sosmetLink } = this.state;
        const instagramData = sosmetLink.filter(item => item.media_sosial_name === 'Instagram');

        return (
            <div className="row containerExportQRPage">
                <div className="col-md-4 col-md-offset-4 exportQRDiv">
                    {
                        outletLogo !== '' ? (
                            <div>
                                <img
                                    className="outletLogo"
                                    src={outletLogo}
                                    alt="Outlet Logo"
                                />
                            </div>
                        ) : ''
                    }
                    <div>
                        <img
                            className="titleqr"
                            src={titleImage}
                            alt="Outlet Logo"
                        />
                    </div>
                    <div>
                        <img
                            className="scanqr"
                            src={scanImage}
                            alt="Outlet Logo"
                        />
                    </div>
                    {
                        qrValue !== '' ? (
                            <div>
                                <div className="qrcodecont">
                                    <div className="qrcodecont-border">
                                        <QRCode
                                            size={160}
                                            value={qrValue}
                                            renderAs="svg"
                                        />
                                    </div>
                                </div>
                                <div className="qrlink">
                                    {qrValue}
                                </div>
                            </div>
                        ) : (
                            <div>
                                <h3 className="qrNotReady">
                                    QR Belum Tersedia
                                </h3>
                            </div>
                        )
                    }
                    <div className="qrsosmet">
                        {instagramData.length > 0 && (
                            <div className="qrsosmet-link">
                                <img className="qrsosmet-link-sosmetlogo" src={IconIG} alt="instagram icon" />
                                <div>{instagramData[0].media_sosial_link}</div>
                            </div>
                        )}
                    </div>
                    <div className="majoologocont">
                        Powered by
                        <img
                            className="majoologocont-logo"
                            src={majooLogo}
                            alt="Majoo Logo"
                        />
                    </div>
                    <div className="hide-print">
                        <button
                            className="btn btn-primary"
                            onClick={() => { this.printAction(); }}
                            style={{
                                marginTop: '15px',
                            }}
                            type="button"
                        >
                            Cetak QR Code ini
                        </button>
                    </div>
                </div>
            </div>
        );
    }
}
