/* eslint-disable react/prop-types */
import React, {
  memo, useContext, useState, useEffect,
} from 'react';
import { connect } from 'react-redux';
import propTypes from 'prop-types';
import _, { debounce } from 'lodash';
import moment from 'moment';
import {
  Paper,
  Heading,
  Separator,
  Box,
  InputSearchbox,
  Table,
  ToastContext,
  Skeleton,
} from '@majoo-ui/react';
import { useTranslation } from 'react-i18next';

import { getBalance, getBalanceHistory, getDashboardWebstore } from '../../../../../data/webOrder';
import { getAccountInfo } from '../../../../../data/users';
import { catchError } from '../../../../../utils/helper';
import { getOutlet } from '../../../../../data/outlets';
import { PERMISSIONS } from '../../../../../constants/permissions';
import { activeOutlets, INTEGRATION_STATUS } from '../../utils';
import { outletIds, sortingOutlets } from '../utils';
import { tableColumns, dataListType } from '../table';
import { isTablet as mobileQuery } from '../../../../../config/config';
import CoreHOC from '../../../../../core/CoreHOC';
import usePrevious from '../../../../../utils/usePrevious';
import FilterModal from './modal/FilterModal';
import { BannerText, FavoriteWrapper, TooltipGuidance } from '../../../../../components/retina';

const SaldoSummary = React.lazy(() => import('./SaldoSummary'));

const BalanceDetail = memo(({ ...props }) => {
  const { t, ready } = useTranslation(['Penjualan/Laporan/Settlement/webstore', 'translation'])
  const { showProgress, hideProgress, selectedBranch, calendar: defaultCalendar, assignCalendar, listBranch, menuPrivilege } = props;
  const isMobile = mobileQuery.matches || window.matchMedia('(max-height: 767px)').matches;
  const { addToast } = useContext(ToastContext);
  const [outletLists, setoutlets] = useState([]);
  const [dataTable, setdataTable] = useState([]);
  const [filterModalOpen, setFilterModalOpen] = useState(false);
  const [isAdmin, setisAdmin] = useState(false);
  const [resetPage, setresetPage] = useState(false);
  const [isFetchFilter, setisFetchFilter] = useState(false);
  const [totalBalance, settotalBalance] = useState(0);
  const [balanceOutlet, setbalanceOutlet] = useState(0);
  const [loadingTable, setloadingTable] = useState(false);
  const [balance, setbalance] = useState({
    totalDebit: 0,
    totalCredit: 0,
  });
  const [calendar, setcalendar] = useState({
    start: moment(defaultCalendar.start, 'DD-MM-YYYY').toDate(),
    end: moment(defaultCalendar.end, 'DD-MM-YYYY').toDate(),
  });
  const [outletName, setoutletName] = useState('');
  const [filter, setfilter] = useState({
    outlet: '',
    type: '',
    startDate: moment(defaultCalendar.start, 'DD-MM-YYYY').toDate(),
    endDate: moment(defaultCalendar.end, 'DD-MM-YYYY').toDate(),
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [tableMeta, setTableMeta] = useState({
    pageIndex: 1,
  });
  const [pageLimit, setPageLimit] = useState(10);

  const handleFetchBalanceByOutlet = async (outlet, date = null) => {
    const payload = {
      outlet_id: outlet,
      start_date: `${moment(calendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD')}`,
      end_date: `${moment(calendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD')}`,
      ...date && {
        start_date: `${moment(date.start, 'DD/MM/YYYY').format('YYYY-MM-DD')}`,
        end_date: `${moment(date.end, 'DD/MM/YYYY').format('YYYY-MM-DD')}`,
      },
      limit: 10,
      page: 1,
      show_type: 'listsummary',
    };

    try {
      const { total, data, meta } = await getBalanceHistory(payload);
      const balanceByOutlet = total;
      setTableMeta({
        totalData: meta.total,
      });
      setdataTable(data);
      setbalanceOutlet(balanceByOutlet);
      return data;
    } catch (error) {
      addToast({
        title: t('translation:toast.somethingWrong'),
        description: catchError(error),
        variant: 'failed',
      });
    }
  };

  const handleFetchBalance = async (outlets) => {
    try {
      const payload = {
        outlet_id: outlets,
      };
      const res = await getBalance(payload);
      settotalBalance(res.data);
    } catch (error) {
      addToast({
        title: t('translation:toast.failGotData'),
        description: catchError(error),
        variant: 'failed',
      });
    }
  };

  const handleFetchBalanceHistoryToday = async (outlets) => {
    try {
      const dateNow = moment().format('YYYY-MM-DD');
      const payload = {
        outlet_id: outlets,
        start_date: dateNow,
        end_date: dateNow,
        show_type: 'summary',
      };
      const res = await getBalanceHistory(payload);
      setbalance({
        totalDebit: res.total_debit,
        totalCredit: res.total_credit,
      });
    } catch (error) {
      addToast({
        title: t('translation:toast.failGotData'),
        description: catchError(error),
        variant: 'failed',
      });
    }
  };

  const fetchOutlets = async ({ mainOutlet }) => {
    showProgress();
    try {
      const res = await getOutlet({ show_weborder_number: 1 });
      if (!res.status) throw new Error(res.msg);
      const outlets = activeOutlets(res.data).map(x => ({ id: x.id_cabang, ...x }));

      let primaryOutlet = outlets.find(outlet => outlet.id_cabang === String(mainOutlet));

      if (!primaryOutlet) [primaryOutlet] = outlets;

      setfilter(prev => ({ ...prev, outlet: primaryOutlet.id_cabang }));
      setoutletName(primaryOutlet.cabang_name);
      setisFetchFilter(true);
      const listOuletsId = outletIds(outlets);
      await handleFetchBalance(String(listOuletsId));
      await handleFetchBalanceByOutlet(primaryOutlet.id_cabang);
      await handleFetchBalanceHistoryToday(String(listOuletsId));
    } catch (error) {
      addToast({
        title: t('translation:error.failedGetDataCustom', { data: 'outlet' }),
        description: catchError(error),
        variant: 'failed',
        dismissAfter: 3000,
      });
    } finally {
      hideProgress();
    }
  };

  const handleFetchWebstoreData = async () => {
    try {
      const res = await getDashboardWebstore();
      if (!res.status) throw new Error(res.msg);
      const { data_integration: dataIntegration } = res;
      if (!INTEGRATION_STATUS.INTEGRATED.includes(dataIntegration.status_integrasi)) {
        hideProgress();
        return;
      }
      await fetchOutlets({ mainOutlet: dataIntegration.id_outlet });
    } catch (error) {
      addToast({
        title: t('translation:toast.somethingWrong'),
        description: catchError(error),
        variant: 'failed',
        dismissAfter: 3000,
      });
    }
  };

  const onFetch = async (state) => {
    showProgress();
    const {
      page, pageSize, keyword, branch, filtering, pageIndex,
      sortAccessor, sortDirection
    } = state;

    let newPage = pageIndex || 0;

    if (resetPage) {
      newPage = 0;
      setresetPage(false);
    }

    let payload = {
      start_date: `${moment(calendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD')}`,
      end_date: `${moment(calendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD')}`,
      limit: state.pageLimit || pageSize || 10,
      page: newPage + 1,
      show_type: 'list',
    };

    if (pageSize) setPageLimit(pageSize);

    if (sortAccessor && sortDirection) payload = { ...payload, sort_by: sortAccessor, sort: sortDirection };

    if (filtering) {
      payload = {
        ...payload,
        outlet_id: filtering.outlet,
        start_date: `${moment(filtering.startDate, 'DD/MM/YYYY').format('YYYY-MM-DD')}`,
        end_date: `${moment(filtering.endDate, 'DD/MM/YYYY').format('YYYY-MM-DD')}`,
        transaction_type: filtering.type,
      };
    }

    if (branch) {
      payload = { ...payload, outlet_id: branch };
    }

    if (filter.outlet !== '') {
      payload = { ...payload, outlet_id: filter.outlet };
    } else {
      const outletId = outletIds(outletLists).join(',');
      payload = { ...payload, outlet_id: outletId }
    }

    if (filter.type !== '') {
      payload = { ...payload, transaction_type: filter.type };
    }

    if (filter.startDate !== '' && filter.endDate !== '') {
      payload = {
        ...payload,
        start_date: `${moment(filter.startDate, 'DD/MM/YYYY').format('YYYY-MM-DD')}`,
        end_date: `${moment(filter.endDate, 'DD/MM/YYYY').format('YYYY-MM-DD')}`,
      };
    }

    if (keyword) {
      payload = { ...payload, search: keyword };
    }

    let tableData = {
      data: [],
      pageCount: 0,
      err: null,
    };

    if (!payload.outlet_id) {
      hideProgress();
      return;
    };

    try {
      setloadingTable(true);
      const { data, meta: { total_page: pageCount, total: numData } } = await getBalanceHistory(payload);
      setTableMeta({
        totalData: numData,
      });
      tableData = { ...tableData, data, pageCount };
      if (isFetchFilter) setisFetchFilter(false);
    } catch (err) {
      addToast({
        title: t('translation:toast.failGotData'),
        description: catchError(err),
        variant: 'failed',
        dismissAfter: 3000,
      });
      tableData = { ...tableData, err };
    } finally {
      hideProgress();
      setloadingTable(false);
    }
    setdataTable(tableData.data);
    return tableData;
  };

  const handleFetchInfoAkun = async () => {
    try {
      const res = await getAccountInfo();
      if (!res.status) throw new Error(res.msg);
      if (res.data.hak_akses_id === PERMISSIONS.ADMIN) {
        setisAdmin(true);
      }
    } catch (error) {
      addToast({
        title: t('translation:toast.somethingWrong'),
        description: catchError(error),
        variant: 'failed',
        dismissAfter: 3000,
      });
    }
  };

  const openBalanceWithdrawal = () => {
    const { router, location } = props;
    router.push(`${location.pathname}/tarik`);
  };

  const onSubmitFilter = async (data) => {
    setFilterModalOpen(false);
    const filterOutletName = _.get(outletLists.find(x => x.id === data.outlet), 'name');
    if (filterOutletName) {
      setoutletName(filterOutletName);
    } else setoutletName('Semua Outlet');
    let filterIds = data.outlet;
    const { startDate, endDate } = data;
    try {
      showProgress();
      if (!filterIds) {
        filterIds = outletIds(outletLists).join(',');
      };
      await handleFetchBalanceByOutlet(filterIds, { start: startDate, end: endDate });
      setcalendar({ start: startDate, end: endDate });
      assignCalendar(moment(startDate).format('DD-MM-YYYY'), moment(endDate).format('DD-MM-YYYY'), null, null);
      setfilter(data);
      addToast({
        title: t('translation:toast.success'),
        description: t('toast.filterSuccess'),
        variant: 'success',
      });
    } catch (_) {
      addToast({
        title: t('translation:toast.error'),
        description: t('toast.filterFailed'),
        variant: 'failed',
      });
    } finally {
      hideProgress();
    }
  };

  const onSearch = debounce(async (keyword) => {
    await onFetch({ keyword, pageLimit });
    setSearchQuery(keyword);
  }, 700);

  useEffect(async () => {
    if (ready) {
      showProgress();
      window.scroll({
        top: 0,
        behavior: 'smooth',
      });
      Promise.all([handleFetchWebstoreData(), handleFetchInfoAkun()]).finally(hideProgress);
    }
  }, [ready]);

  useEffect(() => {
    const outlets = (listBranch || []).reduce((res, cur) => {
      if (cur.id_cabang !== '') {
        res.push({
          id: cur.id_cabang,
          ...cur,
        });
      }
      return res;
    }, []);
    setoutlets(sortingOutlets(outlets));
  }, [listBranch])

  useEffect(() => {
    onFetch({ pageLimit });
  }, [filter]);

  const prevBranch = usePrevious(selectedBranch);

  useEffect(() => {
    if (prevBranch !== undefined) {
      const filterOutletName = _.get(outletLists.find(x => Number(x.id) === Number(selectedBranch)), 'name');
      if (filterOutletName) {
        setoutletName(filterOutletName);
        setfilter({
          ...filter,
          outlet: selectedBranch,
        });
      } else {
        const branchLists = outletIds(outletLists).join(',');
        setoutletName('Semua Outlet');
        setfilter({
          ...filter,
          outlet: branchLists,
        });
      }
    };
  }, [selectedBranch]);

  return (
    <Paper responsive css={{ padding: 'unset', '@md': { mb: 20, padding: 20 } }}>
      <FilterModal
        isMobile={isMobile}
        t={t}
        onSubmit={onSubmitFilter}
        transactionOpt={dataListType(t).map(x => ({ ...x, name: x.label }))}
        filter={filter}
        open={filterModalOpen}
        onOpenChange={open => setFilterModalOpen(open)}
        outletsOpt={outletLists.map(x => ({ ...x, value: x.id })) || []}
      />
      <FavoriteWrapper>
        <Heading heading="pageTitle">{t('main.title', 'Toko Online')}</Heading>
        <TooltipGuidance />
      </FavoriteWrapper>
      <BannerText />
      <Separator css={{ mt: 19, mb: 28 }} />
      <React.Suspense fallback={<Skeleton variant="single" />}>
        <SaldoSummary
          t={t}
          onManageFilter={() => setFilterModalOpen(true)}
          openBalanceWithdrawal={openBalanceWithdrawal}
          isAdmin={isAdmin}
          balanceOutlet={balanceOutlet}
          calendar={calendar}
          totalBalance={totalBalance}
          balance={balance}
          outletName={outletName}
        />
      </React.Suspense>
      <Separator css={{ mt: 27, mb: 29 }} />
      <Heading heading={isMobile ? 'pageTitle' : 'sectionTitle'}>
        {t('main.heading.balanceHistory', 'Riwayat Saldo')}
      </Heading>
      <Box css={{ mt: '$compact', '@md': { mt: 32, width: 260 } }}>
        <InputSearchbox placeholder={t('placeholder.search', { ns: 'translation' }, 'Cari ...')} onChange={onSearch} />
      </Box>
      <Box css={{ my: '$spacing-05', '@md': { my: '$spacing-07' } }}>
        <Table
          id="settlement_webstore"
          css={{ '@sm': { padding: 'unset' } }}
          isLoading={loadingTable}
          columns={tableColumns(outletLists, t)}
          data={dataTable}
          totalData={tableMeta.totalData}
          searchQuery={searchQuery}
          fetchData={onFetch}
        />
      </Box>
    </Paper>
  );
});

BalanceDetail.propTypes = {
  showProgress: propTypes.func,
  hideProgress: propTypes.func,
  selectedBranch: propTypes.string,
  calendar: propTypes.shape({
    start: propTypes.string,
    end: propTypes.string,
  }),
  assignCalendar: propTypes.func,
  listBranch: propTypes.arrayOf(propTypes.shape({})),
  menuPrivilege: propTypes.shape({
    isCanCreate: propTypes.bool.isRequired,
    isCanDelete: propTypes.bool.isRequired,
    isCanUpdate: propTypes.bool.isRequired,
    isCanView: propTypes.bool.isRequired,
    isCanVoid: propTypes.bool.isRequired,
  }).isRequired,
};

BalanceDetail.defaultProps = {
  showProgress: () => { },
  hideProgress: () => { },
  selectedBranch: '',
  calendar: {
    start: moment().startOf('month').toDate(),
    end: moment().endOf('month').toDate(),
  },
  assignCalendar: () => {},
  listBranch: [],
};

const mapStateToProps = state => ({
  selectedBranch: state.branch.filter,
  listBranch: state.branch.list,
  menuPrivilege: state.layouts.detailPrivilege,
});

export default connect(mapStateToProps)(CoreHOC(BalanceDetail));
