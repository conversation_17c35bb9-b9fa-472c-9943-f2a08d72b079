import React, { useContext, useState } from 'react';
import PropTypes from 'prop-types';
import { ToastContext } from '@majoo-ui/react';
import { useTranslation } from 'react-i18next';
import {
    statusEnum,
    nameSpaceTranslation as ns,
} from '../settings/constant';

const setValueContext = (parentProps) => {
    const { addToast } = useContext(ToastContext);
    const [openConfirmation, setOpenConfirmation] = useState(false);
    const [modalConfirmation, setModalConfirmation] = useState({
        type: 'primary',
        title: '',
        description: '',
        buttons: {
            confirmButton: { label: '', action: () => {} },
            cancelButton: { label: '', action: () => {} },
        },
    });
    const [statusFilter, setStatusFilter] = useState(statusEnum.LANGSUNG);
    const { t, ready, i18n } = useTranslation([ns.cashFlowStatement, ns.statement, ns.translation, ns.exportReport], { useSuspense: false });
    const lang = i18n.language;

    const contextData = {
        ...parentProps,
        addToast,
        modalConfirmation,
        openConfirmation,
        statusFilter,
        ready,
        lang,
    };

    const handleSetCalendar = (val) => {
        parentProps.assignCalendar(val[0], val[1]);
    };

    const handleOpenConfirmationModal = ({
        type, title, description, buttons,
    }) => {
        setOpenConfirmation(true);
        setModalConfirmation({
            type,
            title,
            description,
            buttons,
        });
    };

    const handleCloseConfirmationModal = () => {
        setOpenConfirmation(false);
    };

    return {
        ...contextData,
        handleSetCalendar,
        handleOpenConfirmationModal,
        handleCloseConfirmationModal,
        setOpenConfirmation,
        setStatusFilter,
        t,
    };
};

export const ArusKasContext = React.createContext();

const ArusKasContextProvider = ({ children, parentProps }) => {
    const value = setValueContext(parentProps);

    return (
        <ArusKasContext.Provider {...{ value }}>
            {children}
        </ArusKasContext.Provider>
    );
};

ArusKasContextProvider.propTypes = {
    children: PropTypes.element.isRequired,
    parentProps: PropTypes.shape({}),
};

ArusKasContextProvider.defaultProps = {
    parentProps: {},
};

export default ArusKasContextProvider;
