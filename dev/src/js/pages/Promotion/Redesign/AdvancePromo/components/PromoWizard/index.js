import React, { useCallback, useContext } from 'react';
import {
  StepWizard,
  Paper,
  Text,
  Box,
  StepWizardContext,
  ToastContext,
} from '@majoo-ui/react';
import PropTypes from 'prop-types';
import PromoVariant from '../PromoVariant';
import PromoInformation from '../PromoInformation';
import PromoPeriod from '../PromoPeriod';
import PromoSettings from '../PromoSettings';
import PromoSettingsBasic from '../PromoSettingsBasic';
import PromoValue from '../PromoValue';
import AdditionalTerms from '../AdditionalTerms';
import { navContents, navContentsBasic } from '../../constant';
import Preview from '../PreviewPromo';
import PreviewBasic from '../PreviewPromo/components/Basic';

const Index = (props) => {
  const { setWizard, setStep, isMobile, completedSteps, isBasic } = props;

  return (
    <StepWizard
      onStepChange={(e) => {
        setStep(e);
      }}
      isMobile={isMobile}
      setSw={setWizard}
      initialStep={1}
      completedSteps={Object.values(completedSteps)}
      navContents={isBasic ? navContentsBasic : navContents}
    >
      {!isBasic && (
        <RenderContent
          Component={PromoVariant}
          title="Jenis Promo"
          required
          {...props}
        />
      )}
      <RenderContent
        Component={PromoInformation}
        title="Informasi Promo"
        required={false}
        {...props}
      />
      <RenderContent
        Component={PromoPeriod}
        title="Periode Promo"
        required
        {...props}
      />
      <RenderContent
        Component={isBasic ? PromoSettingsBasic : PromoSettings}
        title="Pengaturan Promo"
        required
        {...props}
      />
      {!isBasic && (
        <RenderContent
          Component={PromoValue}
          title="Nilai Promo"
          required
          {...props}
        />
      )}
      {!isBasic && (
        <RenderContent
          Component={AdditionalTerms}
          title="Ketentuan Tambahan"
          {...props}
        />
      )}
      {isBasic ? <PreviewBasic {...props} /> : <Preview {...props} />}
    </StepWizard>
  );
};

const RenderContent = ({ Component, ...rest }) => {
  const contextWizard = useContext(StepWizardContext);
  const toast = useContext(ToastContext);

  const renderAsVariant = useCallback(
    (variant) => {
      return rest.watch('rule_type') === variant;
    },
    [rest.watch('rule_type')],
  );

  // hide side menu wizard final step
  // TODO: remove comment toggle visibility pass parameter to 'hide' state
  // const isFinalStep = useMemo(
  //   () =>
  //     Object.values(rest.completedSteps)
  //       .slice(0, Object.values(rest.completedSteps).length - 1)
  //       .every((step) => step === true),
  //   [rest.completedSteps],
  // );

  // useEffect(() => {
  //   contextWizard.toogleVisibility(isFinalStep);
  // }, [isFinalStep]);

  const isPromoValueWithSpesificVariant =
    rest.title === 'Nilai Promo' &&
    (renderAsVariant('discount') ||
      renderAsVariant('delivery') ||
      !rest.watch('rule_type'));

  return (
    <Paper
      css={{
        padding: '26px 24px',
        boxShadow: '0px 1px 4px rgba(0, 0, 0, 0.04)',
        mr: 0,
      }}
    >
      <Box css={{ mb: isPromoValueWithSpesificVariant ? '6px' : '34px' }}>
        <Text
          css={{
            fontSize: '18px',
            fontWeight: 600,
          }}
          color="primary"
        >
          {rest.title}
          {rest.required && <span style={{ color: 'red' }}> *</span>}
        </Text>
      </Box>
      <Component
        renderAsVariant={renderAsVariant}
        {...contextWizard}
        {...toast}
        {...rest}
      />
    </Paper>
  );
};

RenderContent.propTypes = {
  Component: PropTypes.func.isRequired,
};

Index.propTypes = {
  watch: PropTypes.oneOfType([PropTypes.string, PropTypes.bool, PropTypes.func])
    .isRequired,
  setValue: PropTypes.func.isRequired,
  setWizard: PropTypes.func.isRequired,
  setStep: PropTypes.func.isRequired,
  completedSteps: PropTypes.shape({
    ...Array(6).fill(PropTypes.bool.isRequired),
  }).isRequired,
  register: PropTypes.func.isRequired,
  isMobile: PropTypes.bool.isRequired,
  isBasic: PropTypes.bool.isRequired,
  isEdit: PropTypes.bool.isRequired,
};

export default Index;
