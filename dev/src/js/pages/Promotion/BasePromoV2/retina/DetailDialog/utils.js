import moment from 'moment';
import { styled } from '@stitches/react';
import { Paper } from '@majoo-ui/react';
import { API_BASE } from '~/config/config';
import { getPassport } from '~/utils/passport';
import { ishasProperty } from '~/utils/helper';
import { ACTIVATION_TYPE } from '../../../Components/enum';

export const content = t => [
    {
        title: t('step.one.title', 'Informasi Promo'),
        withTolltip: false,
    },
    {
        title: t('step.two.title', 'Ketentuan Promo'),
        withTolltip: false,
    },
];

export const UPLOAD_IMAGE_ADDRESS = `${API_BASE}upload/do_upload`;
export const TOKEN_UPLOAD = getPassport();

export const PROMO_TERMS = {
    BUNDLING: '1',
    UNIT: '2',
};

export const PROMO_BASED_ON = {
    PURCHASE: 'payment',
    QUANTITY: 'quantity',
};

export const BONUS_TYPE = {
    PERCENT: 'percent',
    NOMINAL: 'nominal',
    PRODUCT: 'product',
    STRIKETHROUGH_PRICE: 'strike_through_price',
};

export const promoActivationTypes = t => [
    { value: ACTIVATION_TYPE.AUTOMATIC, label: t('sidebar.activate.auto'), desc: t('sidebar.activate.autoDesc') },
    { value: ACTIVATION_TYPE.MANUAL, label: t('sidebar.activate.manual'), desc: t('sidebar.activate.manualDesc') },
];

export const createStaticDataPromoBasedOn = t => [
    { value: PROMO_BASED_ON.PURCHASE, label: t('sidebar.basedOn.minimumBuy', { context: '(Rp)' }) },
    { value: PROMO_BASED_ON.QUANTITY, label: t('sidebar.basedOn.minimumQty') },
];

export const createStaticDataBonusType = (t) => ([
    { value: BONUS_TYPE.PERCENT, label: t('sidebar.bonusType.discount', { suffix: '%' }) },
    { value: BONUS_TYPE.NOMINAL, label: t('sidebar.bonusType.discount', { suffix: 'Rp' }) },
    { value: BONUS_TYPE.PRODUCT, label: t('sidebar.bonusType.bonusProduct') },
]);

export const daysList = t => ([
    {
        id: '1',
        title: t('days.monday', { ns: 'translation' }),
        keyDays: 'Mon',
        disabled: false,
    },
    {
        id: '2',
        title: t('days.tuesday', { ns: 'translation' }),
        keyDays: 'Tue',
        disabled: false,
    },
    {
        id: '3',
        title: t('days.wednesday', { ns: 'translation' }),
        keyDays: 'Wed',
        disabled: false,
    },
    {
        id: '4',
        title: t('days.thusday', { ns: 'translation' }),
        keyDays: 'Thu',
        disabled: false,
    },
    {
        id: '5',
        title: t('days.friday', { ns: 'translation' }),
        keyDays: 'Fri',
        disabled: false,
    },
    {
        id: '6',
        title: t('days.saturday', { ns: 'translation' }),
        keyDays: 'Sat',
        disabled: false,
    },
    {
        id: '0',
        title: t('days.sunday', { ns: 'translation' }),
        keyDays: 'Sun',
        disabled: false,
    },
]);

export const ResponsivePaper = styled(Paper, {
    display: 'flex',
    flexDirection: 'column',
    margin: 0,
    backgroundColor: '$white',
    width: '100% !important',
    padding: '26px 24px 24px 24px',
    borderRadius: 8,
    gap: '$cozy',
    boxShadow: 'rgb(0 0 0 / 8%) 0px 2px 12px',
    '@sm': {
        padding: '24px 16px',
        borderRadius: 0,
        boxShadow: 'none',
        gap: '$compact',
    },
});

export const createProductErrorMessage = (errors) => {
    if (!errors) return '';
    if (!ishasProperty(errors, 'criteriaProducts')) return '';
    if (!Array.isArray(errors.criteriaProducts)) {
        return errors.criteriaProducts.message;
    }
    const mostkeys = errors.criteriaProducts.sort(
        (a, b) => Object.keys(b).length - Object.keys(a).length,
    )[0];
    const messages = Object.values(mostkeys).map(({ message }) => message);
    return messages.join('\n');
};

export const getDiffDateToday = value => moment(moment(value).format('YYYYMMDD')).diff(moment().format('YYYYMMDD'), 'days');