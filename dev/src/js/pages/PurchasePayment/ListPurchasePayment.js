import React, { useEffect, useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { debounce } from 'lodash';
import { connect } from 'react-redux';
import queryString from 'query-string';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Box, Paper, Separator, Table, AlertDialog } from '@majoo-ui/react';
import { Trans, useTranslation } from 'react-i18next';
import { tableColumn, startMonth, endMonth, initForm } from './settings/utils';
import { catchError } from '../../utils/helper';
import { useMediaQuery } from '../../utils/useMediaQuery';
import * as OutletApi from '../../data/outlets';
import * as accountingApi from '../../data/accounting';
import * as inventoryApi from '../../data/inventories';
import TitleSection from './components/retina/titleSection';
import FilterSection from './components/retina/filterSection';
import PurchasePaymentForm from './components/retina/form';
import CoreHOC from '../../core/CoreHOC';
import { TRANSACTION_TYPE } from './settings/enums';
import { BannerText } from '../../components/retina';
import ProcessedPaymentBanner from './components/ProcessedPaymentBanner';

const PurchasePaymentList = ({
    showProgress,
    hideProgress,
    addNotification,
    filterBranch,
    isShowProgress,
    calendar,
    assignCalendar,
    menuPrivilege,
    router,
}) => {
    const isMobile = useMediaQuery('(max-width: 1024px)');
    const { t } = useTranslation(['Penjualan/Inventori/invoicePayment', 'translation']);
    const [calendarDate, setCalendarDate] = useState([
        moment(calendar.start, 'DD-MM-YYYY').toDate(),
        moment(calendar.end, 'DD-MM-YYYY').toDate(),
    ]);
    const [typeForm, setTypeForm] = useState('add');
    const [table, setTable] = useState({
        tableData: [],
        totalData: 0,
        pageIndex: 0,
        search: '',
    });
    const [options, setOptions] = useState({
        outletOptions: [],
        bankOptions: [],
        supplierOptions: [],
        transactionTypeOptions: t => [
            { label: t('options.purchasing', 'Pemesanan Stok'), value: TRANSACTION_TYPE.PURCHASE },
            {
                label: t('options.manualStockEntry', 'Tanpa Nomor Referensi'),
                value: TRANSACTION_TYPE.MANUAL_STOCK_ENTRY,
            },
        ],
    });
    const [modal, setModal] = useState({
        isDownloadModal: false,
        isSaveModal: false,
        isOpenForm: false,
    });
    const [downloadPayload, setDownloadPayload] = useState({
        sortTable: '',
        orderTable: '',
        searchTable: '',
    });
    const [formData, setFormData] = useState({
        outlet: '',
        paymentType: '',
        transactionType: TRANSACTION_TYPE.PURCHASE,
        transactionDate: moment().toDate(),
        supplier: '',
        purchasePaymentNo: '',
        tablePayment: [],
        evidence: [],
    });
    const [pageLimit, setPageLimit] = useState(10);
    const [activeRowData, setActiveRowData] = useState(null);
    const [hasProcessedPayment, setHasProcessedPayment] = useState(false);

    const schema = yup.object({
        outlet: yup.string().required(t('form.validation.outlet', '*Mohon pilih Outlet')),
        paymentType: yup.string().required(t('form.validation.paymentType', '*Mohon pilih Jenis Pembayaran')),
        transactionType: yup.string(),
        supplier: yup.string().when('transactionType', {
            is: transactionType => transactionType === TRANSACTION_TYPE.PURCHASE,
            then: yup.string().required(t('form.validation.supplier', '*Mohon pilih Pemasok')),
            otherwise: yup.string(),
        }),
    });

    const methods = useForm({
        resolver: yupResolver(schema),
        defaultValues: { ...initForm, transactionDate: moment().toDate() },
    });

    const fetchOutletData = async () => {
        const payload = { is_cms: 1 };
        const outlets = await OutletApi.getOutletV3(payload);
        const listOutlet = [];
        outlets.data.map(item =>
            listOutlet.push({
                value: item.id_cabang,
                name: item.cabang_name,
            }),
        );
        return listOutlet;
    };

    const fetchBankList = async () => {
        const payload = {
            akunting_is_active: 1,
            akunting_cabang_id: filterBranch,
            akunting_type: 2,
            akunting_is_kas: 1,
        };
        const bank = await accountingApi.getAccountListV2(payload);
        const listBank = [];
        bank.data.map(item =>
            listBank.push({
                value: item.akunting_no,
                name: `${item.akunting_kode} - ${item.akunting_name}`,
            }),
        );
        return listBank;
    };

    const fetchSupplierData = async () => {
        const suppliers = await inventoryApi.getSupplierListV1({ type: 1, bypass_pagination: 1 });
        const listSupplier = [];
        suppliers.data.map(item =>
            listSupplier.push({
                value: item.id_supplier,
                name: item.supplier_name,
            }),
        );
        return listSupplier;
    };

    const fetchDataOption = async () => {
        showProgress();
        try {
            const outletOpt = await fetchOutletData();
            const bankOpt = await fetchBankList();
            const supplierOpt = await fetchSupplierData();
            setOptions(option => ({
                ...option,
                outletOptions: outletOpt,
                bankOptions: bankOpt,
                supplierOptions: supplierOpt,
            }));
        } catch (error) {
            addNotification({
                title: t('toast.error', { ns: 'translation' }),
                message: catchError(error),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    const fetchData = useCallback(async state => {
        const { pageSize, pageIndex, pages, keyword, sortAccessor, sortDirection, pageLimit } = state;

        let newPage = pageIndex || 0;
        if (table.totalData === 0) {
            newPage = 0;
        }

        const payload = {
            start_date: moment(calendarDate[0]).format('YYYY-MM-DD'),
            end_date: moment(calendarDate[1]).format('YYYY-MM-DD'),
            limit: pageLimit || pageSize || 10,
            page: newPage + 1,
        };

        if (pageSize) setPageLimit(pageSize);
        // get filter params
        if (keyword) {
            Object.assign(payload, { keyword });
        }
        if (sortAccessor && sortDirection) {
            Object.assign(payload, {
                order_by: sortAccessor,
                sort: sortDirection,
            });
        }

        if (!!filterBranch && !Number.isNaN(filterBranch)) {
            Object.assign(payload, { outlet_id: filterBranch });
        }

        showProgress();
        try {
            const response = await accountingApi.getPembayaranMainTable(payload);
            if (response.data) {
                setTable(current => ({
                    ...current,
                    tableData: response.data,
                    totalData: response.meta.total_data,
                }));
            }
            setDownloadPayload(current => ({
                ...current,
                sortTable: sortDirection,
                orderTable: sortAccessor,
                searchTable: keyword,
            }));
        } catch (error) {
            addNotification({
                title: t('toast.error', { ns: 'translation' }),
                message: catchError(error),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    });

    const fetchDownloadReport = async () => {
        const payload = {
            start_date: moment(calendarDate[0]).format('YYYY-MM-DD'),
            end_date: moment(calendarDate[1]).format('YYYY-MM-DD'),
            ...(downloadPayload.sortTable && {
                sort: downloadPayload.sortTable,
            }),
            ...(downloadPayload.orderTable && {
                order: downloadPayload.orderTable,
            }),
            ...(downloadPayload.searchTable && {
                search: downloadPayload.searchTable,
            }),
        };
        showProgress();
        try {
            const response = await accountingApi.downloadPurchaseReport(payload);
            if (!response.data) throw Error(response.msg);
            addNotification({
                title: t('toast.success', { ns: 'translation' }),
                message: (
                    <Box css={{ maxWidth: '300px' }}>
                        <Trans t={t} i18nKey="toast.export.success">
                            Laporan <b>Pembayaran Pembelian</b> berhasil diekspor
                        </Trans>
                    </Box>
                ),
                level: 'success',
            });
            window.location = response.data;
        } catch (error) {
            addNotification({
                title: t('toast.error', { ns: 'translation' }),
                message: catchError(error),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    const fetchSubmitForm = async data => {
        let newPaymentList = [];
        if (data.tablePayment.length > 0) {
            newPaymentList = data.tablePayment.map(({ origin, ...item }) => {
                const result = Object.assign({}, item, {
                    potongan: parseFloat(item.potongan),
                    pembayaran: parseFloat(item.pembayaran),
                    ...origin && { tagihan: origin.tagihan },
                });
                return result;
            });
        }

        const findBill = newPaymentList.filter(item => Number(item.pembayaran) !== 0);

        const payload = {
            outlet_id: Number(data.outlet),
            supplier_id: data.supplier,
            bayar_dari: data.paymentType,
            jenis_transaksi: Number(data.transactionType),
            tgl_transaksi: moment(data.transactionDate).format('YYYY-MM-DD HH:mm:ss'),
            no_pembayaran: data.transactionNumber,
            list_pembelian: findBill, // newPaymentList,
            ...(data.purchasePaymentNo && {
                kode_pembayaran: data.purchasePaymentNo,
            }),
            ...((data.evidence && data.evidence.length > 0) && {
                evidence: data.evidence,
            }),
        };

        showProgress();
        try {
            if (findBill.length > 20) {
                addNotification({
                    title: t('toast.error', { ns: 'translation' }),
                    message: t('toast.save.errorMaxPayment'),
                    level: 'error',
                });
            } else if (!data.tableTotalPayment || data.tableTotalPayment.pembayaran === 0) {
                addNotification({
                    title: t('toast.error', { ns: 'translation' }),
                    message: (
                        <Trans t={t} i18nKey="toast.save.errorPayment">
                            <b>Pembayaran Pembelian</b> tidak ada yang dibayarkan
                        </Trans>
                    ),
                    level: 'error',
                });
            } else {
                const response = await accountingApi.createPurchase(payload);
                if ((!response.data || !response.msg) && response.msg !== 'success') throw Error(response.msg);
                addNotification({
                    title: t('toast.success', { ns: 'translation' }),
                    message: (
                        <Box css={{ maxWidth: '300px' }}>
                            <Trans t={t} i18nKey="toast.save.success" />
                        </Box>
                    ),
                    level: 'success',
                });
            }
        } catch (error) {
            addNotification({
                title: t('toast.error', { ns: 'translation' }),
                message: catchError(error),
                level: 'error',
            });
        } finally {
            hideProgress();
            setFormData({ ...initForm, transactionDate: moment().toDate() });
            fetchData({ pageLimit });
        }
    };

    const fetchDetailData = async rowData => {
        const payload = {
            no_pembayaran: rowData.no_pembayaran,
        };
        showProgress();
        try {
            const response = await accountingApi.getDetailPurchase(payload);

            const findOutlet = options.outletOptions.find(
                item => item.name.toLowerCase() === rowData.outlet.toLowerCase(),
            );
            const findPaymentType = options.bankOptions.find(item =>
                item.name.toLowerCase().includes(rowData.bayar_dari.toLowerCase()),
            );
            const findSupplier = options.supplierOptions.find(
                item => item.name.toLowerCase() === rowData.supplier.toLowerCase(),
            );

            let detailFormData = {};

            if (rowData && response && response.data && Array.isArray(response.data)) {
                const responseData = response.data.find(item => item.no_faktur === rowData.no_faktur);
                if (responseData) {
                    setActiveRowData(current => ({
                        ...current,
                        ...responseData,
                    }));
                    detailFormData = {
                        evidence: responseData.evidence || [],
                    }
                }
            }

            detailFormData = {
                ...detailFormData,
                tablePayment: response.data,
                outlet: findOutlet ? findOutlet.value : '',
                paymentType: findPaymentType ? findPaymentType.value : '',
                transactionType: String(rowData.tipe_transaksi),
                transactionDate: moment(rowData.tgl_transaksi).toDate(),
                supplier: findSupplier ? findSupplier.value : '',
                purchasePaymentNo: rowData.kode_pembayaran,
                paymentNumber: rowData.no_pembayaran,
            };

            Object.entries(detailFormData).forEach(([key, value]) => methods.setValue(key, value));
            setFormData(detailFormData);

            setModal(isModal => ({ ...isModal, isOpenForm: true }));
        } catch (error) {
            addNotification({
                title: t('toast.error', { ns: 'translation' }),
                message: catchError(error),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    const fetchVoid = async () => {
        showProgress();

        const payload = {
            id_outlet: +formData.outlet,
            no_pembayaran: activeRowData.no_pembayaran,
        };

        try {
            await inventoryApi.voidInvoicePayment(payload);

            addNotification({
                title: t('toast.success', { ns: 'translation' }),
                description: (
                    <Trans t={t} i18nKey="toast.void.success">
                        Void Pembayaran Faktur {{ noInvoice: formData.purchasePaymentNo }} berhasil dilakukan
                    </Trans>
                ),
                variant: 'success',
                position: 'top-right',
            });

            setModal(isModal => ({
                ...isModal,
                isOpenForm: false,
                isSaveModal: false,
            }));
        } catch (error) {
            addNotification({
                title: t('toast.error', { ns: 'translation' }),
                message: catchError(error),
                level: 'error',
            });
        } finally {
            hideProgress();
            fetchData({ pageLimit });
        }
    };

    const fetchProcessedPaymentStatus = async () => {
        showProgress();
        try {
            const response = await accountingApi.getProcessedPaymentStatus();
            if (response.data) {
                setHasProcessedPayment(response.data.status);
            } else {
                setHasProcessedPayment(false);
            }
        } catch (error) {
            addNotification({
                title: t('toast.error', { ns: 'translation' }),
                message: catchError(error),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    const handleConfirmVoid = () => {
        fetchVoid(formData);
    };

    const onChangeRowMenu = rowData => {
        fetchDetailData(rowData);
        setActiveRowData(rowData);
        setTypeForm('view');
    };

    const handleDateChange = value => {
        setCalendarDate(value);
        assignCalendar(moment(value[0]).format('DD-MM-YYYY'), moment(value[1]).format('DD-MM-YYYY'), null, null);
    };

    const handleTableSearch = debounce(async keyword => {
        setTable(current => ({ ...current, search: keyword }));
        await fetchData({ keyword, pageLimit });
    }, 500);

    const handleSubmit = data => {
        setModal(isModal => ({
            ...isModal,
            isOpenForm: false,
            isSaveModal: false,
        }));
        fetchSubmitForm(data);
    };

    useEffect(() => {
        fetchDataOption();
    }, [filterBranch]);

    useEffect(() => {
        fetchData({ pageLimit });
    }, [calendarDate, filterBranch]);

    useEffect(() => {
        const params = queryString.parse(window.location.search);
        if (Object.keys(params).length > 0) {
            const initFormData = {
                ...initForm,
                transactionDate: moment().toDate(),
                outlet: params.outlet_id,
                transactionType: params.source,
                supplier: params.supplier_id || 0,
                selectedInvoiceNo: params.invoice_no,
            };

            Object.entries(initFormData).forEach(([key, value]) => methods.setValue(key, value));
            setFormData(initFormData);

            setTypeForm('add');
            setModal(isModal => ({ ...isModal, isOpenForm: true }));
        }
        fetchProcessedPaymentStatus();
    }, []);

    return (
        <Paper responsive css={{ padding: 0, '@md': { padding: 20 } }}>
            <TitleSection
                isCanCreate={
                    menuPrivilege.isCanCreate || (menuPrivilege.isCanView && !menuPrivilege.isWhitelistMerchant)
                }
                calendarDate={calendarDate}
                showDownloadModal={() => setModal(isModal => ({ ...isModal, isDownloadModal: true }))}
                showForm={() => {
                    setFormData({ ...initForm, transactionDate: moment().toDate() });
                    setActiveRowData(null);
                    setTypeForm('add');
                    setModal(isModal => ({ ...isModal, isOpenForm: true }));
                }}
                isMobile={isMobile}
                t={t}
                hasProcessedPayment={hasProcessedPayment}
            />
            <BannerText />
            {hasProcessedPayment && (
                <ProcessedPaymentBanner
                    onCheckStatus={fetchProcessedPaymentStatus}
                    buttonLabel={t('header.add', 'Tambah Pembayaran Faktur')}
                    t={t}
                />
            )}
            <Separator responsive css={{ margin: '$spacing-05 0' }} />
            <FilterSection
                isCanCreate={
                    menuPrivilege.isCanCreate || (menuPrivilege.isCanView && !menuPrivilege.isWhitelistMerchant)
                }
                handleTableSearch={handleTableSearch}
                calendarDate={calendarDate}
                handleDateChange={handleDateChange}
                showDownloadModal={() => setModal(isModal => ({ ...isModal, isDownloadModal: true }))}
                showForm={() => {
                    setFormData({ ...initForm, transactionDate: moment().toDate() });
                    setTypeForm('add');
                    setModal(isModal => ({ ...isModal, isOpenForm: true }));
                }}
                isMobile={isMobile}
                t={t}
            />
            <Separator css={{ margin: '$spacing-05 0' }} />
            <Table
                keyId="id"
                columns={tableColumn(rowData => onChangeRowMenu(rowData), t)}
                data={table.tableData}
                totalData={table.totalData}
                fetchData={fetchData}
                onRowClick={({ original }) => onChangeRowMenu(original)}
                isLoading={isShowProgress}
                searchQuery={table.search}
                css={{ padding: 0, '@md': { padding: 20 } }}
            />
            {modal.isOpenForm && (
                <PurchasePaymentForm
                    isOpen={modal.isOpenForm}
                    onOpenChange={() => {
                        setModal(isModal => ({ ...isModal, isOpenForm: false }));
                        setTypeForm('add');
                        setFormData({ ...initForm, transactionDate: moment().toDate() });
                        router.push('/pengeluaran/daftar-pembayaran-pembelian');
                    }}
                    methods={methods}
                    typeForm={typeForm}
                    formData={formData}
                    setFormData={(name, value) => setFormData(current => ({ ...current, [name]: value }))}
                    options={options}
                    modalForm={modal}
                    handleSaveData={handleSubmit}
                    isMobile={isMobile}
                    handleConfirmVoid={handleConfirmVoid}
                    activeRowData={activeRowData}
                    parentProps={{
                        filterBranch,
                        showProgress,
                        hideProgress,
                        addNotification,
                    }}
                    t={t}
                    hasProcessedPayment={hasProcessedPayment}
                    fetchProcessedPaymentStatus={fetchProcessedPaymentStatus}
                />
            )}
            <AlertDialog
                isMobile={isMobile}
                open={modal.isDownloadModal}
                title={t('label.exportReport', { ns: 'translation' })}
                description={
                    <Trans t={t} i18nKey="modal.export.description">
                        Laporan <b>Pembayaran Faktur</b> pada periode{' '}
                        <b>{{ startDate: moment(calendarDate[0]).format('DD MMMM YYYY') }}</b> sampai{' '}
                        <b>{{ endDate: moment(calendarDate[1]).format('DD MMMM YYYY') }}</b> akan diekspor dan disimpan
                        di perangkat yang digunakan. Lanjutkan?
                    </Trans>
                }
                labelConfirm={t('label.continue', { ns: 'translation' })}
                labelCancel={t('label.cancel', { ns: 'translation' })}
                onCancel={() => setModal(isModal => ({ ...isModal, isDownloadModal: false }))}
                onConfirm={() => {
                    setModal(isModal => ({ ...isModal, isDownloadModal: false }));
                    fetchDownloadReport();
                }}
            />
        </Paper>
    );
};

PurchasePaymentList.propTypes = {
    filterBranch: PropTypes.string,
    showProgress: PropTypes.func,
    hideProgress: PropTypes.func,
    addNotification: PropTypes.func.isRequired,
    isShowProgress: PropTypes.bool.isRequired,
    calendar: PropTypes.shape({
        start: PropTypes.string,
        end: PropTypes.string,
    }),
    assignCalendar: PropTypes.func,
    router: PropTypes.shape({
        push: PropTypes.func,
    }),
    menuPrivilege: PropTypes.shape({
        isCanCreate: PropTypes.bool.isRequired,
        isCanDelete: PropTypes.bool.isRequired,
        isCanUpdate: PropTypes.bool.isRequired,
        isCanView: PropTypes.bool.isRequired,
        isCanVoid: PropTypes.bool.isRequired,
        isWhitelistMerchant: PropTypes.bool.isRequired,
    }).isRequired,
};

PurchasePaymentList.defaultProps = {
    showProgress: () => {},
    hideProgress: () => {},
    filterBranch: '',
    calendar: {
        start: startMonth,
        end: endMonth,
    },
    assignCalendar: () => {},
    router: {
        push: () => {},
    },
};

const mapStateToProps = state => ({
    menuPrivilege: { ...state.layouts.detailPrivilege, isWhitelistMerchant: state.layouts.isWhitelistMerchant },
});

export default connect(mapStateToProps)(CoreHOC(PurchasePaymentList));
