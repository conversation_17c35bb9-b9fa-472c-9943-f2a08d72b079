import React, {
    useEffect,
    useState,
    useContext,
    useMemo,
} from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import {
    Flex,
    Paper,
    Box,
    Text,
    Heading,
    Separator,
    ToastContext,
    InputDatePicker,
    InputSelectTag,
    Pagination,
    Table,
    AlertDialog,
} from '@majoo-ui/react';
import moment from 'moment';
import { Trans, useTranslation } from 'react-i18next';
import { isEqual } from 'lodash';
import { toMoment } from '../CashBook/TransferList/utils/helper';
import { getListAkunReportNonDatamart } from '../../data/bukuBesar';
import { catchError, resetDateRangeHelper } from '../../utils/helper';
import CoreHOC from '../../core/CoreHOC';
import InfiniteScrollTable from './components/InfiniteScrollTable';
import { tableMetaBukuBesar } from './utils/utils';
import { getOutletData } from '../../data/outlets/selectors';
import { FavoriteWrapper, TooltipGuidance, BannerText } from '../../components/retina';
import { requestExportReportV1 } from '../../data/accounting';
import { useMediaQuery } from '../../utils/useMediaQuery';
import ExportReport from '../../components/retina/export/ExportReport';

const BukuBesarListV2 = (props) => {
    const {
        showProgress,
        hideProgress,
        calendar,
        filterBranch,
        assignCalendar,
        outletList,
        addNotification,
    } = props;

    const { t, i18n, ready } = useTranslation(['Keuangan/generalLedgerReport', 'Keuangan/exportReport', 'translation'], { useSuspense: false });
    const lang = i18n.language;

    const menuName = lang === 'en' ? 'General Ledger' : 'Buku Besar';
    const [loading, setLoading] = useState(true);
    const [optionAccount, setOptionAccount] = useState([]);
    const [valueOptionAccount, setValueOptionAccount] = useState([]);
    const [totalData, setTotalData] = useState(0);
    const [tableData, setTableData] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [limit, setLimit] = useState(2);
    const [prevFilter, setPrevFilter] = useState({});
    const [dialogInfoExport, setDialogInfoExport] = useState({
        isDialog: false,
        state: {
            title: '',
            description: '',
            btnCloseOnly: false,
        },
    });

    const outlet = useMemo(() => outletList.find(data => Number(data.id_cabang) === Number(filterBranch), [outletList, filterBranch]));

    const isMobile = useMediaQuery('(max-width: 767px)');
    const { addToast } = useContext(ToastContext);

    const handlePageChange = async (incomingPage = 1, incomingLimit = limit) => {
        try {
            setLoading(true);
            const payload = {
                outlet_id: filterBranch || 0,
                page: incomingLimit !== limit ? 1 : incomingPage,
                start_date: moment(calendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                end_date: moment(calendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                account_no_list: valueOptionAccount.length === 0 ? '' : valueOptionAccount.join(','),
                per_page: incomingLimit,
            };
            const { data: dataPage, meta: { page, total } } = await getListAkunReportNonDatamart(payload);
            setTableData(dataPage);
            setTotalData(total);
            setLimit(incomingLimit);
            setCurrentPage(page);
        } catch (error) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            setLoading(false);
        }
    };

    const fetchAccountList = async () => {
        try {
            setLoading(true);
            const payload = {
                start_date: moment(calendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                end_date: moment(calendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                is_all: true,
                ...(filterBranch && { outlet_id: filterBranch }),
            };
            const response = await getListAkunReportNonDatamart(payload);
            if (response.data.length > 0) {
                const listAccountOpt = response.data.map(item => ({
                    id: item.account.no,
                    name: `(${item.account.code}) ${item.account.name}`,
                }));
                const selectAll = response.data.map(item => item.account.no);
                setOptionAccount(listAccountOpt);
                setValueOptionAccount(selectAll);
            }
        } catch (error) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            setLoading(false);
        }
    };

    const checkMoreThanThreeMonths = value => resetDateRangeHelper(value[0], value[1], 90);

    const handleChangeDateRange = (value) => {
        const { newStartDate, newEndDate, isForceReset } = checkMoreThanThreeMonths(value);
        setTableData([]);
        if (isForceReset) {
            addToast({
                title: t('toast.errorMaxDate', 'Terjadi Kesalahan!', { ns: 'translation' }),
                description: t('toast.errorMaxDateRange', 'Maksimum rentang waktu yang dapat dipilih: 3 bulan', { ns: 'translation' }),
                variant: 'pending',
                id: 'warning-date',
                preventDuplicate: true,
            });
            assignCalendar(newStartDate, newEndDate);
        } else {
            assignCalendar(value[0], value[1]);
        }
    };

    const downloadLaporan = async (type = 'xlsx') => {
        setLoading(true);

        const payload = {
            report_name: 'accounting_ledger',
            report_format: type,
            parameters: {
                accounting_date_min: moment(calendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                accounting_date_max: moment(calendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                ...(valueOptionAccount.length && ({ account_no_list: valueOptionAccount })),
                ...(filterBranch && ({ outlet_id: Number(filterBranch) || 0 })),
            },
        };

        try {
            await requestExportReportV1(payload);

            setDialogInfoExport({
                isDialog: true,
                state: {
                    title: (
                        <Trans
                            t={t}
                            i18nKey="dialog.labelExport"
                            defaults="Ekspor Laporan Buku Besar"
                            values={{ menu: menuName }}
                        />
                    ),
                    description: (
                        <Trans
                            t={t}
                            i18nKey="dialog.descriptionExport"
                            defaults="Mohon menunggu, sistem sedang memproses Laporan <strong>Buku Besar</strong>. Anda akan menerima pemberitahuan notifikasi dan email saat data siap untuk diunduh."
                            components={{ strong: <b /> }}
                            values={{ menu: menuName }}
                        />
                    ),
                    btnCloseOnly: false,
                },
            });
        } catch (error) {
            if (error.cause && error.cause.status && error.cause.status.code) {
                if (Number(error.cause.status.code) === 422002) {
                    setDialogInfoExport({
                        isDialog: true,
                        state: {
                            title: (
                                <Trans
                                    t={t}
                                    i18nKey="label.exportReport"
                                    defaults="Ekspor Laporan"
                                />
                            ),
                            description: error.cause.status.message,
                            btnCloseOnly: true,
                        },
                    });
                    return;
                }
            }

            const message = catchError(error);
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: message,
                variant: 'failed',
                position: 'top-right',
            });
        } finally {
            setLoading(false);
        }
    };

    const responsivePaper = {
        '@sm': {
            padding: 0,
            backgroundColor: 'transparent',
            boxShadow: 'none',
        },
        '@md': {
            padding: '$spacing-05 0px',
            backgroundColor: '$white',
            boxShadow: '0px 2px 12px #********',
        },
    };

    useEffect(() => {
        if (loading) {
            showProgress();
        } else {
            hideProgress();
        }
    }, [loading]);

    useEffect(() => {
        handlePageChange();
    }, [filterBranch, calendar, valueOptionAccount]);

    useEffect(() => {
        const filter = { filterBranch, calendar };
        const { isForceReset } = checkMoreThanThreeMonths([calendar.start, calendar.end]);
        fetchAccountList();
        if (isForceReset) {
            handleChangeDateRange([calendar.start, calendar.end]);
        } else if (!isEqual(prevFilter, filter) && ready) {
            setPrevFilter({ filterBranch, calendar });
        }
    }, [filterBranch, calendar, ready]);

    return (
        <Flex
            direction="column"
            css={{ gap: '$comfortable', '@md': { marginBottom: '28px' } }}
        >
            <Paper css={responsivePaper}>
                <Box css={{ '@sm': { height: 'auto' }, '@md': { height: '100%' } }}>
                    <Flex justify="between" css={{ '@md': { padding: '$spacing-03 $spacing-06 $spacing-05 $spacing-06' } }}>
                        <Box>
                            <FavoriteWrapper css={{ marginBottom: '$spacing-02', '@md': { marginBottom: '12px' } }}>
                                <Heading
                                    as="h1"
                                    heading="pageTitle"
                                >
                                    {t('titleSection.title', 'Laporan Buku Besar')}
                                    <Text as="span" css={{ fontSize: '14px', fontWeight: '300' }}>{` (${t('label.inRupiah', { ns: 'translation', defaultValue: 'dalam Rupiah' })})`}</Text>
                                </Heading>
                                <TooltipGuidance />
                            </FavoriteWrapper>
                            <Flex align="center" css={{ gap: '$spacing-03', marginTop: '$spacing-02' }}>
                                <Text as="b" css={{ color: '$textSecondary' }}>
                                    {!outlet ? t('label.allOutlets', { ns: 'translation', defaultValue: 'Semua Outlet' }) : outlet.cabang_name}
                                </Text>
                            </Flex>
                        </Box>
                        <ExportReport
                            onExport={downloadLaporan}
                            calendar={calendar}
                            title={t('titleSection.title', 'Laporan Buku Besar')}
                            type="multi"
                            css={{
                                '@sm': {
                                    display: 'none',
                                },
                                '@md': {
                                    display: 'inline-flex',
                                },
                            }}
                        />
                    </Flex>
                </Box>
                <Box css={{ padding: '0px $spacing-06' }}>
                    <BannerText css={{ my: 0 }} />
                </Box>
                <Box css={{ padding: '0px $spacing-06' }}>
                    <Separator
                        css={{ '@sm': { display: 'none' }, '@md': { display: 'block' } }}
                    />
                </Box>
                <Box css={{ margin: '$spacing-03 $spacing-05' }}>
                    <Flex align="start" css={{ '@sm': { flexDirection: 'column' }, '@md': { flexDirection: 'row', gap: '16px' } }}>
                        <InputDatePicker
                            css={{
                                width: '258px',
                                '@sm': {
                                    width: '100%',
                                    padding: '12px 0',
                                },
                                '@md': {
                                    width: '258px',
                                },
                            }}
                            type="advance"
                            onChange={handleChangeDateRange}
                            date={[toMoment(calendar.start).toDate(), toMoment(calendar.end).toDate()]}
                        />
                        <InputSelectTag
                            key={String(valueOptionAccount.length)}
                            showSelectAll
                            onChange={setValueOptionAccount}
                            placeholder={t('filterSection.selectAccount', 'Pilih Nama Akun')}
                            size="sm"
                            css={{
                                width: '290px',
                                '@sm': {
                                    width: '100%',
                                    padding: '12px 0',
                                },
                                '@md': {
                                    width: '258px',
                                },
                            }}
                            value={valueOptionAccount}
                            option={optionAccount}
                            selectAllLabel={t('filterSection.selectAll', 'Pilih Semua')}
                            showCounter
                            labelCounter={t('filterSection.labelCounter', 'Akun Terpilih', { count: valueOptionAccount.length })}
                        />
                        <ExportReport
                            onExport={downloadLaporan}
                            calendar={calendar}
                            title={t('titleSection.title', 'Laporan Buku Besar')}
                            type="multi"
                            css={{
                                '@sm': {
                                    display: 'inline-flex',
                                    width: '100%',
                                },
                                '@md': {
                                    display: 'none',
                                },
                            }}
                        />
                    </Flex>
                </Box>
                <Box css={{ padding: '0px $spacing-05' }}>
                    <Separator
                        css={{ '@sm': { display: 'none' }, '@md': { display: 'block' } }}
                    />
                </Box>

                {/* Empty data table */}
                {!totalData && (
                    <Box
                        css={{
                            padding: '0px $spacing-05',
                            '& > div > div:nth-child(2)': {
                                display: !totalData ? 'block' : 'none',
                            },
                        }}
                    >
                        <Table
                            columns={tableMetaBukuBesar(t)}
                            data={[]}
                            totalData={totalData}
                            isLoading={loading}
                            hidePagination
                            hideDataInfo
                            css={{ overflowX: 'hidden' }}
                        />
                    </Box>
                )}

                {/* Content Infinite Scroll Table */}
                {tableData.map((item, index) => (
                    <React.Fragment>
                        <Box css={{ padding: '0px $spacing-05' }}>
                            <InfiniteScrollTable
                                key={item.account.no}
                                item={item}
                                calendar={calendar}
                                filterBranch={filterBranch}
                                indexTable={index}
                                t={t}
                            />
                        </Box>
                        {index + 1 !== tableData.length && (
                            <Separator css={{ margin: '$spacing-06 0px' }} />
                        )}
                    </React.Fragment>
                ))}

                {/* Custom Pagination */}
                {!!totalData && (
                    <Pagination
                        currentPage={currentPage}
                        limit={limit}
                        onLimitChange={incomingLimit => handlePageChange(currentPage, incomingLimit)}
                        onPageChange={incomingPage => handlePageChange(incomingPage, limit)}
                        totalData={totalData}
                        rowsPerPageOptions={[1, 2, 3, 5, 10]}
                        css={{
                            padding: '$spacing-04 0',
                            '@md': { padding: '$spacing-06 $spacing-07' },
                        }}
                    />
                )}
            </Paper>
            <AlertDialog
                isMobile={isMobile}
                onConfirm={() => {
                    setDialogInfoExport(prev => ({ ...prev, isDialog: false }));
                    addNotification({
                        title: t('toast.success', { ns: 'translation', defaultValue: 'Berhasil!' }),
                        message: (
                            <Trans
                                t={t}
                                i18nKey="toast.successRequestExport"
                                components={{ strong: <b /> }}
                                values={{ menu: menuName }}
                            >
                                Laporan
                                {' '}
                                <strong>Buku Besar</strong>
                                {' '}
                                dalam proses ekspor
                            </Trans>
                        ),
                        level: 'success',
                    });
                }}
                onCancel={() => setDialogInfoExport(prev => ({ ...prev, isDialog: false }))}
                open={dialogInfoExport.isDialog}
                title={dialogInfoExport.state.title}
                description={dialogInfoExport.state.description}
                labelConfirm={(
                    <Trans
                        t={t}
                        i18nKey="label.confirm"
                        defaults="Oke, Mengerti"
                    />
                )}
                labelCancel={(
                    <Trans
                        t={t}
                        i18nKey="label.close"
                        defaults="Tutup"
                    />
                )}
                css={{
                    width: isMobile ? 'unset' : '422px',
                }}
                actionButtonProps={{ size: 'md' }}
                cancelButtonProps={{ size: 'md' }}
                singleButton={!dialogInfoExport.state.btnCloseOnly}
                hideActionButton={dialogInfoExport.state.btnCloseOnly}
            />
        </Flex>
    );
};

BukuBesarListV2.propTypes = {
    filterBranch: PropTypes.string,
    showProgress: PropTypes.func,
    hideProgress: PropTypes.func,
    assignCalendar: PropTypes.func,
    calendar: PropTypes.shape({
        start: PropTypes.string,
        end: PropTypes.string,
    }).isRequired,
    router: PropTypes.shape({
        push: PropTypes.func,
    }),
    location: PropTypes.shape({
        pathname: PropTypes.string,
    }),
    notificationSystem: PropTypes.shape({
        addNotification: PropTypes.func,
    }),
    outletList: PropTypes.arrayOf(PropTypes.object),
    addNotification: PropTypes.func.isRequired,
};

BukuBesarListV2.defaultProps = {
    filterBranch: '',
    showProgress: () => { },
    hideProgress: () => { },
    assignCalendar: () => { },
    router: null,
    location: null,
    notificationSystem: null,
    outletList: [],
};

const mapStateToProps = state => ({
    amaUser: state.user.profile.user_name,
    listCabang: state.branch.list,
    outletList: getOutletData(),
});

export default connect(mapStateToProps, null)(CoreHOC(BukuBesarListV2));
