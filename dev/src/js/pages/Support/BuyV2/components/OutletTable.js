import React, { useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import {
    Box, EditableTable, FormHelper, InputCounter, Paragraph, Tag, Flex, Heading, Text,
    InputCheckbox, AlertDialogFunction,
} from '@majoo-ui/react';
import * as moment from 'moment';
import { debounce, get } from 'lodash';
import { useAccountType } from '../../../LayoutV2/hooks';
import { useMediaQuery } from '../../../../utils/useMediaQuery';
import {
    ACCOUNT_TYPE, UNIT_PERIOD,
} from '../enum';
import {
    formatPrice, MemoEditableTable, recalculateExpDateOutlet,
    defaultOutletPayload, calculateProratePriceNonCC,
} from '../utils';
import { useTranslationHook } from '../lang.utils';

const OutletTable = React.forwardRef((props, ref) => {
    const {
        selectedSubscription, selectedSubscriptionDetail, isAutodebit, onChangeTable, column, ecommerce,
        onChangeSelected, selectedOutlets, search, isRenewal, isUpgrade, isUpdateBill,
        isTrialPromo,
    } = props;
    const { LANG_DATA, ready, currentLang } = useTranslationHook();
    const filteredData = search ? selectedSubscriptionDetail.data.filter(x => x.nama_cabang.toLowerCase().includes(search.toLowerCase())) : selectedSubscriptionDetail.data;
    const isYearly = selectedSubscription.duration_unit_support === UNIT_PERIOD.YEARLY;
    // const isDiscount = selectedSubscription.support_price_before_discount && +selectedSubscription.support_price_before_discount > +selectedSubscription.support_monthly_price;
    const [tableData, setTableData] = useState(filteredData);
    const [tableData2, setTableData2] = useState(filteredData);
    const [generalQuantity, setGeneralQuantity] = useState(1);
    const [selectedData, setSelectedData] = useState(selectedOutlets || []);
    const keyCount = useRef(tableData.length);
    const accountType = useAccountType();
    const conditions = {
        isHideButton: (
            (accountType === ACCOUNT_TYPE.TRIAL || accountType === ACCOUNT_TYPE.FREE)
            && (!isAutodebit)),
    };
    const isMobile = useMediaQuery('(max-width: 767px)');
    const handleTableAction = (val) => {
        const { type, payload } = val;
        switch (type) {
            case 'ADD': {
                const key = `row-${keyCount.current}`;
                setTableData2((d) => {
                    const _payload = defaultOutletPayload(selectedSubscriptionDetail, key, isAutodebit);
                    const clearedError = d.map((c) => {
                        if (c.nama_cabang) {
                            return { ...c, _isInvalid: false };
                        }
                        if (c._isInvalid !== undefined) {
                            return { ...c, _isInvalid: true };
                        }
                        return c;
                    });
                    const added = [...clearedError, _payload];
                    setTableData(added);
                    onChangeTable({ key, table: added });
                    return added;
                });
                keyCount.current += 1;
                break;
            }
            case 'DELETE': {
                const deleteOutlet = () => {
                    const { _index } = payload;
                    setTableData2((ye) => {
                        const deleted = ye.filter((_, index) => index !== _index);
                        setTableData(deleted);
                        onChangeTable({ table: deleted });
                        return deleted;
                    });
                };
                if (ecommerce) {
                    deleteOutlet();
                } else {
                    const dialog = new AlertDialogFunction({
                        title: LANG_DATA.LABEL_DELETE_CONFIRMATION,
                        description: LANG_DATA.MAIN_SUBSCRIPTION_DELETE_CONFIRMATION,
                        dialogType: 'negative',
                        labelConfirm: LANG_DATA.LABEL_CONTINUE,
                        labelCancel: LANG_DATA.LABEL_CANCEL,
                        hideCloseButton: true,
                        onCancel: () => setTableData2((d) => {
                            const updated = d.map((u) => {
                                const match = u._key === payload._key;
                                if (match) return { ...u, claim_qty: 1 };
                                return u;
                            });
                            setTableData(updated);
                            onChangeTable({ table: updated, key: payload._key });
                            return updated;
                        }),
                        onConfirm: () => deleteOutlet(),
                    });
                    dialog.show();
                }
                break;
            }
            case 'UPDATE':
                if (payload.claim_qty === 0) {
                    handleTableAction({ type: 'DELETE', payload });
                    return;
                }
                setTableData2((d) => {
                    const updated = d.map((u) => {
                        const match = u._key === payload._key;
                        if (match) {
                            return ({
                                ...u,
                                nama_cabang: payload.nama_cabang,
                                _isInvalid: false,
                                invalidMessage: '',
                                ...(typeof payload.claim_qty === 'number' && { claim_qty: payload.claim_qty }),
                            });
                        }
                        if (payload.invalidMessage === LANG_DATA.FORM_ERROR.MAIN.DUPLICATE_OUTLET_NAME) {
                            return ({
                                ...u,
                                _isInvalid: false,
                                invalidMessage: '',
                            });
                        }
                        return u;
                    });
                    onChangeTable({ table: updated, key: payload._key });
                    setTableData(updated);
                    return updated;
                });
                break;
            case 'UPDATE_QUANTITY':
                setTableData2((d) => {
                    const updated = d.map((u) => {
                        const match = u._key === payload._key;
                        if (match) {
                            return ({
                                ...u,
                                ...(typeof payload.claim_qty === 'number' && { claim_qty: payload.claim_qty }),
                            });
                        }
                        return u;
                    });
                    setTableData(updated);
                    onChangeTable({ table: updated, key: payload._key });
                    return updated;
                });
                break;
            default:
                break;
        }
    };

    useEffect(() => {
        if (isAutodebit) {
            const updated = tableData2.map(item => ({ ...item, claim_qty: 1 }));
            setTableData(updated);
            setTableData2(updated);
            onChangeTable({ table: updated });
        }
    }, [isAutodebit]);

    useImperativeHandle(ref, () => ({
        action: (val) => {
            handleTableAction(val);
        },
    }));

    const headerData = useCallback(onRowAction => [
        ...(isRenewal ? [{
            Header: LANG_DATA.LABEL_PRODUCT,
            accessor: 'key',
            width: 150,
            Cell: () => (
                <Box css={{ m: 'auto 0px' }}>
                    <Paragraph>
                        {selectedSubscription.support_name}
                    </Paragraph>
                </Box>
            ),
        }] : []),
        {
            Header: () => <Box id="_outlet">OUTLET</Box>,
            accessor: 'nama_cabang',
            isMobileHeader: true,
            width: 200,
            Cell: (instance) => {
                const [str, setStr] = useState('');
                const { value, row: { original } } = instance;
                return original._isNewRow ? (
                    <Box
                        css={{ width: '100%', paddingRight: '$compact' }}
                    >
                        <EditableTable.Input
                            css={{ margin: '13px 0px', width: '100%' }}
                            placeholder={LANG_DATA.OUTLET_TABLE.PLACEHOLDER}
                            onChange={(y) => {
                                setStr(y.target.value);
                                handleTableAction({ type: 'UPDATE', payload: { ...original, nama_cabang: y.target.value, _isInvalid: false } });
                            }}
                            defaultValue={original.nama_cabang}
                            isInvalid={original._isInvalid && !str}
                        />
                    </Box>
                ) : (
                    <Box css={{
                        display: 'flex', flexDirection: 'row', gap: '$spacing-03', '@md': { flexDirection: 'column', gap: '$spacing-02', justifyContent: 'center' },
                    }}
                    >
                        <Paragraph>
                            {value}
                        </Paragraph>
                        {original.is_utama === '1' && (
                            <Tag type="varian">{LANG_DATA.ACTIVE_SECTION_MODAL_DETAIL_MAIN_OUTLET}</Tag>
                        )}
                    </Box>
                );
            },
        },
        {
            Header: LANG_DATA.OUTLET_TABLE.COLUMN_STATUS,
            accessor: 'exp_date',
            hidden: x => get(x, 'row.original._isNewRow'),
            Cell: (instance) => {
                const { value, row: { original } } = instance;
                let expirationDate = moment(value, 'YYYY-MM-DD HH:mm:ss');
                if (original.is_for_trial === '1') {
                    expirationDate = moment();
                }
                return (
                    <Box css={{ m: 'auto 0px' }}>
                        <Paragraph>
                            <span>
                                {LANG_DATA.OUTLET_TABLE.ACTIVE_SINCE}
                                <br />
                            </span>
                            {expirationDate.format('DD MMM YYYY')}
                        </Paragraph>
                    </Box>
                );
            },
        },
        {
            Header: LANG_DATA.OUTLET_TABLE.COLUMN_GROUP,
            accessor: 'code',
            hidden: x => get(x, 'row.original._isNewRow'),
            Cell: (instance) => {
                const { value } = instance;
                return (
                    <Box css={{ m: 'auto 0px' }}>
                        <Paragraph>
                            {value || '-'}
                        </Paragraph>
                    </Box>
                );
            },
        },
        {
            id: 'duration',
            Header: (
                <Box css={{ maxWidth: 100, '@md': { maxWidth: 'unset' } }}>
                    <Heading as="p" heading="headerTable" color="secondary">
                        {(() => {
                            if (ecommerce) {
                                return LANG_DATA.OUTLET_TABLE.COLUMN_DURATION;
                            }
                            if (isYearly) {
                                return LANG_DATA.OUTLET_TABLE.COLUMN_DURATION_YEAR;
                            }
                            return LANG_DATA.OUTLET_TABLE.COLUMN_DURATION_MONTH;
                        })()}
                    </Heading>
                </Box>
            ),
            Cell: (instance) => {
                const { row: { original, index } } = instance;
                const currentItem = tableData2.find(item => item._key === original._key) || original;
                const quantity = currentItem.claim_qty;
                // const limit = isYearly ? PURCHASE_LIMIT.YEARLY : PURCHASE_LIMIT.MONTHLY;
                if (quantity === 0 && isAutodebit) { return <Box css={{ m: 'auto 0px' }}>-</Box>; }
                return (
                    <Box css={{ m: 'auto 0px' }}>
                        <InputCounter
                            disabledDecrement={(isUpdateBill && quantity === 1) || isAutodebit || (quantity === 1 && selectedSubscription.current_support_id !== selectedSubscription.support_id)}
                            disabledIncrement={isAutodebit}
                            value={quantity}
                            onChange={debounce((n) => {
                                // let qty = n;
                                // if (isDiscount && +n > limit) qty = limit;
                                handleTableAction({
                                    type: 'UPDATE',
                                    payload: {
                                        ...original, claim_qty: n, _index: index,
                                    },
                                });
                            }, 700)}
                            css={{
                                mb: 4,
                                width: 150,
                                '@md': { width: 180 },
                                '& > input:disabled': {
                                    backgroundColor: '$btnDisable !important',
                                    color: '$textDisable !important',
                                },
                            }}
                            size="md"
                        />
                    </Box>
                );
            },
        },
        {
            id: 'Active Subscription Duration',
            Header: LANG_DATA.OUTLET_TABLE.COLUMN_DURATION,
            accessor: 'statusBerlangganan',
            width: 164,
            hidden: x => get(x, 'row.original._isNewRow'),
            Cell: (instance) => {
                const { row: { original } } = instance;
                const estimateExp = recalculateExpDateOutlet(original, isAutodebit, original.claim_qty, isYearly, isTrialPromo);
                return (
                    <Box css={{ m: 'auto 0px' }}>
                        <Paragraph>
                            <span>
                                {LANG_DATA.OUTLET_TABLE.VALID_UNTIL}
                                <br />
                            </span>
                            {estimateExp.locale(currentLang).format('DD MMM YYYY')}
                        </Paragraph>
                    </Box>
                );
            },
        },
        {
            Header: LANG_DATA.OUTLET_TABLE.COLUMN_PRICE,
            accessor: 'prorate_price',
            width: 32,
            hidden: ecommerce,
            Cell: (instance) => {
                const { row: { original }, value } = instance;
                let price = value; // for autodebit
                if (!original._isAutodebit) {
                    price = calculateProratePriceNonCC(moment(), isYearly, selectedSubscriptionDetail.basic_price, selectedSubscriptionDetail.prorate_perday, original);
                }
                // let price = value;
                // if (!original._isAutodebit) {
                //     // old code:
                //     // if (isYearly) price = original.support_monthly_annual_price * original.claim_qty;
                //     // else price = original.support_monthly_price * original.claim_qty;

                //     // billing profiling:
                //     price = original.support_transactions_price * original.claim_qty;
                // }
                return (
                    <Box css={{ m: 'auto 0px' }}>
                        {formatPrice(price)}
                    </Box>
                );
            },
        },
        ...(!isUpgrade) ? [{
            Header: '',
            accessor: 'remove',
            width: 32,
            hidden: isRenewal,
            Cell: (instance) => {
                const { row } = instance;
                if (conditions.isHideButton) return (<React.Fragment />);
                return (
                    <Box css={{ m: 'auto 0px' }}>
                        <EditableTable.Remove onRemove={() => onRowAction({ type: 'DELETE', payload: { ...row, _index: row.index } })} />
                    </Box>
                );
            },
        }] : [],
    ], [isMobile, isAutodebit, accountType, isYearly, generalQuantity, selectedData.length, conditions.isHideButton, ecommerce, tableData2]);

    const strRequired = tableData2.some(r => !r.nama_cabang) && tableData.some(r => r._isInvalid);
    const { invalidMessage } = tableData.find(r => r._isInvalid) || {};
    const handleChangeQuantity = (n) => {
        setGeneralQuantity(n);
        const filteredTable = isAutodebit ? tableData2.filter(r => selectedData.includes(r._key)) : tableData2.filter(r => selectedData.includes(r._key));
        filteredTable.forEach((r) => {
            const index = tableData2.findIndex(u => u._key === r._key);
            handleTableAction({
                type: 'UPDATE_QUANTITY',
                payload: {
                    ...r, claim_qty: n, _key: r._key, _index: index,
                },
            });
        });
    };

    const handleIndex = (x) => {
        setSelectedData(x.selected);
        onChangeSelected(x.selected);
    };

    const getRowHeight = () => {
        if (isAutodebit) {
            return 72;
        }
        return 96;
    };

    return ready && (
        <React.Fragment>
            <MemoEditableTable
                key={isAutodebit}
                showCheckbox={!ecommerce && !isRenewal}
                data={tableData.filter(x => x.claim_qty > 0)}
                columns={column === undefined ? headerData(val => handleTableAction(val)) : column}
                rowHeight={getRowHeight()}
                css={{
                    padding: 0,
                    '& thead': { position: 'static', height: 44, '@md': { height: 64 } },
                    '& td:first-child > div > div:first-child': { width: '100%' },
                    '@md': {
                        '& td:first-child > div > div:first-child': { width: 'unset' },
                        '& button[role="checkbox"]': { alignSelf: 'center', margin: '0px 28px 0px 12px' },
                    },
                }}
                keyId="_key"
                onSelectedChange={handleIndex}
                selectedIds={selectedData.reduce((prev, current) => {
                    Object.assign(prev, { [current]: true });
                    return prev;
                }, {})
                }
                customRows={x => [
                    {
                        predicate: x.original._isCounter,
                        Row: () => {
                            const [_val, _setVal] = useState(generalQuantity);
                            const _isMobile = useMediaQuery('(max-width: 767px)');
                            return (
                                <EditableTable.TableRow css={{
                                    position: 'sticky', top: 39, zIndex: '$sticky', backgroundColor: '$bgGray', maxHeight: 78, '& > *': { height: 78 }, '@md': { top: 46, maxHeight: 68, '& > *': { height: 68 } },
                                }}
                                >
                                    {_isMobile ? (
                                        <React.Fragment>
                                            <EditableTable.TableCell colSpan={2}>
                                                <Box
                                                    css={{
                                                        display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: '$spacing-03', mr: '$compact',
                                                    }}
                                                >
                                                    <Heading as="span" heading="headerTable" color="secondary">{`${LANG_DATA.OUTLET_TABLE.ADD_MORE} ${isYearly ? `(${LANG_DATA.OUTLET_TABLE.YEAR})` : `(${LANG_DATA.OUTLET_TABLE.MONTH})`}`}</Heading>
                                                    <InputCounter
                                                        disabledIncrement={isAutodebit && _val > 0}
                                                        disabledDecrement={isAutodebit && _val < 1}
                                                        value={generalQuantity}
                                                        onChange={(n) => {
                                                            _setVal(n);
                                                            setGeneralQuantity(n);
                                                            handleChangeQuantity(n);
                                                        }}
                                                        css={{
                                                            mb: 4,
                                                            width: 180,
                                                            '& input': { backgroundColor: '$white' },
                                                        }}
                                                        size="md"
                                                    />
                                                </Box>
                                            </EditableTable.TableCell>
                                        </React.Fragment>
                                    ) : (
                                        <React.Fragment>
                                            <EditableTable.TableCell />
                                            <EditableTable.TableCell colSpan={4} />
                                            <EditableTable.TableCell colSpan={1}>
                                                <Box css={{ m: 'auto 0px', width: 70 }}>
                                                    <InputCounter
                                                        disabledIncrement={isAutodebit && _val > 0}
                                                        disabledDecrement={isAutodebit && _val < 1}
                                                        value={generalQuantity}
                                                        onChange={(n) => {
                                                            _setVal(n);
                                                            setGeneralQuantity(n);
                                                            handleChangeQuantity(n);
                                                        }}
                                                        css={{
                                                            mb: 4,
                                                            width: 150,
                                                            '@md': { width: 180 },
                                                            '& input': { backgroundColor: '$white' },
                                                        }}
                                                        size="md"
                                                    />
                                                </Box>
                                            </EditableTable.TableCell>
                                        </React.Fragment>
                                    )}
                                </EditableTable.TableRow>
                            );
                        },
                    }, {
                        predicate: x.original._isNewRow,
                        Row: (_, renderRow) => {
                            const checkProps = x && x.getToggleRowSelectedProps();
                            const [str, setStr] = useState('');
                            const [quantity, setQuantity] = useState(x.original.claim_qty);

                            let price = selectedSubscriptionDetail.prorate; // for autodebit
                            const estimateExp = recalculateExpDateOutlet(x.original, isAutodebit, quantity, isYearly);
                            // if (isAutodebit) {
                            //     estimateExp = moment(selectedSubscriptionDetail.prorate_exp_date).locale('id-ID');
                            // }

                            if (!isAutodebit) {
                                price = calculateProratePriceNonCC(moment(), isYearly, selectedSubscriptionDetail.basic_price, selectedSubscriptionDetail.prorate_perday, x.original);
                            }

                            const _isMobile = useMediaQuery('(max-width: 767px)');
                            return _isMobile ? (renderRow()) : (
                                <EditableTable.TableRow>
                                    {!ecommerce && !isRenewal && (
                                        <EditableTable.TableCell>
                                            <InputCheckbox
                                                {...checkProps}
                                                onCheckedChange={e => x.toggleRowSelected(e)}
                                            />
                                        </EditableTable.TableCell>
                                    )}
                                    {isRenewal && (<EditableTable.TableCell>{get(selectedSubscription, 'support_name')}</EditableTable.TableCell>)}
                                    <EditableTable.TableCell colSpan={3}>
                                        <Box css={{ mr: '$spacing-03' }}>
                                            <EditableTable.Input
                                                placeholder={LANG_DATA.OUTLET_TABLE.PLACEHOLDER}
                                                name={x.original._key}
                                                maxLength={50}
                                                defaultValue={x.original.nama_cabang}
                                                onChange={debounce((y) => {
                                                    const { value } = y.target;
                                                    setStr(value);
                                                    handleTableAction({
                                                        type: 'UPDATE',
                                                        payload: {
                                                            ...x.original,
                                                            nama_cabang: y.target.value,
                                                            _index: x.index,
                                                            err: x.original.invalidMessage,
                                                        },
                                                    });
                                                }, 500)}
                                                isInvalid={x.original._isInvalid && !str}
                                            />
                                        </Box>
                                    </EditableTable.TableCell>
                                    <EditableTable.TableCell>
                                        <InputCounter
                                            value={quantity}
                                            disabledIncrement={isAutodebit && quantity > 0}
                                            disabledDecrement={isAutodebit && quantity < 1}
                                            disabled={isAutodebit}
                                            onChange={(n) => {
                                                setQuantity(n);
                                                handleTableAction({
                                                    type: 'UPDATE',
                                                    payload: {
                                                        ...x.original, claim_qty: n, _index: x.index,
                                                    },
                                                });
                                            }}
                                            css={{
                                                mb: 4,
                                                width: 150,
                                                '@md': { width: 180 },
                                                '& > input:disabled': {
                                                    backgroundColor: '$btnDisable !important',
                                                    color: '$textDisable !important',
                                                },
                                            }}
                                            size="md"
                                        />
                                    </EditableTable.TableCell>
                                    <EditableTable.TableCell>
                                        <Flex direction="column">
                                            <Text color="secondary">{LANG_DATA.OUTLET_TABLE.VALID_UNTIL}</Text>
                                            <Text color="primary">
                                                {estimateExp.locale(currentLang).format('DD MMM YYYY')}
                                            </Text>
                                        </Flex>
                                    </EditableTable.TableCell>
                                    {!ecommerce && (
                                        <EditableTable.TableCell>
                                            {formatPrice(price)}
                                        </EditableTable.TableCell>
                                    )}
                                    {!isRenewal && (
                                        <EditableTable.TableCell>
                                            <Box>
                                                <EditableTable.Remove onRemove={() => handleTableAction({ type: 'DELETE', payload: { ...x, _index: x.index } })} />
                                            </Box>
                                        </EditableTable.TableCell>
                                    )}
                                </EditableTable.TableRow>
                            );
                        },
                    }]
                }
            />
            {strRequired && (
                <FormHelper error css={{ mt: '$compact !important', '@md': { my: '$spacing-03 !important' } }}>{LANG_DATA.FORM_ERROR.MAIN.OUTLET_NAME}</FormHelper>
            )}
            {(invalidMessage && !strRequired) && (
                <FormHelper error css={{ mt: '$compact !important', '@md': { my: '$spacing-03 !important' } }}>{invalidMessage}</FormHelper>
            )}
        </React.Fragment>
    );
});

OutletTable.propTypes = {
    selectedSubscription: PropTypes.shape({
        duration_unit_support: PropTypes.string,
        support_price_before_discount: PropTypes.number,
        support_monthly_price: PropTypes.number,
        support_name: PropTypes.string,
        current_support_id: PropTypes.number,
        support_id: PropTypes.number,
    }),
    selectedSubscriptionDetail: PropTypes.shape({
        data: PropTypes.arrayOf(PropTypes.shape({})),
        basic_price: PropTypes.number,
        prorate_perday: PropTypes.number,
        prorate: PropTypes.number,
    }),
    isAutodebit: PropTypes.bool,
    onChangeTable: PropTypes.func,
    column: PropTypes.arrayOf(),
    ecommerce: PropTypes.bool,
    onChangeSelected: PropTypes.func,
    selectedOutlets: PropTypes.arrayOf(),
    search: PropTypes.string,
    isRenewal: PropTypes.bool,
    isUpgrade: PropTypes.bool,
    isTrialPromo: PropTypes.bool,
    isUpdateBill: PropTypes.bool,
};

OutletTable.defaultProps = {
    selectedSubscription: {
        duration_unit_support: UNIT_PERIOD.YEARLY,
        support_price_before_discount: 0,
        support_monthly_price: 0,
    },
    selectedSubscriptionDetail: {
        data: [],
    },
    isAutodebit: true,
    onChangeTable: () => { },
    column: undefined,
    ecommerce: false,
    onChangeSelected: () => { },
    selectedOutlets: [],
    search: '',
    isRenewal: false,
    isUpgrade: false,
    isTrialPromo: false,
    isUpdateBill: false,
};

export default OutletTable;
