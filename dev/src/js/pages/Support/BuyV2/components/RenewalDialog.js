import React, {
    forwardRef,
    useContext, useEffect, useImperativeHandle, useMemo, useRef, useState,
} from 'react';
import {
    Box, Paper, Button, Flex, ModalDialog, Tag, Banner, BannerClose, TagStatus, AlertDialogFunction,
    PageDialog, PageDialogContent, PageDialogTitle, Text, Heading, Skeleton, InputSearchbox, InputSelect, Separator,
    Tooltip,
} from '@majoo-ui/react';
import { CircleInfoOutline } from '@majoo-ui/icons';
import { getSoonExpiredOutlets } from '~/components/virtualaccount/utils';
import _, { get } from 'lodash';
import SupportContext from '../context/SupportContext';
import {
    capitalizeFirstLetter, formatPrice, generateAdditionalSupportPayload, generateIdOrderKirim, getAdditionalSupportService, getSubscriptionName,
    predictExpDateProrate,
} from '../utils';
import {
    ACCOUNT_TYPE, PAYMENT_METHOD, PAYMENT_METHOD_CONVERT, PAYMENT_METHOD_OPTIONS, PAYMENT_PROVIDER_VA, SUBSCRIPTION_PACKAGE,
} from '../enum';
import userUtil from '../../../../utils/user.util';
import ModalAutodebitAlreadyHasGroup from './ModalAutodebitAlreadyHasGroup';
import ModalAutodebit from './ModalAutodebit';
import { catchError } from '../../../../utils/helper';
import ModalVirtualAccount from './ModalVirtualAccount';
import OutletTable from './OutletTable';
import {
    chargeCreditCard, creditCardConfirmation, creditCardInput, updateRecurringPayment,
} from '../PaymentMethods';
import * as SupportApi from '../../../../data/supports';
import TransactionItem from './TransactionItem';
import SupportForm from './FormBuySubscription/StepTwo/SupportForm';
import CardBg from '../assets/bg-card.png';
import AlertChangeSubscription from './AlertChangeSubscription';
import { isSupportExpired } from '../../../../services/session';
import store from '../../../../store';
import { useTranslationHook } from '../lang.utils';

const RenewalDialog = forwardRef((props, ref) => {
    const {
        supportCategory, currentSubscriptionId, addToast, idUser, isMobile,
        additionalSupportProduct, additionalSupportService,
        showProgress, hideProgress, t, accountInfo, paymentInstructionDialogRef, setTab,
        onFetchActiveTransaction, onFetchSupportExpiredVA, getAllBranch, supportKey,
    } = useContext(SupportContext);
    const { virtualAccountPayment: { virtualAccountOutlets }, outlets: listOutlets } = store.getState();
    const { LANG_DATA, LANG_KEY, TransComponent } = useTranslationHook();
    const soonExpiredOutlets = useMemo(() => getSoonExpiredOutlets({ virtualAccountOutlets, outlets: listOutlets }), [virtualAccountOutlets, listOutlets]);

    const [openDialog, setOpenDialog] = useState(false);
    const [selectedOutlet, setSelectedOutlet] = useState([]);
    const [total, setTotal] = useState(0);
    const [stampDuty, setStampDuty] = useState(0);
    const [openHasGroup, setOpenHasGroup] = useState(false);
    const [openVirtualAccount, setOpenVirtualAccount] = useState(false);
    const [openAutodebit, setOpenAutodebit] = useState(false);
    const [openCreditCardPayment, setOpenCreditCardPayment] = useState(false);
    const [paymentURL, setPaymentURL] = useState('');
    const [cardError, setCardError] = useState('');
    const [formData, setFormData] = useState({
        supportId: '',
        package: SUBSCRIPTION_PACKAGE.MONTHLY,
        paymentMethod: PAYMENT_METHOD.AUTO_DEBIT,
        outlets: [],
        additional_support: [],
        data_transaction: [],
        tnc: true,
        orderId: null,
        referenceNumber: '',
    });
    const [recurringGroupList, setRecurringGroupList] = useState([]);
    const [isTransacting, setIsTransacting] = useState(false);
    const [loading, setLoading] = useState({
        calculate: false,
        table: false,
    });
    const [tableKey, setTableKey] = useState(0);
    const [search, setSearch] = useState('');
    const [orderID, setOrderID] = useState();
    const [selectedSubcriptionDetail, setSelectedSubscriptionDetail] = useState({ data: [] });
    const [isDiscountCC, setIsDiscountCC] = useState(false);
    const [showBannerCC, setShowBannerCC] = useState(true);
    const [isUpgrade, setIsUpgrade] = useState(false);
    const alertChangeSubscriptionRef = useRef(null);

    const paymentMethodOptions = useMemo(() => PAYMENT_METHOD_OPTIONS(LANG_DATA).map(x => ({
        ...x,
        name: x.label,
        ...(isDiscountCC && x.value === PAYMENT_METHOD.AUTO_DEBIT) ? {
            render: () => (
                <Flex gap={4} align="center">
                    <Text>{x.label}</Text>
                    <Tag type="success" css={{ '& > div:first-child': { display: 'none' } }}>{LANG_DATA.DISCOUNT25}</Tag>
                </Flex>
            ),
        } : { },
    })), [isDiscountCC]);
    const selectedSupport = useMemo(() => supportCategory[formData.package].find(x => String(x.support_id) === String(formData.supportId)), [formData.supportId, formData.package, supportCategory]);
    const selectedSubscription = useMemo(() => {
        if (formData.supportId && formData.package) {
            return supportCategory[formData.package].find(x => String(x.support_id) === String(formData.supportId));
        }
        return null;
    }, [formData.supportId, formData.package, JSON.stringify(supportCategory)]);
    const upgradeAccountList = useMemo(() => {
        if (formData.referenceNumber) return [];
        const dataSubs = supportCategory[formData.package].filter(x => x.support_name !== ACCOUNT_TYPE.PRIMEPLUS);
        const findSubIndex = dataSubs.findIndex(x => String(x.support_id) === String(formData.supportId));
        if (findSubIndex >= 0) {
            return dataSubs.filter((sub, index) => index > findSubIndex);
        }
        return [];
    }, [formData.supportId, formData.package, supportCategory, formData.referenceNumber]);
    const filteredAdditionalSupportService = useMemo(() => getAdditionalSupportService(additionalSupportService, get(selectedSupport, 'alias')), [additionalSupportService, selectedSupport]);
    const terminOptions = [
        { value: SUBSCRIPTION_PACKAGE.MONTHLY, name: 'Termin Langganan Bulanan' },
        { value: SUBSCRIPTION_PACKAGE.YEARLY, name: 'Termin Langganan Tahunan' },
    ];

    const checkQtySupport = (outletID) => {
        const getOutletID = Object.keys(userUtil.getLocalConfigByKey('outletExp'));
        return getOutletID.includes(outletID);
    };

    const isPayable = () => {
        const validOutletSupportList = formData.outlets.filter(outlet => outlet.code && checkQtySupport(outlet.cabang_id) && outlet.claim_qty);
        const outletByCode = _.groupBy(validOutletSupportList, 'code');
        const dataKeys = Object.keys(outletByCode);
        const _recurringGroupList = dataKeys.map(x => Object.assign({}, {
            idGroup: x,
            detail: outletByCode[x].map(z => z.nama_cabang),
        }));
        if (_recurringGroupList.length > 0) {
            setRecurringGroupList(_recurringGroupList);
            setOpenHasGroup(true);
            return false;
        }
        return true;
    };

    const handlePaymentFinishing = async (result) => {
        try {
            const resUpdatePayment = await updateRecurringPayment(result.data);
            if (resUpdatePayment.status) {
                addToast({
                    title: LANG_DATA.TOAST_SUCCESS,
                    description: (
                        <TransComponent i18nKey={LANG_KEY.TOAST_TRANSACTION_SUCCESS}>
                            {{ name: capitalizeFirstLetter(selectedSupport.support_name) }}
                        </TransComponent>
                    ),
                    variant: 'success',
                });
            }
        } catch (e) {
            const message = catchError(e);
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: message,
                variant: 'failed',
            });
        } finally {
            setTimeout(() => {
                setIsTransacting(false);
                setOpenAutodebit(false);
                setOpenDialog(false);
                setTab('history');
                getAllBranch();
            }, 3000);
        }
    };

    const getCalculation = async () => {
        if (loading.calculate) return;
        try {
            const isYearly = formData.package === SUBSCRIPTION_PACKAGE.YEARLY;
            const { outlets, paymentMethod } = formData;
            const modifier = isYearly ? 12 : 1;
            setLoading(prev => ({ ...prev, calculate: true }));
            const payload = {
                support_id: formData.supportId,
                user_id: +idUser,
                payment: formData.paymentMethod === PAYMENT_METHOD.AUTO_DEBIT ? 2 : 1,
                package: isYearly ? SUBSCRIPTION_PACKAGE.YEARLY : SUBSCRIPTION_PACKAGE.MONTHLY,
                outlet: formData.outlets.map(x => ({ id: x.cabang_id ? +x.cabang_id : 0, qty: x.claim_qty })),
                additional_support: generateAdditionalSupportPayload(formData.additional_support, modifier),
            };
            const payloadCC = {
                outlet_ids: outlets.filter(x => x.cabang_id).map(x => x.cabang_id),
            }; const [res, resCC] = (await Promise.allSettled([
                SupportApi.getCalculateSubscription(payload),
                SupportApi.checkOutletsCC(payloadCC)])).map((result) => {
                if (result && result.status === 'fulfilled') return result.value;
                return {};
            });
            const filterOutletCCDiscount = outlets.filter((x) => {
                if (typeof resCC.data === 'object') resCC.data = Object.values(resCC.data);
                const listOutletCC = (resCC.data || []).map(dt => dt.outlet_id);
                return !listOutletCC.includes(String(x.cabang_id)) || !x.cabang_id;
            });
            let discount = 0;
            discount = (!isYearly && paymentMethod === PAYMENT_METHOD.AUTO_DEBIT && !isSupportExpired()) ? 35 : 25;
            const nominal = res.nominal && paymentMethod === PAYMENT_METHOD.AUTO_DEBIT ? res.nominal : selectedSubscription.support_monthly_price * filterOutletCCDiscount.length * (discount / 100);
            setIsDiscountCC(Boolean(filterOutletCCDiscount.length) || Boolean(res.nominal));
            setFormData(prev => ({
                ...prev,
                supportId: formData.supportId,
                package: formData.package,
                paymentMethod: formData.paymentMethod,
                outlets: formData.outlets,
                data_transaction: res.data_transaction.filter((x) => {
                    if (x.minus === 2) return false;
                    // if (['Promo Pembelian Langganan', 'Promo Pembelian Langganan Outlet Baru'].includes(x.key)) return false;
                    return true;
                }),
                discount,
                discountCC: nominal,
            }));
        } catch (e) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            setLoading(prev => ({ ...prev, calculate: false }));
        }
    };

    const fetchSubscriptionDetail = async () => {
        setLoading(prev => ({ ...prev, table: true }));
        const isAutoDebit = formData.paymentMethod === PAYMENT_METHOD.AUTO_DEBIT;
        const payload = {
            id_support: formData.supportId,
            package: formData.package,
            payment: isAutoDebit ? 2 : 1,
        };
        try {
            const res = await SupportApi.getOutletHasSupportV4(payload);
            if (!res.status) throw new Error(res.msg);
            const findSupport = supportCategory[formData.package].find(x => String(x.support_id) === String(formData.supportId));
            const monthlyPrice = findSupport ? findSupport.support_monthly_price : 0;
            const annualPrice = findSupport ? findSupport.support_monthly_annual_price : 0;
            const filteredResult = selectedOutlet.length ? res.data.filter(x => selectedOutlet.includes(x.cabang_id)) : res.data;
            const remapResult = filteredResult.map((d) => ({
                    ...d,
                    _isAutodebit: isAutoDebit,
                    claim_qty: 1,
                    _key: d.cabang_id,
                    support_monthly_price: monthlyPrice,
                    support_monthly_annual_price: annualPrice,
                }));
            const final = {
                ...res,
                data: remapResult,
                support_monthly_price: monthlyPrice,
                support_monthly_annual_price: annualPrice,
            };
            setSelectedSubscriptionDetail(final);
            let tempOutlet = formData.outlets.length ? formData.outlets.map((x) => {
                const findDt = filteredResult.find(dt => dt.cabang_id === x.cabang_id);
                if (findDt) {
                    return ({
                        ...x,
                        ...findDt,
                        ...isAutoDebit && { claim_qty: 1 },
                    });
                }
                // outlet baru
                return ({
                    ...x,
                    ...isAutoDebit && { claim_qty: 1 },
                    prorate_price: res.prorate,
                    prorate_perday: res.prorate_perday,
                    support_monthly_price: monthlyPrice,
                    support_monthly_annual_price: annualPrice,
                });
            }) : remapResult;
            const newOutletsKey = tempOutlet.filter(x => x._isNewRow).map(x => x._key);
            if (Array.isArray(formData.newOutlets)) tempOutlet = [...tempOutlet, ...formData.newOutlets.filter(x => !newOutletsKey.includes(x._key))];
            setTimeout(() => setFormData(prev => ({ ...prev, outlets: tempOutlet })), 300);
        } catch (e) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            setLoading(prev => ({ ...prev, table: false }));
        }
    };

    const handleChangeTable = async (param) => {
        setFormData(prev => ({ ...prev, outlets: param.table, newOutlets: param.table.filter(x => x._isNewRow) }));
    };

    const generatePayloadCC = () => {
        const orderId = orderID || generateIdOrderKirim(idUser, formData.supportId);
        const modifier = formData.package === SUBSCRIPTION_PACKAGE.YEARLY ? 12 : 1;
        if (!orderID) setOrderID(orderId);
        const outletPayload = formData.outlets.map(outlet => ({
            id_cabang: outlet.cabang_id || '',
            claim_qty: 1,
            cabang_name: outlet.cabang_id ? `${selectedSupport.support_name} - ${outlet.nama_cabang}` : outlet.nama_cabang,
            isUtama: outlet.is_utama === '1',
        }));
        const admin = {
            id_cabang: 1,
            claim_qty: 1,
            tagihan: stampDuty,
            cabang_name: 'Biaya Admin',
        };
        return {
            order_id: orderId,
            support: formData.supportId,
            id_outlet: JSON.stringify([...outletPayload, admin]),
            name: accountInfo.user_name,
            email: accountInfo.user_email,
            tnc: Number(formData.tnc),
            package: formData.package,
            additional_support: generateAdditionalSupportPayload(formData.additional_support, modifier),
        };
    };

    const generatePayload = (bankId) => {
        const orderId = orderID || generateIdOrderKirim(idUser, formData.supportId);
        const modifier = formData.package === SUBSCRIPTION_PACKAGE.YEARLY ? 12 : 1;
        if (!orderID) setOrderID(orderId);
        return {
            order_id: orderId,
            payment: {
                method: formData.paymentMethod,
                bank_id: bankId,
            },
            tnc: Number(formData.tnc),
            package: formData.package,
            support: {
                id: formData.supportId,
                contact: {
                    name: accountInfo.user_name,
                    email: accountInfo.user_email,
                },
                outlets: formData.outlets.map(x => ({
                    id: x.cabang_id || '',
                    qty: x.claim_qty,
                    name: x.nama_cabang,
                    is_main: +x.is_utama,
                })),
                promo_code: '',
                voucher_code: '',
            },
            additional_support: generateAdditionalSupportPayload(formData.additional_support, modifier),
        };
    };


    const onBuySubscription = async (bankId) => {
        showProgress();
        setIsTransacting(true);
        try {
            const payload = generatePayload(bankId);
            const res = await (formData.referenceNumber ? SupportApi.updateBilling(formData.referenceNumber, payload) : SupportApi.buyPackage(payload));
            if (!_.get(res, 'data')) throw new Error(res.msg);
            addToast({
                title: LANG_DATA.TOAST_SUCCESS,
                description: (
                    <TransComponent i18nKey={LANG_KEY.TOAST_TRANSACTION_SUCCESS}>
                        {{ name: capitalizeFirstLetter(selectedSupport.support_name) }}
                    </TransComponent>
                ),
                variant: 'success',
            });
            setOpenDialog(false);
            if (formData.paymentMethod === PAYMENT_METHOD.VIRTUAL_ACCOUNT) {
                if (formData.referenceNumber) onFetchSupportExpiredVA();
                else onFetchActiveTransaction();
                paymentInstructionDialogRef.current.handleShowDialog({
                    bill: {
                        id: _.get(res, 'data.id'),
                        va_expire_date: _.get(res, 'data.va.exp_date'),
                        total: _.get(res, 'data.bill'),
                        va_number: _.get(res, 'data.va.number'),
                        bank_id: bankId,
                    },
                }, () => setTab('history'));
            } else if (_.get(res, 'data.payment_link')) {
                setTab('history');
                window.open(res.data.payment_link, '_blank');
            }
        } catch (error) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(error),
                variant: 'failed',
            });
            return;
        } finally {
            setIsTransacting(false);
            hideProgress();
        }
    };

    const nonAutodebitPayment = async (bankId = null) => {
        setOpenVirtualAccount(false);
        try {
            const payloadCheck = {
                payment: {
                    method: formData.paymentMethod,
                    bank_id: bankId,
                },
                outlets: formData.outlets.filter(({ claim_qty, cabang_id }) => claim_qty && cabang_id).map(({ cabang_id }) => cabang_id),
            };
            const { data } = await SupportApi.checkExistingBill(payloadCheck);
            if (data && data.is_conflict && data.previous_outlets.length) {
                const { previous_outlets: prevOutlets, previous_payment: prevPayment } = data;
                const firstOutlet = prevOutlets[0].name;
                let paymentMethod = PAYMENT_METHOD_CONVERT[prevPayment.method];
                const findBank = PAYMENT_PROVIDER_VA.find(x => +x.id === +prevPayment.bank_id);
                if (findBank) paymentMethod = `${findBank.name} ${paymentMethod}`;
                const dialog = new AlertDialogFunction({
                    title: `${LANG_DATA.LABEL_ATTENTION}!`,
                    description: (
                        <TransComponent i18nKey={prevOutlets.length > 1 ? LANG_KEY.EXIST_TRANSACTION_MULTI : LANG_KEY.EXIST_TRANSACTION_SINGLE}>
                            {{ outlet: firstOutlet, method: paymentMethod, total: prevOutlets.length - 1 }}
                        </TransComponent>
                    ),
                    labelConfirm: LANG_DATA.LABEL_CONTINUE,
                    labelCancel: LANG_DATA.BILLING_SECTION_ACTION.CHANGE_PAYMENT_METHOD,
                    onConfirm: () => onBuySubscription(bankId),
                });
                dialog.show();
            } else onBuySubscription(bankId);
        } catch (error) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(error),
                variant: 'failed',
            });
        }
    };

    const autodebitPayment = async (cardPayload) => {
        setIsTransacting(true);
        try {
            const token = await creditCardInput(cardPayload);
            let ccPayload = generatePayloadCC();
            ccPayload = Object.assign(ccPayload, {
                card_number: cardPayload.card_number,
                token: token.data,
            });
            const resMidtrans = await chargeCreditCard(ccPayload);
            const redirectURL = resMidtrans.charge.redirect_url;
            const res3DSPayment = await creditCardConfirmation(redirectURL, setPaymentURL, setOpenCreditCardPayment);
            if (!res3DSPayment.data.saved_token_id) {
                res3DSPayment.data.saved_token_id = resMidtrans.data.token;
            }
            handlePaymentFinishing(res3DSPayment);
        } catch (e) {
            setIsTransacting(false);
            const message = catchError(e);
            switch (message) {
                case 'One or more parameters in the payload is invalid.':
                    setCardError(t('errors.invalid', { ns: 'Pengaturan/subscription/modalAutodebit' }));
                    break;
                case 'Card is not authenticated.':
                    setCardError(t('errors.unautenticated', { ns: 'Pengaturan/subscription/modalAutodebit' }));
                    break;
                default:
                    setCardError(message);
                break;
            }
        }
    };

    const setErrorOutlet = (errors, invalidMessage) => {
        const injectedTable = formData.outlets.map((r, index) => {
            const match = errors.includes(index) && r._isNewRow;
            if (match) {
                Object.assign(r, { _isInvalid: true, invalidMessage });
            } else {
                Object.assign(r, { _isInvalid: false, invalidMessage: '' });
            }
            return r;
        });
        setFormData(prev => ({ ...prev, outlets: injectedTable }));
    };

    const handleBuy = async ({ bypass = false }) => {
        const orderId = generateIdOrderKirim(idUser, formData.supportId);
        setOrderID(orderId);
        const isAutodebit = formData.paymentMethod === PAYMENT_METHOD.AUTO_DEBIT;
        const isVirtualAccount = formData.paymentMethod === PAYMENT_METHOD.VIRTUAL_ACCOUNT;
        const nameOutlets = formData.outlets.map(x => x.nama_cabang);
        const isOutletExist = formData.outlets.map((x, index) => {
            if (nameOutlets.filter(outlet => x.nama_cabang === outlet).length > 1) return index;
            return null;
        }).filter(x => x !== null);
        const isOutletEmpty = formData.outlets.map((x, idx) => (!x.nama_cabang ? idx : null)).filter(x => x !== null);
        if (isOutletEmpty.length) {
            setErrorOutlet(isOutletEmpty, LANG_DATA.FORM_ERROR.MAIN.OUTLET_NAME);
            return;
        }
        if (isOutletExist.length) {
            setErrorOutlet(isOutletExist, LANG_DATA.FORM_ERROR.MAIN.DUPLICATE_OUTLET_NAME);
            return;
        }
        if (isAutodebit) {
            if (!isPayable()) return;
            setCardError('');
            setOpenAutodebit(true);
            return;
        }
        if (!isAutodebit) {
            if (bypass && isVirtualAccount) {
                setOpenVirtualAccount(true);
                setOpenHasGroup(false);
                return;
            }
            if (!isPayable()) return;
            if (isVirtualAccount) setOpenVirtualAccount(true);
            else nonAutodebitPayment();
        }
    };

    const handleOpenDialog = async (dataList = [], supportId, refno, newOutlets) => {
        showProgress();
        setSelectedOutlet(dataList);
        const singleSoonExpiredData = soonExpiredOutlets[0];
        setFormData({
            supportId: supportId || currentSubscriptionId,
            package: singleSoonExpiredData ? singleSoonExpiredData.bill.package : supportKey,
            paymentMethod: PAYMENT_METHOD.VIRTUAL_ACCOUNT,
            outlets: [],
            additional_support: [],
            data_transaction: [],
            tnc: true,
            orderId: null,
            referenceNumber: refno || '',
            newOutlets,
        });
        setOpenDialog(true);
        hideProgress();
    };

    const handleUpgrade = (data) => {
        setIsUpgrade(true);
        handleOpenDialog([], data.support_id);
    };

    useImperativeHandle(ref, () => ({
        handleOpenDialog,
    }));

    useEffect(() => {
        const totalPackage = formData.data_transaction.reduce((prev, curr) => {
            let result = prev;
            if (curr.key === 'Biaya Admin') setStampDuty(curr.value);
            if (curr.minus) result = prev - curr.value;
            else result = prev + curr.value;
            return result;
        }, 0);
        setTotal(totalPackage);
    }, [JSON.stringify(formData.data_transaction), JSON.stringify(formData.additional_support)]);

    useEffect(() => {
        if (formData.outlets.length && selectedSubscription) getCalculation();
        setTableKey(prev => prev + 1);
    }, [JSON.stringify(formData.outlets), selectedSubscription, JSON.stringify(formData.additional_support), formData.paymentMethod]);

    useEffect(() => {
        const isAutodebit = formData.paymentMethod === PAYMENT_METHOD.AUTO_DEBIT;
        const remap = formData.outlets.map(r => ({
            ...r,
            _isAutodebit: isAutodebit,
            claim_qty: isAutodebit && !isUpgrade ? 1 : r.claim_qty,
        }));
        setFormData(prev => ({ ...prev, outlets: remap }));
    }, [formData.paymentMethod]);

    useEffect(() => {
        if (formData.supportId && formData.package && openDialog && supportCategory[formData.package].length) {
            setTableKey(prev => prev + 1);
            fetchSubscriptionDetail();
        }
    }, [formData.supportId, formData.package, openDialog, supportCategory, formData.paymentMethod]);

    return (
        <PageDialog open={openDialog} onOpenChange={() => { setOpenDialog(false); setIsUpgrade(false); }}>
            <PageDialogTitle>
                {LANG_DATA.RENEWAL_TITLE}
            </PageDialogTitle>
            <PageDialogContent wrapperSize="lg">
                <div id="a-dialog" />
                {(isDiscountCC && formData.discountCC > 0 && showBannerCC) && (
                    <Banner css={{ marginBottom: '$cozy' }} variant={formData.paymentMethod === PAYMENT_METHOD.AUTO_DEBIT ? 'info' : 'warning'} onRemove={() => setShowBannerCC(false)} bannerBlock>
                        <Flex justify="between" css={{ width: '$full' }}>
                            <Text color="primary">
                                <TransComponent i18nKey={LANG_KEY.BANNER_DISCOUNT_CC}>
                                    {{ discountValue: formData.discount }}
                                    {{ discountNominal: formatPrice(formData.discountCC) }}
                                </TransComponent>
                            </Text>
                            <BannerClose />
                        </Flex>
                    </Banner>
                )}
                <Box css={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '$cozy',
                        '@lg': {
                            display: 'grid', gridTemplateColumns: '2fr 1fr',
                        },
                    }}
                >
                    <Paper>
                        <Heading heading="sectionSubTitle">Outlet</Heading>
                        <Text>{LANG_DATA.RENEWAL_DESCRIPTION}</Text>
                        {(loading.table || !selectedSubscription) ? (
                            <Skeleton />
                        ) : (
                            <Box css={{ marginTop: '$cozy' }}>
                                <Flex justify="between" mb={4} direction={isMobile ? 'column' : 'row'} gap={3}>
                                    <InputSearchbox
                                        value={search}
                                        onChange={(value) => {
                                            setSearch(value);
                                            setTableKey(prev => prev + 1);
                                        }}
                                        css={{ flexBasis: '30%' }}
                                    />
                                    <Flex gap={4} align="center" css={{ flexBasis: '40%', width: '100%' }}>
                                        <InputSelect
                                            size="sm"
                                            option={terminOptions}
                                            value={terminOptions.find(x => x.value === formData.package)}
                                            onChange={e => setFormData(prev => ({ ...prev, package: e.value }))}
                                            css={{
                                                flexBasis: '20%',
                                                flex: 1,
                                            }}
                                        />
                                    </Flex>
                                </Flex>
                                <OutletTable
                                    key={`outlet-${tableKey}`}
                                    search={search}
                                    selectedSubscription={selectedSubscription}
                                    selectedSubscriptionDetail={({ ...selectedSubcriptionDetail, data: formData.outlets })}
                                    isAutodebit={formData.paymentMethod === PAYMENT_METHOD.AUTO_DEBIT}
                                    onChangeTable={handleChangeTable}
                                    isUpgrade={isUpgrade}
                                    isUpdateBill={!!formData.referenceNumber}
                                    isRenewal
                                />
                            </Box>
                        )}
                        <Box
                            css={{
                                margin: '$cozy 0px',
                                display: 'grid',
                                gap: '$cozy',
                                gridTemplateRows: 'repeat(2, 1fr)',
                                gridTemplateColumns: 'unset',
                                '@lg': {
                                    gridTemplateRows: 'unset',
                                    gridTemplateColumns: 'repeat(2, 1fr)',
                                },
                            }}
                        >
                            {upgradeAccountList.map(subcription => (
                                <Flex
                                    direction="column"
                                    key={subcription.support_id}
                                    gap={4}
                                    css={{
                                        border: '1px solid $bgBorder',
                                        borderRadius: '$lg',
                                        padding: '$compact',
                                        position: 'relative',
                                        zIndex: 2,
                                        overflow: 'hidden',
                                        '&::before': {
                                            content: '',
                                            top: -50,
                                            width: '$full',
                                            height: '$full',
                                            position: 'absolute',
                                            margin: '-$compact',
                                            backgroundImage: `url(${CardBg})`,
                                            transform: 'scaleX(-1)',
                                            backgroundPosition: 'top right',
                                            backgroundSize: 'contain',
                                            backgroundRepeat: 'no-repeat',
                                            zIndex: -1,
                                        },
                                    }}
                                >
                                    <Flex gap={3} align="center">
                                        <Heading heading="sectionTitle">
                                            {capitalizeFirstLetter(subcription.support_name)}
                                        </Heading>
                                        {subcription.support_is_populer
                                            && (
                                            <TagStatus type="pending" css={{ padding: '4px 8px' }}>
                                                {LANG_DATA.POPULAR}
                                            </TagStatus>
                                        )}
                                    </Flex>
                                    <Text>{LANG_DATA.FEATURES_DESCRIPTION_RENEWAL[getSubscriptionName(subcription.support_name)]}</Text>
                                    <Flex justify="between" align="end">
                                        <Text color="primary">
                                            <strong>{formatPrice(subcription.support_monthly_price)}</strong>
                                            /
                                            {LANG_DATA.ADDITIONAL_SECTION_MONTH}
                                        </Text>
                                        <Button type="button" size="sm" onClick={() => alertChangeSubscriptionRef.current.openModalAlert(subcription)}>Upgrade</Button>
                                    </Flex>
                                </Flex>
                            ))}
                        </Box>
                        {!formData.referenceNumber && (
                            <SupportForm
                                key={formData.supportId}
                                supportProducts={additionalSupportProduct}
                                supportServices={filteredAdditionalSupportService}
                                formData={formData}
                                setFormData={setFormData}
                                isRenewal
                            />
                        )}
                    </Paper>
                    <Paper css={{ position: 'sticky', top: '$cozy', height: 'fit-content' }}>
                        <Flex
                            direction="column"
                            gap={3}
                        >
                            <Heading heading="sectionSubTitle">{LANG_DATA.MAIN_SUBSCRIPTION_PACKAGE_PAYMENT_METHOD}</Heading>
                            <InputSelect
                                option={paymentMethodOptions}
                                value={paymentMethodOptions.find(x => x.value === formData.paymentMethod)}
                                onChange={e => setFormData(prev => ({ ...prev, paymentMethod: e.value }))}
                            />
                            <Heading heading="sectionSubTitle">{LANG_DATA.RENEWAL_SUMMARY}</Heading>
                            {formData.data_transaction.map((trans, idx) => (
                                <React.Fragment>
                                    {trans.key === 'Biaya Langganan Tambahan' && formData.additional_support.filter(add => add.outlets.length).map(additional => (
                                            <Box css={{ color: '$textPrimary !important' }}>
                                                <Flex justify="between" css={{ color: '$textPrimary !important' }}>
                                                    <TransactionItem label={additional.support_name} value={0} hideTotal />
                                                </Flex>
                                                <strong>{`(${additional.outlets.length} outlet)`}</strong>
                                            </Box>
                                        ))}
                                    <Box css={{ color: '$textPrimary !important' }}>
                                        <Flex justify="between">
                                            <TransactionItem label={trans.key} value={trans.value} />
                                        </Flex>
                                        {idx === 0 && (
                                            <Flex align="center" gap={3}>
                                                <strong>{`(${formData.outlets.length} outlet)`}</strong>
                                                <Tooltip
                                                    label={(
                                                        <ul style={{ paddingLeft: '16px' }}>
                                                            {formData.outlets.map((outlet) => {
                                                                const isYearly = formData.package === SUBSCRIPTION_PACKAGE.YEARLY;
                                                                const { prorateDate, extendExpDate } = predictExpDateProrate(outlet, isYearly, LANG_DATA, formData.paymentMethod);
                                                                
                                                                return (
                                                                    <li key={outlet._key}>
                                                                        <strong>{outlet.nama_cabang}</strong>
                                                                        {outlet._isNewRow && prorateDate !=='' && (
                                                                            <Text css={{ fontSize: 12 }}>
                                                                                {prorateDate}
                                                                            </Text>
                                                                        )}
                                                                        {outlet._isNewRow && extendExpDate !=='' && (
                                                                            <Text css={{ fontSize: 12 }}>
                                                                                {extendExpDate}
                                                                            </Text>
                                                                        )}
                                                                    </li>
                                                                );
                                                            })}
                                                        </ul>
                                                    )}
                                                    side="right"
                                                    withClick={isMobile}
                                                >
                                                    <CircleInfoOutline />
                                                </Tooltip>
                                            </Flex>
                                        )}
                                    </Box>
                                </React.Fragment>
                            ))}
                            <Separator />
                            <Flex justify="between">
                                <TransactionItem label="Total" value={total} bold />
                            </Flex>
                            <Button type="button" block onClick={handleBuy} disabled={formData.outlets < 1}>{LANG_DATA.RENEWAL_UPDATE_BILLS}</Button>
                        </Flex>
                    </Paper>
                </Box>
                {openHasGroup && (
                    <ModalAutodebitAlreadyHasGroup open={openHasGroup} onOpenChange={setOpenHasGroup} detail={recurringGroupList} paymentMethod={formData.paymentMethod} onConfirm={() => handleBuy({ bypass: true })} />
                )}
                {openVirtualAccount && (<ModalVirtualAccount open={openVirtualAccount} onOpenChange={setOpenVirtualAccount} onClick={nonAutodebitPayment} isMobile={isMobile} />)}
                {openAutodebit && (
                    <ModalAutodebit onConfirm={autodebitPayment} open={openAutodebit} onOpenChange={setOpenAutodebit} price={total} orderID={formData.orderId} cardError={cardError} isTransacting={isTransacting} />
                )}
                <AlertChangeSubscription ref={alertChangeSubscriptionRef} onConfirm={handleUpgrade} />
                <ModalDialog open={openCreditCardPayment} onOpenChange={setOpenCreditCardPayment}>
                    <ModalDialog.Content>
                        <iframe title="title" frameBorder="0" style={{ height: '80vh', width: '100%' }} src={paymentURL} />
                    </ModalDialog.Content>
                </ModalDialog>
            </PageDialogContent>
        </PageDialog>
    );
});

export default RenewalDialog;
