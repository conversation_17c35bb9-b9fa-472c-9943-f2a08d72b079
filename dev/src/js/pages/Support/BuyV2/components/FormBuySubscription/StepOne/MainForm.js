import React, { useEffect, useMemo, useState, useRef, useContext } from 'react';
import PropTypes from 'prop-types';
import {
    Paper,
    Box,
    Heading,
    FormGroup,
    FormLabel,
    InputSelect,
    InputRadioGroup,
    InputRadio,
    Text,
    Separator,
    Flex,
    Button,
    Tag,
    Skeleton,
    InputSearchbox,
    AlertDialogFunction,
    Banner,
    BannerClose,
} from '@majoo-ui/react';
import { PlusOutline } from '@majoo-ui/icons';
import { isNumber } from 'lodash';
import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import usePrevious from '~/utils/usePrevious';
import { ACCOUNT_TYPE, PAYMENT_METHOD, PAYMENT_METHOD_OPTIONS, SUBSCRIPTION_PACKAGE } from '../../../enum';
import { formatPrice } from '../../../utils';
import { getOutletHasSupportV4 } from '../../../../../../data/supports';
import { catchError } from '../../../../../../utils/helper';
import ModalAddOutlet from '../../ModalAddOutlet';
import ModalDuration from './ModalDuration';
import OutletTable from '../../OutletTable';
import { useTranslationHook } from '../../../lang.utils';
import AlertChangeSubscription from '../../AlertChangeSubscription';
import SupportContext from '../../../context/SupportContext';

yup.addMethod(yup.object, 'testUnique', function yupTest(propertyName, message) {
    return this.test('unique', message, function testUniqe(value) {
        if (!value || !value[propertyName]) {
            return true;
        }
        if (
            this.parent
                .filter(v => v !== value)
                .some(v => v[propertyName].toLowerCase() === value[propertyName].toLowerCase())
        ) {
            throw this.createError({
                path: `${this.path}.${propertyName}`,
                message,
            });
        }
        return true;
    });
});

const StepOne = ({
    formData, supportCategory, isTrialPromo, addToast, isDiscountCC, setFormData, onSubmit,
    openCompareDialog, isMobile, loading, setLoading, isTrial,
    onCloseFormBuySubscription,
}) => {
    const { LANG_DATA, ready, LANG_KEY, TransComponent, t } = useTranslationHook();
    const { currentSubscriptionId, renewalDialogRef } = useContext(SupportContext);
    const { control, clearErrors, setValue, watch, handleSubmit, reset } = useForm({
        defaultValues: {
            supportId: formData.supportId,
            package: formData.package,
            paymentMethod: formData.paymentMethod,
            outlets: formData.outlets,
        },
        resolver: yupResolver(
            yup.object({
                supportId: yup.number(),
                package: yup.string(),
                paymentMethod: yup.string(),
                outlets: yup
                    .array()
                    .of(
                        yup
                            .object()
                            .shape({
                                nama_cabang: yup.string().required(LANG_DATA.FORM_ERROR.MAIN.OUTLET_NAME),
                            })
                            .testUnique('nama_cabang', LANG_DATA.FORM_ERROR.MAIN.DUPLICATE_OUTLET_NAME),
                    )
                    .min(1, LANG_DATA.FORM_ERROR.MAIN.MINIMUM_OUTLET),
            }),
        ),
    });
    const [selectedSubcriptionDetail, setSelectedSubscriptionDetail] = useState({ data: [] });
    const [modal, setModal] = useState({
        duration: false,
        add: false,
    });
    const [tableKey, setTableKey] = useState(0);
    const [search, setSearch] = useState('');
    const [selectedOutlets, setSelectedOutlets] = useState([]);
    const [dataOpt, setDataOpt] = useState('DELETE');
    const [showBannerCC, setShowBannerCC] = useState(true);
    const [isUpgrade, setIsUpgrade] = useState(false);
    const [terminOptions, setTerminOptions] = useState([]);
    const { package: terminPackage, supportId, paymentMethod, outlets } = watch();
    const alertChangeSubscriptionRef = useRef(null);

    const prevSupportId = usePrevious(supportId);

    const packageOptions = useMemo(() => {
        const data = supportCategory[terminPackage]
            .filter(x => x.support_name !== ACCOUNT_TYPE.PRIMEPLUS)
            .map(support => ({
                ...support,
                value: support.support_id,
                name: support.support_name,
            }));
        return data;
    }, [terminPackage]);

    const fetchTerminOptions = async () => {
        let hargaTahunan = 0,
            hargaBulanan = 0;
        try {
            const isAutoDebit = paymentMethod === PAYMENT_METHOD.AUTO_DEBIT;
            const resTahunan = await getOutletHasSupportV4({
                id_support: supportId,
                package: SUBSCRIPTION_PACKAGE.YEARLY,
                payment: isAutoDebit ? 2 : 1,
            });
            hargaTahunan = resTahunan.basic_price || 0;

            const resBulanan = await getOutletHasSupportV4({
                id_support: supportId,
                package: SUBSCRIPTION_PACKAGE.MONTHLY,
                payment: isAutoDebit ? 2 : 1,
            });
            hargaBulanan = resBulanan.basic_price || 0;

            setTerminOptions([
                {
                    value: SUBSCRIPTION_PACKAGE.YEARLY,
                    label: (
                        <Flex direction="column" gap={2}>
                            <Flex gap={2}>
                                <Text color="primary">
                                    <strong>1 {LANG_DATA.YEAR}</strong>{' '}
                                    {`(${formatPrice(hargaTahunan && hargaTahunan > 0 ? hargaTahunan / 12 : 0)}/${t(
                                        'month',
                                        'Bulan',
                                    ).toLowerCase()})`}
                                </Text>
                                {isTrialPromo && (
                                    <Tag type="success" css={{ '& > div:first-child': { display: 'none' } }}>
                                        {LANG_DATA.FREE_ONE_MONTH}
                                    </Tag>
                                )}
                            </Flex>
                            <Text color="secondary" css={{ fontSize: 12 }}>
                                {LANG_DATA.PAID_ANNUALY(
                                    formatPrice(hargaTahunan && hargaTahunan > 0 ? hargaTahunan : 0),
                                )}
                            </Text>
                        </Flex>
                    ),
                },
                {
                    value: SUBSCRIPTION_PACKAGE.MONTHLY,
                    label: (
                        <Flex direction="column" gap={2}>
                            <Flex gap={2}>
                                <Text color="primary">
                                    <strong>1 {LANG_DATA.MONTH}</strong>{' '}
                                    {`(${formatPrice(hargaBulanan && hargaBulanan > 0 ? hargaBulanan : 0)}/${t(
                                        'month',
                                        'Bulan',
                                    ).toLowerCase()})`}
                                </Text>
                                {isTrialPromo && (
                                    <Tag type="success" css={{ '& > div:first-child': { display: 'none' } }}>
                                        {LANG_DATA.DISCOUNT10}
                                    </Tag>
                                )}
                            </Flex>
                            <Text color="secondary" css={{ fontSize: 12 }}>
                                {LANG_DATA.PAID_MONTHLY}
                            </Text>
                        </Flex>
                    ),
                },
            ]);
        } catch (error) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(error),
                variant: 'failed',
            });
        }
    };

    const selectedSubscription = useMemo(() => {
        if (supportId && terminPackage) {
            return supportCategory[terminPackage].find(x => x.support_id === supportId);
        }
        return null;
    }, [supportId, terminPackage]);

    const selectedOptions = useMemo(
        () => [
            {
                value: 'UPDATE',
                name: LANG_DATA.MODAL_DURATION_TITLE,
                isDisabled: paymentMethod === PAYMENT_METHOD.AUTO_DEBIT,
            },
            { value: 'DELETE', name: LANG_DATA.DELETE_OUTLET, isDisabled: isUpgrade },
        ],
        [paymentMethod, isUpgrade],
    );

    const fetchSubscriptionDetail = async () => {
        setLoading(prev => ({ ...prev, table: true }));
        const isAutoDebit = paymentMethod === PAYMENT_METHOD.AUTO_DEBIT;
        const payload = {
            id_support: supportId,
            package: terminPackage,
            payment: isAutoDebit ? 2 : 1,
        };
        try {
            const res = await getOutletHasSupportV4(payload);
            if (!res.status) throw new Error(res.msg);
            const findSupport = supportCategory[terminPackage].find(x => x.support_id === supportId);
            const monthlyPrice = findSupport ? findSupport.support_monthly_price : 0;
            const annualPrice = findSupport ? findSupport.support_monthly_annual_price : 0;
            const remapResult = res.data.map(d => ({
                ...d,
                _isAutodebit: isAutoDebit,
                claim_qty: 1,
                _key: d.cabang_id,
                support_monthly_price: monthlyPrice,
                support_monthly_annual_price: annualPrice,
            }));
            const newOutlet = outlets
                .filter(x => x._isNewRow)
                .map(y => ({
                    ...y,
                    ...(isAutoDebit && { claim_qty: 1 }),
                    _isAutodebit: isAutoDebit,
                    prorate_price: res.prorate,
                    prorate_perday: res.prorate_perday,
                    basic_price: res.basic_price,
                    prorate_exp_date: res.prorate_exp_date,
                }));
            const final = {
                ...res,
                data: [...remapResult, ...newOutlet],
                support_monthly_price: monthlyPrice,
                support_monthly_annual_price: annualPrice,
            };
            const isHaveUpgradePrice = res.data.some(x => +x.selisih_harga > 0);
            setIsUpgrade(!isTrial && isHaveUpgradePrice);
            const tempOutlet =
                outlets.length && !isHaveUpgradePrice
                    ? outlets.map(x => {
                          const findDt = res.data.find(dt => dt.cabang_id === x.cabang_id);
                          if (findDt) {
                              return {
                                  ...x,
                                  ...findDt,
                                  ...(isAutoDebit && { claim_qty: 1 }),
                                  _isAutodebit: isAutoDebit,
                                  support_monthly_price: monthlyPrice,
                                  support_monthly_annual_price: annualPrice,
                              };
                          }
                          // outlet baru
                          return {
                              ...x,
                              ...(isAutoDebit && { claim_qty: 1 }),
                              _isAutodebit: isAutoDebit,
                              support_monthly_price: monthlyPrice,
                              support_monthly_annual_price: annualPrice,
                              prorate_price: final.prorate,
                              prorate_perday: final.prorate_perday,
                              basic_price: final.basic_price,
                              prorate_exp_date: final.prorate_exp_date,
                          };
                      })
                    : [...remapResult, ...newOutlet];
            setSelectedSubscriptionDetail(final);
            setTimeout(() => setValue('outlets', tempOutlet), 100);
            setDataOpt('DELETE');
        } catch (e) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            setTableKey(prev => prev + 1);

            setTimeout(() => {
                setLoading(prev => ({ ...prev, table: false }));
            }, 100);
        }
    };

    const handleChangeTable = async param => {
        const tempDt = param.table.map(x => x._key);
        clearErrors('outlets');
        setSelectedOutlets(prev => prev.filter(x => tempDt.includes(x)));
        setTableKey(prev => prev + 1);
        setValue('outlets', param.table);
    };

    const onError = err => {
        const errorIndex = err.outlets.map((_, index) => index).filter(x => isNumber(x));
        const injectedTable = outlets.map((r, index) => {
            const match = errorIndex.includes(index) && r._isNewRow;
            if (match) {
                Object.assign(r, { _isInvalid: true, invalidMessage: err.outlets[index].nama_cabang.message });
            } else {
                Object.assign(r, { _isInvalid: false, invalidMessage: '' });
            }
            return r;
        });
        setValue('outlets', injectedTable);
    };

    const handleApplyOption = () => {
        if (dataOpt === 'DELETE') {
            const dialog = new AlertDialogFunction({
                title: LANG_DATA.LABEL_DELETE_CONFIRMATION,
                description: LANG_DATA.MAIN_SUBSCRIPTION_DELETE_CONFIRMATION,
                dialogType: 'negative',
                labelConfirm: LANG_DATA.LABEL_CONTINUE,
                labelCancel: LANG_DATA.LABEL_CANCEL,
                onConfirm: () => {
                    setValue(
                        'outlets',
                        outlets.filter(x => !selectedOutlets.includes(x._key)),
                    );
                    setSelectedOutlets([]);
                },
            });
            dialog.show();
        } else if (dataOpt === 'UPDATE') {
            setModal(prev => ({ ...prev, duration: true }));
        }
    };

    const handleChangeDuration = async val => {
        const remapOutlets = outlets.map(dt => {
            let { claim_qty: claimQty } = dt;
            if (selectedOutlets.includes(dt._key)) {
                claimQty = val;
            }
            return {
                ...dt,
                claim_qty: claimQty,
            };
        });
        setValue('outlets', remapOutlets);
        setSelectedOutlets([]);
        setModal(prev => ({ ...prev, duration: false }));
    };

    const handleBuy = () => {
        onCloseFormBuySubscription();
        renewalDialogRef.current.handleOpenDialog([], formData.supportId, null, outlets.filter(x => x._isNewRow));
    };

    useEffect(() => {
        if (outlets.length) {
            setFormData(prev => ({
                ...prev,
                outlets,
            }));
        }
        if (outlets.length === 0) {
            setFormData(prev => ({
                ...prev,
                outlets,
                data_transaction: [],
            }));
        }
        setTableKey(prev => prev + 1);
    }, [JSON.stringify(outlets)]);

    // useEffect(() => {
    //     const isAutodebit = paymentMethod === PAYMENT_METHOD.AUTO_DEBIT;
    //     const remap = outlets.map(r => ({
    //         ...r,
    //         _isAutodebit: isAutodebit,
    //         claim_qty: isAutodebit ? 1 : r.claim_qty,
    //     }));
    //     setValue('outlets', remap);
    //     setDataOpt('DELETE');
    // }, [paymentMethod]);

    useEffect(() => {
        if (supportId && terminPackage) {
            setFormData(prev => ({
                ...prev,
                supportId,
                package: terminPackage,
            }));
            fetchSubscriptionDetail();
        }
    }, [supportId, terminPackage, paymentMethod]);

    useEffect(() => {
        if (
            supportId !== formData.supportId ||
            terminPackage !== formData.package ||
            paymentMethod !== formData.paymentMethod ||
            JSON.stringify(outlets) !== JSON.stringify(formData.outlets)
        ) {
            reset({
                supportId: formData.supportId,
                package: formData.package,
                paymentMethod: formData.paymentMethod,
                outlets: formData.outlets,
            });
        }
    }, [JSON.stringify(formData)]);

    useEffect(() => {
        const upgradePrice = selectedSubcriptionDetail.data.reduce((previous, current) => {
            if (!current._isNewRow) {
                return previous + Number(current.selisih_harga) * Number(current.jarak);
            }
            return previous;
        }, 0);
        setTimeout(
            () =>
                setFormData(prev => ({
                    ...prev,
                    upgradePrice,
                    upgradeOutlet: selectedSubcriptionDetail.data.map(outlet => ({
                        nama_cabang: outlet.nama_cabang,
                        jarak: outlet.jarak,
                        price: outlet.selisih_harga * outlet.jarak,
                    })),
                })),
            500,
        );
    }, [selectedSubcriptionDetail]);

    useEffect(() => {
        if (supportId && prevSupportId !== supportId) {
            fetchTerminOptions();
        }
    }, [supportId]);

    return (
        <Paper responsive>
            <div id="a-dialog" />
            <Box as="form" id="mainForm" onSubmit={handleSubmit(onSubmit, onError)}>
                <Heading as="h4" heading="pageTitle" color="primary">
                    {LANG_DATA.MAIN_SUBSCRIPTION_FORM_PACKAGE_TITLE}
                </Heading>
                <Flex direction="column" gap={4} mt={4}>
                    <Flex
                        gap={3}
                        css={{
                            flexDirection: 'column-reverse',
                            '@lg': {
                                flexDirection: 'row',
                            },
                        }}
                    >
                        <FormGroup responsive="input" css={{ width: '$full' }}>
                            <FormLabel>{LANG_DATA.MAIN_SUBSCRIPTION_PACKAGE_SELECT}</FormLabel>
                            <Controller
                                name="supportId"
                                control={control}
                                render={({ field: { value, onChange } }) => (
                                    <InputSelect
                                        value={packageOptions.find(x => x.value === value)}
                                        option={packageOptions}
                                        onChange={x => {
                                            onChange(x.value);
                                            setValue('additional_support', []);
                                            if (!isTrial) {
                                                alertChangeSubscriptionRef.current.openModalAlert(
                                                    packageOptions.find(opt => x.value === opt.value),
                                                );
                                            }
                                        }}
                                    />
                                )}
                            />
                        </FormGroup>
                        <Button
                            type="button"
                            onClick={openCompareDialog}
                            buttonType="ghost"
                            size="sm"
                            css={{ fontWeight: 400, width: '160px !important', textDecoration: 'underline' }}
                        >
                            {LANG_DATA.MAIN_SUBSCRIPTION_COMPARE}
                        </Button>
                    </Flex>
                    <FormGroup responsive="input">
                        <FormLabel>{LANG_DATA.MAIN_SUBSCRIPTION_PACKAGE_TERM}</FormLabel>
                        <Controller
                            name="package"
                            control={control}
                            render={({ field: { value, onChange } }) => (
                                <InputRadioGroup
                                    direction={isMobile ? 'column' : 'row'}
                                    value={value}
                                    gap={3}
                                    onValueChange={e => onChange(e)}
                                    css={{ width: '$full' }}
                                    outlined
                                >
                                    {terminOptions.map(termin => (
                                        <InputRadio
                                            value={termin.value}
                                            label={termin.label}
                                            css={{
                                                flex: 1,
                                                width: '100%',
                                                '& > div:first-child': { alignItems: 'baseline !important' },
                                            }}
                                        />
                                    ))}
                                </InputRadioGroup>
                            )}
                        />
                        {isDiscountCC && formData.discountCC > 0 && showBannerCC && (
                            <Banner
                                css={{ margin: '$cozy 0px' }}
                                variant={paymentMethod === PAYMENT_METHOD.AUTO_DEBIT ? 'info' : 'warning'}
                                onRemove={() => setShowBannerCC(false)}
                            >
                                <Flex justify="between" css={{ width: '$full' }}>
                                    <Text color="primary">
                                        <TransComponent i18nKey={LANG_KEY.BANNER_DISCOUNT_CC}>
                                            {{ discountValue: formData.discount }}
                                            {{ discountNominal: formatPrice(formData.discountCC) }}
                                        </TransComponent>
                                    </Text>
                                    <BannerClose />
                                </Flex>
                            </Banner>
                        )}
                    </FormGroup>
                    <FormGroup responsive="input">
                        <FormLabel>{LANG_DATA.MAIN_SUBSCRIPTION_PACKAGE_PAYMENT_METHOD}</FormLabel>
                        <Controller
                            name="paymentMethod"
                            control={control}
                            render={({ field: { value, onChange } }) => (
                                <InputRadioGroup
                                    direction="row"
                                    value={value}
                                    gap={3}
                                    onValueChange={e => {
                                        setFormData(prev => ({ ...prev, paymentMethod: e }));
                                        onChange(e);
                                    }}
                                    css={{
                                        width: '$full',
                                        display: 'grid !important',
                                        gridTemplateColumns: 'repeat(2, 1fr)',
                                        '@lg': { display: 'flex !important' },
                                    }}
                                    outlined
                                >
                                    {PAYMENT_METHOD_OPTIONS(LANG_DATA).map(method => (
                                        <InputRadio
                                            value={method.value}
                                            label={
                                                isDiscountCC && method.value === PAYMENT_METHOD.AUTO_DEBIT ? (
                                                    <Flex
                                                        gap={2}
                                                        align="center"
                                                        css={{ '@lg': { width: 'max-content' } }}
                                                    >
                                                        <Text color="primary">{method.label}</Text>
                                                        <Tag
                                                            type="success"
                                                            css={{ '& > div:first-child': { display: 'none' } }}
                                                        >
                                                            {LANG_DATA.DISCOUNT25}
                                                        </Tag>
                                                    </Flex>
                                                ) : (
                                                    method.label
                                                )
                                            }
                                            css={{ flex: 1, width: '100%' }}
                                        />
                                    ))}
                                </InputRadioGroup>
                            )}
                        />
                        {isDiscountCC && paymentMethod === PAYMENT_METHOD.AUTO_DEBIT && (
                            <Text css={{ '&::before': { content: '*', color: '$textRed' } }}>
                                {LANG_DATA.MAIN_SUBSCRIPTION_FORM_CREDIT_CARD_PROMO}
                            </Text>
                        )}
                    </FormGroup>
                    <Separator />
                    <Heading heading="sectionTitle">Outlet</Heading>
                    <Separator />
                    {loading.table || !selectedSubscription ? (
                        <Skeleton />
                    ) : (
                        <Box>
                            <Flex
                                justify="between"
                                mb={4}
                                css={{
                                    flexDirection: 'column',
                                    '@lg': { flexDirection: 'row' },
                                }}
                                gap={3}
                            >
                                <InputSearchbox
                                    value={search}
                                    onChange={value => {
                                        setSearch(value);
                                        setTableKey(prev => prev + 1);
                                    }}
                                    css={{ flexBasis: '30%' }}
                                />
                                <Flex gap={4} align="center" css={{ flexBasis: '40%', width: '100%' }}>
                                    <InputSelect
                                        size="sm"
                                        option={selectedOptions}
                                        value={selectedOptions.find(x => x.value === dataOpt)}
                                        onChange={e => setDataOpt(e.value)}
                                        disabled={selectedOutlets.length === 0}
                                        css={{
                                            flexBasis: '20%',
                                            flex: 1,
                                        }}
                                    />
                                    <Button
                                        type="button"
                                        onClick={handleApplyOption}
                                        size="sm"
                                        disabled={selectedOutlets.length === 0}
                                    >
                                        {LANG_DATA.LABEL_APPLY}
                                    </Button>
                                </Flex>
                            </Flex>
                            <OutletTable
                                key={`outlet-${tableKey}`}
                                search={search}
                                selectedSubscription={selectedSubscription}
                                selectedSubscriptionDetail={{ ...selectedSubcriptionDetail, data: outlets }}
                                isAutodebit={paymentMethod === PAYMENT_METHOD.AUTO_DEBIT}
                                onChangeTable={handleChangeTable}
                                selectedOutlets={selectedOutlets}
                                onChangeSelected={setSelectedOutlets}
                                isUpgrade={isUpgrade}
                                isTrialPromo={isTrialPromo}
                            />
                            <Button
                                css={{ width: '100%', mt: '$spacing-05', '@md': { mt: '$spacing-03' } }}
                                type="button"
                                onClick={() => setModal(prev => ({ ...prev, add: true }))}
                                size="md"
                                buttonType="secondary"
                                leftIcon={<PlusOutline color="currentColor" />}
                            >
                                {LANG_DATA.ACTIVE_SECTION_ADD_TITLE}
                            </Button>
                        </Box>
                    )}
                </Flex>
                {modal.add && (
                    <ModalAddOutlet
                        open={modal.add}
                        paymentMethod={paymentMethod}
                        selectedSubcriptionDetail={selectedSubcriptionDetail}
                        onOpenChange={() => setModal(prev => ({ ...prev, add: false }))}
                        listOutlets={outlets}
                        onSave={newOutlets => {
                            setValue('outlets', [...outlets, ...newOutlets]);
                            setModal(prev => ({ ...prev, add: false }));
                        }}
                    />
                )}
                {modal.duration && (
                    <ModalDuration
                        open={modal.duration}
                        onOpenChange={() => setModal(prev => ({ ...prev, duration: false }))}
                        onSave={handleChangeDuration}
                    />
                )}
                <AlertChangeSubscription
                    ref={alertChangeSubscriptionRef}
                    onConfirm={handleBuy}
                    onCancel={() => {
                        setValue('supportId', currentSubscriptionId);
                    }}
                />
            </Box>
        </Paper>
    );
};

StepOne.propTypes = {
    formData: PropTypes.shape({
        supportId: PropTypes.string,
        package: PropTypes.string,
        paymentMethod: PropTypes.string,
        outlets: PropTypes.arrayOf(),
    }).isRequired,
    setFormData: PropTypes.func.isRequired,
    supportCategory: PropTypes.arrayOf().isRequired,
    isTrialPromo: PropTypes.bool,
    addToast: PropTypes.func.isRequired,
    onSubmit: PropTypes.func,
    openCompareDialog: PropTypes.func,
    isMobile: PropTypes.bool,
    isTrial: PropTypes.bool,
};

StepOne.defaultProps = {
    isTrialPromo: false,
    onSubmit: () => {},
    openCompareDialog: () => {},
    isMobile: false,
    isTrial: false,
};

export default StepOne;
