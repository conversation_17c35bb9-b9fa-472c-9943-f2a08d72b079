import React, {
    useContext, useEffect, useMemo, useState,
} from 'react';
import PropTypes from 'prop-types';
import {
    ModalDialog, DialogClose, Button, InputSelectTag,
    Flex, Banner, FormGroup, FormLabel, Box,
    BannerDescription, InputCheckbox, InputCounter, Separator, Heading, FormHelper,
} from '@majoo-ui/react';
import { CircleInfoFilled } from '@majoo-ui/icons';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import _ from 'lodash';
import SupportContext from '../../../context/SupportContext';
import { getProvince } from '../../../../../../data/locations';
import { catchError } from '../../../../../../utils/helper';
import ServiceForm from './ServiceForm';
import { SUPPORT_ALIAS } from '../../../enum';

const ModalAddSupport = ({
    open, onOpenChange, support, onSave, additionalPriceOptions,
    savedData, newOutlets, isYearly,
}) => {
    const {
        LANG_DATA, outletOptions, accountInfo, mainOutletDetail, addToast,
    } = useContext(SupportContext);
    const { alias } = support;
    const {
        control, formState: { errors },
        setValue, watch, register,
        handleSubmit, clearErrors,
        reset, getValues,
    } = useForm({
        defaultValues: {
            qty: 1,
            sameInfo: alias === SUPPORT_ALIAS.ECOMMERCE || alias === SUPPORT_ALIAS.FOOD_ORDER || alias === SUPPORT_ALIAS.WHATSAPP_LAPORAN,
            outlets: [],
        },
        resolver: yupResolver(yup.object({
            qty: yup.number().required('Mohon pilih outlet'),
            sameInfo: yup.boolean(),
            outlets: yup.array().of(yup.object().shape({
                cabang_id: yup.string(),
                sameInfo: yup.bool(),
                cabang_name: yup.string(),
                name: yup.string().when('alias', {
                    is: () => alias !== SUPPORT_ALIAS.ECOMMERCE && alias !== SUPPORT_ALIAS.WHATSAPP_LAPORAN,
                    then: yup.string().test({
                        name: 'name',
                        exclusive: true,
                        message: LANG_DATA.FORM_ERROR.ADDITIONAL.NAME,
                        test: (value, context) => !!value || (context.parent.sameInfo && context.parent.index !== 0),
                    }),
                }),
                email: yup.string().email(LANG_DATA.FORM_ERROR.ADDITIONAL.EMAIL_FORMAT).test({
                    name: 'email',
                    exclusive: true,
                    message: LANG_DATA.FORM_ERROR.ADDITIONAL.EMAIL,
                    test: (value, context) => !!value || (context.parent.sameInfo && context.parent.index !== 0),
                }),
                phone_number: yup.string().when('alias', {
                    is: () => ![SUPPORT_ALIAS.ECOMMERCE, SUPPORT_ALIAS.FOOD_ORDER, SUPPORT_ALIAS.WHATSAPP_LAPORAN].includes(alias),
                    then: yup.string().test({
                        name: 'phone_number',
                        exclusive: true,
                        message: LANG_DATA.FORM_ERROR.ADDITIONAL.PHONE_NUMBER,
                        test: (value, context) => !!value || (context.parent.sameInfo && context.parent.index !== 0),
                    }),
                }),
                address: yup.string().when('alias', {
                    is: () => alias !== SUPPORT_ALIAS.ECOMMERCE && alias !== SUPPORT_ALIAS.WHATSAPP_LAPORAN,
                    then: yup.string().test({
                        name: 'address',
                        exclusive: true,
                        message: LANG_DATA.FORM_ERROR.ADDITIONAL.ADDRESS,
                        test: (value, context) => !!value || (context.parent.sameInfo && context.parent.index !== 0),
                    }),
                }),
                province_id: yup.string().when('alias', {
                    is: () => alias !== SUPPORT_ALIAS.ECOMMERCE && alias !== SUPPORT_ALIAS.WHATSAPP_LAPORAN,
                    then: yup.string().test({
                        name: 'province_id',
                        exclusive: true,
                        message: LANG_DATA.FORM_ERROR.ADDITIONAL.PROVINCE,
                        test: (value, context) => !!value || (context.parent.sameInfo && context.parent.index !== 0),
                    }),
                }),
                city_id: yup.string().when('alias', {
                    is: () => alias !== SUPPORT_ALIAS.ECOMMERCE && alias !== SUPPORT_ALIAS.WHATSAPP_LAPORAN,
                    then: yup.string().test({
                        name: 'city_id',
                        exclusive: true,
                        message: LANG_DATA.FORM_ERROR.ADDITIONAL.CITY,
                        test: (value, context) => !!value || (context.parent.sameInfo && context.parent.index !== 0),
                    }),
                }),
                acomodation_fee: yup.number(),
            })).min(1, LANG_DATA.FORM_ERROR.MAIN.MINIMUM_OUTLET),
        })),
    });

    const [provinceOptions, setProvinceOptions] = useState([]);
    const { outlets, sameInfo, qty } = watch();

    const filteredOutlets = useMemo(() => {
        if (sameInfo) return outlets.filter((x, idx) => idx === 0);
        return outlets;
    }, [outlets, sameInfo]);

    const calculateAccomodation = (cityID) => {
        const currentPriceOptions = additionalPriceOptions[support.support_id];
        if (!currentPriceOptions) return 0;
        const cityPrice = currentPriceOptions.find(p => p.city_id === Number(cityID));
        if (!cityPrice) return 0;
        return cityPrice.price;
    };

    const handleSelectOutlet = (data) => {
        clearErrors();
        let tempOutletData = [];
        tempOutletData = outlets;
        tempOutletData = tempOutletData.filter(x => data.includes(x.cabang_id));
        const existingOutlets = outlets.map(x => x.cabang_id);
        data.filter(x => !existingOutlets.includes(x)).map((x) => {
            const outletInfo = [...outletOptions, ...newOutlets].find(opt => opt.id === x);
            const newOutlet = {
                alias,
                sameInfo,
                cabang_id: x,
                cabang_name: outletInfo.name || '',
                name: accountInfo.user_name || '',
                email: accountInfo.user_email || '',
                phone_number: mainOutletDetail.cabang_notlp || '',
                address: mainOutletDetail.cabang_address || '',
                city_id: mainOutletDetail.id_kota || '',
                province_id: mainOutletDetail.id_provinsi || '',
                acomodation_fee: calculateAccomodation(mainOutletDetail.id_kota) || 0,
            };
            tempOutletData.push(newOutlet);
            return x;
        });

        setValue('outlets', tempOutletData.map((x, index) => ({ ...x, index })));
    };

    const handleSave = () => {
        let newAdditionalData = {
            id: support.support_id,
            support_name: support.support_name,
            alias: support.alias,
            isProduct: support.isProduct,
            qty,
            outlets: [],
        };
        const price = {
            additional_fee: 0,
            amount: support.discount_price,
        };
        outlets.forEach((x) => {
            const dataOutlet = {
                id: x.cabang_id,
                cabang_name: x.cabang_name,
                qty,
                ...(!sameInfo && ({
                    ...x,
                    ...price,
                })),
            };
            newAdditionalData.outlets.push(dataOutlet);
        });
        if (sameInfo) newAdditionalData = Object.assign(newAdditionalData, { ...price, ...outlets[0] });
        onSave(newAdditionalData);
    };

    const fetchProvinces = async () => {
        try {
            const response = await getProvince({ id_negara: '107' });
            setProvinceOptions(
                response.data.map(item => ({
                value: item.id_provinsi,
                name: item.provinsi_name,
                })),
            );
        } catch (e) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(e),
                variant: 'failed',
            });
        }
    };

    useEffect(() => {
        fetchProvinces();
    }, []);

    useEffect(() => {
        if (savedData) {
            const isSame = !!_.get(savedData, 'name');
            const tempOutlets = savedData.outlets.map((outlet) => {
                const dataFrom = isSame ? savedData : outlet;
                const {
                    name, email, phone_number: phoneNumber, address,
                    city_id: cityId, province_id: provinceId,
                    acomodation_fee: accommodationFee,
                } = dataFrom;
                return {
                    alias,
                    cabang_id: outlet.id,
                    cabang_name: outlet.cabang_name,
                    name,
                    email,
                    phone_number: phoneNumber,
                    address,
                    city_id: cityId,
                    province_id: provinceId,
                    acomodation_fee: accommodationFee,
                };
            });
            reset({
                qty: savedData.qty,
                sameInfo: isSame,
                outlets: tempOutlets,
            });
        }
    }, [savedData]);

    return (
        <ModalDialog open={open} onOpenChange={onOpenChange} size="xl">
            <ModalDialog.Title>
                {support.support_name}
            </ModalDialog.Title>
            <ModalDialog.Content css={{ overflow: 'auto' }}>
                <Box
                    as="form"
                    id="addAdditionalForm"
                    css={{
                        maxHeight: '80vh',
                    }}
                >
                    <Flex direction="column" gap={5}>
                        <Banner variant="info" bannerBlock>
                            <Flex direction="row" gap={4} align="start" wrap="wrap" css={{ width: '$full' }}>
                                <CircleInfoFilled />
                                <BannerDescription css={{ whiteSpace: 'unset' }}>
                                    {support.isProduct ? LANG_DATA.MAIN_SUBSCRIPTION_SUPPORT_BANNER_PRODUCT : LANG_DATA.MAIN_SUBSCRIPTION_SUPPORT_BANNER_SERVICE}
                                </BannerDescription>
                            </Flex>
                        </Banner>
                        <FormGroup responsive="input">
                            <FormLabel>{support.isProduct ? LANG_DATA.ADDITIONAL_SUBSCRIPTION_QTY_PRODUCT : LANG_DATA.ADDITIONAL_SUBSCRIPTION_QTY_SERVICE}</FormLabel>
                            <Flex gap={5} align="center">
                                <Controller
                                    name="qty"
                                    control={control}
                                    render={({ field: { value, onChange } }) => (
                                        <InputCounter
                                            disabledDecrement={value <= 1}
                                            value={value}
                                            onChange={(n) => {
                                                onChange(n);
                                            }}
                                            size="md"
                                            css={{ flexBasis: '50%' }}
                                        />
                                    )}
                                />
                                {support.isProduct && !isYearly && LANG_DATA.MONTH}
                                {support.isProduct && isYearly && LANG_DATA.YEAR}
                            </Flex>
                        </FormGroup>
                        <FormGroup responsive="input">
                            <FormLabel>{`${LANG_DATA.LABEL_CHOOSE} Outlet`}</FormLabel>
                            <Controller
                                name="outlets"
                                control={control}
                                render={({ field: { value } }) => (
                                    <InputSelectTag
                                        placement="top"
                                        value={value.map(x => x.cabang_id)}
                                        option={[...newOutlets, ...outletOptions]}
                                        onChange={handleSelectOutlet}
                                        isInvalid={!!_.get(errors, 'outlets.message')}
                                    />
                                )}
                            />
                            <FormHelper errorMessage={_.get(errors, 'outlets.message')} />
                        </FormGroup>
                        <Separator />
                        {support.alias !== SUPPORT_ALIAS.ECOMMERCE && alias !== SUPPORT_ALIAS.WHATSAPP_LAPORAN && filteredOutlets.map((outlet, idxOutlet) => (
                            <Flex direction="column" key={outlet.cabang_id} gap={4} css={{ padding: '$cozy 0px' }}>
                                <Box>
                                    <Heading heading="sectionTitle">
                                        Info Kontak
                                        {' '}
                                        {outlet.cabang_name}
                                    </Heading>
                                    {idxOutlet === 0 && alias !== SUPPORT_ALIAS.FOOD_ORDER && (
                                        <Controller
                                            name="sameInfo"
                                            control={control}
                                            render={({ field: { value, onChange } }) => (
                                                <InputCheckbox
                                                    checked={value}
                                                    label="Gunakan informasi kontak outlet yang sama untuk semua outlet"
                                                    onCheckedChange={(val) => {
                                                        onChange(val);
                                                        setValue('outlets', outlets.map(x => ({ ...x, sameInfo: val })));
                                                    }}
                                                />
                                            )}
                                        />
                                    )}
                                </Box>
                                <ServiceForm
                                    data={outlet}
                                    register={register}
                                    control={control}
                                    errors={errors}
                                    setValue={setValue}
                                    getValues={getValues}
                                    idx={idxOutlet}
                                    provinceOptions={provinceOptions}
                                    calculateAccomodation={calculateAccomodation}
                                />
                            </Flex>
                        ))}
                    </Flex>
                </Box>
            </ModalDialog.Content>
            <ModalDialog.Footer css={{ gap: '$compact' }}>
                <DialogClose asChild>
                    <Button buttonType="ghost" size="sm">{LANG_DATA.LABEL_CANCEL}</Button>
                </DialogClose>
                <Button form="addAdditionalForm" size="sm" onClick={handleSubmit(handleSave)}>{LANG_DATA.LABEL_SAVE}</Button>
            </ModalDialog.Footer>
        </ModalDialog>
    );
};

ModalAddSupport.propTypes = {
    open: PropTypes.bool.isRequired,
    onOpenChange: PropTypes.func.isRequired,
    support: PropTypes.shape().isRequired,
    onSave: PropTypes.func,
    additionalPriceOptions: PropTypes.shape(),
    savedData: PropTypes.shape(),
    newOutlets: PropTypes.arrayOf(),
    isYearly: PropTypes.bool,
};

ModalAddSupport.defaultProps = {
    onSave: () => { },
    additionalPriceOptions: [],
    savedData: null,
    newOutlets: [],
    isYearly: false,
};

export default ModalAddSupport;
