import React from 'react';
import PropTypes from 'prop-types';
import {
    Flex, Box, Heading, Button, Text, Banner,
    TagStatus, TagList, Separator,
} from '@majoo-ui/react';
import { StarFilled, CircleInfoOutline } from '@majoo-ui/icons';
import { useTranslationHook } from '../../../lang.utils';
import { formatPrice } from '../../../utils';
import { SUPPORT_ALIAS } from '../../../enum';
import { colors } from '../../../../../../stitches.config';
import CardBg from '../../../assets/bg-card.png';

const AdditionalCard = ({
    support, isProduct, onClick, savedData,
    onChangeData, isRenewal,
}) => {
    const {
        support_name: supportName,
        support_monthly_annual_price: supportPrice,
        alias,
    } = support;
    const { LANG_DATA } = useTranslationHook();

    const handleRemoveOutlet = (param) => {
        const tempData = Object.assign({}, savedData);
        tempData.outlets = tempData.outlets.filter(x => x.id !== param.id);
        onChangeData(tempData);
    };

    return (
        <Box
            css={{
                width: '$full',
                display: 'flex',
                flexDirection: 'column',
                gap: '$compact',
                padding: '$cozy',
                borderRadius: '$lg',
                boxShadow: '0px 4px 10px 0px rgba(0, 0, 0, 0.05), 0px 2px 4px 0px rgba(0, 0, 0, 0.05), 0px 1px 1px 0px rgba(0, 0, 0, 0.07)',
                ...(!isProduct && {
                    borderLeft: 'solid 4px $bgGreen',
                    backgroundImage: `url(${CardBg})`,
                    backgroundPosition: 'bottom right',
                    backgroundSize: 'contain',
                    backgroundRepeat: 'no-repeat',
                    '@lg': { backgroundPosition: 'top right' },
                }),
                ...(isRenewal && {
                    boxShadow: 'none',
                    border: '1px solid $bgBorder',
                }),
            }}
        >
            <Flex
                justify="between"
                css={{
                    width: '$full',
                    flexDirection: 'column',
                    alignItems: 'start',
                    gap: '$cozy',
                    '@lg': {
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: 8,
                    },
                }}
            >
                <Flex direction="column" gap={3} css={{ '@lg': { flexBasis: '65%' } }}>
                    <Flex align="center" gap={3}>
                        <Heading heading="sectionSubTitle">{supportName}</Heading>
                        {([SUPPORT_ALIAS.TRAINING_DAN_SETUP, SUPPORT_ALIAS.TRAINING_DAN_SETUP_ADVANCE, SUPPORT_ALIAS.TRAINING_DAN_SETUP_PRIME].includes(alias)) && (
                            <Banner
                                variant="success"
                                css={{
                                    padding: '2px 4px',
                                    width: 'fit-content',
                                    borderRadius: 100,
                                    '& > div': { flexWrap: 'nowrap' },
                                }}
                            >
                                <StarFilled color={colors.bgGreen} />
                                <Text css={{ whiteSpace: 'noWrap' }}>
                                    {LANG_DATA.MAIN_SUBSCRIPTION_SUPPORT_RECOMENDED}
                                </Text>
                            </Banner>
                        )}
                        {/* TODO: Add tooltip? */}
                        {isRenewal && isProduct && (<CircleInfoOutline />)}
                    </Flex>
                    <Text>
                        {isRenewal ? (
                            <React.Fragment>
                                {!isProduct && (
                                    <Text color="secondary">{LANG_DATA.SUPPORT_DESCRIPTION[alias]}</Text>
                                )}
                                <strong style={{ color: colors.textPrimary }}>{formatPrice(supportPrice)}</strong>
                                /
                                {isProduct ? LANG_DATA.ADDITIONAL_SECTION_MONTH : LANG_DATA.ADDITIONAL_SECTION_SESSION}
                            </React.Fragment>
                        ) : LANG_DATA.SUPPORT_DESCRIPTION[alias]}
                    </Text>
                </Flex>
                <Flex
                    gap={3}
                    css={{
                        flexDirection: 'column',
                        alignItems: 'start',
                        '@lg': {
                            flexDirection: 'row',
                            alignItems: 'center',
                        },
                    }}
                >
                    {!isRenewal && (
                        <Text>
                            <strong>{formatPrice(supportPrice)}</strong>
                            /
                            {isProduct ? LANG_DATA.ADDITIONAL_SECTION_MONTH : LANG_DATA.ADDITIONAL_SECTION_SESSION}
                        </Text>
                    )}
                    <Button type="button" onClick={() => onClick({ ...support, isProduct })} buttonType={(isProduct || (savedData && savedData.outlets.length)) ? 'secondary' : 'primary'}>
                        {(savedData && savedData.outlets.length) ? LANG_DATA.CHANGE_PACKAGE : LANG_DATA.SELECT_PACKAGE}
                    </Button>
                </Flex>
            </Flex>
            {savedData && !!savedData.outlets.length && (
                <React.Fragment>
                    <Separator />
                    <Flex justify="between" align="center">
                        <Flex align="center" gap={3}>
                            <Text>
                                {LANG_DATA.RENEWAL_SELECTED_OUTLET}
                                :
                            </Text>
                            <Box css={{ '& > p:last-child': { display: 'none' } }}>
                                <TagList
                                    selectedItems={savedData.outlets.map(x => ({ id: x.id, name: x.cabang_name }))}
                                    counterLabel=""
                                    onRemoveItem={handleRemoveOutlet}
                                />
                            </Box>
                        </Flex>
                        <TagStatus
                            type="success"
                            size="sm"
                            css={{
                                flexShrink: 0,
                                maxWidth: 'unset',
                                alignItems: 'center',
                                '& > div:first-child': {
                                    display: 'none',
                                },
                            }}
                        >
                            {isProduct ? LANG_DATA.ADDITIONAL_SUBSCRIPTION_QTY_PRODUCT : LANG_DATA.ADDITIONAL_SUBSCRIPTION_QTY_SERVICE}
                            {': '}
                            {savedData.qty}
                        </TagStatus>
                    </Flex>
                </React.Fragment>
            )}
        </Box>
    );
};

AdditionalCard.propTypes = {
    support: PropTypes.shape({
        support_name: PropTypes.string,
        support_monthly_annual_price: PropTypes.number,
        support_subtitle: PropTypes.string,
        alias: PropTypes.string,
    }).isRequired,
    onClick: PropTypes.func,
    isProduct: PropTypes.bool,
    savedData: PropTypes.shape(),
    onChangeData: PropTypes.func,
    isRenewal: PropTypes.bool,
};

AdditionalCard.defaultProps = {
    onClick: () => { },
    isProduct: false,
    savedData: null,
    onChangeData: () => { },
    isRenewal: false,
};

export default AdditionalCard;
