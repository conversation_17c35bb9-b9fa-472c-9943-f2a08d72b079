import React, {
    useContext, useEffect, useMemo, useState,
} from 'react';
import PropTypes from 'prop-types';
import {
    Box, Button, Flex, Heading, Grid, GridItem,
} from '@majoo-ui/react';
import { groupBy } from 'lodash';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import AdditionalCard from './AdditionalCard';
import ModalAddSupport from './ModalAddSupport';
import { catchError } from '../../../../../../utils/helper';
import SupportContext from '../../../context/SupportContext';
import { getLocationSupportPrice } from '../../../../../../data/supports';
import { ACCOUNT_TYPE, isPrimeBenefits, SUBSCRIPTION_PACKAGE } from '../../../enum';

const SupportForm = ({
    supportProducts, supportServices, formData, setFormData,
    onSubmit, isRenewal,
}) => {
    const {
        LANG_DATA, addToast, supportCategory, isMobile,
    } = useContext(SupportContext);
    const [additionalPriceOptions, setAdditionalPriceOptions] = useState([]);
    const {
        watch, handleSubmit, reset, setValue,
    } = useForm({
        defaultValues: {
            additional_support: formData.additional_support,
        },
        resolver: yupResolver(yup.object({
            additional_support: yup.array(),
        })),
    });
    const [selectedAddon, setSelectedAddon] = useState();
    const supportName = useMemo(() => {
        const findSupport = supportCategory[SUBSCRIPTION_PACKAGE.MONTHLY].find(x => String(x.support_id) === String(formData.supportId));
        if (findSupport) return findSupport.support_name;
        return '';
    }, [supportCategory, formData.supportId]);
    const isNeedProductAddon = [ACCOUNT_TYPE.TRIAL, ACCOUNT_TYPE.STARTER_BASIC, ACCOUNT_TYPE.STARTER, ACCOUNT_TYPE.ADVANCE].includes(supportName);
    const [openModal, setOpenModal] = useState(false);
    const [showMore, setShowMore] = useState(!isRenewal);
    const { additional_support: additionalSupport } = watch();

    const handleOpenModal = (data) => {
        setSelectedAddon(data);
        setOpenModal(true);
    };

    const fetchAdditionalSubscriptionPrice = async (param) => {
        const payload = {
            package: param,
        };
        try {
            const res = await getLocationSupportPrice(payload);
            if (!res.status) throw new Error(res.msg);
            const priceOptions = groupBy(res.data, 'support_id');
            setAdditionalPriceOptions(priceOptions);
        } catch (e) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(e),
                variant: 'failed',
            });
        }
    };

    const handleAddSupport = (data) => {
        let tempAdditionalData = formData.additional_support;
        const findAdditionalIndex = tempAdditionalData.findIndex(x => x.id === data.id);
        if (findAdditionalIndex >= 0) tempAdditionalData[findAdditionalIndex] = data;
        else tempAdditionalData = [...tempAdditionalData, data];
        setFormData(prev => ({ ...prev, additional_support: tempAdditionalData }));
        setOpenModal(false);
    };

    const handleChangeData = (data) => {
        let tempAdditionalData = formData.additional_support;
        const findAdditionalIndex = tempAdditionalData.findIndex(x => x.id === data.id);
        if (findAdditionalIndex >= 0) {
            tempAdditionalData[findAdditionalIndex] = data;
        } else {
            tempAdditionalData = tempAdditionalData.filter(x => x.id !== data.id);
        }
        setValue('additional_support', tempAdditionalData);
        setFormData(prev => ({ ...prev, additional_support: tempAdditionalData }));
    };

    useEffect(() => {
        fetchAdditionalSubscriptionPrice(formData.package);
    }, [formData.package]);

    useEffect(() => {
        reset({
            additional_support: formData.additional_support,
        });
    }, [JSON.stringify(formData.additional_support)]);

    return (
        <Box
            as="form"
            id="supportForm"
            onSubmit={handleSubmit(onSubmit)}
        >
            <Flex direction="column" gap={5} mb={5} css={{ ...(isRenewal && { flexDirection: 'column-reverse !important' }) }}>
                {showMore && (
                    <Flex direction="column" gap={5}>
                        <Heading heading="sectionTitle" css={{ marginBottom: '$spacing-05' }}>
                            {LANG_DATA.SUPPORT_SERVICE}
                        </Heading>
                        {supportServices.sort((a, b) => b.support_name.localeCompare(a.support_name)).map(add => (
                            <AdditionalCard
                                key={add.support_id}
                                support={add}
                                onClick={handleOpenModal}
                                savedData={additionalSupport.find(x => x.id === add.support_id)}
                                onChangeData={handleChangeData}
                                isRenewal={isRenewal}
                            />
                        ))}
                    </Flex>
                )}
                <Flex direction="column" gap={5}>
                    <Heading heading="sectionTitle" css={{ marginBottom: '$spacing-05' }}>
                        {LANG_DATA.SUPPORT_PRODUCT}
                    </Heading>
                    <Grid columns={isRenewal && !isMobile ? 2 : 1} align="center" flow="row" gap={3} gapY={2}>
                        {supportProducts.filter(support => isNeedProductAddon || !isPrimeBenefits(support.alias)).map(add => (
                            <GridItem key={add.support_id}>
                                <AdditionalCard
                                    support={add}
                                    onClick={handleOpenModal}
                                    savedData={additionalSupport.find(x => x.id === add.support_id)}
                                    onChangeData={handleChangeData}
                                    isProduct
                                    isRenewal={isRenewal}
                                />
                            </GridItem>
                        ))}
                    </Grid>
                </Flex>
            </Flex>
            {isRenewal && (
                <Button
                    type="button"
                    buttonType="ghost"
                    onClick={() => setShowMore(prev => !prev)}
                    css={{ textDecoration: 'underline', width: '$full' }}
                    block
                >
                    {showMore ? LANG_DATA.VIEW_LESS : LANG_DATA.VIEW_MORE}
                </Button>
            )}
            {selectedAddon && openModal && (
                <ModalAddSupport
                    open={openModal}
                    onOpenChange={() => setOpenModal(false)}
                    onSave={handleAddSupport}
                    savedData={additionalSupport.find(x => x.id === selectedAddon.support_id)}
                    additionalPriceOptions={additionalPriceOptions}
                    support={selectedAddon}
                    newOutlets={formData.outlets.filter(x => x._isNewRow).map(x => ({ id: x.nama_cabang, name: x.nama_cabang }))}
                    isYearly={formData.package === SUBSCRIPTION_PACKAGE.YEARLY}
                />
            )}
        </Box>
    );
};

SupportForm.propTypes = {
    formData: PropTypes.shape({
        outlets: PropTypes.arrayOf(PropTypes.shape()),
        additional_support: PropTypes.arrayOf(PropTypes.shape()),
        package: PropTypes.string,
        supportId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    }).isRequired,
    setFormData: PropTypes.func.isRequired,
    supportProducts: PropTypes.arrayOf(PropTypes.shape()).isRequired,
    supportServices: PropTypes.arrayOf(PropTypes.shape()).isRequired,
    onSubmit: PropTypes.func,
    isRenewal: PropTypes.bool,
};

SupportForm.defaultProps = {
    onSubmit: () => { },
    isRenewal: false,
};

export default SupportForm;
