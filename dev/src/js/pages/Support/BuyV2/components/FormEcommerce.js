import React, {
    useEffect,
    useRef, useState,
} from 'react';
import PropTypes from 'prop-types';
import {
    Box, Paragraph, Paper, Flex, Button, Heading, FormLabel, PageDialog, Separator, DialogClose, InputRadio, InputRadioGroup,
} from '@majoo-ui/react';
import { useTranslation } from 'react-i18next';
import { Controller } from 'react-hook-form';
import {
    formatPrice, formLabelStyle,
} from '../utils';
import {
    PAYMENT_METHOD,
    PAYMENT_METHOD_OPTIONS,
    SUBSCRIPTION_PACKAGE,
    SUPPORT_ALIAS,
} from '../enum';
import OutletTable from './OutletTable';
import { useTranslationHook } from '../lang.utils';
import * as SupportApi from '../../../../data/supports';
import { catchError } from '../../../../utils/helper';

const calculateSubscriptionPrice = (selectedSubscriptionDetail, selectedSubscription) => {
    const subscriptionPrice = selectedSubscriptionDetail
        .reduce((previous, current) => {
            const result = previous + (Number(selectedSubscription.discount_price) * current.claim_qty);
            return result;
        }, 0);
    return subscriptionPrice;
};

const FormEcommerce = (props) => {
    const {
        open, onOpenChange, hookForm, selectedSubscription, addToast,
        selectedSubscriptionDetail, isTransacting, onConfirm, idUser,
    } = props;
    const { t } = useTranslation(['Pengaturan/subscription/additionalSubsForm', 'translation']);
    const { LANG_DATA } = useTranslationHook();
    const {
        setValue, control, getValues, watch,
    } = hookForm;
    const tableInstance = useRef();

    const [price, setPrice] = useState({
        subscriptionPrice: calculateSubscriptionPrice(selectedSubscriptionDetail.data, selectedSubscription),
        administrationPrice: selectedSubscription.additional_price,
        totalPrice: calculateSubscriptionPrice(selectedSubscriptionDetail.data, selectedSubscription) + selectedSubscription.additional_price,
    });
    const [billing, setBilling] = useState([]);
    const { payment_method: paymentMethod, _outletRow } = watch();
    const isAutodebit = paymentMethod === PAYMENT_METHOD.AUTO_DEBIT;

    const handleBuy = (e) => {
        e.preventDefault();
        setValue('additional_price', price.administrationPrice);
        setValue('total', price.totalPrice);
        onConfirm();
    };

    const handleChangeTable = (param) => {
        setValue('_outletRow', param.table);
        const subscriptionPrice = calculateSubscriptionPrice(param.table, selectedSubscription);
        const totalPrice = price.administrationPrice + subscriptionPrice;
        setPrice(x => ({
            ...x, subscriptionPrice, totalPrice,
        }));
    };

    const getCalculation = async () => {
        try {
            const data = getValues();
            if (!data._outletRow || data._outletRow.length <= 0) return;
            const payload = {
                user_id: +idUser,
                support_id: data.support_id,
                payment: isAutodebit ? 2 : 1,
                package: SUBSCRIPTION_PACKAGE.MONTHLY,
                outlet: [],
                additional_support: [{
                    id: data.support_id,
                    outlets: data._outletRow.map(({ _key: id, claim_qty: qty }) => ({ id, qty: isAutodebit ? 1 : qty })),
                    additional_fee: data.additional_price,
                    accomodation_fee: data.acomodation_fee || 0,
                }],
            };
            const res = await SupportApi.getCalculateSubscription(payload);
            setBilling(res.data_transaction);
            setPrice(x => ({
                ...x, totalPrice: res.total,
            }));
        } catch (e) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(e),
                variant: 'failed',
            });
        }
    };

    useEffect(() => {
        getCalculation();
    }, [JSON.stringify(_outletRow), paymentMethod]);

    return (
        <React.Fragment>
            <PageDialog open={open} onOpenChange={onOpenChange} disabledCloseButton={isTransacting}>
                <PageDialog.Title css={{
                    position: 'relative', left: '-18px', whiteSpace: 'nowrap', '@md': { left: 0 },
                }}
                >
                    {t('title', 'Beli Layanan Support Tambahan')}
                </PageDialog.Title>
                <PageDialog.Content wrapperSize="md">
                    <Box
                        css={{
                            display: 'grid',
                            gridTemplateColumns: '1fr',
                            height: '100%',
                            width: '100%',
                            position: 'relative',
                            '@md': {
                                gap: '$cozy',
                            },
                        }}
                    >
                        <Paper
                            as="form"
                            id="form-ecommerce"
                            responsive
                            css={{
                                width: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '$compact',
                                padding: '$cozy $compact',
                                '@md': {
                                    padding: '$cozy',
                                    gap: '$cozy',
                                    height: 'fit-content',
                                },
                            }}
                        >
                            <Box
                                css={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    gap: '$compact',
                                    '@md': {
                                        flexDirection: 'row',
                                        justifyContent: 'space-between',
                                        mb: '$spacing-03',
                                    },
                                }}
                            >
                                <Heading
                                    as="h3"
                                    heading="pageTitle"
                                >
                                    {`${selectedSubscription.support_name} - ${formatPrice(selectedSubscription.discount_price)}/${t(`period.${selectedSubscription.duration_unit_support}`)}`}
                                </Heading>
                            </Box>
                            <FormLabel css={formLabelStyle}>Outlet</FormLabel>
                            <Box>
                                <OutletTable
                                    ecommerce
                                    key={`${selectedSubscriptionDetail.data.length}-outlet-table-ecommerce`}
                                    ref={tableInstance}
                                    selectedSubscription={selectedSubscription}
                                    selectedSubscriptionDetail={selectedSubscriptionDetail}
                                    isAutodebit={isAutodebit}
                                    onChangeTable={handleChangeTable}
                                />
                            </Box>
                            <Box css={{ display: 'grid', gap: '$compact' }}>
                                <Box css={{
                                    display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '$compact', color: '$textPrimary',
                                }}
                                >
                                    {billing.length > 0 ? billing.filter(x => x.value).map(bill => (
                                        <React.Fragment key={bill.key}>
                                            <Paragraph paragraph="shortContentRegular">
                                                {bill.key}
                                            </Paragraph>
                                            <Paragraph paragraph="shortContentBold" align="right">
                                                {formatPrice(bill.value)}
                                            </Paragraph>
                                        </React.Fragment>
                                    )) : (
                                        <React.Fragment>
                                            <Paragraph paragraph="shortContentRegular">
                                                {t('supportFee', 'Biaya Support (n)')}
                                            </Paragraph>
                                            <Paragraph paragraph="shortContentBold" align="right">
                                                {formatPrice(price.subscriptionPrice)}
                                            </Paragraph>
                                            <Paragraph paragraph="shortContentRegular">
                                                {t('admFee', 'Biaya Administrasi')}
                                            </Paragraph>
                                            <Paragraph paragraph="shortContentBold" align="right">
                                                {formatPrice(price.administrationPrice)}
                                            </Paragraph>
                                        </React.Fragment>
                                    )}
                                </Box>
                                <Box>
                                    <Separator css={{ my: 20, '@md': { my: 12 } }} />
                                    <Flex justify="between">
                                        <Heading as="span" heading="sectionTitle">{t('totalFee', 'TOTAL TAGIHAN')}</Heading>
                                        <Heading as="span" heading="sectionTitle">{formatPrice(price.totalPrice)}</Heading>
                                    </Flex>
                                    <Separator css={{ mt: 20, mb: 4, '@md': { mt: 12 } }} />
                                </Box>
                            </Box>
                            <Controller
                                name="payment_method"
                                control={control}
                                render={({ field: { value, onChange } }) => (
                                    <InputRadioGroup direction="column" value={value} gap={3} onValueChange={e => onChange(e)} css={{ width: '$full' }}>
                                        {PAYMENT_METHOD_OPTIONS(LANG_DATA)
                                            .filter(
                                                method =>
                                                    method.value !== PAYMENT_METHOD.AUTO_DEBIT ||
                                                    selectedSubscription.alias !== SUPPORT_ALIAS.WHATSAPP_LAPORAN,
                                            )
                                            .map(method => (
                                                <InputRadio value={method.value} label={method.label} css={{ flex: 1, width: '100%' }} />
                                            ))}
                                    </InputRadioGroup>
                                )}
                            />
                        </Paper>
                    </Box>
                </PageDialog.Content>
                <PageDialog.Footer
                    css={{
                        justifyContent: 'center',
                        height: 'auto',
                        padding: '$spacing-05',
                        borderTop: '1px solid $gray150',
                        '@md': { height: 64, padding: '12px 24px', borderTop: 'none' },
                    }}
                >
                    <Box
                        css={{
                            display: 'flex',
                            width: '100%',
                            gap: '$compact',
                            justifyContent: 'flex-end',
                            '@md': {
                                maxWidth: 982,
                            },
                        }}
                    >
                        <Flex gap={5} css={{ flex: 1, '@md': { flex: 'unset' } }}>
                            <DialogClose asChild>
                                <Button
                                    disabled={isTransacting}
                                    size="md"
                                    buttonType="ghost"
                                    type="button"
                                    css={{ flex: 1, '@md': { flex: 'unset' } }}
                                >
                                    {t('label.cancel', { ns: 'translation', defaultValue: 'Batal' })}
                                </Button>
                            </DialogClose>
                        </Flex>
                        <Button
                            disabled={isTransacting || getValues('_outletRow').length < 1}
                            size="md"
                            type="button"
                            css={{ flex: 1, '@md': { flex: 'unset' } }}
                            onClick={handleBuy}
                        >
                            {t('buy', 'Beli')}
                        </Button>
                    </Box>
                </PageDialog.Footer>
            </PageDialog>
        </React.Fragment>
    );
};

FormEcommerce.propTypes = {
    onConfirm: PropTypes.func.isRequired,
    isTransacting: PropTypes.bool.isRequired,
    open: PropTypes.bool.isRequired,
    onOpenChange: PropTypes.func.isRequired,
    hookForm: PropTypes.shape({
        register: PropTypes.func,
        setValue: PropTypes.func,
        formState: PropTypes.shape({
            errors: PropTypes.shape(),
        }),
        clearErrors: PropTypes.func,
        getValues: PropTypes.func,
        control: PropTypes.func,
        watch: PropTypes.func,
    }).isRequired,
    selectedSubscription: PropTypes.shape({
        support_name: PropTypes.string,
        discount_price: PropTypes.number,
        additional_price: PropTypes.number,
        duration_unit_support: PropTypes.string,
        alias: PropTypes.string,
    }).isRequired,
    selectedSubscriptionDetail: PropTypes.shape({
        data: PropTypes.arrayOf(
            PropTypes.shape({
                _key: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
                claim_qty: PropTypes.number,
            }),
        ),
    }).isRequired,
    addToast: PropTypes.func.isRequired,
    idUser: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
};

export default FormEcommerce;
