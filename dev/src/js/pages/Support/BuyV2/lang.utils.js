import React from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { SUPPORT_ALIAS } from './enum';

export const useTranslationHook = () => {
    const { t, ready, i18n } = useTranslation([
        'Pengaturan/subscription',
        'translation',
        'Pengaturan/subscription/additionalSubsForm',
        'Pengaturan/subscription/mainSubscriptionForm',
        'Pengaturan/subscription/outletTable',
    ]);

    const TransComponent = ({ i18nKey, children, components }) => (
        <Trans t={t} i18nKey={i18nKey} components={components}>
            {children}
        </Trans>
    );

    const LANG_KEY = {
        ACTIVE_SECTION_DAYS_LEFT: 'activeSection.daysLeft',
        ACTIVE_SECTION_MODAL_DETAIL_LINKED_OUTLET_DESC: 'activeSection.modalDetail.linkedOutletDesc',
        ADDITIONAL_SECTION_FREE: 'additionalSection.free',
        TNC_LABEL: 'Pengaturan/subscription/mainSubscriptionForm:tncLabel',
        TNC_REQUIRED: 'Pengaturan/subscription/mainSubscriptionForm:tncRequired',
        TOAST_TRANSACTION_SUCCESS: 'Pengaturan/subscription/mainSubscriptionForm:toast:success',
        UPGRADE_DESCRIPTION: 'upgrade.description',
        UPGRADE_BANNER: 'upgrade.banner',
        DOWNGRADE_DESCRIPTION: 'downgrade.description',
        DOWNGRADE_BANNER: 'downgrade.banner',
        BANNER_DISCOUNT_CC: 'Pengaturan/subscription/mainSubscriptionForm:discountCC.banner.desc',
        CHANGE_PAYMENT_CONFIRM_DESC: 'billingSection.changePayment.confirmChangesDesc',
        CHANGE_PAYMENT_CONFIRM_DESC_MULTI: 'billingSection.changePayment.confirmChangesDescMulti',
        ADD_OUTLET_TO_GROUP: 'billingSection.groupPayment.addOutletTo',
        TOAST_ADD_OUTLET_GROUP: 'toast.addGroupPayment',
        STOP_AUTODEBET_OUTLET: 'billingSection.groupPayment.stopRecurentOutlet',
        STOP_AUTODEBET_OUTLETS: 'billingSection.groupPayment.stopRecurentOutlets',
        STOP_AUTODEBET_GROUP: 'billingSection.groupPayment.stopRecurentGroups',
        EXIST_TRANSACTION_SINGLE: 'popupExistTransaction.descSingle',
        EXIST_TRANSACTION_MULTI: 'popupExistTransaction.descMulti',
    };

    const retval = {
        LANG_KEY,
        LANG_DATA: {
            TITLE: t('titleSection.title', 'Langganan & Support'),
            MONTHLY: t('monthly', 'Bulanan'),
            YEARLY: t('annual', 'Tahunan'),
            DAY: t('day', 'Hari'),
            MONTH: t('month', 'Bulan'),
            YEAR: t('year', 'Tahun'),
            DISCOUNT20: t('discount', 'Hemat 20%'),
            DISCOUNT10: t('discount10', 'Diskon 10%'),
            DISCOUNT25: t('discount25', 'Promo Diskon 20%'),
            FREE_ONE_MONTH: t('freeOneMonth', '+1 Bulan Gratis'),
            BEFORE_TRIAL_EXPIRED: t('beforeTrialExpired', 'Sebelum masa Trial berakhir'),
            POPULAR: t('popular', 'Populer'),
            BUY_NOW: t('buyNow', 'Beli Sekarang'),
            EACH_OUTLET: t('eachOutlet', 'per outlet/bulan'),
            EACH_OUTLET_ANNUAL: t('eachOutletAnnual', 'per outlet/tahun'),
            ANNUAL_PAYMENT: t('annualPayment', 'Pembayaran Tahunan'),
            MONTHLY_PAYMENT: t('monthlyPayment', 'Pembayaran Bulanan'),
            PAID_ANNUALY: price => t('paidAnnually', { price }),
            PAID_MONTHLY: t('paidMonthly'),
            SELECT_PACKAGE: t('selectPackage', 'Pilih Paket'),
            CHANGE_PACKAGE: t('changePackage', 'Ubah Paket'),
            SET: t('set', 'Atur'),
            MORE: t('more', 'Lebih banyak'),
            LESS: t('less', 'Lebih sedikit'),
            VIEW_MORE: t('viewMore', 'Tampilkan lebih banyak'),
            VIEW_LESS: t('viewLess', 'Tampilkan lebih sedikit'),
            OPEN: t('open', 'Buka'),
            DELETE_OUTLET: t('deleteOutlet', 'Hapus Outlet'),
            TABS_ACTIVE: t('tabs.active', 'Langganan Aktif'),
            TABS_BILLING: t('tabs.billing', 'Billing'),
            TABS_SUPPORT: t('tabs.support', 'Produk Pendukung'),
            TABS_HISTORY: t('tabs.history', 'Riwayat Pembelian'),
            FEATURES_DESCRIPTION: {
                STARTER: t('featureDesc.STARTER', 'Paket langganan untuk usaha kecil atau yang baru memulai sistem kasir digital.'),
                ADVANCE: t('featureDesc.ADVANCE', 'Paket langganan untuk usaha berkembang yang butuh fitur tambahan lebih lengkap.'),
                PRIME: t('featureDesc.PRIME', 'Paket langganan untuk usaha skala besar yang membutuhkan fitur otomasi.'),
            },
            FEATURES_DESCRIPTION_RENEWAL: {
                STARTER: t('featureDescRenewal.STARTER', 'Untuk semua jenis usaha kelola operasional dan penjualan lengkap.'),
                ADVANCE: t('featureDescRenewal.ADVANCE', 'Untuk semua jenis usaha kelola operasional, penjualan, dan keuangan lengkap.'),
                PRIME: t('featureDescRenewal.PRIME', 'Untuk semua jenis usaha automasi operasional, penjualan, keuangan, karyawan, dan penggajian lengkap.'),
            },
            SUPPORT_DESCRIPTION: {
                TOKOONLINE: t('additionalFeature.TOKOONLINE', 'Paket untuk optimalisasi transaksi melalui lini bisnis marketplace. Anda dapat menghubungkannya dengan marketplace (Tokopedia, Bukalapak, Shopee dan GrabMart).'),
                ADDONGRAB: t('additionalFeature.ADDONGRAB', 'Opsi tambahan tepat untuk jenis usaha kuliner yang mengoptimalkan omset dari pesanan secara online seperti GrabFood dan GoFood. Anda dapat menghubungkannya dengan Food Order (GrabFood & GoFood).'),
                SUPPORTKEDATANGAN: t('additionalFeature.SUPPORTKEDATANGAN', 'Selesaikan kendala teknis software maupun hardware dengan dukungan dari teknisi terlatih kami'),
                SUPPORTKEDATANGANADVANCE: t('additionalFeature.SUPPORTKEDATANGAN', 'Selesaikan kendala teknis software maupun hardware dengan dukungan dari teknisi terlatih kami'),
                SUPPORTKEDATANGANPRIME: t('additionalFeature.SUPPORTKEDATANGAN', 'Selesaikan kendala teknis software maupun hardware dengan dukungan dari teknisi terlatih kami'),
                TRAININGDANSETUP: t('additionalFeature.TRAININGDANSETUP', 'Maksimalkan fitur dashboard hingga POS dengan pengetahuan mendetail  serta setup perangkat sesuai kebutuhan outlet dengan bantuan teknisi ahli majoo'),
                TRAININGDANSETUPADVANCE: t('additionalFeature.TRAININGDANSETUP', 'Maksimalkan fitur dashboard hingga POS dengan pengetahuan mendetail  serta setup perangkat sesuai kebutuhan outlet dengan bantuan teknisi ahli majoo'),
                TRAININGDANSETUPPRIME: t('additionalFeature.TRAININGDANSETUP', 'Maksimalkan fitur dashboard hingga POS dengan pengetahuan mendetail  serta setup perangkat sesuai kebutuhan outlet dengan bantuan teknisi ahli majoo'),
                [SUPPORT_ALIAS.WHATSAPP_LAPORAN]: t('additionalFeature.whatsapp_laporan', 'Paket automasi kirim laporan tutup kasir, laporan penjualan per jam dan berbagai laporan lain menggunakan integrasi notifikasi whatsapp'),
            },
            BANNER_PRIME_PLUS_TITLE: t('primePlusBanner.title', 'Paket langganan untuk usaha skala besar yang membutuhkan fitur automasi premium lebih lengkap, mulai dari penjualan, keuangan, karyawan, penggajian, warehouse, sampai workflow.'),
            BANNER_PRIME_PLUS_SIDE_TITLE: t('primePlusBanner.sideTitle', 'Konsultasi Harga'),
            BANNER_PRIME_PLUS_SIDE_SUBTITLE: t('primePlusBanner.sideSubtitle', 'Hubungi Konsultan majoo'),
            BANNER_PRIME_PLUS_CONSULT_NOW: t('primePlusBanner.consultNow', 'Konsultasi Sekarang'),
            ACTIVE_SECTION_SUBTITLE: t('activeSection.subtitle', 'Pilih Paket Langganan Sesuai Kebutuhan Bisnis Anda'),
            ACTIVE_SECTION_CURRENT_PACKAGE: t('activeSection.currentPackage', 'Paket saat ini:'),
            ACTIVE_SECTION_EXPIRED: t('activeSection.expired', 'Kedaluwarsa'),
            ACTIVE_SECTION_DAYS_LEFT: days => t('activeSection.daysLeft', `Sisa ${days} hari lagi`, { days }),
            ACTIVE_SECTION_SUBSCRIPTION_ACTIVE: t('activeSection.subscriptionActive', 'Langganan Aktif'),
            ACTIVE_SECTION_ACTIVE_OUTLET: t('activeSection.activeOutlet', 'Outlet Aktif:'),
            ACTIVE_SECTION_EXPIRED_OUTLET: t('activeSection.expiredOutlet', 'Outlet Kedaluwarsa:'),
            ACTIVE_SECTION_ADD_TITLE: t('activeSection.addOutlet', 'Tambah Outlet'),
            ACTIVE_SECTION_SUPPORT_PRODUCT: t('activeSection.supportProduct', 'Produk & Layanan Pendukung'),
            ACTIVE_SECTION_LINKED_OUTLET: t('activeSection.linkedOutlet', 'Outlet Terhubung'),
            ACTIVE_SECTION_VIEW_DETAILS: t('activeSection.viewDetail', 'Lihat Detail'),
            ACTIVE_SECTION_VIEW_SUBSCRIPTION_DETAIL: t('activeSection.viewSubscriptionDetails', 'Lihat Detail Langganan'),
            ACTIVE_SECTION_VIEW_COMPLETE_COMPARISON: t('activeSection.viewCompleteFeaturesComparison', 'Lihat perbandingan lengkap fitur'),
            ACTIVE_SECTION_MODAL_DETAIL_TITLE: t('activeSection.modalDetail.title', 'Atur Outlet'),
            ACTIVE_SECTION_MODAL_DETAIL_MAIN_OUTLET: t('activeSection.modalDetail.mainOutlet', 'Outlet Utama'),
            ACTIVE_SECTION_MODAL_DETAIL_RENEW: t('activeSection.modalDetail.renew', 'Perpanjang'),
            ACTIVE_SECTION_MODAL_DETAIL_LINKED_OUTLET: t('activeSection.modalDetail.linkedOutlet', 'Daftar Outlet Terhubung'),
            ACTIVE_SECTION_MODAL_DETAIL_FILTER: {
                ALL: t('activeSection.modalDetail.filterStatus.all', 'Semua Status'),
                ACTIVE: t('activeSection.modalDetail.filterStatus.active', 'Aktif'),
                EXPIRED: t('activeSection.modalDetail.filterStatus.expired', 'Kedaluwarsa'),
            },
            ACTIVE_SECTION_MODAL_DETAIL_COLUMN: {
                OUTLET_NAME: t('activeSection.modalDetail.column.outletName', 'Nama Outlet'),
                PAYMENT_GROUP: t('activeSection.modalDetail.column.paymentGroup', 'Grup Pembayaran'),
                VALIDITY_PERIOD: t('activeSection.modalDetail.column.validityPeriod', 'Masa Berlangganan'),
                ACTION: t('activeSection.modalDetail.column.action', 'Aksi'),
                SCHEDULE_CODE: t('activeSection.modalDetail.column.scheduleCode', 'Kode Jadwal'),
                QTY: t('activeSection.modalDetail.column.qty', 'Jumlah'),
                SUPPORT_TEAM: t('activeSection.modalDetail.column.supportTeam', 'Tim Support'),
                INSTALATION_TYPE: t('activeSection.modalDetail.column.installationType', 'Tipe Instalasi'),
                REGION: t('activeSection.modalDetail.column.supportTeam', 'Daerah'),
            },
            BILLING_SECTION_PAID: t('billingSection.paid', 'Sudah Bayar'),
            BILLING_SECTION_UNPAID: t('billingSection.unPaid', 'Belum Bayar'),
            BILLING_SECTION_PAYMENT_INSTRUCTIONS: t('billingSection.paymentInstructions', 'Petunjuk Bayar'),
            BILLING_SECTION_CHECK_PAYMENT_STATUS: t('billingSection.checkPaymentStatus', 'Cek Status Bayar'),
            BILLING_SECTION_GROUP_PAYMENT_TITLE: t('billingSection.groupPayment.title', 'Group Pembayaran Kredit/Debit'),
            BILLING_SECTION_PER_OUTLET: t('billingSection.payPerOutlet', 'Bayar Per Outlet'),
            BILLING_SECTION_ACTION: {
                OUTLET_DETAIL: t('billingSection.action.outletDetail', 'Detail Outlet'),
                VIEW_BILLING_DETAIL: t('billingSection.action.viewBillingDetail', 'Lihat Detail Tagihan'),
                CHANGE_PAYMENT_METHOD: t('billingSection.action.changePaymentMethod', 'Ubah Metode Pembayaran'),
                VIEW_OUTLET_DETAIL: t('billingSection.action.viewOutletDetail', 'Lihat Detail Outlet'),
                MOVE_TO_OTHER_GROUP: t('billingSection.action.moveToOtherPaymentGroup', 'Pindahkan ke Grup Pembayaran lain'),
                STOP_AUTODEBIT: t('billingSection.action.stopAutodebit', 'Berhenti Autodebit'),
                MOVE: t('billingSection.action.move', 'Pindahkan'),
            },
            BILLING_SECTION_OUTLET_DETAIL: {
                TITLE: t('billingSection.outletDetail.title', 'Informasi Detail Langganan Outlet'),
                TITLE_GROUP: t('billingSection.outletDetail.titleGroup', 'Detail Outlet Group Pembayaran'),
                SUBS_LIST: t('billingSection.outletDetail.subList', 'Daftar Langganan'),
            },
            BILLING_SECTION_CHANGE_PAYMENT: {
                CURRENT: t('billingSection.changePayment.current', 'Bank Saat ini'),
                OUTLET_LIST: t('billingSection.changePayment.outletList', 'Daftar Outlet'),
                OUTLET_LIST_DESC: t('billingSection.changePayment.outletListDesc', 'Pilih outlet yang akan diubah metode pembayarannya.'),
                REGISTERED_CARD: t('billingSection.changePayment.registeredCard', 'Kartu Terdaftar'),
                ENDED: t('billingSection.changePayment.ended', 'Berakhir'),
                ADDED: t('billingSection.changePayment.added', 'Ditambahkan'),
                INPUT_CVV: t('billingSection.changePayment.inputCVV', 'Masukan Kode CVV'),
                ERROR_CVV: t('billingSection.changePayment.errorCVV', 'Mohon lengkapi Kode CVV'),
                CONFIRM_TITLE: t('billingSection.changePayment.confirmChanges', 'Konfirmasi Perubahan'),
            },
            BILLING_SECTION_GROUP_PAYMENT_COLUMN: {
                GROUP_ID: t('billingSection.groupPayment.column.groupId', 'ID Grup'),
                NUMBER_OUTLET: t('billingSection.groupPayment.column.numberOutlet', 'Jml. Outlet'),
                TERM: t('billingSection.groupPayment.column.term', 'Termin'),
                TOTAL_BILL: t('billingSection.groupPayment.column.totalBill', 'Total Tagihan'),
                UPCOMING_BILL: t('billingSection.groupPayment.column.upcomingBill', 'Tagihan Berikutnya'),
                BILLING_DATE: t('billingSection.groupPayment.column.billingDate', 'Tanggal Tagihan'),
                CARD_INFO: t('billingSection.groupPayment.column.cardInfo', 'Info Kartu'),
                DUE_DATE: t('billingSection.groupPayment.column.dueDate', 'Tanggal Jatuh Tempo'),
            },
            BILLING_SECTION_MOVE_GROUP: {
                MOVE: t('billingSection.groupPayment.movePaymentGroup', 'Pindah Grup Pembayaran'),
                PAYMENT_GROUP: t('billingSection.groupPayment.paymentGroup', 'Grup Pembayaran'),
            },
            SUPPORT_PRODUCT: t('additionalSection.product', 'Produk Pendukung'),
            SUPPORT_SERVICE: t('additionalSection.service', 'Layanan Pendukung'),
            ADDITIONAL_SECTION_MONTH: t('additionalSection.month', 'bulan'),
            ADDITIONAL_SECTION_SESSION: t('additionalSection.session', 'kedatangan'),
            ADDITIONAL_SECTION_FREE: t('additionalSection.free', 'GRATIS!!! untuk paket langganan Advance dan Prime'),
            ADDITIONAL_SECTION_ACTIVE: date => t('additionalSection.active', 'Aktif hingga {{date}}', { date }),
            MODAL_SET_SUPPORT_TITLE: support => t('additionalSection.selectType', 'Pilih Jenis', { support }),
            HISTORY_SECTION_PAYMENT_ALL: t('historySection.payment.all'),
            HISTORY_SECTION_PAYMENT_PAY_NOW: t('historySection.payNow'),
            HISTORY_SECTION_PAYMENT_AUTO_DEBIT: t('historySection.payment.autodebit', 'Kartu Kredit'),
            HISTORY_SECTION_STATUS_ALL: t('historySection.status.all'),
            HISTORY_SECTION_STATUS_VERIFY: t('historySection.status.verify'),
            HISTORY_SECTION_STATUS_CANCELED: t('historySection.status.canceled'),
            HISTORY_SECTION_STATUS_WAIT: t('historySection.status.wait'),
            HISTORY_SECTION_STATUS_SUCCEED: t('historySection.status.succeed'),
            HISTORY_SECTION_STATUS_INACTIVE: t('historySection.status.inactivate'),
            HISTORY_SECTION_HEADER_CODE: t('historySection.header.code', 'Kode Transaksi'),
            HISTORY_SECTION_HEADER_PURCHASE: t('historySection.header.purchase', 'Pembelian'),
            HISTORY_SECTION_HEADER_DATE: t('historySection.header.date', 'Tanggal'),
            HISTORY_SECTION_HEADER_TOTAL: t('historySection.header.total', 'Total'),
            HISTORY_SECTION_HEADER_METHOD: t('historySection.header.method', 'Metode Pembayaran'),
            MAIN_SUBSCRIPTION_FORM_TITLE: t('Pengaturan/subscription/mainSubscriptionForm:title', 'Beli Langganan'),
            MAIN_SUBSCRIPTION_FORM_CREDIT_CARD_PROMO: t('Pengaturan/subscription/mainSubscriptionForm:discountCreditCard', 'Diskon akan ditambahkan pada pembayaran bulan ke-2'),
            MAIN_SUBSCRIPTION_FORM_PACKAGE_TITLE: t('Pengaturan/subscription/mainSubscriptionForm:step.package.title', 'Pembelian Paket'),
            MAIN_SUBSCRIPTION_FORM_SUPPORT_TITLE: t('Pengaturan/subscription/mainSubscriptionForm:step.support.title', 'Produk Pendukung'),
            MAIN_SUBSCRIPTION_FORM_BILLING_TITLE: t('Pengaturan/subscription/mainSubscriptionForm:step.billing.title', 'Pembayaran'),
            MAIN_SUBSCRIPTION_TOTAL_PURCHASE: t('Pengaturan/subscription/mainSubscriptionForm:totalFee', 'Total Pembelian'),
            MAIN_SUBSCRIPTION_COMPARE: t('Pengaturan/subscription/mainSubscriptionForm:compare', 'Bandingkan Paket'),
            MAIN_SUBSCRIPTION_PAY: t('Pengaturan/subscription/mainSubscriptionForm:pay', 'Bayar'),
            MAIN_SUBSCRIPTION_PACKAGE_SELECT: t('Pengaturan/subscription/mainSubscriptionForm:field.selectPackage', 'Pilih Paket'),
            MAIN_SUBSCRIPTION_PACKAGE_TERM: t('Pengaturan/subscription/mainSubscriptionForm:field.subscriptionTerm', 'Termin Langganan'),
            MAIN_SUBSCRIPTION_PACKAGE_PAYMENT_METHOD: t('Pengaturan/subscription/mainSubscriptionForm:field.paymentMethod', 'Pilih Metode Pembayaran'),
            MAIN_SUBSCRIPTION_PACKAGE_NEW_OUTLET_NAME: t('Pengaturan/subscription/mainSubscriptionForm:field.newOutletName', 'Nama outlet baru'),
            MAIN_SUBSCRIPTION_PACKAGE_CREATE_NEW_OUTLET: t('Pengaturan/subscription/mainSubscriptionForm:step.package.createNewOutlet', 'Buat Outlet Baru'),
            MAIN_SUBSCRIPTION_PACKAGE_ADD_NEW_OUTLET: t('Pengaturan/subscription/mainSubscriptionForm:step.package.addNewOutlet', 'Tambah Outlet Baru'),
            MAIN_SUBSCRIPTION_SUPPORT_RECOMENDED: t('Pengaturan/subscription/additionalSubsForm:recomended', 'Direkomendasikan'),
            MAIN_SUBSCRIPTION_SUPPORT_BANNER_SERVICE: t('Pengaturan/subscription/additionalSubsForm:serviceBanner', 'Jumlah kedatangan akan dibagi ke setiap outlet. Tim kami akan mengkonfirmasi pembagian jumlah kedatangan untuk setiap outlet yang dipilih.'),
            MAIN_SUBSCRIPTION_SUPPORT_BANNER_PRODUCT: t('Pengaturan/subscription/additionalSubsForm:productBanner', 'Masing-masing outlet terpilih akan mendapatkan jumlah durasi yang sama'),
            MAIN_SUBSCRIPTION_BILLING_ORDER_DETAIL: t('Pengaturan/subscription/mainSubscriptionForm:step.billing.orderDetail', 'Rincian Order'),
            MAIN_SUBSCRIPTION_DELETE_CONFIRMATION: t('Pengaturan/subscription/mainSubscriptionForm:deleteOutletConfirmation', 'Apakah anda yakin ingin menghapus outlet terpilih dari pembelian?'),
            MAIN_SUBSCRIPTION_ADDITIONAL_FEE: t('Pengaturan/subscription/mainSubscriptionForm:additionalFee', 'Biaya Tambahan'),
            ADDITIONAL_SUBSCRIPTION_PIC_LABEL: t('Pengaturan/subscription/additionalSubsForm:PIC.label', 'Nama PIC'),
            ADDITIONAL_SUBSCRIPTION_PHONE_LABEL: t('Pengaturan/subscription/additionalSubsForm:phone.label', 'No Telepon'),
            ADDITIONAL_SUBSCRIPTION_ADDRESS_LABEL: t('Pengaturan/subscription/additionalSubsForm:address.label', 'Alamat'),
            ADDITIONAL_SUBSCRIPTION_PROVINCE_LABEL: t('Pengaturan/subscription/additionalSubsForm:province.label', 'Provinsi'),
            ADDITIONAL_SUBSCRIPTION_CITY_LABEL: t('Pengaturan/subscription/additionalSubsForm:city.label', 'Kota'),
            ADDITIONAL_SUBSCRIPTION_PIC_PLACEHOLDER: t('Pengaturan/subscription/additionalSubsForm:PIC.placeholder', 'Contoh: Messi'),
            ADDITIONAL_SUBSCRIPTION_EMAIL_PLACEHOLDER: t('Pengaturan/subscription/additionalSubsForm:email.placeholder', 'Contoh: <EMAIL>'),
            ADDITIONAL_SUBSCRIPTION_PHONE_PLACEHOLDER: t('Pengaturan/subscription/additionalSubsForm:phone.placeholder', 'Contoh: 081344549494'),
            ADDITIONAL_SUBSCRIPTION_ADDRESS_PLACEHOLDER: t('Pengaturan/subscription/additionalSubsForm:address.placeholder', 'Contoh: Jl. Mawar No 12'),
            ADDITIONAL_SUBSCRIPTION_PROVINCE_PLACEHOLDER: t('Pengaturan/subscription/additionalSubsForm:province.placeholder', 'Contoh: Jawa Timur'),
            ADDITIONAL_SUBSCRIPTION_CITY_PLACEHOLDER: t('Pengaturan/subscription/additionalSubsForm:city.placeholder', 'Contoh: Kab. Sidoarjo'),
            ADDITIONAL_SUBSCRIPTION_QTY_SERVICE: t('Pengaturan/subscription/additionalSubsForm:qtySupport', 'Jumlah Kedatangan'),
            ADDITIONAL_SUBSCRIPTION_QTY_PRODUCT: t('Pengaturan/subscription/additionalSubsForm:qtyProduct', 'Jumlah Durasi'),
            RENEWAL_TITLE: t('renewal.title', 'Bayar Tagihan'),
            RENEWAL_DESCRIPTION: t('renewal.description', 'Daftar outlet dibawah adalah outlet yang terpasang layanan & produk pendukung'),
            RENEWAL_SUMMARY: t('renewal.summary', 'Rincian'),
            RENEWAL_SUBCRIPTION_FEE: t('renewal.subscriptionFee', 'Biaya Langganan'),
            RENEWAL_UPDATE_BILLS: t('renewal.updateBills', 'Update tagihan'),
            RENEWAL_SELECTED_OUTLET: t('renewal.selectedOutlet', 'Outlet Terpilih'),
            MODAL_DURATION_TITLE: t('Pengaturan/subscription/mainSubscriptionForm:changeDuration', 'Ubah Durasi'),
            ORDER_TABLE: {
                PRODUCT: t('Pengaturan/subscription/mainSubscriptionForm:step.billing.column.productName', 'Nama Produk'),
                QUANTITY: t('Pengaturan/subscription/mainSubscriptionForm:step.billing.column.quantity', 'Jumlah'),
            },
            OUTLET_TABLE: {
                PLACEHOLDER: t('Pengaturan/subscription/outletTable:outletPlaceholder', 'Contoh: Outlet 1'),
                VALID_UNTIL: t('Pengaturan/subscription/outletTable:validUntil'),
                ACTIVE_SINCE: t('Pengaturan/subscription/outletTable:activeSince'),
                ADD_MORE: t('Pengaturan/subscription/outletTable:addMore'),
                MONTH: t('Pengaturan/subscription/outletTable:period.month'),
                YEAR: t('Pengaturan/subscription/outletTable:period.year'),
                COLUMN_STATUS: t('Pengaturan/subscription/outletTable:header.status'),
                COLUMN_GROUP: t('Pengaturan/subscription/outletTable:header.group'),
                COLUMN_DURATION: t('Pengaturan/subscription/outletTable:header.duration'),
                COLUMN_DURATION_YEAR: t('Pengaturan/subscription/outletTable:header.durationYear'),
                COLUMN_DURATION_MONTH: t('Pengaturan/subscription/outletTable:header.durationMonth'),
                COLUMN_PRICE: t('Pengaturan/subscription/outletTable:header.price'),
            },
            FORM_ERROR: {
                MAIN: {
                    OUTLET_NAME: t('errors.outletName', 'Nama outlet baru tidak boleh kosong'),
                    DUPLICATE_OUTLET_NAME: t('errors.outletNameDuplicate', 'Nama outlet baru tidak boleh sama'),
                    MINIMUM_OUTLET: t('errors.minimumOutlet', 'Pilih minimal 1 outlet'),
                },
                ADDITIONAL: {
                    NAME: t('Pengaturan/subscription/additionalSubsForm:PIC.error', 'Mohon lengkapi Nama PIC'),
                    EMAIL: t('Pengaturan/subscription/additionalSubsForm:email.required', 'Mohon lengkapi Email'),
                    EMAIL_FORMAT: t('Pengaturan/subscription/additionalSubsForm:email.format', 'Format email tidak valid'),
                    ADDRESS: t('Pengaturan/subscription/additionalSubsForm:address.error', 'Mohon lengkapi Alamat'),
                    PROVINCE: t('Pengaturan/subscription/additionalSubsForm:province.error', 'Harap pilih Provinsi'),
                    CITY: t('Pengaturan/subscription/additionalSubsForm:city.error', 'Harap pilih Kota'),
                    PHONE_NUMBER: t('Pengaturan/subscription/additionalSubsForm:phone.error', 'Mohon lengkapi No Telepon'),
                },
            },
            UPGRADE_TITLE: t('upgrade.title', 'Konfirmasi Upgrade'),
            UPGRADE_SELECTED_PACKAGE: t('upgrade.selectedPackage', 'Paket Tujuan'),
            UPGRADE_UPGRADE_NOW: t('upgrade.upgradeNow', 'Upgrade Sekarang'),
            DOWNGRADE_TITLE: t('downgrade.title', 'Konfirmasi Downgrade'),
            DOWNGRADE_CURRENT_SUBSCRIPTION: t('downgrade.currentSubscription', 'Paket saat ini'),
            TOAST_BUY_ADDITIONAL_SUCCESS: t('toast.buyAdditionalSuccess', 'Pembelian berhasil diproses. Cek verifikasi pembayaran'),
            TOAST_CANCEL_PAYMENT: t('toast.cancelPayment', 'Pembayaran berhasil dibatalkan'),
            TOAST_CHANGE_PAYMENT: t('toast.changePayment', 'Metode pembayaran berhasil diubah'),
            TOAST_MOVE_GROUP: t('toast.moveGroup', 'Outlet berhasil dipindahkan'),
            LABEL_CLOSE: t('translation:label.close', 'Tutup'),
            LABEL_BACK: t('translation:label.back', 'Kembali'),
            LABEL_NEXT: t('translation:label.nextThen', 'Selanjutnya'),
            LABEL_SAVE: t('translation:label.save', 'Simpan'),
            LABEL_CANCEL: t('translation:label.cancel', 'Batal'),
            LABEL_APPLY: t('translation:label.apply', 'Terapkan'),
            LABEL_CHOOSE: t('translation:label.choose', 'Pilih'),
            LABEL_CONTINUE: t('translation:label.continue', 'Ya, Lanjutkan'),
            LABEL_ADD: t('translation:label.add2', 'Tambah'),
            LABEL_PRODUCT: t('translation:label.product', 'Product'),
            LABEL_DELETE_CONFIRMATION: t('translation:label.deleteConfirmation', 'Konfirmasi Hapus'),
            LABEL_COPY: t('translation:label.copy', 'Salin'),
            LABEL_ADD2: t('translation:label.add', 'Tambahkan'),
            LABEL_ATTENTION: t('popupExistTransaction.title', 'Perhatian'),
            PLACEHOLDER_EXAMPLE: t('translation:placeholder.example', 'Contoh:'),
            TOAST_ERROR: t('translation:toast.error', 'Gagal'),
            TOAST_SUCCESS: t('translation:toast.success', 'Berhasil'),
            TOAST_REFERENCE_NUMBER: t('toast.referenceNumber', 'Reference Number tidak ditemukan'),
        },
        t,
        TransComponent,
        ready,
        currentLang: i18n.language,
    };

    return retval;
};
