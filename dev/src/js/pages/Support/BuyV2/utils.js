import React from 'react';
import { EditableTable } from '@majoo-ui/react';
import * as moment from 'moment';
import { get, isEqual } from 'lodash';
import {
    ACCOUNT_TYPE, PAYMENT_METHOD, PAYMENT_PROVIDER_VA, SUPPORT_ALIAS,
} from './enum';
import { formatCurrency } from '../../../utils/helper';
import SliderDot from './components/SliderDot';

export const SLIDER_SETTINGS = (slidesToShow = 3) => ({
    dots: false,
    arrows: false,
    slidesToShow,
    slidesToScroll: 1,
    adaptiveHeight: false,
    infinite: false,
    draggable: true,
    // set default ke data kedua (Advance/isPopular)
    initialSlide: 0,
    responsive: [
        {
            breakpoint: 766,
            settings: {
                dots: true,
                slidesToShow: 1,
                slidesToScroll: 1,
                infinite: false,
                centerMode: true,
                centerPadding: '40px',
            },
        },
    ],
    appendDots: SliderDot,
});

export const SLIDER_SETTINGS_COMPARE = {
    dots: false,
    arrows: false,
    slidesToShow: 3,
    slidesToScroll: 1,
    adaptiveHeight: false,
    infinite: false,
    draggable: true,
    initialSlide: 0,
    responsive: [
        {
            breakpoint: 766,
            settings: {
                dots: true,
                slidesToShow: 1,
                slidesToScroll: 1,
                infinite: false,
            },
        },
    ],
    appendDots: SliderDot,
};

export const recalculateExpDateOutlet = (originRow, isAutodebit, quantity, isYearly, isTrialPromo) => {
    const period = isYearly ? 'years' : 'months';

    if (isAutodebit) {
        return moment(originRow.prorate_exp_date, 'YYYY-MM-DD').locale('id-ID');
    }

    if (originRow._isNewRow || originRow.is_for_trial === '1') {
        const today = moment()

        if (isYearly) {
            // Jika TRIAL dan beli TAHUNAN, gratis 1 Bulan
            if (originRow.is_for_trial === '1' && isTrialPromo) today.add(1, 'month');
            return today.startOf('month').add(quantity, 'year');
        }

        const dateToday = today.format('D');
        if (+dateToday > 15) {
            return today.startOf('month').add(quantity + 1, 'month');
        } 
        return today.startOf('month').add(quantity, 'month');
    }

    if (moment(originRow.exp_date) < moment()) {
        return moment().add(Number(quantity), period);
    }

    return moment(originRow.exp_date).add(Number(quantity), period);
};

export const getSubscriptionName = (str) => {
    const account = ACCOUNT_TYPE;
    if (str.toUpperCase() === account.ENTERPRISE) return account.PRIME;
    return account[str] || str;
};

export const formatPrice = number => formatCurrency(number, { maximumFractionDigits: 0 });
export const formatDate = (val, format = 'DD/MM/YYYY') => moment(val, 'YYYY-MM-DD').format(format);

export const capitalizeFirstLetter = (str) => {
    const lowerCase = str.toLowerCase();
    const capitalized = lowerCase.charAt(0).toUpperCase() + lowerCase.slice(1);
    return capitalized;
};

export const capitalizeEachWord = (str) => {
    const capitalize = str.split(' ').map((word) => {
        if (word.toLowerCase() === 'dan') {
        return word.toLowerCase();
        }
        return capitalizeFirstLetter(word);
    });
    return capitalize.join(' ');
};

export const generateIdOrderKirim = (userId, idSupport) => {
    const currentdate = new Date();
    const result = `SUPPORT-${userId}-${idSupport}-${currentdate.getFullYear()}-${(currentdate.getMonth() + 1)}-${currentdate.getDate()}-${currentdate.getHours()}-${currentdate.getMinutes()}-${currentdate.getSeconds()}`;
    return result;
};

export const generateAdditionalSupportPayload = (data = null, modifier = 1) => {
    if (!data) return null;
    return data.filter(x => x.outlets.length).map((x) => {
        switch (x.alias) {
            case SUPPORT_ALIAS.WHATSAPP_LAPORAN:
            case SUPPORT_ALIAS.ECOMMERCE:
                return {
                    id: x.id,
                    outlets: x.outlets.map(({ id, qty }) => {
                        if (Number.isNaN(Number(id))) return { name: id, qty: qty * modifier };
                        return { id, qty };
                    }),
                    additional_fee: 0,
                    accomodation_fee: 0,
                };
            case SUPPORT_ALIAS.FOOD_ORDER:
            case SUPPORT_ALIAS.TRAINING_DAN_SETUP:
            case SUPPORT_ALIAS.TRAINING_DAN_SETUP_ADVANCE:
            case SUPPORT_ALIAS.TRAINING_DAN_SETUP_PRIME:
            case SUPPORT_ALIAS.SUPPORT_KEDATANGAN:
            case SUPPORT_ALIAS.SUPPORT_KEDATANGAN_ADVANCE:
            case SUPPORT_ALIAS.SUPPORT_KEDATANGAN_PRIME:
                return {
                    id: x.id,
                    outlets: x.outlets.map(outlet => ({
                        ...(Number.isNaN(Number(outlet.id)) ? { name: outlet.id } : { id: outlet.id }),
                        qty: x.alias === SUPPORT_ALIAS.FOOD_ORDER ? outlet.qty * modifier : outlet.qty,
                        contact: {
                            name: outlet.name || x.name,
                            email: outlet.email || x.email,
                            phone_number: outlet.phone_number || x.phone_number,
                            address: outlet.address || x.address,
                            city_id: outlet.city_id || +x.city_id,
                            province_id: outlet.province_id || +x.province_id,
                            country_id: 107,
                        },
                    })),
                    additional_fee: 0,
                    accomodation_fee: x.acomodation_fee || 0,
                };
            default:
                return { ...x };
        }
    });
};

export const formLabelStyle = {
    alignItems: 'start !important',
    '& > span': {
        color: '$textPrimary',
        fontSize: '$section-sub-title',
        fontWeight: 600,
        lineHeight: '$section-sub-title',
        letterSpacings: '$section-sub-title',
        width: 175,
    },
};

export const MemoEditableTable = React.memo(EditableTable, (prev, next) => isEqual(prev.data, next.data));

export const defaultOutletPayload = (selectedSubscriptionDetail, key, isAutodebit) => ({
    _key: key,
    _isNewRow: true,
    _isAutodebit: isAutodebit,
    nama_cabang: '',
    claim_qty: 1,
    is_utama: '0',
    basic_price: selectedSubscriptionDetail.basic_price,
    prorate_perday: selectedSubscriptionDetail.prorate_perday,
    prorate_price: selectedSubscriptionDetail.prorate,
    prorate_exp_date: selectedSubscriptionDetail.prorate_exp_date,
    support_monthly_price: selectedSubscriptionDetail.support_monthly_price,
    support_monthly_annual_price: selectedSubscriptionDetail.support_monthly_annual_price,
});

export const mapSummaryData = (data) => {
    if (data && typeof data === 'object' && !Array.isArray(data)) {
        const bankName = get(PAYMENT_PROVIDER_VA.find(val => String(val.id) === (data.payment_method_bank_id)), 'name');
        return {
            status: data.status,
            package: data.package,
            va_expire_date: data.va_expired_date,
            created_at: data.created_at,
            bank_name: bankName,
            details: data.details || [],
            va_number: data.va_number,
            total: data.total_amount,
            total_strikeout: data.strikeout_price,
            discount: data.total_discount,
            payment_method: data.payment_method,
            bank_id: data.payment_method_bank_id,
            reference_number: data.reff_no,
            active_date: data.active_date,
            payment_date: data.payment_date,
            counter: 0,
            payment_link: data.payment_link,
            expired_date: (data.details || []).reduce((prev, x) => {
                if (prev === null) prev = x.support_exp_date;
                else prev = x.support_exp_date.localeCompare(prev) < 0 ? x.support_exp_date : prev;
                return prev;
            }, null),
            isCRM: +data.is_crm === 1,
            isContactMajoo: data.summary_note === 'hubungi tim majoo',
            summaryNoteCRM: data.summary_note,
        };
    }
    return null;
};

export const getAdditionalSupportService = (supports = [], subscription = '') => {
    let supportAlias,
        trainingAlias;
    switch (subscription) {
        case ACCOUNT_TYPE.ADVANCE:
        case ACCOUNT_TYPE.MAXIMA_ADVANCE:
            supportAlias = SUPPORT_ALIAS.SUPPORT_KEDATANGAN_ADVANCE;
            trainingAlias = SUPPORT_ALIAS.TRAINING_DAN_SETUP_ADVANCE;
            break;
        case ACCOUNT_TYPE.PRIME:
        case ACCOUNT_TYPE.PRIMEPLUS:
        case ACCOUNT_TYPE.ENTERPRISE:
            supportAlias = SUPPORT_ALIAS.SUPPORT_KEDATANGAN_PRIME;
            trainingAlias = SUPPORT_ALIAS.TRAINING_DAN_SETUP_PRIME;
            break;
        default:
            supportAlias = SUPPORT_ALIAS.SUPPORT_KEDATANGAN;
            trainingAlias = SUPPORT_ALIAS.TRAINING_DAN_SETUP;
            break;
    }
    const tempData = [];
    const findSupportKedatangan = supports.find(x => x.alias === supportAlias);
    const findTraining = supports.find(x => x.alias === trainingAlias);
    if (findSupportKedatangan) tempData.push(findSupportKedatangan);
    if (findTraining) tempData.push(findTraining);
    supports.forEach((x) => {
        const isNotExist = !tempData.find(temp => temp.support_name === x.support_name);
        if (isNotExist) tempData.push(x);
    });
    return tempData;
};

export const predictExpDateProrate = (dataOutlet, isYearly, langData, paymentMethod, isTrialPromo) => {
    const isAutodebit = paymentMethod === PAYMENT_METHOD.AUTO_DEBIT;
    let prorateDate = '', extendExpDate = '';

    if (isAutodebit) {
        if (dataOutlet._isNewRow || dataOutlet.is_for_trial === '1') {
            prorateDate = `${moment().format('MMM YYYY')}: ${moment().endOf('month').diff(moment(), 'days')} ${langData.DAY}`;

            if (isYearly) {
                let newExpDate = moment();
                // Jika TRIAL dan beli TAHUNAN, gratis 1 Bulan (DI SKIP DULU, karena beda scope PRD. sementara ditampilkan apa adanya)
                // if (dataOutlet.is_for_trial === '1' && isTrialPromo) newExpDate.add(1, 'month');
                // code dibawah ini, perlu di enhance lagi jika code diatasnya di aktifkan. diubah jadi add(1, 'year')
                newExpDate = newExpDate.startOf('month').add(11, 'month');
                extendExpDate = `${moment().startOf('month').add(1, 'month').format('MMM YYYY')} - ${newExpDate.format('MMM YYYY')}`;
            }
        } else {
            const diffDays = moment(dataOutlet.exp_date).endOf('month').diff(moment(dataOutlet.exp_date), 'days');
            if (diffDays === 0) {
                const nextMonth = moment(dataOutlet.exp_date).add(1, 'month');
                prorateDate = `${moment(nextMonth).format('MMM YYYY')}: ${moment(nextMonth).endOf('month').diff(moment(nextMonth).startOf('month'), 'days')} ${langData.DAY}`;
                if (isYearly) {
                    let newExpDate = moment(nextMonth);
                    newExpDate = newExpDate.startOf('month').add(11, 'month');
                    extendExpDate = `${moment(nextMonth).startOf('month').add(1, 'month').format('MMM YYYY')} - ${newExpDate.format('MMM YYYY')}`;
                }
            } else {
                prorateDate = `${moment(dataOutlet.exp_date).format('MMM YYYY')}: ${moment(dataOutlet.exp_date).endOf('month').diff(moment(dataOutlet.exp_date), 'days')} ${langData.DAY}`;
                if (isYearly) {
                    let newExpDate = moment(dataOutlet.exp_date);
                    newExpDate = newExpDate.startOf('month').add(11, 'month');
                    extendExpDate = `${moment(newExpDate).startOf('month').add(1, 'month').format('MMM YYYY')} - ${newExpDate.format('MMM YYYY')}`;
                }
            }
        }
        
        return { prorateDate, extendExpDate };
    }

    if (dataOutlet._isNewRow || dataOutlet.is_for_trial === '1') {
        prorateDate = `${moment().format('MMM YYYY')}: ${moment().endOf('month').diff(moment(), 'days') + 1} ${langData.DAY}`;
        if (isYearly) {
            // prorate + 11 bulan
            // contoh: 20 okt 2024, exp di 1 okt 2025
            let newExpDate = moment();
            // Jika TRIAL dan beli TAHUNAN, gratis 1 Bulan 
            if (dataOutlet.is_for_trial === '1' && isTrialPromo) newExpDate.add(1, 'month');
            newExpDate = newExpDate.startOf('month').add(dataOutlet.claim_qty, 'year');

            extendExpDate = `${moment().startOf('month').add(1, 'month').format('MMM YYYY')} - ${newExpDate.format('MMM YYYY')}`;
        } else {
            const dateToday = moment().format('D');
            if (+dateToday <= 15) {
                // jika 1-15, prorate saja, exp di tgl 1 bulan berikutnya
                // contoh: 3 okt 2024, exp di 1 nov 2024, dst
                if (dataOutlet.claim_qty === 1) {
                    extendExpDate = `${moment().startOf('month').add(1, 'month').format('MMM YYYY')}: 1 ${langData.DAY}`;
                } else {
                    extendExpDate = `${moment().add(1, 'month').format('MMM YYYY')} - ${moment().add(dataOutlet.claim_qty, 'month').format('MMM YYYY')}`;
                }
            } else {
                // jika 16-31, prorate + 1 bulan, dan exp di tgl 1 bulan berikutnya
                // contoh: 18 okt 2024, exp di 1 des 2024, dst
                const nextMonth = moment().add(1, 'month');
                extendExpDate = `${moment(nextMonth).format('MMM YYYY')} - ${moment(nextMonth).add(dataOutlet.claim_qty, 'month').format('MMM YYYY')}`;
            }
        }
    } else {
        // for renewal
        const period = isYearly ? 'year' : 'month';
        extendExpDate = `${moment(dataOutlet.exp_date).format('D MMM YYYY')} - ${moment(dataOutlet.exp_date).add(dataOutlet.claim_qty, period).format('D MMM YYYY')}`;
    }


    return {
        prorateDate, // contoh output -> Okt 2024: 4 hari
        extendExpDate, // contoh output -> Nov 2024: 1 hari // Nov 2024 - Des 2024
    }
};

export const calculateDaysHistory = ({
    support_expired: startExp, next_expired: endExp,
}) => {
    const startDate = moment(startExp);
    const nextExpired = moment(endExp);

    return `${startDate.format('D MMM YYYY')} - ${nextExpired.format('D MMM YYYY')}`;
};

export const calculateProratePriceNonCC = (startDateSupport, isYearly, subscriptionPrice, proratePerDay, dataOutlet) => {
    let price = 0;
    if (dataOutlet._isNewRow || dataOutlet.is_for_trial === '1') {
        const finalQty = +dataOutlet.claim_qty - 1;
        const prorateDay = moment(startDateSupport).endOf('month').diff(moment(startDateSupport), 'days') + 1;
        const proratePrice = parseFloat(+prorateDay * (+proratePerDay || 0)).toFixed(2);
        if (isYearly) {
            // prorate + 11 bulan -> jika qty lebih dari 1 + (harga tahunan * sisa qty)
            price = +proratePrice + (11 * (+subscriptionPrice/12)) + (+subscriptionPrice * finalQty);
        } else {
            // jika 1-15, prorate saja, jika qty lebih dari 1 + (harga bulanan * sisa qty)
            // jika 16-31, prorate + 1 bulan, jika qty lebih dari 1 + (harga bulanan * sisa qty)
            const dateTransaction = moment(startDateSupport).format('D');
            if (+dateTransaction <= 15) {
                price = +proratePrice + (+subscriptionPrice * finalQty);
            } else {
                price = +proratePrice + (+subscriptionPrice * (finalQty + 1));
            }
        }
    } else {
        // tahunan/bulanan otomatis ambil harga dasar yang sudah diset
        price = +subscriptionPrice * +dataOutlet.claim_qty
    }
    return price;
};
