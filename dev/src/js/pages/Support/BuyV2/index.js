import React, {
    useContext, useEffect, useMemo, useRef, useState,
} from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import {
    Paper, Tabs, TabsList, TabsTrigger, ToastContext,
} from '@majoo-ui/react';
import { isEmpty, get } from 'lodash';
import { usePartialState } from '~/utils/usePartialState';
import CoreHOC from '../../../core/CoreHOC';
import * as supportsActionCreators from '../../../data/supports/actions';
import * as outletActionCreators from '../../../data/outlets/actions';
import * as locationActionCreators from '../../../data/locations/actions';
import { fetchHistoryPayment, fetchSupportExpiredVA } from '../../../actions/virtualAccountPaymentActions';
import TitleSection from './TitleSection';
import SupportContext from './context/SupportContext';
import { useTranslationHook } from './lang.utils';
import { getSubscriptionType } from '../../../v2-utils';
import { getAllBranchSupport } from '../../../data/outlets';
import ActiveSection from './ActiveSection';
import BillingSection from './BillingSection';
import SupportSection from './SupportSection';
import HistorySection from './HistorySection';
import { isSupportExpired, getExpiredBranchId, getNearestExpiredOutlets } from '../../../services/session';
import {
    ACCOUNT_TYPE, SUBSCRIPTION_PACKAGE, SUPPORT_ALIAS, SUPPORT_CATEGORY_NAME,
} from './enum';
import { catchError } from '../../../utils/helper';
import * as outletApi from '../../../data/outlets';
import * as usersApi from '../../../data/users';
import * as supportApi from '../../../data/supports';
import { getSubscriptionName } from './utils';
import FormPrimePlusSubscription from './components/FormPrimePlusSubscription';
import FormBuySubscription from './components/FormBuySubscription';
import ComparePackage from './components/ComparePackage';
import { styled } from '../../../stitches.config';
import userUtil from '../../../utils/user.util';
import { useMediaQuery } from '../../../utils/useMediaQuery';
import PaymentInstructionDialog from '../../../components/virtualaccount/PaymentInstructionDialog';
import RenewalDialog from './components/RenewalDialog';
import { PERMISSIONS } from '../../../constants/permissions';
import PaymentHistoryDetail from './components/PaymentHistoryDetail';

const Support = (props) => {
    const {
        showProgress, hideProgress, actions, location, idPermission,
        filterBranch, idCabang, outlets: { list: outletList }, router,
    } = props;
    const [tab, setTab] = useState('active');
    const { addToast } = useContext(ToastContext);
    const translation = useTranslationHook();
    const { LANG_DATA, t } = translation;
    let currentSubscription = getSubscriptionType();
    if (currentSubscription === ACCOUNT_TYPE.ENTERPRISE) currentSubscription = ACCOUNT_TYPE.PRIME;
    const listExpiredOutletId = getExpiredBranchId();
    const listWarnedOutlet = getNearestExpiredOutlets();
    const [branchList, setBranchList] = useState([]);
    const [groupPayment, setGroupPayment] = useState([]);
    const [outletOptions, setOutletOptions] = useState([]);
    const [supportCategory, setSupportCategory] = useState({
        [SUBSCRIPTION_PACKAGE.MONTHLY]: [],
        [SUBSCRIPTION_PACKAGE.YEARLY]: [],
        [SUPPORT_CATEGORY_NAME.ADDITIONAL_SERVICE]: [],
    });
    const [isYearly, setIsYearly] = useState(true);
    const [openDialog, setOpenDialog] = useState({
        primePlus: false,
        compare: false,
        buy: false,
    });
    const [formDataPrimePLus, setFormDataPrimePlus] = useState({
        ownerName: '',
        businessName: '',
        phone: '',
        email: '',
        address: '',
        province: '',
        city: '',
        businessType: '',
        selectedFeature: [],
    });
    const [{ marketplaceExpDate, foodorderExpDate, whatsappLaporanExpDate }, setAddOnProductExpDate] = usePartialState({
        marketplaceExpDate: null,
        foodorderExpDate: null,
        whatsappLaporanExpDate: null,
    });
    const [selectedPackage, setSelectedPackage] = useState({});
    const supportProduct = [SUPPORT_ALIAS.FOOD_ORDER, SUPPORT_ALIAS.ECOMMERCE, SUPPORT_ALIAS.WHATSAPP_LAPORAN];
    const [openHistory, setOpenHistory] = useState(false);
    const [detailTransaction, setDetailTransaction] = useState({});
    const [currentSubscriptionId, setCurrentSubscriptionId] = useState();
    const [selectedSubcriptionDetail, setSelectedSubscriptionDetail] = useState({ data: [] });
    const [selectedOutlet, setSelectedOutlet] = useState([]);
    const [supportSummary, setSupportSummary] = useState([]);
    const TabItem = styled(TabsTrigger, {
        background: 'transparent',
        height: 'fit-content',
        width: 'fit-content',
        flex: 'unset',
        padding: '$spacing-03',
        '@lg': { padding: '$compact' },
    });
    const paymentInstructionDialogRef = useRef(null);
    const renewalDialogRef = useRef(null);
    const historyRef = useRef(null);
    const isMobile = useMediaQuery('(max-width: 766px)');
    const additionalSupportProduct = useMemo(() => supportCategory[SUPPORT_CATEGORY_NAME.ADDITIONAL_SERVICE].filter(x => supportProduct.includes(x.alias)), [supportCategory[SUPPORT_CATEGORY_NAME.ADDITIONAL_SERVICE]]);
    const additionalSupportService = useMemo(() => supportCategory[SUPPORT_CATEGORY_NAME.ADDITIONAL_SERVICE].filter(x => !supportProduct.includes(x.alias)), [supportCategory[SUPPORT_CATEGORY_NAME.ADDITIONAL_SERVICE]]);
    const supportKey = useMemo(() => {
        if (isYearly) return SUBSCRIPTION_PACKAGE.YEARLY;
        return SUBSCRIPTION_PACKAGE.MONTHLY;
    }, [isYearly]);
    const mainOutletDetail = useMemo(() => {
        if ((filterBranch || idCabang) && outletList.length > 0) {
            return outletList.find(x => String((filterBranch || idCabang)) === String(x.id_cabang));
        }
        return {};
    }, [idCabang, filterBranch, JSON.stringify(outletList)]);
    const defaultSubscription = useMemo(() => {
        const findSubs = supportCategory[supportKey].find(x => x.support_name === currentSubscription);
        if (findSubs) return findSubs;
        return supportCategory[supportKey].find(x => x.support_name === ACCOUNT_TYPE.ADVANCE);
    }, [supportCategory, supportKey, currentSubscription]);
    const isAdmin = useMemo(() => String(idPermission) === PERMISSIONS.ADMIN, [idPermission]);

    const getAllBranch = async () => {
        try {
            const [res, resGroupCC, resSupportSummary] = (await Promise.allSettled([
                getAllBranchSupport(),
                supportApi.getListGroupCC(),
                supportApi.getSupportSummary(),
            ])).map((result) => {
                if (result && result.status === 'fulfilled') return result.value;
                return { data: [] };
            });
            setSupportSummary(resSupportSummary.data['add-On']);
            setGroupPayment(resGroupCC.data.filter(x => x.card.length).map(group => ({
                ...group,
                total: (group.info && group.info.search('Rp. ') > -1) ? get(group.info.split('Rp. '), '1') : 0,
            })));
            setBranchList(res.data.map((x) => {
                let code = '';
                if (resGroupCC.data) {
                    const findOutlet = resGroupCC.data.find(cc => cc.id_outlet.includes(x.id_cabang));
                    if (findOutlet) ({ code } = findOutlet);
                }
                return { ...x, code, nama_cabang: x.cabang };
            }));
        } catch (error) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    const fetchSupportCategory = async (param) => {
        const payload = {
            package: param,
            is_cms: 1,
            outlet_id: filterBranch || idCabang,
            // ...filterBranch && { outlet_id: filterBranch },
        };
        try {
            const res = await actions.support.getCategory(payload);
            if (!Array.isArray(res)) throw new Error();
            const subscriptionService = res.find(s => s.support_category_name === SUPPORT_CATEGORY_NAME.SUBSCRIPTION_SERVICE);
            const remapSubscriptionName = subscriptionService.list.map(l => ({
                ...l,
                support_name: getSubscriptionName(l.support_name),
                alias: getSubscriptionName(l.alias),
            }));
            const additionalService = res.find(s => s.support_category_name === SUPPORT_CATEGORY_NAME.ADDITIONAL_SERVICE);
            const remapAdditionalList = additionalService.list.map(l => ({
                ...l,
                support_name: t(`supportName.${l.support_name}`, { defaultValue: false }) || l.support_name,
            }));
            if (!currentSubscriptionId) {
                const findSupport = remapSubscriptionName.find(x => x.support_name === currentSubscription);
                if (findSupport) setCurrentSubscriptionId(findSupport.support_id);
            }
            setSupportCategory(curr => ({ ...curr, [param]: remapSubscriptionName, [SUPPORT_CATEGORY_NAME.ADDITIONAL_SERVICE]: remapAdditionalList }));
        } catch (e) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    const openCompareDialog = () => {
        setOpenDialog(curr => ({ ...curr, compare: true }));
    };

    const fetchAccountDetail = () => new Promise(async (resolve, reject) => {
        showProgress();
        try {
            const { data: business } = await outletApi.getBusinessInfo();
            const { data: account } = await usersApi.getAccountInfo();
            setFormDataPrimePlus(prev => ({
                ...prev,
                ownerName: account.user_name,
                businessName: business[0].name,
                phone: account.user_notlp,
                email: business[0].email,
                address: business[0].address,
            }));
            resolve({ account, business });
        } catch (e) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(e),
                variant: 'failed',
            });
            reject();
        } finally {
            hideProgress();
        }
    });

    const openFormPrimePlus = () => {
        fetchAccountDetail().then(() => setOpenDialog(curr => ({ ...curr, primePlus: true })));
    };

    const checkAddonSubscription = async (branchId) => {
        showProgress();
        setAddOnProductExpDate({ marketplaceExpDate: null, foodorderExpDate: null, whatsappLaporanExpDate: null });
        try {
            const parentId = userUtil.getLocalConfigByKey('parentId');

            const res = await supportApi.getSupportInfo({ id_merchant: parentId });
            if (
                res.data
                && res.data[branchId]
                && res.data[branchId].addon
                && SUPPORT_ALIAS.ECOMMERCE in res.data[branchId].addon
                && res.data[branchId].addon.TOKOONLINE.status
            ) {
                setAddOnProductExpDate({ marketplaceExpDate: res.data[branchId].addon.TOKOONLINE.exp_date });
            }
            if (
                res.data
                && res.data[branchId]
                && res.data[branchId].addon
                && SUPPORT_ALIAS.FOOD_ORDER in res.data[branchId].addon
                && res.data[branchId].addon.ADDONGRAB.status
            ) {
                setAddOnProductExpDate({ foodorderExpDate: res.data[branchId].addon.ADDONGRAB.exp_date });
            }
            if (
                res.data
                && res.data[branchId]
                && res.data[branchId].addon
                && SUPPORT_ALIAS.WHATSAPP_LAPORAN in res.data[branchId].addon
                && res.data[branchId].addon[SUPPORT_ALIAS.WHATSAPP_LAPORAN].status
            ) {
                setAddOnProductExpDate({ whatsappLaporanExpDate: res.data[branchId].addon[SUPPORT_ALIAS.WHATSAPP_LAPORAN].exp_date });
            }
        } catch (e) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    const fetchOutlet = async () => {
        showProgress();
        try {
            const options = await actions.outlet.getOutlet() || [];
            setOutletOptions(options.map(val => ({
                id: val.id_cabang,
                value: val.id_cabang,
                name: val.cabang_name,
            })));
        } catch (e) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    const openDetailTransaction = async (param, isChange = false) => {
        const payload = param.id;
        try {
            const res = await supportApi.getSupportTransactionDetail(payload);
            if (!res.status) throw new Error(res.msg);
            setDetailTransaction({ ...param, ...res.data, isChange });
            setOpenHistory(true);
        } catch (e) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(e),
                variant: 'failed',
            });
        }
    };

    const openDialogBuySupport = (data, outlets = []) => {
        setSelectedOutlet(outlets);
        setSelectedPackage(data);
        setOpenDialog(prev => ({ ...prev, buy: true }));
    };

    const getDetailSupport = async () => {
        try {
            const payload = {
                id_support: defaultSubscription.support_id,
                package: supportKey,
            };
            const res = await supportApi.getOutletHasSupportV4(payload);
            if (!res.status) throw new Error(res.msg);
            const monthlyPrice = defaultSubscription ? defaultSubscription.support_monthly_price : 0;
            const annualPrice = defaultSubscription ? defaultSubscription.support_monthly_annual_price : 0;
            setSelectedSubscriptionDetail({
                ...res,
                support_monthly_price: monthlyPrice,
                support_monthly_annual_price: annualPrice,
            });
        } catch (e) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(e),
                variant: 'failed',
            });
        }
    };

    useEffect(() => {
        if (defaultSubscription) getDetailSupport();
    }, [JSON.stringify(defaultSubscription)]);

    useEffect(() => {
        getAllBranch();
        fetchOutlet();
    }, []);

    useEffect(() => {
        fetchSupportCategory(SUBSCRIPTION_PACKAGE.MONTHLY);
        fetchSupportCategory(SUBSCRIPTION_PACKAGE.YEARLY);
    }, [filterBranch]);

    useEffect(() => {
        if (filterBranch || idCabang) checkAddonSubscription(filterBranch || idCabang);
    }, [filterBranch, idCabang]);

    useEffect(async () => {
        const _selectedSubscription = get(location, 'query.support');
        const _key = get(location, 'query.key') || SUBSCRIPTION_PACKAGE.YEARLY;
        const detailId = get(location, 'query.detail');
        const reffNo = get(location, 'query.change_payment');
        if (_selectedSubscription && _key && supportCategory[_key].length) {
            setIsYearly(_key === SUBSCRIPTION_PACKAGE.YEARLY);
            const acceptedSubscription = [ACCOUNT_TYPE.PRIME, ACCOUNT_TYPE.ADVANCE, ACCOUNT_TYPE.STARTER, ACCOUNT_TYPE.STARTER_BASIC];
            if (acceptedSubscription.includes(_selectedSubscription)) {
                const fx = supportCategory[_key].find(f => f.alias === _selectedSubscription || f.support_name === _selectedSubscription);
                if (fx) {
                    if (currentSubscription === ACCOUNT_TYPE.TRIAL) openDialogBuySupport(fx);
                    else if (fx.support_id === currentSubscriptionId) renewalDialogRef.current.handleOpenDialog([], fx.support_id);
                }
            }
        } else if (reffNo) setTab('billing');
        else if (detailId) {
            const detailDt = JSON.parse(localStorage.getItem('DETAIL::TRANSACTION'));
            if (detailDt && String(detailDt.id) === String(detailId)) openDetailTransaction(detailDt, detailDt.isChange);
            localStorage.removeItem('DETAIL::TRANSACTION');
        }
    }, [JSON.stringify(location), JSON.stringify(supportCategory), currentSubscriptionId]);

    useEffect(() => {
      const selectedTab = get(location, 'query.tab');
      if (selectedTab) setTab(selectedTab);
    }, [JSON.stringify(location)]);
    

    const contextData = {
        ...props,
        ...translation,
        addToast,
        currentSubscription,
        currentSubscriptionId,
        listExpiredOutletId,
        getAllBranch,
        branchList,
        isSupportExpired: isSupportExpired(),
        listWarnedOutlet,
        supportCategory,
        additionalSupportProduct,
        additionalSupportService,
        isYearly,
        setIsYearly,
        openCompareDialog,
        openFormPrimePlus,
        marketplaceExpDate,
        foodorderExpDate,
        whatsappLaporanExpDate,
        outletOptions,
        mainOutletDetail,
        supportKey,
        isMobile,
        openDialogBuySupport,
        paymentInstructionDialogRef,
        renewalDialogRef,
        openHistory,
        setOpenHistory,
        detailTransaction,
        setDetailTransaction,
        openDetailTransaction,
        selectedOutlet,
        setTab,
        defaultSubscription,
        selectedSubcriptionDetail,
        isAdmin,
        groupPayment,
        setGroupPayment,
        supportSummary,
        historyRef,
    };

    return (
        <SupportContext.Provider value={contextData}>
            <Paper
                responsive
                css={{
                    padding: 0,
                    display: 'grid',
                    mb: '$cozy',
                    gridTemplateColumns: '100%',
                    '@md': {
                        padding: '16px 24px',
                    },
                }}
            >
                <TitleSection />
                <Tabs
                    activationMode="automatic"
                    dir="ltr"
                    value={tab}
                    orientation="horizontal"
                    style={{ boxShadow: 'none' }}
                    onValueChange={val => setTab(val)}
                >
                    <TabsList css={{ boxShadow: 'none' }}>
                        <TabItem value="active" key="active">
                            {LANG_DATA.TABS_ACTIVE}
                        </TabItem>
                        {currentSubscription !== ACCOUNT_TYPE.TRIAL && (
                            <TabItem value="billing" key="billing">
                                {LANG_DATA.TABS_BILLING}
                            </TabItem>
                        )}
                        <TabItem value="support" key="support">
                            {LANG_DATA.TABS_SUPPORT}
                        </TabItem>
                        <TabItem value="history" key="history">
                            {LANG_DATA.TABS_HISTORY}
                        </TabItem>
                    </TabsList>
                    <ActiveSection />
                    <BillingSection />
                    <SupportSection />
                    <HistorySection ref={historyRef} />
                </Tabs>
                {openDialog.buy && (
                    <FormBuySubscription
                        open={openDialog.buy}
                        onOpenChange={() => setOpenDialog(curr => ({ ...curr, buy: false }))}
                        selectedPackage={selectedPackage}
                        selectedOutlet={selectedOutlet}
                    />
                )}
                {openDialog.primePlus && (
                    <FormPrimePlusSubscription
                        open={openDialog.primePlus}
                        onOpenChange={() => setOpenDialog(curr => ({ ...curr, primePlus: false }))}
                        formData={formDataPrimePLus}
                    />
                )}
                {openDialog.compare && (
                    <ComparePackage
                        open={openDialog.compare}
                        onOpenChange={() => setOpenDialog(curr => ({ ...curr, compare: false }))}
                    />
                )}
                {openHistory && (<PaymentHistoryDetail open={openHistory} onOpenChange={setOpenHistory} detail={detailTransaction} router={router} />)}
                <PaymentInstructionDialog ref={paymentInstructionDialogRef} openDetailTransaction={openDetailTransaction} />
                <RenewalDialog ref={renewalDialogRef} />
            </Paper>
        </SupportContext.Provider>
    );
};

Support.propTypes = {
    filterBranch: PropTypes.string.isRequired,
    idCabang: PropTypes.string.isRequired,
    supports: PropTypes.shape({
        hasActiveProcess: PropTypes.bool,
        transaction: PropTypes.arrayOf(PropTypes.shape()),
        category: PropTypes.arrayOf(PropTypes.shape()),
    }).isRequired,
    router: PropTypes.shape({
        push: PropTypes.func,
    }),
    showProgress: PropTypes.func.isRequired,
    hideProgress: PropTypes.func.isRequired,
    location: PropTypes.shape({
        pathname: PropTypes.string,
    }).isRequired,
    outlets: PropTypes.shape().isRequired,
    actions: PropTypes.shape({
        support: PropTypes.shape({
            getCategory: PropTypes.func,
        }),
        outlet: PropTypes.shape({
            getOutlet: PropTypes.func,
        }),
    }),
    idPermission: PropTypes.string.isRequired,
};

Support.defaultProps = {
    actions: {
        support: {
            getCategory: () => { },
        },
        outlet: {
            getOutlet: () => { },
        },
    },
    router: {
        push: () => { },
    },
};

const mapStateToProps = state => ({
    supports: state.supports,
    outlets: state.outlets,
    locations: state.locations,
    filterOutlet: state.branch.filter,
    socketSupportHistory: state.report.socketSupportHistory,
    accountInfo: state.accountInfo.accountInfoResult,
});

const mapDispatchToProps = dispatch => ({
    actions: {
        support: bindActionCreators(supportsActionCreators, dispatch),
        outlet: bindActionCreators(outletActionCreators, dispatch),
        location: bindActionCreators(locationActionCreators, dispatch),
    },
    onFetchActiveTransaction: () => dispatch(fetchHistoryPayment()),
    onFetchSupportExpiredVA: (payload = {}) => dispatch(fetchSupportExpiredVA(payload)),
});

export default connect(mapStateToProps, mapDispatchToProps)(CoreHOC(Support));
