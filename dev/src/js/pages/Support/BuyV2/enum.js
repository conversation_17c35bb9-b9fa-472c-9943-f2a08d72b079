import { accountType, dataPackage } from '../../../config/enum';
import Grab<PERSON>art<PERSON>ogo from './assets/icons/grab-mart-logo.png';
import GrabFoodLogo from './assets/icons/grab-food-logo.png';
import GoFoodLogo from './assets/icons/go-food-logo.png';
import TokopediaLogo from './assets/icons/tokopedia-logoV2.png';
import ShopeeLogo from './assets/icons/shopee-logoV2.png';
import BRILogo from '../../../../assets/images/icons/payment/bri-logo.png';
import MandiriLogo from '../../../../assets/images/icons/payment/mandiri-logo.png';
import BNILogo from '../../../../assets/images/icons/payment/bni-logo.png';
import BCALogo from '../../../../assets/images/icons/payment/bca-logo.png';
import PermataLogo from '../../../../assets/images/icons/payment/permata-logo.png';

export const SUPPORT_CATEGORY_NAME = {
    SUBSCRIPTION_SERVICE: 'LAYANAN BERLANGGANAN',
    ADDITIONAL_SERVICE: 'LAYANAN SUPPORT TAMBAHAN',
};

export const SUBSCRIPTION_PACKAGE = {
    MONTHLY: dataPackage.PACKAGE_BULANAN,
    YEARLY: dataPackage.PACKAGE_TAHUNAN,
};

export const PAYMENT_STATUS = {
    ACTIVE: '1',
    CANCEL: '4',
    SUCCESS: '35',
    ON_PROGRESS: '34',
    NEW: '39',
    EXPIRED: '42',
    INACTIVE: '6',
};

export const SUPPORT_ALIAS = {
    FOOD_ORDER: 'ADDONGRAB',
    ECOMMERCE: 'TOKOONLINE',
    SUPPORT_KEDATANGAN: 'SUPPORTKEDATANGAN',
    SUPPORT_KEDATANGAN_ADVANCE: 'SUPPORTKEDATANGANADVANCE',
    SUPPORT_KEDATANGAN_PRIME: 'SUPPORTKEDATANGANPRIME',
    TRAINING_DAN_SETUP: 'TRAININGDANSETUP',
    TRAINING_DAN_SETUP_ADVANCE: 'TRAININGDANSETUPADVANCE',
    TRAINING_DAN_SETUP_PRIME: 'TRAININGDANSETUPPRIME',
    WHATSAPP_LAPORAN: 'whatsapp_laporan'
};

export const ACCOUNT_TYPE = accountType;

export const PAYMENT_METHOD = {
    AUTO_DEBIT: 'credit_card',
    E_WALLET: 'e_wallet',
    BANK_TRANSFER: 'banktransfer',
    AKULAKU: 'akulaku',
    LINK_PAYMENT: 'link_payment',
    VIRTUAL_ACCOUNT: 'virtual_account',
};

export const PAYMENT_METHOD_LABEL = {
    AUTO_DEBIT: 'Kartu Kredit',
    E_WALLET: 'E-Wallet',
    BANK_TRANSFER: 'Bank Transfer',
    AKULAKU: 'Akulaku',
    LINK_PAYMENT: 'Link Payment',
    VIRTUAL_ACCOUNT: 'Virtual Account',
};

export const PAYMENT_METHOD_CONVERT = {
    autodebit: PAYMENT_METHOD_LABEL.AUTO_DEBIT,
    e_wallet: PAYMENT_METHOD_LABEL.E_WALLET,
    banktransfer: PAYMENT_METHOD_LABEL.BANK_TRANSFER,
    bank_transfer: PAYMENT_METHOD_LABEL.BANK_TRANSFER,
    akulaku: PAYMENT_METHOD_LABEL.AKULAKU,
    link_payment: PAYMENT_METHOD_LABEL.LINK_PAYMENT,
    virtual_account: PAYMENT_METHOD_LABEL.VIRTUAL_ACCOUNT,
    credit_card: PAYMENT_METHOD_LABEL.AUTO_DEBIT,
};

export const PAYMENT_METHOD_OPTIONS = LANG_DATA => [
    {
        value: PAYMENT_METHOD.AUTO_DEBIT,
        label: LANG_DATA ? LANG_DATA.HISTORY_SECTION_PAYMENT_AUTO_DEBIT : PAYMENT_METHOD_LABEL.AUTO_DEBIT,
    },
    { value: PAYMENT_METHOD.VIRTUAL_ACCOUNT, label: PAYMENT_METHOD_LABEL.VIRTUAL_ACCOUNT },
    { value: PAYMENT_METHOD.LINK_PAYMENT, label: PAYMENT_METHOD_LABEL.LINK_PAYMENT },
];

export const UNIT_PERIOD = {
    MONTHLY: 'Bulan',
    YEARLY: 'Tahun',
};

export const FEATURE_LIST = (t) => [
    {
        name: t('comparePackage.tokoOnline'),
        additional: [
            {
                name: t('comparePackage.punyaTokoOnlineDanNamaDomainSendiri'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.tampilanBrowserResponsifMobileDanDesktop'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.tambahanFiturWebsiteDanWebtree'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.terimaDanProsesOrderOnlineTerintegrasiDenganSemuaChannelPenjualan'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.terimaBerbagaiPembayaranOnline'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.pilihanMetodePembayaranOnlineAtauBayarDiTempatInStore'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.pengirimanOrderOnlineDenganBerbagaiPilihanKurir'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.pilihanPengirimanPickupAtauKurirSendiri'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.pengaturanPromoLengkapTerintegrasiDenganModulCrm'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.sinkronisasiStokOtomatisDenganSemuaChannelPenjualan'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.terintegrasiDenganModulProdukInventory'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.terintegrasiDenganModulAkuntansi'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.cetakLabelStickerAlamatPengiriman'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.cetakDownloadPdfInvoiceA4'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
        ],
    },
    {
        name: t('comparePackage.marketplace'),
        additional: [
            {
                name: t('comparePackage.integrasiDenganTokopediaShopeeBukalapak'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: false,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.terimaDanProsesPenjualanDariTokopediaShopeeBukalapak'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: false,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.bisaSetingHargaDanNamaYangBerbedaUntukSetiapMarketplace'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: false,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.bisaSetingAkunMarketplaceYangBerbedaUntukSetiapCabang'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: false,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.terintegrasiDenganModulProdukInventory'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: false,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.terintegrasiDenganModulAkuntansi'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: false,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.cetakLabelStickerAlamatPengiriman'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
        ],
    },
    {
        name: t('comparePackage.kasirOnlinePos'),
        additional: [
            {
                name: t('comparePackage.aplikasiKasirSmatphoneTabletDesktopDualScreen'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.aplikasiWaitressSmartphoneDanTablet'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.empatJenisPilihanNavigasiKasirQuickServiceDineInRetailJasaEcommerce'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatBerbagaiJenisOrderBungkusDeliveryTable'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatJenisProdukMultiVarianMultiQuantity'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatPembayaranTunaiDanNonTunai'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.melakukanOrderDanDenahMeja'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.melakukanSplitBill'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.melakukanDanMencatatPembatalanOrderVoidDanRefund'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatHargaPembelianMinimum'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatPenjualanTanpaProduk'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatPenjualanOjekOnlinePenjualanBedaHarga'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatHargaNego'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatKomisiPenjualan'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatKasKecilPengeluaranOutlet'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mengaturBiayaServiceChargePajakPembulatanPecahan'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencetakStrukKasirCheckerDapurLabelDelivery'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatPenjualanHargaMultisatuan'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.melakukanManajemenMejaGabungPisahPindah'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.membatasiWaktuDudukMeja'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.menjadikanPembayaranInvoice'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatUangDepositPelanggan'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mengirimStrukDigitalWhatsappSmsEmail'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mengirimStrukDenganTambahanWeblink'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.emenuQrDanWeblink'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
        ],
    },
    {
        name: t('comparePackage.crm'),
        additional: [
            {
                name: t('comparePackage.mencatatDataPelanggan'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatHargaSpesialGrupPelanggan'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.poinLoyaltyDanPenukaran'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.bikinPromoPerPenjualanDenganKombinasiHariJamJumlahTransaksiKelipatanDll'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.bikinPromoPerProdukDenganKombinasiHariJamJumlahTransaksiKelipatanDll'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.diskonPromoDalamBentukPersenNomimalAtauGratisProduk'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.laporanAktifitasPelangganLengkap'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.laporanPromoDanLoyalti'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.laporanAnalisaKepuasanPelanggan'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.kupon'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.depositDanaPelanggan'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
        ],
    },
    {
        name: t('comparePackage.marketingCampaign'),
        additional: [
            {
                name: t('comparePackage.smsBroadcastLongNumberMasking'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.whatsappBlastMaskingMajoo'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.smsLbaTelkomsel'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.emailBlastMaskingMajoo'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.designInstagramFeed'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
        ],
    },
    {
        name: t('comparePackage.integrasi'),
        additional: [
            {
                name: t('comparePackage.terimaPembayaranDigitalQrNasional'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.aksesTambahanModalKerjaSDRp2M'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.terimaPromoTelkomselPoin'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.integrasiDenganEdcBca'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
        ],
    },
    {
        name: t('comparePackage.onlineFoodOrder'),
        additional: [
            {
                name: t('comparePackage.grabfood'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: false,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
        ],
    },
    {
        name: t('comparePackage.inventory'),
        additional: [
            {
                name: t('comparePackage.mencatatStokHargaModalDanHargaJual'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatResep'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatBahanBaku'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.memasukkanStokBaru'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.melakukanStokProduksi'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.melakukanStokOpname'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.notifikasiStokMinimum'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatStokMultisatuan'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatHppCogsAverage'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.melakukanStokTerbuang'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.melakukanPurchaseOrderInvoice'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatDataSupplier'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.melakukanMutasiStokAntarOutlet'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
        ],
    },
    {
        name: t('comparePackage.managementKaryawan'),
        additional: [
            {
                name: t('comparePackage.mencatatAbsensiDigital'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatShiftKasir'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.mencatatKomisiKaryawanOtomatis'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.laporanKaryawanAbsensiShiftKasirKomisi'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.pengaturanOtorisasiDanAksesKaryawan'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
        ],
    },
    {
        name: t('comparePackage.laporanLengkap'),
        additional: [
            {
                name: t('comparePackage.laporanPenjualan30JenisLaporan'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.laporanKasOutlet'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.downloadLaporan'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.laporanAnalisaBisnis'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.dikirimEmailOtomatisLaporanHarian'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
        ],
    },
    {
        name: t('comparePackage.akuntansi'),
        additional: [
            {
                name: t('comparePackage.fakturPenjualan'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.biayaDanPengeluaran'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.saldoDanKasBank'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.daftarAkunDanBukuBesar'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.jurnalOtomatis'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.laporanKeuanganNeracaRugiLabaArusKasBukuBesarJurnalHutangPiutang'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
        ],
    },
    {
        name: t('comparePackage.aplikasiOwner'),
        additional: [
            {
                name: t('comparePackage.ownerAppAplikasiDashboardUntukOwnerManager'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.multiPengguna'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
        ],
    },
    {
        name: t('comparePackage.inspirasiBisnis'),
        additional: [
            {
                name: t('comparePackage.aksesArtikelWirausahaPilihanTipsKisahSuksesInspirasi'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.aksesBlogVideoWirausahaPilihan'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.eventGratisWebinarDanKelasMajoo'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.majalahWirausaha'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
        ],
    },
    {
        name: t('comparePackage.layanan'),
        additional: [
            {
                name: t('comparePackage.call24Jam'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.chatWhatsapp24Jam'),
                [ACCOUNT_TYPE.STARTER]: true,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.chatDashboard24Jam'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: true,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
        ],
    },
    {
        name: t('comparePackage.aplikasiOtomasi'),
        additional: [
            {
                name: t('comparePackage.kitchenDisplay'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: false,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.orderDisplay'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: false,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.selfOrder'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: false,
                [ACCOUNT_TYPE.PRIME]: true,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
        ],
    },
    {
        name: t('comparePackage.aplikasiOtomasiPremium'),
        additional: [
            {
                name: t('comparePackage.kustomisasiLayoutTable'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: false,
                [ACCOUNT_TYPE.PRIME]: false,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.membership'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: false,
                [ACCOUNT_TYPE.PRIME]: false,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.compareReportAccounting'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: false,
                [ACCOUNT_TYPE.PRIME]: false,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.warehouseManagement'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: false,
                [ACCOUNT_TYPE.PRIME]: false,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.cetakBarcode'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: false,
                [ACCOUNT_TYPE.PRIME]: false,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
            {
                name: t('comparePackage.extendedDataCustomer'),
                [ACCOUNT_TYPE.STARTER]: false,
                [ACCOUNT_TYPE.ADVANCE]: false,
                [ACCOUNT_TYPE.PRIME]: false,
                [ACCOUNT_TYPE.PRIMEPLUS]: true,
            },
        ],
    },
];

export const PURCHASE_LIMIT = {
    MONTHLY: 12,
    YEARLY: 1,
};

export const FEATURES_PRIMEPLUS = t => [
    { label: t('features.customizedLayoutTable'), value: 'customized-layout-table' },
    { label: t('features.warehouseManagement'), value: 'warehouse-management' },
    { label: t('features.compareReportAccounting'), value: 'compare-report-accounting' },
    { label: t('features.extendedDataCustomer'), value: 'extended-data-customer' },
    { label: t('features.membership'), value: 'membership' },
    { label: t('features.barcodePrinting'), value: 'cetak-barcode' },
];

export const BUSINESS_TYPES = [
    { value: 'fnb-plus', name: 'Prime F&B+' },
    { value: 'retail-plus', name: 'Prime Retail+' },
    { value: 'jasa-plus', name: 'Prime Jasa+' },
    { value: 'beauty-plus', name: 'Prime Beauty+' },
];

export const ADD_ON_SUPPORT = {
    [SUPPORT_ALIAS.ECOMMERCE]: [
        { name: 'Tokopedia', icon: TokopediaLogo, link: '/tokopedia' },
        { name: 'Shopee', icon: ShopeeLogo, link: '/shopee' },
        { name: 'Grab Mart', icon: GrabMartLogo, link: '/grabmart' },
        // { name: 'Bukalapak', icon: BukalapakLogo, link: '/bukalapak' },
    ],
    [SUPPORT_ALIAS.FOOD_ORDER]: [
        { name: 'Grab Food', icon: GrabFoodLogo, link: '/grabfood' },
        { name: 'Go Food', icon: GoFoodLogo, link: '/gofood' },
    ],
};

export const MAIN_FORM = {
    1: 'mainForm',
    2: 'supportForm',
    3: 'billingForm',
};

export const PAYMENT_PROVIDER_VA = [
    {
        id: 1,
        logo: BCALogo,
        name: 'BCA',
    },
    {
        id: 2,
        logo: BNILogo,
        name: 'BNI',
    },
    {
        id: 5,
        logo: PermataLogo,
        name: 'Permata',
    },
    {
        id: 3,
        logo: BRILogo,
        name: 'BRI',
    },
    {
        id: 4,
        logo: MandiriLogo,
        name: 'Mandiri',
    },
];

export const BILLING_ACTION = {
    ADD_OUTLET: 'add-outlet',
    DETAIL_OUTLET: 'detail-outlet',
    DETAIL_TRANSACTION: 'detail-transaction',
    CHANGE_PAYMENT: 'change-payment',
    PAYMENT_GUIDE: 'payment-guide',
    CHECK_PAYMENT_STATUS: 'check-payment-status',
    DETAIL_GROUP_PAYMENT: 'detail-group-payment',
    STOP_RECURRENT: 'stop-recurrent',
    MOVE_GROUP: 'move-group',
};

export const isPrimeBenefits = (alias) => [SUPPORT_ALIAS.ECOMMERCE, SUPPORT_ALIAS.FOOD_ORDER].includes(alias);
