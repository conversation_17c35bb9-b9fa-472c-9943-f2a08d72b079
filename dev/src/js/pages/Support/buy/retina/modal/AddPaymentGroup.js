import React, {
  useEffect,
  useState,
  useCallback,
} from 'react';
import {
  PageDialog,
  DialogClose,
  Button,
  Box,
  Flex,
  ToastContext,
  Paper,
  Heading,
  InputText,
  FormGroup,
  FormLabel,
  Paragraph,
  Separator,
  EditableTable,
  Tag,
} from '@majoo-ui/react';
import { PlusOutline } from '@majoo-ui/icons';
import { useForm } from 'react-hook-form';
import moment from 'moment';
import {
  get, groupBy, isEmpty,
} from 'lodash';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useTranslation } from 'react-i18next';
import { catchError } from '../../../../../utils/helper';
import {
  PAYMENT_METHOD, SUBSCRIPTION_PACKAGE,
} from '../enum';
import {
 formLabelStyle, generateIdOrderKirim, remapAutodebitPayload, formatPrice as format,
} from '../utils';
import userUtil from '../../../../../utils/user.util';
import ModalAutodebit from './ModalAutodebit';
import { midtransPaidAutodebit } from '../../library/payment';
import ModalAutodebitAlreadyHasGroup from './ModalAutodebitAlreadyHasGroup';

const calculatePrice = (_currentSubscription, _outletSupport) => {
  const administrationPrice = _currentSubscription.additional_price;
  const subscriptionPrice = _outletSupport.reduce((previous, current) => previous + Number(current.prorate_price), 0);
  return {
    administrationPrice,
    subscriptionPrice,
    totalPrice: subscriptionPrice + administrationPrice,
  };
};

const schema = yup.object().shape({
  _outletRow: yup.array().of(yup.object().shape({
    nama_cabang: yup.string().required('Mohon lengkapi Nama Outlet'),
  })),
});

const defaultOutletPayload = (name = '', id, outletSupport) => ({
  cabang_id: id,
  nama_cabang: name,
  claim_qty: 1,
  is_utama: '0',
  prorate_price: outletSupport.prorate,
  prorate_exp_date: outletSupport.prorate_exp_date,
});

const AddPaymentGroup = (props) => {
  const {
    open, onOpenChange, currentSubscription, paymentMethod, outletSupport, idUser, accountInfo, subscriptionFilter,
  } = props;

  const { t } = useTranslation(['Pengaturan/subscription/mainSubscriptionForm', 'translation', 'Pengaturan/subscription/modalAutodebit']);

  const isYearly = subscriptionFilter === SUBSCRIPTION_PACKAGE.YEARLY;
  const {
    register, getValues, setValue, handleSubmit, formState: { errors }, unregister,
  } = useForm({
    resolver: yupResolver(schema),
    shouldUnregister: false,
    defaultValues: {
      id_order: '',
      support: currentSubscription ? currentSubscription.support_id : '',
      id_outlet: '',
      bill_total: '',
      package: isYearly ? SUBSCRIPTION_PACKAGE.YEARLY : SUBSCRIPTION_PACKAGE.MONTHLY,
      card_number: '',
      nama: accountInfo.user_name,
      email: accountInfo.user_email,
      _outletRow: outletSupport.data || [],
    },
  });

  const { addToast } = React.useContext(ToastContext);
  const [openHasGroup, setOpenHasGroup] = useState(false);
  const [recurringGroupList, setRecurringGroupList] = useState([]);
  const [rowCounter, setRowCounter] = useState(0);

  const [openAutodebit, setOpenAutodebit] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [orderID, setOrderID] = useState('');
  const [price, setPrice] = useState({
    totalPrice: 0,
    subscriptionPrice: 0,
    administrationPrice: 0,
  });
  const [cardError, setCardError] = useState('');

  const headerData = useCallback(onRowAction => [
    {
      Header: 'OUTLET',
      accessor: 'nama_cabang',
      isMobileHeader: true,
      width: 100,
      Cell: (instance) => {
        const { value, row: { original, index } } = instance;
        const key = `_outletRow.${index}.nama_cabang`;
        // key yang jelas tidak ada jika outlet baru agar trigger InputText
        if (!original.id_support) {
          return (
            <InputText {...register(key)} defaultValue={getValues(key)} placeholder="Contoh: Outlet 1" isInvalid={!!get(errors, key)} />
          );
        }
        return (
          <Box css={{
            display: 'flex', flexDirection: 'row', gap: '$spacing-03', '@md': { flexDirection: 'column', gap: '$spacing-02' },
          }}
          >
            <Paragraph>
              {value}
            </Paragraph>
            {original.is_utama === '1' && (
              <Tag type="varian">Outlet Utama</Tag>
            )}
          </Box>
        );
      },
    },
    {
      Header: 'STATUS LANGGANAN',
      accessor: 'exp_date',
      width: 100,
      Cell: (instance) => {
        const { value } = instance;
        return (
          <Paragraph>
            {moment(value, 'YYYY-MM-DD HH:mm:ss').locale('id-ID').format('DD MMM YYYY')}
          </Paragraph>
        );
      },
    },
    {
      Header: 'GRUP PEMBAYARAN',
      accessor: 'code',
      width: 100,
      Cell: (instance) => {
        const { value } = instance;
        return (
          <Paragraph>
            {value || '-'}
          </Paragraph>
        );
      },

    },
    {
      id: 'duration',
      Header: 'MASA BERLANGGANAN',
      Cell: (instance) => {
        const { row: { original } } = instance;
        const getOutletID = Object.keys(userUtil.getLocalConfigByKey('outletExp'));
        const findAvailableOutlet = getOutletID.find(z => z === original.cabang_id);
        const qtySupport = findAvailableOutlet ? 1 : 0;
        const period = isYearly ? 'months' : 'years';
        let message = moment(original.exp_date).add(Number(qtySupport), period).format('DD MMM YYYY');

        if (paymentMethod === PAYMENT_METHOD.AUTO_DEBIT && original.is_prorate) {
          message = moment(original.prorate_exp_date, 'YYYY-MM-DD').locale('id-ID').format('DD MMM YYYY');
        }
        if (moment(original.exp_date) < moment()) {
          message = moment().add(Number(qtySupport), period).format('DD MMM YYYY');
        }
        return (
          <Box>
            <Paragraph color="secondary" variant="helper">{`Estimasi hingga: ${message}`}</Paragraph>
          </Box>
        );
      },
    },
    {
      Header: '',
      accessor: 'remove',
      width: 24,
      Cell: ({ row: { index, original } }) => {
        const id = index;
        return (
          <Box css={{ ml: 'auto' }}>
            <EditableTable.Remove onRemove={() => onRowAction({ type: 'DELETE', payload: id })} />
          </Box>
        );
      },
    },
  ], [tableData.length, errors._outletRow]);

  // Init
  useEffect(() => {
    if (isEmpty(currentSubscription) || isEmpty(outletSupport.data)) {
      return;
    }
    setValue('_outletRow', outletSupport.data);
    setTableData(outletSupport.data);
    setPrice(calculatePrice(currentSubscription, outletSupport.data));
  }, [subscriptionFilter, outletSupport.prorate]);


  const handleTableAction = (args) => {
    const cache = getValues('_outletRow');
    switch (args.type) {
      case 'DELETE':
        setValue('_outletRow', []);
        setTableData((data) => {
          const filtered = data.filter((val, index) => {
            const match = index !== args.payload;
            return match;
          });
          setValue('_outletRow', filtered);
          setPrice(calculatePrice(currentSubscription, filtered));
          return filtered;
        });
        break;
      case 'ADD':
        setTableData((data) => {
          const res = cache.map((c, index) => {
            const match = data.find((d) => {
              const cond = d.cabang_id === c.cabang_id;
              return cond;
            });
            if (match && match.id_support) {
              return match;
            }
            return defaultOutletPayload(c.nama_cabang, c.cabang_id, outletSupport);
          });
          const filtered = [
            ...res,
            defaultOutletPayload('', `row-${rowCounter}`, outletSupport),
          ];
          setValue('_outletRow', filtered);
          setPrice(calculatePrice(currentSubscription, filtered));
          return filtered;
        });
        setRowCounter(c => c + 1);
        break;
      default:
        break;
    }
  };

  const checkQtySupport = (outletID) => {
    const getOutletID = Object.keys(userUtil.getLocalConfigByKey('outletExp'));
    return getOutletID.includes(outletID);
  };

  const isPayable = () => {
    const validOutletSupportList = tableData.filter(outlet => outlet.code && checkQtySupport(outlet.cabang_id));
    const outletByCode = groupBy(validOutletSupportList, 'code');
    const dataKeys = Object.keys(outletByCode);
    const _recurringGroupList = dataKeys.map(x => Object.assign({}, {
      idGroup: x,
      detail: outletByCode[x].map(z => z.nama_cabang),
    }));
    if (_recurringGroupList.length > 0) {
      setRecurringGroupList(_recurringGroupList);
      setOpenHasGroup(true);
      return false;
    }
    return true;
  };

  const handleOpenAutodebit = (e) => {
    e.preventDefault();
    if (!isPayable()) return;
    handleSubmit(() => {
      const supportID = Number(currentSubscription.support_id);
      setOpenAutodebit(true);
      setOrderID(generateIdOrderKirim(idUser, supportID));
    })();
  };

  const handleConfirm = async (cardPayload) => {
    setValue('id_order', orderID);
    setValue('bill_total', price.totalPrice);

    const outletPayload = getValues('_outletRow').map(outlet => ({
      // If new outlet should be empty string
      id_cabang: outlet.id_support ? outlet.cabang_id : '',
      // autodebit selalu 1?
      claim_qty: 1,
      cabang_name: outlet.nama_cabang,
      isUtama: outlet.is_utama === '1',
    }));
    const admin = {
      id_cabang: 1,
      claim_qty: 1,
      tagihan: price.administrationPrice,
      cabang_name: 'Biaya Administrasi',
    };

    setValue('id_outlet', JSON.stringify([...outletPayload, admin]));

    try {
      const payload = {
        ...getValues(),
        ...cardPayload,
      };
      const resMidtrans = await midtransPaidAutodebit(remapAutodebitPayload(payload));
      if (!resMidtrans.status) throw new Error(resMidtrans.message);
    } catch (e) {
      const message = catchError(e);
      switch (message) {
        case 'One or more parameters in the payload is invalid.':
          setCardError(t('errors.invalid', { ns: 'Pengaturan/subscription/modalAutodebit' }));
          break;
        case 'Card is not authenticated.':
          setCardError(t('errors.unautenticated', { ns: 'Pengaturan/subscription/modalAutodebit' }));
          break;
        default:
          setCardError(message);
          break;
      }
    }
  };

  return (
    <React.Fragment>
      <PageDialog open={open} onOpenChange={onOpenChange}>
        <PageDialog.Title css={{
          position: 'relative', left: '-18px', whiteSpace: 'nowrap', '@md': { left: 0 },
        }}
        >
          {`Beli Paket Berlangganan ${isYearly ? '(Tahunan)' : '(Bulanan)'}`}
        </PageDialog.Title>
        <PageDialog.Content>
          <Box
            css={{
              display: 'grid',
              gridTemplateColumns: '1fr',
              height: '100%',
              width: '100%',
              position: 'relative',
              '@md': {
                gridTemplateColumns: '1fr 647px 1fr',
                padding: '40px 24px',
                gap: '$cozy',
              },
            }}
          >
            <Box />
            <Paper
              as="form"
              id="form-detail-history"
              responsive
              css={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                gap: '$compact',
                padding: '$cozy $compact',
                '@md': {
                  padding: '$cozy',
                  gap: '$cozy',
                  height: 'fit-content',
                },
              }}
            >
              <Box
                css={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '$compact',
                  '@md': {
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    mb: '$spacing-03',
                  },
                }}
              >
                <Heading
                  as="h3"
                  heading="pageTitle"
                >
                  {`${currentSubscription.support_name} - ${format(currentSubscription.discount_price)}/${currentSubscription.duration_unit_support}`}
                </Heading>
              </Box>
              <FormGroup css={{ gridTemplateColumns: '1fr auto' }}>
                <FormLabel css={formLabelStyle}>Gunakan Kupon</FormLabel>
                <Button type="button" size="sm" buttonType="secondary">Pilih Kupon</Button>
              </FormGroup>
              <FormGroup css={{ gridTemplateColumns: '1fr auto' }}>
                <FormLabel css={formLabelStyle}>Outlet</FormLabel>
                <Button type="button" size="sm" buttonType="primary">Atur Outlet</Button>
              </FormGroup>
              <Box>
                <EditableTable
                  key={`${tableData.length}-table`}
                  data={tableData}
                  columns={headerData(val => handleTableAction(val))}
                  rowHeight={72}
                  css={{
                    padding: 0,
                  }}
                />
              </Box>
              <Button
                type="button"
                onClick={() => { handleTableAction({ type: 'ADD' }); }}
                size="md"
                buttonType="secondary"
                leftIcon={<PlusOutline color="currentColor" />}
              >
                Tambah Outlet
              </Button>
              <Box>
                <Box css={{
                  display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '$compact', color: '$textPrimary',
                }}
                >
                  <Paragraph paragraph="shortContentRegular">
                    Biaya Langganan
                  </Paragraph>
                  <Paragraph paragraph="shortContentBold" align="right">
                    {format(price.subscriptionPrice)}
                  </Paragraph>
                  <Paragraph paragraph="shortContentRegular">
                    Biaya Administrasi
                  </Paragraph>
                  <Paragraph paragraph="shortContentBold" align="right">
                    {format(price.administrationPrice)}
                  </Paragraph>
                </Box>
                <Box>
                  <Separator css={{ my: 20, '@md': { my: 12 } }} />
                  <Flex justify="between">
                    <Heading as="span" heading="sectionTitle">TOTAL TAGIHAN</Heading>
                    <Heading as="span" heading="sectionTitle">{format(price.totalPrice)}</Heading>
                  </Flex>
                  <Separator css={{ mt: 20, mb: 4, '@md': { mt: 12 } }} />
                </Box>
              </Box>
            </Paper>
          </Box>
        </PageDialog.Content>
        <PageDialog.Footer
          css={{
            justifyContent: 'center',
            height: 'auto',
            padding: '$spacing-05',
            borderTop: '1px solid $gray150',
            '@md': { height: 64, padding: '12px 24px', borderTop: 'none' },
          }}
        >
          <Box
            css={{
              display: 'flex',
              width: '100%',
              gap: '$compact',
              justifyContent: 'flex-end',
              '@md': {
                maxWidth: 647,
              },
            }}
          >
            <Flex gap={5} css={{ flex: 1, '@md': { flex: 'unset' } }}>
              <DialogClose asChild>
                <Button
                  size="md"
                  buttonType="ghost"
                  type="button"
                  css={{ flex: 1, '@md': { flex: 'unset' } }}
                >
                  Batal
                </Button>
              </DialogClose>
            </Flex>
            <Button
              size="md"
              type="button"
              css={{ flex: 1, '@md': { flex: 'unset' } }}
              onClick={handleOpenAutodebit}
            >
              Beli
            </Button>
          </Box>
        </PageDialog.Footer>
      </PageDialog>
      <ModalAutodebit onConfirm={handleConfirm} open={openAutodebit} onOpenChange={setOpenAutodebit} price={price.totalPrice} orderID={orderID} cardError={cardError} />
      <ModalAutodebitAlreadyHasGroup open={openHasGroup} onOpenChange={setOpenHasGroup} detail={recurringGroupList} />
    </React.Fragment>
  );
};

export default AddPaymentGroup;
