import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import CoreHOC from '../../../../../core/CoreHOC';
import BatalkanPengajuanGrab from './BatalkanPengajuanGrab';
import PutuskanIntegrasi from './PutuskanIntegrasi';
import PengajuanOutlet from '../PengajuanOutlet';
import PengajuanUlang from './PengajuanUlang';
import ModalPopup from '../../../../../components/modalpopup/Container';
import STATUS from './EnumStatusMarketplace';
import { catchError } from '../../../../../utils/helper';

import * as supportAccessAPI from '../../../../../data/access/index';

let selectedIndex = null;
@CoreHOC
export default
class PanelPengajuanGrab extends PureComponent {
    static propTypes = {
        showProgress: PropTypes.func.isRequired,
        idPermission: PropTypes.string,
        hideProgress: PropTypes.func,
        namaUser: PropTypes.string,
        addNotification: PropTypes.func,
        assignCalendar: PropTypes.func,
        assignButtons: PropTypes.func,
        idUser: PropTypes.string.isRequired,
        idCabang: PropTypes.string.isRequired,
        hidePanelSideGrab: PropTypes.func,
    };

    static defaultProps = {
        idPermission: '',
        namaUser: '',
        hideProgress: () => {},
        addNotification: () => {},
        assignCalendar: () => {},
        assignButtons: () => {},
        hidePanelSideGrab: () => {},
    };

    constructor(props) {
        super(props);
        this.state = {
            cutSubmissionData: null,
            callCancellationData: null,
            callSubmissionAgainData: null,
            callSubmissionData: null,
            callAdjustmentData: null,
            userAccessPayload: undefined,
        };
    }

    componentWillMount() {
        this._fetchDetailAkun();
    }

    _fetchDetailAkun = async () => {
        const {
            idCabang, namaUser,
            addNotification, showProgress, hideProgress, idUser, idPermission,
        } = this.props;

        showProgress();
        try {
            const payload = {
                id_outlet: idCabang,
            };
            const res = await supportAccessAPI.getEmployeeAccess(payload);
            const { status, data, msg } = res;
            if (!status) throw new Error(msg);
            const selectedUser = data.find(x => String(x.id_employee) === String(idUser));
            const userData = {
                namaUser,
                outlet: selectedUser.outlet_name,
                email: selectedUser.username,
                accessType: selectedUser.permission_name,
                store: selectedUser.store_name,
                storeId: selectedUser.id_store,
            };
            // debugger;
            const payloadUserAccess = {
                id_employee: idUser,
                username: userData.email,
                start_date: moment().format('YYYY-MM-DD HH:mm'),
                store: userData.store,
                id_outlet: idCabang,
                outlet: userData.outlet,
                id_permission: idPermission,
                permission: userData.accessType,
                duration: '720',
                note: 'Integrasi Grabfood',
                name: userData.namaUser,
            };
            // debugger;
            this.setState({
                userAccessPayload: payloadUserAccess,
            });
        } catch (err) {
            addNotification({
                level: 'error',
                title: 'error when trying to fetch support user data',
                message: catchError(err),
            });
        } finally {
            hideProgress();
        }
    }

    statusOutletForma = (status) => {
        let val = '';
        switch (status) {
            case STATUS.STATUS_OUTLET.REVIEW:
                val = 'Proses Peninjauan';
                break;
            case STATUS.STATUS_OUTLET.DENIED:
                val = 'Ditolak';
                break;
            case STATUS.STATUS_OUTLET.DRAFT:
                val = 'Draft';
                break;
            case STATUS.STATUS_OUTLET.APPROVED:
                val = 'Disetujui';
                break;
            case STATUS.STATUS_OUTLET.TERMINATION:
                val = 'Pengajuan Pemutusan Integrasi';
                break;
            case STATUS.STATUS_OUTLET.WAITING_FOR_INTEGRATED:
                val = 'Menunggu proses integrasi';
                break;
            case STATUS.STATUS_OUTLET.INTEGRATED:
                val = 'Terintegrasi';
                break;
            case STATUS.STATUS_OUTLET.REMOVED:
                val = 'Dihapus';
                break;
            case STATUS.STATUS_OUTLET.NOT_SUBMITTED_YET:
                val = 'Belum Mengajukan';
                break;
            default:
                val = 'Belum Mengajukan';
    }

    return val;
    };

    generateButtonOutletAction = (outlet, index) => {
        const buttonContainer = [];

        switch (String(outlet.status)) {
            case STATUS.STATUS_OUTLET.NOT_SUBMITTED_YET:
                buttonContainer.push(
                    <button
                        key={index}
                        type="button"
                        className="btn btn-primary pull-right"
                        onClick={() => this.callConfirmPengajuan(index)}
                    >
                        Ajukan
                    </button>,
                );

                break;
            case STATUS.STATUS_OUTLET.DENIED:
                buttonContainer.push(
                    <button
                        key={index}
                        type="button"
                        className="btn btn-primary pull-right"
                        onClick={() => {
                            this.callConfirmPengajuanUlang(index);
                        }}
                    >
                        Ajukan Ulang
                    </button>,
                );

                break;
            case STATUS.STATUS_OUTLET.APPROVED:
                buttonContainer.push(
                    <button
                        key={index}
                        type="button"
                        className="btn btn-default pull-right"
                        onClick={() => {
                            this.callAdjustment(index);
                        }}
                    >
                        Lakukan Pengaturan Awal
                    </button>,
                );

                break;


            case STATUS.STATUS_OUTLET.INTEGRATED:
                buttonContainer.push(
                    <button
                        key={index}
                        type="button"
                        className="btn pull-right redTextBtn"
                        onClick={() => {
                            this.cutSubmission(index);
                        }}
                    >
                        Putuskan Integrasi
                    </button>,
                );

                break;

            default:
                break;
        }

        return buttonContainer;
    };

    // tujuan ulang
    callSubmissionAgain = () => {
        const { data } = this.props;
        const { grabDataFull } = data;
        const { grabData } = grabDataFull;

        this.confirmAjukanGrabfoodUlang.hidePopup();
        const result = {
                submission_id: grabData.submission_id,
                submission_number: grabData.submission_number,
                provider_id: grabData.provider_id,
                merchant: grabData.merchant,
                outlets: grabData.outlets[selectedIndex],
            };
        this.setState({
                callSubmissionAgainData: result,
            },
            () => {
                this.submissionAgainModal.showPopup();
            });
    };

    callCancellation = (index) => {
        const { data } = this.props;
        const { grabDataFull } = data;
        const { grabData } = grabDataFull;

        const result = {
                submission_id: grabData.submission_id,
                submission_number: grabData.submission_number,
                provider_id: grabData.provider_id,
                merchant: grabData.merchant,
                outlets: grabData.outlets[index],
            };

        this.setState({
                callCancellationData: result,
            },
            () => {
                this.cancelSubmission.showPopup();
            });
    };

    cutSubmission = (index) => {
        const { data } = this.props;
        const { grabDataFull } = data;
        const { grabData } = grabDataFull;

        const result = {
                submission_id: grabData.submission_id,
                submission_number: grabData.submission_number,
                provider_id: grabData.provider_id,
                merchant: grabData.merchant,
                outlets: grabData.outlets[index],
            };

        this.setState({
                cutSubmissionData: result,
            },
            () => {
                this.CutSubmissionModal.showPopup();
            });
    };

    callAdjustment = (index) => {
        const { data, hidePanelSideGrab, router } = this.props;
        const { grabDataFull } = data;
        const { grabData } = grabDataFull;

        const result = {
                submission_id: grabData.submission_id,
                submission_number: grabData.submission_number,
                provider_id: grabData.provider_id,
                merchant: grabData.merchant,
                outlets: grabData.outlets[index],
            };

        this.setState({
            callAdjustmentData: result,
        });

        hidePanelSideGrab();
        router.push(
            `/order-online/pengajuan-order-online/pengaturan-grab/${grabData.outlets[index].id_outlet}`,
        );
    };

    callSubmission = (index) => {
        const { data } = this.props;
        const { grabDataFull } = data;
        const { grabData } = grabDataFull;

        const rawData = {
                submission_id: grabData.submission_id,
                submission_number: grabData.submission_number,
                provider_id: grabData.provider_id,
                merchant: grabData.merchant,
                outlets: grabData.outlets[index],
            };

        // fix empty value / null
        const result = JSON.parse(
            JSON.stringify(rawData).replace(/\:null/gi, ':""'),
        );

        this.setState({
                callSubmissionData: result,
            },
            () => {
                this.submissionModal.showPopup();
            });
    };

    gotoSubmissionGrab = () => {
        this.confirmAjukanGrabfood.hidePopup();
        this.callSubmission(selectedIndex);
    };

    gotoCreateGrabfoodMerchant = () => {
        const {
            hidePanelSideGrab,
            router, data: { grabDataFull: { grabData: { outlets } } },
        } = this.props;
        const selectedIdOutlet = outlets[selectedIndex].id_outlet;

        this.confirmAjukanGrabfood.hidePopup();
        hidePanelSideGrab();
        router.push(`/pengajuan-akun-digital/grab-merchant/create?idOutletSelected=${selectedIdOutlet}`);
    };

    callConfirmPengajuan = (index) => {
        selectedIndex = index;
        this.confirmAjukanGrabfood.showPopup();
    }

    callConfirmPengajuanUlang = (index) => {
        selectedIndex = index;
        this.confirmAjukanGrabfoodUlang.showPopup();
    }

    render() {
        const {
            addNotification,
            showProgress,
            hideProgress,
            idUser,
            data,
        } = this.props;
        const { grabDataFull } = data;
        const { grabData } = grabDataFull;

        const {
            cutSubmissionData,
            callCancellationData,
            callSubmissionAgainData,
            callSubmissionData,
            callAdjustmentData,
            userAccessPayload,
        } = this.state;
        return (
            <div style={{ position: 'relative' }}>
                <div className="row mb-lg side-popup-title ">
                    <div className="col-sm-6">
                        <h4 className="kas-bank__color--black">
                            <strong>Integrasi Grab - Pilih Outlet</strong>
                        </h4>
                    </div>
                </div>

                <div className="row">
                    <div className="col-sm-12">
                        {/* List Content */}
                        <div className="panel panel-default">
                            <div className="panel-body panelMarket">
                                <span className="fa fa-info-circle panelMarket__Info" />
                                Pilih outlet yang akan disambungkan dengan Grab
                            </div>
                        </div>
                        {/* List Content */}
                    </div>
                </div>
                {/* List Outlet */}
                <div className="row mb-lg p15">
                    {grabData.outlets.map((list, index) => (
                        <div
                            key={`list-outlet-${index}`}
                            className="col-sm-12 listOutlet"
                        >
                            <div className="col-sm-6">
                                <ul className="list-unstyled">
                                    <li className="">
                                        <strong>{list.name}</strong>
                                    </li>
                                    <li className="">
                                        {'Status '}
                                        {this.statusOutletForma(list.status)}
                                    </li>
                                </ul>
                            </div>
                            <div className="col-sm-6">
                                {this.generateButtonOutletAction(list, index)}
                            </div>
                        </div>
                    ))}
                </div>
                {/* List Outlet */}
                <BatalkanPengajuanGrab
                    {...this.props}
                    data={callCancellationData}
                    addNotification={addNotification}
                    showProgress={showProgress}
                    hideProgress={hideProgress}
                    idUser={idUser}
                    text="Apabila pengajuan integrasi dibatalkan, maka status integrasi outlet terkait akan menjadi 'Belum Mengajukan' dan harus melakukan proses pengajuan kembali dari awal."
                    ref={(c) => {
                        this.cancelSubmission = c;
                    }}
                />
                <PutuskanIntegrasi
                    {...this.props}
                    data={cutSubmissionData}
                    addNotification={addNotification}
                    showProgress={showProgress}
                    hideProgress={hideProgress}
                    idUser={idUser}
                    text="Memutuskan integrasi dengan GrabFood akan menghapus semua relasi outlet dengan akun GrabFood yang sudah terintegrasi sebelumnya.
                    Proses ini membutuhkan waktu 2 - 7 x 24 jam hingga proses pemutusan selesai sepenuhnya."
                    ref={(c) => {
                        this.CutSubmissionModal = c;
                    }}
                />
                <PengajuanOutlet
                    {...this.props}
                    data={callSubmissionData}
                    addNotification={addNotification}
                    showProgress={showProgress}
                    hideProgress={hideProgress}
                    idUser={idUser}
                    userAccessPayload={userAccessPayload}
                    ref={(c) => {
                        this.submissionModal = c;
                    }}
                />
                <PengajuanUlang
                    {...this.props}
                    data={callSubmissionAgainData}
                    addNotification={addNotification}
                    showProgress={showProgress}
                    hideProgress={hideProgress}
                    idUser={idUser}
                    ref={(c) => {
                        this.submissionAgainModal = c;
                    }}
                />
                <ModalPopup
                    type="upload"
                    ref={(c) => {
                        this.confirmAjukanGrabfood = c;
                    }}
                    confirmText="Sudah"
                    cancelText="Belum"
                    customCancelHandler={() => this.gotoCreateGrabfoodMerchant()}
                    confirmHandle={() => this.gotoSubmissionGrab()}
                    headerTitle="Konfirmasi"
                >
                    Apakah anda sudah memiliki akun Grabfood Merchant?
                </ModalPopup>
                <ModalPopup
                    type="upload"
                    ref={(c) => {
                        this.confirmAjukanGrabfoodUlang = c;
                    }}
                    confirmText="Sudah"
                    cancelText="Belum"
                    customCancelHandler={() => this.gotoCreateGrabfoodMerchant()}
                    confirmHandle={() => this.callSubmissionAgain()}
                    headerTitle="Konfirmasi"
                >
                    Apakah anda sudah memiliki akun Grabfood Merchant?
                </ModalPopup>
                
            </div>
        );
    }
}
