import React, { Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import moment from 'moment';
import update from 'immutability-helper';
import InputCheckboxDark from '../../../../../components/form/InputCheckboxDark';
import InputText from '../../../../../components/form/InputText';
import FileUpload from '../../../../../components/form/WalletFileUpload';
import CoreHOC from '../../../../../core/CoreHOC';
import CheckPopup from '../../asset/CheckPopup';
import '../../asset/css/StyleMarketPlace.less';
import { catchError } from '../../../../../utils/helper';

import * as marketplaceAPI from '../../../../../data/marketplace';
import * as supportAccessAPI from '../../../../../data/access/index';
import './styleList.css';

export default
@connect(state => ({
    listCabang: state.branch.list,
    namaUser: state.user.profile.user_name,
    namaUsaha: state.user.profile.user_usaha_name,
    selectedCabang: state.branch.filter,
    supportNeeded: state.layout.supportNeeded,
}))
@CoreHOC
class SubmissionGrab extends Component {
    static propTypes = {
        showProgress: PropTypes.func.isRequired,
        idPermission: PropTypes.string,
        hideProgress: PropTypes.func,
        idCabang: PropTypes.string.isRequired,
        namaUser: PropTypes.string,
        addNotification: PropTypes.func,
        assignCalendar: PropTypes.func,
        assignButtons: PropTypes.func,
        idUser: PropTypes.string.isRequired,
        routeParams: PropTypes.shape({
            id: PropTypes.string,
        }).isRequired,
    };

    static defaultProps = {
        idPermission: '',
        namaUser: '',
        hideProgress: () => {},
        addNotification: () => {},
        assignCalendar: () => {},
        assignButtons: () => {},
    };

    constructor(props) {
        super(props);

        this.state = {
            termCheck: false,
            termCheckIntegration: false,
            termCheckPengakuan: false,
            termCheckKetentuan: false,
            activePage: 1,
            ketentuanTerm: false,
            pengakuanTerm: false,
            form: {
                nama_usaha: '',
                jenis_usaha: '',
                email: '',
                nama_pemilik: '',
                jumlah_outlet: 0,
                outlets: [],
            },
            isForRegister: true,
            isValidForSubmit: false,
            userData: undefined,
        };
    }

    componentDidMount() {
        const { assignCalendar, assignButtons } = this.props;

        assignCalendar(null, null, null);
        assignButtons([]);

        this._fetchDataUsahaForm();
        this._fetchDetailAkun();
    }

    _fetchDetailAkun = async () => {
        const {
            idCabang, namaUser,
            addNotification, showProgress, hideProgress, idUser,
        } = this.props;

        showProgress();
        try {
            const payload = {
                id_outlet: idCabang,
            };
            const res = await supportAccessAPI.getEmployeeAccess(payload);
            const { status, data, msg } = res;

            if (!status) throw new Error(msg);
            const selectedUser = data.find(x => String(x.id_employee) === String(idUser));
            const userData = {
                namaUser,
                outlet: selectedUser.outlet_name,
                email: selectedUser.username,
                accessType: selectedUser.permission_name,
                store: selectedUser.store_name,
                storeId: selectedUser.id_store,
            };
            this.setState({
                userData,
            });
        } catch (err) {
            addNotification({
                level: 'error',
                title: 'error when trying to fetch support user data',
                message: catchError(err),
            });
        } finally {
            hideProgress();
        }
    }

    _fetchDataUsahaForm = async () => {
        const { hideProgress, addNotification } = this.props;
        try {
            const result = await marketplaceAPI.getFormDataUsaha();
            this._fillFormSubmission(result.data);
        } catch (error) {
            const message = catchError(error);
            addNotification({
                title: 'Gagal mengambil data',
                message,
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    _fillFormSubmission = (rawData) => {
        this.setState(
            prevState => ({
                ...prevState,
                form: rawData,
            }),
            () => {
                this._addCheckboxStatus();
            },
        );
    };

    _addCheckboxStatus = () => {
        const { form } = this.state;
        const { outlets } = form;

        const outletsWithCheckbox = outlets.map(el => Object.assign({}, el, {
                status: 0,
            }));

        this.setState(prevState => ({
            ...prevState,
            form: {
                ...prevState.form,
                outlets: outletsWithCheckbox,
            },
        }));
    };

    changeTermHandler = (val, field) => {
        this.setState({ [field]: val }, () => this.changeTermHandlerIntegrasiOutlet());
    };

    changeTermHandlerIntegrasiOutlet = () => {
        const { termCheckIntegration, termCheckKetentuan, termCheckPengakuan } = this.state;
        let val = false;
        if (termCheckIntegration === true && termCheckPengakuan === true && termCheckKetentuan === true) {
            val = true;
        }
        this.setState({ termCheck: val });
    };


    changeOutletHandler = (val, fieldName, index, e) => {
        const { form } = this.state;

        const newValue = update(form, {
            outlets: {
                [index]: {
                    [fieldName]: {
                        $set: val,
                    },
                },
            },
        });

        this.setState(
            {
                form: newValue,
            },
            () => {
                this.isValidForSubmit();
            },
        );
    };

    changeCheckHandler = (val, index) => {
        const { form } = this.state;

        let newValue = update(form, {
            outlets: {
                [index]: {
                    status: {
                        $set: val,
                    },
                },
            },
        });

        if (val === 0) {
            newValue = update(newValue, {
                outlets: {
                    [index]: {
                        profile_grab_merchant: {
                            $set: null,
                        },
                    },
                },
            });
        }

        this.setState(
            {
                form: newValue,
            },
            () => {
                this.isValidForSubmit();
            },
        );
    };

    isValidForSubmit = () => {
        const {
            form: { outlets },
        } = this.state;
        const isValid = outlets.filter(el => el.status > 0).length > 0;

        this.setState(prevState => ({
            ...prevState,
            isValidForSubmit: isValid,
        }));
    };

    hitSupportAccess = () => {
        const { idUser, idPermission, idCabang } = this.props;
        const { userData } = this.state;
        const payloadUserAccess = {
            id_employee: idUser,
            username: userData.email,
            start_date: moment().format('YYYY-MM-DD HH:mm'),
            store: userData.store,
            id_outlet: idCabang,
            outlet: userData.outlet,
            id_permission: idPermission,
            permission: userData.accessType,
            duration: '720',
            note: 'Integrasi Grabfood',
            name: userData.namaUser,
        };
             // User Access
              return supportAccessAPI.createEmployeeAccess(payloadUserAccess);
            //   .then((res) => {
            //     const { status, msg } = res;
            //  });
        }

    confirmHandler = async () => {
        const { activePage, form, userData } = this.state;
        const {
                idCabang, showProgress, hideProgress, addNotification, idUser, idPermission,
            } = this.props;
        // const payloadUserAccess = {
        //     id_employee: idUser,
        //     username: userData.email,
        //     start_date: moment().format('YYYY-MM-DD HH:mm'),
        //     store: userData.store,
        //     id_outlet: idCabang,
        //     outlet: userData.outlet,
        //     id_permission: idPermission,
        //     permission: userData.accessType,
        //     duration: '720',
        //     note: 'Integrasi Grabfood',
        //     name: userData.namaUser,
        // };
        if (activePage === 1) {
            this.setState(prevState => ({
                ...prevState,
                activePage: 2,
                termCheck: false,
            }));
        }
        if (activePage === 2) {
            const checkedOutlet = form.outlets.filter(x => x.status === 1);
            const doesntHasPhotoProfile = checkedOutlet.find(x => !Object.prototype.hasOwnProperty.call(x, 'profile_grab_merchant') || x.profile_grab_merchant === null);
            if (doesntHasPhotoProfile) {
                addNotification({
                    title: `Lengkapi Data Outlet ${doesntHasPhotoProfile.outlet_name}`,
                    message: 'Silahkan Upload Screenshot Aplikasi Grab Merchant',
                    level: 'error',
                });
                return;
            }

            const generatedOutletParam = form.outlets.map((x) => {
                if (x.status === 1 && Object.prototype.hasOwnProperty.call(x, 'profile_grab_merchant') && x.profile_grab_merchant !== null) {
                    return { ...x, profile_grab_merchant: [x.profile_grab_merchant.filename] };
                }

                return x;
            });

            try {
                showProgress();
                const sendData = Object.assign(
                    {},
                    {
                        provider_id: 2,
                        merchant: {
                            name: form.nama_usaha,
                            type: form.jenis_usaha,
                            email: form.email,
                            owner_name: form.nama_pemilik,
                            number_of_outlets: form.jumlah_outlet,
                        },
                        outlets: generatedOutletParam,
                    },
                );
                // const res = await supportAccessAPI.createEmployeeAccess(payloadUserAccess);
                // const { status, msg } = res;
                // if (!status) throw new Error(msg);
                // addNotification({
                //     level: 'success',
                //     title: 'Support Access has been created',
                //     message: '',
                // });
                this.hitSupportAccess().finally(async () => {
                    await marketplaceAPI.sendSubmission(sendData);
                    this.submissionModal.showPopup();
                });
            } catch (error) {
                const message = catchError(error);
                addNotification({
                    title: 'Gagal menyimpan data',
                    message,
                    level: 'error',
                });
            } finally {
                hideProgress();
            }
        }
    };

    slidePanelPengajuan = () => {
        this.panelSide.showPopup();
    };

    cancelHandler = () => {
        const { router } = this.props;
        router.push('/order-online/pengajuan-order-online');
    };

    notifyUploadFailed = (err) => {
        const { addNotification } = this.props;
        addNotification({
          title: 'Upload file gagal',
          message: err,
          level: 'error',
          autoDismiss: 10,
        });
      }

    generateOutlet = () => {
        const { form } = this.state;
        const { outlets } = form;
        const outletTemplate = (outletListData, index) => (
            <div className="col-md-6 p1" key={index}>
                <div className="col-md-12 outletBox">
                    <div
                        className="checkbox"
                        style={{
                            position: 'relative',
                            display: 'block',
                            paddingLeft: '15px',
                            marginBottom: '8px',
                        }}
                    >
                        <InputCheckboxDark
                            checked={outletListData[index].status !== 0}
                            changeEvent={val => this.changeCheckHandler(val ? 1 : 0, index)}
                            name={`checked-${index}`}
                            required
                            label={(
                                <span
                                    style={{
                                        position: 'relative',
                                        display: 'block',
                                        right: '15px',
                                        bottom: '3px',
                                    }}
                                >
                                    {outletListData[index].outlet_name}
                                </span>
                              )}
                        />
                    </div>

                    <div className="col-sm-12 mb-xs">
                        <label className="control-label kas-bank__color--black">
                            Nama Toko di GrabFood
                        </label>
                        <InputText
                            maxlength="50"
                            name={`outlet_name-${index}`}
                            value={outletListData[index].outlet_name}
                            changeEvent={(value, event) => this.changeOutletHandler(
                                    value,
                                    'outlet_name',
                                    index,
                                    event,
                                )
                            }
                            noDelimiter
                            disabled
                        />
                    </div>
                    <div className="col-sm-12 mb-xs">
                        <label className="control-label kas-bank__color--black">
                            Alamat Outlet
                        </label>
                        <InputText
                            maxlength="50"
                            name={`alamat-${index}`}
                            value={
                                outletListData[index].outlet_address === null
                                    ? ''
                                    : outletListData[index].outlet_address
                            }
                            changeEvent={(value, event) => this.changeOutletHandler(
                                    value,
                                    'outlet_address',
                                    index,
                                    event,
                                )
                            }
                            noDelimiter
                            disabled
                        />
                    </div>
                    <div className="col-sm-12 mb-sm">
                        <label className="control-label kas-bank__color--black">
                            Kota
                        </label>
                        <InputText
                            maxlength="50"
                            name="city"
                            value={
                                outletListData[index].city === null
                                    ? ''
                                    : outletListData[index].city
                            }
                            changeEvent={(value, event) => this.changeOutletHandler(
                                    value,
                                    'city',
                                    index,
                                    event,
                                )
                            }
                            noDelimiter
                            disabled
                        />
                    </div>
                    <div className="col-xs-12">
                        <FileUpload
                            acceptUpload="application/pdf,image/*"
                            label="Screenshot Aplikasi Grab Merchant"
                            name="profile_grab_merchant"
                            value={outletListData[index].profile_grab_merchant}
                            changeEvent={value => this.changeOutletHandler(
                                    value.filename === '' ? null : value,
                                    'profile_grab_merchant',
                                    index,
                                )
                            }
                            disabled={outletListData[index].status === 0}
                            onError={this.notifyUploadFailed}
                        />
                    </div>
                    <div className="col-sm-12 label-upload-file mb-sm">
                        <p className="label-text">Screenshot halaman profil Grab Merchant anda sesuai dengan outlet yang dipilih dengan langkah-langkah berikut:</p>
                        <ol className="list">
                            <li>Buka apps Grab merchant</li>
                            <li>Masuk ke menu Account</li>
                            <li>Buka profil toko anda</li>
                            <li>Lakukan screenshot pada halaman Profil Toko Grab Merchant</li>
                        </ol>
                    </div>
                </div>
            </div>
        );
        const result = [];
        for (let index = 0; index < outlets.length; index += 1) {
            result.push(outletTemplate(outlets, index));
        }
        return result;
    };

    showPengakuan = () => {
        this.setState(prevState => ({
            pengakuanTerm: !prevState.pengakuanTerm,
          }));
    }

    showKetentuan = () => {
        this.setState(prevState => ({
            ketentuanTerm: !prevState.ketentuanTerm,
          }));
    }

    btnTextAnimation = (val) => {
        const { pengakuanTerm, ketentuanTerm } = this.state;
        if (val === 'pengakuan' && pengakuanTerm) {
            return (
                <div>
                    &nbsp;
                    Sembunyikan
                    &nbsp;
                    <i className="fa fa-chevron-up" style={{ fontSize: '11px' }} />
                </div>
            );
        } if (val === 'pengakuan' && !pengakuanTerm) {
            return (
                <div>
                    &nbsp;
                    Lihat lebih lanjut
                    &nbsp;
                    <i className="fa fa-chevron-down" style={{ fontSize: '11px' }} />
                </div>
            );
        }
        if (val === 'ketentuan' && ketentuanTerm) {
            return (
                <div>
                    &nbsp;
                    Sembunyikan
                    &nbsp;
                    <i className="fa fa-chevron-up" style={{ fontSize: '11px' }} />
                </div>
            );
        } if (val === 'ketentuan' && !ketentuanTerm) {
            return (
                <div>
                    &nbsp;
                    Lihat lebih lanjut
                    &nbsp;
                    <i className="fa fa-chevron-down" style={{ fontSize: '11px' }} />
                </div>
            );
        }
    }

    render() {
        const { router } = this.props;
        const {
            isForRegister,
            isValidForSubmit,
            termCheck,
            termCheckKetentuan,
            termCheckPengakuan,
            termCheckIntegration,
            activePage,
            pengakuanTerm,
            ketentuanTerm,
            form: {
                nama_usaha,
                jenis_usaha,
                email,
                nama_pemilik,
                jumlah_outlet,
            },
        } = this.state;
        return (
            <div>
                <section className="panel">
                    <div className="panel-heading table-header">
                        <div className="row">
                            <div className="col-md-6">
                                <h4 className="title">Integrasi Grabfood</h4>
                            </div>
                            <div className="col-md-6">
                                {/* panel step */}
                                <div className="col-sm-12 boxStep px0 py2">
                                    <div className="col-sm-12 px0">
                                        <div className="col-sm-6 TextTitle">
                                            Terms & Condition
                                        </div>
                                        <div className="col-sm-6 pull-right stepFont">
                                            Step
                                            {' '}
                                            {activePage === 1 ? '1' : '2'}
                                            {' '}
                                            of 2
                                        </div>
                                    </div>
                                    <div className="col-sm-12">
                                        <div className="col-sm-6 px0">
                                            <div className="dividerGreen" />
                                        </div>
                                        <div className="col-sm-6 px0">
                                            {activePage === 1 && (
                                                <div className="divider" />
                                            )}
                                            {activePage === 2 && (
                                                <div className="dividerGreen" />
                                            )}
                                        </div>
                                    </div>
                                </div>
                                {/* panel step */}
                            </div>
                        </div>
                    </div>
                    <div className="panel-body">
                        <div className="row">
                            <div className="col-md-12">
                                {/* Konten */}
                                {activePage === 1 && (
                                    <div className="widthTerm">
                                        <h1 className="titleIntegrasi">
                                            Syarat dan Ketentuan Integrasi
                                            Outlet dengan GrabFood
                                        </h1>
                                        <p>
                                        Syarat dan Ketentuan ini mengatur hubungan serta Integrasi antar platform antara pengguna/ pelanggan Majoo (Anda) dan Majoo Indonesia/PT Majoo Teknologi Indonesia (Kami) selaku pemilik konten dan operator layanan aplikasi Majoo (Aplikasi Wirausaha) beserta situs yang beralamat di www.majoo.id dan GrabFood (Pihak Ketiga).
                                        </p>
                                        <InputCheckboxDark
                                            checked={termCheckIntegration}
                                            changeEvent={(val, e) => this.changeTermHandler(val, 'termCheckIntegration')
                                            }
                                            name="termCheckIntegration"
                                            required
                                            label={(
                                                <span
                                                    style={{
                                                        position: 'relative',
                                                        display: 'block',
                                                        left: '3px',
                                                        bottom: '3px',
                                                    }}
                                                >
                                                    Saya menyetujui syarat dan
                                                    ketentuan di atas.
                                                </span>
                                              )}
                                        />
                                        <h1 className="titleIntegrasi">
                                            Pengakuan dan Persetujuan Anda
                                        </h1>
                                        <p>
                                            Sebelum mendaftar dan menggunakan layanan Majoo, Anda wajib membaca dan memahami Syarat dan Ketentuan ini. Dengan mendaftarkan diri dan menggunakan Majoo, berarti Anda telah membaca, memahami, dan menyetujui Syarat dan Ketentuan ini. Hal ini sangatlah penting, karena pengakuan dan persetujuan Anda berkaitan erat dengan Ketentuan Layanan yang harus Anda patuhi sebagai pengguna Majoo.
                                        </p>
                                        <p>
                                            Ketentuan ini mengatur akses Anda ke dan penggunaan Layanan MAJOO dan semua Konten Situs dan merupakan perjanjian hukum yang mengikat antara Anda dan MAJOO ("Perjanjian").
                                        </p>
                                        <p style={{ display: pengakuanTerm ? 'block' : 'none' }}>
                                            Anda juga disarankan untuk memeriksa Syarat dan Ketentuan ini dari waktu ke waktu, untuk mengetahui setiap update dan perubahan yang dapat mempengaruhi Anda.
                                        </p>
                                            <div
                                                style={{
                                                color: '#04C99E', cursor: 'pointer', display: 'inline-block', paddingTop: '10px', paddingBottom: '18px',
                                                }}
                                                role="presentation"
                                                onClick={this.showPengakuan}
                                            >
                                                <div className="text">
                                                   {this.btnTextAnimation('pengakuan')}
                                                </div>
                                            </div>
                                        <InputCheckboxDark
                                            checked={termCheckPengakuan}
                                            changeEvent={(val, e) => this.changeTermHandler(val, 'termCheckPengakuan')
                                            }
                                            name="termCheckPengakuan"
                                            required
                                            label={(
                                                <span
                                                    style={{
                                                        position: 'relative',
                                                        display: 'block',
                                                        left: '3px',
                                                        bottom: '3px',
                                                    }}
                                                >
                                                    Saya menyetujui syarat dan
                                                    ketentuan di atas.
                                                </span>
                                              )}
                                        />
                                        <h1 className="titleIntegrasi">
                                            Ketentuan Umum
                                        </h1>
                                        <p>
                                        Ketentuan ini mengatur cara-cara legal yang harus Anda penuhi dalam menggunakan layanan Majoo. Dalam hal ini, kami memberikan Anda hak dan lisensi penggunaan layanan Majoo yang bersifat terbatas, dapat ditarik kembali, serta
                                        tidak dapat disublisensikan untuk menginstal dan menggunakan layanan.
                                        Berikut Ketentuan Penggunaan Layanan Majoo yang
                                                {' '}
                                                <b>WAJIB</b>
                                                {' '}
                                                Anda penuhi:
                                        </p>
                                        <p>
                                            <ul>
                                                <li>
                                                    Merchant yang ingin mengikuti program integrasi Grab Merchant X Majoo, harus sudah menjadi merchant di Grab Merchant / Grab Food terlebih dahulu.
                                                </li>
                                                <li>
                                                    Semua perubahan yang terjadi setelah proses integrasi adalah tanggung jawab merchant sendiri.
                                                </li>
                                                <li>
                                                    Daftar produk yang digunakan adalah daftar produk yang ada di dalam dashboard Majoo, bukan daftar produk yang berada di dalam Grab Merchant.
                                                </li>
                                                <li>
                                                    Pastikan Jumlah Produk, Harga, dan Nama Produk di Dashboard Majo SAMA DENGAN di Grab Merchant.
                                                </li>
                                                <li>
                                                Majoo dapat menerima pendaftaran langsung untuk membantu merchant Majoo untuk dapat berjualan secara langsung ke Grab Merchant,
                                                dengan estimasi waktu kurang lebih 14 hari kerja setelah semua
                                                dokumen diterima. Merchant Menyetujui pengaktifan Fitur
                                                    {' '}
                                                    {' '}
                                                    <b>Support Access Login</b>
                                                    {' '}
                                                untuk membantu merchant selama proses integrasi
                                                </li>
                                                <li>
                                                    Untuk paket Majoo yang terdaftar harus
                                                    {' '}
                                                    {' '}
                                                    <b>ADVANCED & PRIME</b>
                                                    {' '}
                                                    {' '}
                                                    jika selain itu tidak akan dapat melakukan integrasi.
                                                </li>
                                            </ul>
                                        </p>
                                        <div className="row" style={{ display: ketentuanTerm ? 'block' : 'none' }}>
                                            <div className="col-md-12">
                                                <ul className="listFirst">
                                                    <li>
                                                        Merchant yang bisa ikut program ini harus merchant yang sudah memiliki verifikasi centang hijau di toko aplikasi grab foodnya.
                                                    </li>
                                                    <li>
                                                        Bagi merchant yang tidak bisa membayar dengan OVO (atau cash only) di aplikasi Grab Foodnya, maka disarankan untuk melakukan pengajuan ulang atas Grab Merchantnya.
                                                    </li>
                                                    <li>
                                                        Kegiatan integrasi ini membutuhkan waktu kurang lebih 10 hari kerja setelah semua data pendukung di terima Majoo.
                                                    </li>
                                                    <li>
                                                        Kegiatan ini tidak akan memakan biaya apapun baik dari pihak Majoo & Grab Food.
                                                    </li>
                                                    <li>
                                                        Merchant dapat langsung mengintegrasikan / mengedit / menambahkan / mengurangkan produknya langsung dari aplikasi Majoo, tanpa harus mengedit kembali di Grab Merchant.
                                                    </li>
                                                    <li>
                                                        Untuk pengaturan promosi, tidak bisa dilakukan dari aplikasi Majoo (tetapi untuk fitur ini dapat disesuaikan kembali jika API sudah selesai).
                                                    </li>
                                                    <li>
                                                        Untuk pengajuan Merchant Majoo yang dibantu oleh Majoo, Merchant wajib memberikan data-data dan persetujuan kepada Majoo, tim kami akan menghubungi Anda melalui nomer (0811-366-4700), dengan permintaan data sebagaimana berikut
                                                    </li>
                                                </ul>
                                                <ol className="listNumber" style={{ marginLeft: '25px' }}>
                                                    <li>
                                                        Merchant wajib memberikan nomor yang dapat dihubungi dan terintegrasi melalui WhatsAp.
                                                    </li>
                                                    <li>
                                                        Merchant wajib memberikan OTP kepada Majoo pada saat awal pengajuan dimulai;.
                                                    </li>
                                                    <li>
                                                        Merchant wajib memberikan screen capture lokasi tempat berjualan, secara spesifik dari google maps Merchant.
                                                    </li>
                                                    <li>
                                                        Merchant wajib menyertakan alamat yang selengkap - lengkapnya.
                                                    </li>
                                                    <li>
                                                        Pada akhir bagian pendaftaran, pihak Majoo akan meminta Merchant untuk mengecek email, dan menandatangani Perjanjian Kerjasamanya.
                                                    </li>
                                                    <li>
                                                        Merchant wajib melakukan konfirmasi ke pihak Majoo sebagai proses finalisasi
                                                    </li>
                                                </ol>
                                                {/* // Ketenuan Umum // */}
                                                <h1 className="titleIntegrasi">
                                                    Hak Kekayaan Intelektual MAJOO
                                                </h1>
                                                    <p>
                                                    Anda mengakui bahwa seluruh informasi dan data, seperti halaman situs, layanan situs,
                                                    file data, teks, program komputer, video, audio, gambar, foto, animasi atau material lainnya
                                                    (secara bersama-sama
                                                    {' '}
                                                    <b>“Konten”</b>
                                                    ) yang dapat Anda akses sebagai bagian dari, atau melalui penggunaan Anda atas, Layanan serta Layanan itu sendiri adalah sepenuhnya milik MAJOO. Anda mengakui dan menyetujui bahwa MAJOO memiliki seluruh hak, kepemilikan dan kepentingan yang sah secara hukum di dalam dan pada Layanan dan Konten, termasuk setiap hak kekayaan intelektual yang ada dalam Layanan dan Konten (baik hak tersebut terdaftar atau tidak, dan
                                                                                                    di negara manapun dimana hak-hak tersebut timbul). Anda sepakat untuk tidak menantang atau melakukan hal apapun yang tidak konsisten dengan kepemilikan tersebut.
                                                    </p>
                                                    <p>
                                                        Anda memberikan MAJOO, anak perusahaan, afiliasi, dan penerus hak MAJOO suatu hak yang bebas royalti, tidak dapat dibatalkan, dan berlaku terus menerus (termasuk untuk keperluan komersial) untuk menggunakan informasi dan/atau data yang diperoleh MAJOO melalui penggunaan Layanan oleh Anda, dengan ketentuan bahwa kami akan mengaggregasi atau melakukan anonimisasi informasi atau data tersebut sebelum menggunakannya.
                                                    </p>
                                                    <p>
                                                        Selain itu, anda juga memberikan MAJOO, anak perusahaan, afiliasi, dan penerus hak MAJOO suatu hak yang berlaku di seluruh dunia, non-eksklusif, bebas royalti, telah dibayar penuh, dapat dialihkan, dan dapat disublisensikan untuk menggunakan, mereproduksi, mengubah, mengadaptasi, mengumumkan, membuat karya turunan dari, mendistribusikan, melaksanakan di muka umum, dan mengumumkan di muka umum konten anda di seluruh dunia dalam media apapun untuk menyediakan dan mempromosikan Layanan, sepanjang diperkenankan peraturan perundang-undangan.
                                                    </p>
                                                    <p>
                                                        Selain dari hak-hak tersebut, MAJOO tidak mengklaim hak kekayaan intelektual sehubungan dengan informasi dan konten yang anda masukkan ke dalam layanan.
                                                    </p>
                                                    <p>
                                                        Anda mengakui dan sepakat bahwa Hak penggunaan yang diberikan kepada Anda oleh Ketentuan Penggunaan ini bukanlah suatu pengalihan atau penjualan hak kepemilikan MAJOO atas Layanan, Konten, sistem, atau bagiannya. Selain dari hak penggunaan dari Ketentuan Penggunaan ini, MAJOO tetap menguasai seluruh hak, hak milik dan kepentingan (termasuk seluruh hak kekayaan intelektual) atas Layanan ataupun salinannya. Anda mengakui dan sepakat bahwa upaya apapun untuk menggunakan Layanan di luar yang diperbolehkan oleh lisensi ini akan mengakhiri berlakunya lisensi yang dimaksud secara otomatis, dan Anda dengan ini memberikan hak kepada MAJOO untuk memproses pelanggaran tersebut berdasarkan peraturan perundang-undangan yang berlaku.
                                                    </p>
                                                    <p>
                                                        Anda mengakui dan sepakat bahwa tidak ada satu ketentuan pun di dalam Ketentuan Penggunaan ini yang memberikan kepada Anda hak untuk menggunakan setiap merek dagang, nama dagang, merek jasa, logo atau nama domain MAJOO dengan cara apapun yang dimaksudkan atau dapat menimbulkan penyesatan konsumen sehubungan dengan kepemilikan atau pengguna yang berwenang dari setiap merek dagang, nama dagang, merek jasa, logo atau nama domain tersebut. Anda tidak diperbolehkan untuk mempublikasikan atau menggunakan setiap merek dagang, nama dagang, merek jasa atau logo MAJOO, kecuali dengan persetujuan tertulis sebelumnya dari MAJOO.
                                                    </p>
                                                    <p>
                                                        Anda dapat memberikan kami komentar, tanggapan, atau saran sehubungan dengan Layanan, dan anda setuju bahwa kami bebas untuk menggunakan, mengubah, dan menuangkan saran tersebut tanpa suatu kewajiban apapun kepada anda.
                                                    </p>
                                                    {/* // Ketenuan Umum // */}
                                                    <h1 className="titleIntegrasi">
                                                        Keterbatasan Kewajiban & Ganti Rugi
                                                    </h1>
                                                    <p>
                                                        Anda mengakui dan sepakat bahwa sejauh mana diperbolehkan oleh hukum yang berlaku, MAJOO tidak akan bertanggung jawab atas segala kerusakan dan/atau kerugian langsung, tidak langsung, insidental, khusus, konsekuensial, atau tipikal (exemplary damages) yang mungkin ditimbulkan atau diderita oleh Anda, termasuk namun tidak terbatas pada kehilangan keuntungan, usaha, goodwill, reputasi, data atau kerugian tidak berwujud lainnya sehubungan dengan penggunaan Layanan atau tidak dapat digunakannya Layanan sebagaimana mestinya.
                                                    </p>
                                                    <p>
                                                        MAJOO tidak akan bertanggung jawab atas setiap kerusakan atau kerugian yang timbul sebagai akibat dari (a) penggunaan atau ketidakmampuan untuk menggunakan Layanan (secara keseluruhan atau sebagian) atau kerusakan teknis apapun; (b) biaya atas pengadaan barang atau jasa pengganti; (c) akses yang terlarang untuk, atau perubahan atas, komunikasi atau data Pelanggan; (d) pernyataan atau tindakan pihak ketiga manapun sehubungan dengan Layanan; (e) pengandalan Anda terhadap kelengkapan atau akurasi Layanan atau bagian manapun daripadanya, termasuk namun tidak terbatas pada, setiap kesalahan matematika atau nomor numerik yang terkait dengan transaksi keuangan apapun yang diberikan dalam Layanan; (f) setiap perubahan yang mungkin dilakukan MAJOO atas Layanan, atau setiap penghentian permanen maupun sementara dari Layanan; (g) penghapusan, korupsi atau kegagalan untuk menyimpan setiap konten dan data komunikasi lainnya yang dipelihara atau transmisikan oleh atau melalui penggunaan Layanan oleh Anda; (h) kegagalan Anda untuk memberikan informasi akun yang akurat kepada MAJOO; atau (i) segala masalah lainnya sehubungan dengan Layanan.
                                                    </p>
                                                    <p>
                                                        Anda harus membebaskan MAJOO dan pejabat, direktur, pemegang saham, dan karyawannya, dari dan terhadap semua klaim, tuntutan, proses, kerugian, kewajiban, dan biaya, baik dalam gugatan, kontrak, atau lainnya, yang timbul dari atau terkait, termasuk tetapi tidak terbatas pada biaya pengacara , secara keseluruhan atau sebagian yang timbul dari atau disebabkan oleh pelanggaran Perjanjian ini atau aktivitas apa pun oleh Anda sehubungan dengan Situs atau penggunaan Layanan oleh Anda.
                                                    </p>
                                                    <p>
                                                        Dalam kasus manapun, Anda mengakui dan sepakat bahwa tanggung jawab maksimal dari MAJOO sehubungan dengan Ketentuan Penggunaan atau penggunaan Layanan adalah terbatas pada jumlah biaya penggunaan Layanan yang dibayarkan oleh Pelanggan dalam 1 (satu) bulan terakhir.
                                                    </p>
                                                    <h1 className="titleIntegrasi">
                                                        Hukum dan Forum yang Mengatur Perselisihan
                                                    </h1>
                                                    <p>
                                                        Anda sepakat bahwa: (i) Layanan akan diberikan dari Indonesia; (ii) hukum Indonesia berlaku untuk Ketentuan Penggunaan ini, termasuk setiap permasalahan kontraktual atau non-kontraktual atau perselisihan yang timbul dari atau sehubungan dengan Ketentuan Penggunaan ini, akses dan penggunaan Layanan oleh Anda, dan hubungan antara MAJOO dengan Anda; dan (iii) setiap konflik yang timbul dari atau sehubungan dengan Layanan atau konflik antara MAJOO dengan Anda sehubungan dengan Layanan akan diajukan dan diselesaikan pertama-tama melalui musyawarah untuk mufakat dalam waktu 30 (tiga puluh) hari terhitung sejak diterimanya pemberitahuan timbulnya konflik.Hukum Negara Kesatuan Republik Indonesia yang mengatur.
                                                    </p>
                                                    <p>
                                                        SETIAP SENGKETA ATAU KLAIM YANG BERKAITAN DENGAN CARA APA PUN UNTUK LAYANAN ATAU KETENTUAN PENGGUNAAN INI AKAN DILAKUKAN DENGAN MENGIKUTI KETENTUAN DAN YURISDIKSI DARI PENGADILAN JAKARTA SELATAN.
                                                    </p>
                                            </div>
                                        </div>
                                            <div
                                                style={{
                                                color: '#04C99E',
                                                cursor: 'pointer',
                                                display: 'inline-block',
                                                paddingTop: '15px',
                                                paddingBottom: '15px',
                                                }}
                                                role="presentation"
                                                onClick={this.showKetentuan}
                                            >
                                                <div className="text">
                                                    {this.btnTextAnimation('ketentuan')}
                                                </div>
                                            </div>
                                        <InputCheckboxDark
                                            checked={termCheckKetentuan}
                                            changeEvent={(val, e) => this.changeTermHandler(val, 'termCheckKetentuan')
                                            }
                                            name="termCheckKetentuan"
                                            required
                                            label={(
                                                <span
                                                    style={{
                                                        position: 'relative',
                                                        display: 'block',
                                                        left: '3px',
                                                        bottom: '3px',
                                                    }}
                                                >
                                                    Saya menyetujui syarat dan
                                                    ketentuan di atas.
                                                </span>
                                              )}
                                        />
                                    </div>
                                )}
                                {activePage === 2 && (
                                    <div className="col-md-12">
                                        <div className="col-md-12">
                                            <div className="col-md-12">
                                                <div className="titleIntegrasi mb-xs">
                                                    Info Usaha
                                                </div>
                                            </div>
                                            <div className="col-md-12 mb-xs">
                                                <label className="control-label kas-bank__color--black">
                                                    Nama Usaha
                                                </label>
                                                <InputText
                                                    maxlength="50"
                                                    name="nama_usaha"
                                                    value={nama_usaha}
                                                    noDelimiter
                                                    disabled
                                                />
                                            </div>
                                            <div className="col-md-12 mb-xs">
                                                <label className="control-label kas-bank__color--black">
                                                    Jenis Usaha
                                                </label>
                                                <InputText
                                                    maxlength="50"
                                                    name="jenis_usaha"
                                                    value={jenis_usaha}
                                                    noDelimiter
                                                    disabled
                                                />
                                            </div>
                                            <div className="col-md-12 mb-xs">
                                                <label className="control-label kas-bank__color--black">
                                                    Alamat Email
                                                </label>
                                                <InputText
                                                    maxlength="50"
                                                    name="email"
                                                    value={email}
                                                    noDelimiter
                                                    disabled
                                                />
                                            </div>
                                            <div className="col-md-12 mb-xs">
                                                <label className="control-label kas-bank__color--black">
                                                    Nama Pemilik Usaha
                                                </label>
                                                <InputText
                                                    maxlength="50"
                                                    name="namaPemilik"
                                                    value={nama_pemilik}
                                                    noDelimiter
                                                    disabled
                                                />
                                            </div>
                                            <div className="col-md-12 mb-xs">
                                                <label className="control-label kas-bank__color--black">
                                                    Jumlah Outlet
                                                </label>
                                                <InputText
                                                    maxlength="50"
                                                    name="jumlahOutlet"
                                                    value={jumlah_outlet}
                                                    noDelimiter
                                                    disabled
                                                />
                                            </div>
                                        </div>
                                        <div className="col-md-12 mt-sm mb-sm">
                                            <div className="divider" />
                                        </div>
                                        <div className="col-md-12">
                                            <div className="col-md-12 mb-xs">
                                                <div className="titleIntegrasi">
                                                    Pilih Outlet
                                                </div>
                                            </div>
                                            <div className="col-md-12">
                                                <div className="row">
                                                    {this.generateOutlet()}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )}
                                {/* Konten */}
                            </div>
                        </div>
                    </div>
                    <div className="panel-footer bgPanel">
                        <div className="row">
                            <div className="col-md-12">
                                {/* Konten */}
                                <ul className="list-inline pull-right">
                                    <li className="">
                                        <button
                                            className="btn"
                                            onClick={this.cancelHandler}
                                            style={{
                                                height: '100%',
                                                maxWidth: '95%',
                                            }}
                                            type="button"
                                            disabled={false}
                                        >
                                            Batal
                                        </button>
                                    </li>
                                    <li className="">
                                        <button
                                            className="btn btn-primary"
                                            style={{
                                                height: '100%',
                                                maxWidth: '95%',
                                            }}
                                            disabled={(function checkDisable() {
                                                if (activePage === 1) {
                                                    return !termCheck;
                                                }
                                                return !isValidForSubmit;
                                            }())}
                                            onClick={this.confirmHandler}
                                            type="button"
                                        >
                                            {activePage === 1
                                                ? 'Selanjutnya'
                                                : 'Ajukan'}
                                        </button>
                                    </li>
                                </ul>
                                {/* Konten */}
                            </div>
                        </div>
                    </div>
                </section>
                <CheckPopup
                    isForRegister
                    router={router}
                    title="Data Berhasil Diajukan"
                    content="Integrasi dengan GrabFood sedang dalam proses peninjauan. Proses ini membutuhkan waktu 7x24 jam.
                    Apabila status integrasi belum berubah dalam 7x24 jam, silahkan hubungi customer service kami."
                    ref={(c) => {
                        this.submissionModal = c;
                    }}
                />
            </div>
        );
    }
}
