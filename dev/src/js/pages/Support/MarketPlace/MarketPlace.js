import React, { Component } from 'react';
import PropTypes from 'prop-types';
import CoreHOC from '../../../core/CoreHOC';

import SidePopup from '../../../components/sidepopup/Container';
import PanelPengajuanGrab from './Grab/PengajuanAwal/PanelPengajuanGrab';
// import OlahProduk from '../../../components/_modules/marketplace/components/OlahProduk';
import grabfood from './asset/img/grabfood.png';
import './asset/css/StyleMarketPlace.less';
import PutuskanSemuaIntegrasi from './Grab/PengajuanAwal/PutuskanSemuaIntegrasi';
import * as marketplaceAPI from '../../../data/marketplace';
import { catchError } from '../../../utils/helper';
// import DataList from '../../../components/assigncoupon/DataList';
import ModalPopup from '../../../components/modalpopup/Container';
import STATUS from './Grab/PengajuanAwal/EnumStatusMarketplace';

export default
@CoreHOC
class MarketPlace extends Component {
    static propTypes = {
        showProgress: PropTypes.func.isRequired,
        assignButtons: PropTypes.func,
        hideProgress: PropTypes.func.isRequired,
        addNotification: PropTypes.func.isRequired,
        router: PropTypes.oneOfType([PropTypes.object]).isRequired,
        idUser: PropTypes.string.isRequired,
    };

    static defaultProps = {
        assignButtons: () => {},
    }

    constructor(props) {
        super(props);

        this.state = {
            dataList: [],
        };
    }

    componentWillMount() {
        const { assignButtons } = this.props;
        assignButtons([]);
        this._fetchDataSubmission();
    }

    _fetchDataSubmission = async () => {
        const { hideProgress, addNotification } = this.props;
        try {
            const result = await marketplaceAPI.getDataSubmission();
            const { data } = result;

            if (data.length > 0) {
                let grabData = data.filter(res => res.provider_id === '2');
                // there's anomaly data. select the last index array =>
                grabData = grabData ? grabData[grabData.length - 1] : [];
                const consumerData = data.filter(
                    res => res.provider_id === '3',
                );
                let customDataOutletGrab = [];

                if (grabData) {
                    customDataOutletGrab = [
                        {
                            outletNotSubmittedGrab:
                                grabData.outlets.filter(
                                    res => res.status === STATUS.STATUS_OUTLET.NOT_SUBMITTED_YET,
                                ).length || 0,
                            outletInprogressGrab:
                                grabData.outlets.filter(
                                    res => res.status === STATUS.STATUS_OUTLET.REVIEW,
                                ).length || 0,
                            outletRejectedGrab:
                                grabData.outlets.filter(
                                    res => res.status === STATUS.STATUS_OUTLET.DENIED,
                                ).length || 0,
                            outletDraftGrab:
                                grabData.outlets.filter(
                                    res => res.status === STATUS.STATUS_OUTLET.DRAFT,
                                ).length || 0,
                            outletApprovedGrab:
                                grabData.outlets.filter(
                                    res => res.status === STATUS.STATUS_OUTLET.APPROVED,
                                ).length || 0,
                            outletSuspendedGrab:
                                grabData.outlets.filter(
                                    res => res.status === STATUS.STATUS_OUTLET.TERMINATION,
                                ).length || 0,
                            outletIntegratedGrab:
                                grabData.outlets.filter(
                                    res => res.status === STATUS.STATUS_OUTLET.WAITING_FOR_INTEGRATED,
                                ).length || 0,
                            outletIntegratedDoneGrab:
                                grabData.outlets.filter(
                                    res => res.status === STATUS.STATUS_OUTLET.INTEGRATED,
                                ).length || 0,
                            outletDeletedGrab:
                                grabData.outlets.filter(
                                    res => res.status === STATUS.STATUS_OUTLET.REMOVED,
                                ).length || 0,
                        },
                    ];
                }

                const grabDataFull = { grabData, customDataOutletGrab };
                const FullListEdited = {
                    grabDataFull,
                    consumerData,
                };

                this.setState({
                    dataList: FullListEdited,
                });
            }
        } catch (error) {
            const message = catchError(error);
            // if (message !== 'Record not found') {
            addNotification({
                title: 'Gagal mengambil Data List Submission',
                message,
                level: 'error',
            });
            // }
        } finally {
            hideProgress();
        }
    };

    slidePanelPengajuanGrab = () => {
        this.panelSideGrab.showPopup();
    };

    // callPanelProduk = () => {
    //     this.panelProduk.showPopup();
    // };

    _handlePutuskanSemua = async (action, callback) => {
        this.CutAllSubmissionModal.showPopup();
        callback();
    };

    generateStatusOutlet = (statusOutletGrab) => {
        const result = [];
        if ('grabDataFull' in statusOutletGrab && statusOutletGrab.grabDataFull.customDataOutletGrab.length > 0) {
            if (
                statusOutletGrab.grabDataFull.customDataOutletGrab[0]
                    .outletNotSubmittedGrab > 0
            ) {
                result.push(
                    <p key="1">
                        {`${statusOutletGrab.grabDataFull.customDataOutletGrab[0].outletNotSubmittedGrab} `}
                        Outlet belum mengajukan
                    </p>,
                );
            }
            if (
                statusOutletGrab.grabDataFull.customDataOutletGrab[0]
                    .outletInprogressGrab > 0
            ) {
                result.push(
                    <p key="2">
                        {`${statusOutletGrab.grabDataFull.customDataOutletGrab[0].outletInprogressGrab} `}
                        Outlet dalam proses peninjauan
                    </p>,
                );
            }
            if (
                statusOutletGrab.grabDataFull.customDataOutletGrab[0]
                    .outletRejectedGrab > 0
            ) {
                result.push(
                    <p key="3">
                        {`${statusOutletGrab.grabDataFull.customDataOutletGrab[0].outletRejectedGrab} `}
                        Pengajuan outlet ditolak
                    </p>,
                );
            }
            if (
                statusOutletGrab.grabDataFull.customDataOutletGrab[0]
                    .outletApprovedGrab > 0
            ) {
                result.push(
                    <p key="4">
                        {`${statusOutletGrab.grabDataFull.customDataOutletGrab[0].outletApprovedGrab} `}
                        Pengajuan outlet diterima
                    </p>,
                );
            }
            if (
                statusOutletGrab.grabDataFull.customDataOutletGrab[0]
                    .outletSuspendedGrab > 0
            ) {
                result.push(
                    <p key="5">
                        {`${statusOutletGrab.grabDataFull.customDataOutletGrab[0].outletSuspendedGrab} `}
                        Pengajuan Pemutusan Integrasi
                    </p>,
                );
            }
            if (
                statusOutletGrab.grabDataFull.customDataOutletGrab[0]
                    .outletIntegratedGrab > 0
            ) {
                result.push(
                    <p key="6">
                        {`${statusOutletGrab.grabDataFull.customDataOutletGrab[0].outletIntegratedGrab} `}
                        Menunggu Proses Integrasi
                    </p>,
                );
            }
            if (
                statusOutletGrab.grabDataFull.customDataOutletGrab[0]
                    .outletIntegratedDoneGrab > 0
            ) {
                result.push(
                    <p key="7">
                        {`${statusOutletGrab.grabDataFull.customDataOutletGrab[0].outletIntegratedDoneGrab} `}
                        Terintegrasi
                    </p>,
                );
            }
        } else {
            return <p>Belum Mengajukan</p>;
        }
        return result;
    };

    generateCancelAll = (dataList) => {
        if ('grabDataFull' in dataList && dataList.grabDataFull.customDataOutletGrab.length > 0) {
            if (
                dataList.grabDataFull.customDataOutletGrab[0]
                    .outletIntegratedDoneGrab > 0
            ) { return this._handlePutuskanSemua; }
            return (false);
        }
    }

    generateStatusOutletButton = (statusOutletBtn) => {
        if ('grabDataFull' in statusOutletBtn && statusOutletBtn.grabDataFull.customDataOutletGrab.length > 0) {
            return (
                <button
                    type="button"
                    className="btn btn-primary mt-2"
                    onClick={this.slidePanelPengajuanGrab}
                >
                    Atur Integrasi
                </button>
            );
        }
        return (
            <button
                type="button"
                className="btn btn-primary mt-2"
                onClick={() => this.confirmAjukanGrabfood.showPopup()}
            >
                Ajukan
            </button>
        );
    };

    gotoSubmissionGrab = () => {
        const { router, idUser } = this.props;
        router.push(
            `/order-online/pengajuan-order-online/pengajuan-grab/${idUser}`,
        );
    };

    gotoCreateGrabfoodMerchant = () => {
        const { router } = this.props;
        router.push(
            '/pengajuan-akun-digital/grab-merchant/create',
        );
    };

    render() {
        const {
            addNotification,
            showProgress,
            hideProgress,
            router,
            idUser,
        } = this.props;
        const { dataList } = this.state;
        return (
            <div>
                <section className="panel">
                    <div className="panel-heading table-header">
                        <div className="row">
                            <div className="col-md-12">
                                <h4 className="title">
                                    Pengajuan Order Online
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div className="panel-body">
                        <div className="row">
                            <div className="col-md-12">
                                {/* list service marketplace */}
                                {/* GrabFood */}
                                <div className="col-md-4">
                                    <div className="col-md-12 marketplaceBox">
                                        <div className="roundIcon">
                                            <img
                                                src={grabfood}
                                                alt="grabfood"
                                                className="imgMarket"
                                            />
                                        </div>
                                        <h1 className="titleMarketplace">
                                            GrabFood
                                        </h1>
                                        {this.generateStatusOutlet(dataList)}
                                        {this.generateStatusOutletButton(
                                            dataList,
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <SidePopup
                    ref={(c) => {
                        this.panelSideGrab = c;
                    }}
                    width={850}
                    // saveHandle={this._handlePutuskanSemua}
                    saveHandle={this.generateCancelAll(dataList)}
                    confirmButtonClass="bgOrange"
                    btnSaveText="Putuskan Integrasi Semua Outlet"
                    btnCancelText="Batal"
                    render={({ show, validateInput }) => {
                        if (show) {
                            return (
                                <PanelPengajuanGrab
                                    ref={(c) => {
                                        this.pengajuanPanel = c;
                                    }}
                                    validateInput={validateInput}
                                    addNotification={addNotification}
                                    showProgress={showProgress}
                                    hideProgress={hideProgress}
                                    router={router}
                                    hidePanelSideGrab={() => this.panelSideGrab.hidePopup()}
                                    data={dataList}
                                    idUser={idUser}
                                    reFetch={this._fetchDataSubmission}
                                />
                            );
                        }
                        return null;
                    }}
                />
                <PutuskanSemuaIntegrasi
                    text="Memutuskan integrasi dengan GrabFood akan menghapus semua relasi outlet dengan akun GrabFood yang sudah terintegrasi sebelumnya.
                    Proses ini membutuhkan waktu 2 - 7 x 24 jam hingga proses pemutusan selesai sepenuhnya."
                    data={dataList}
                    addNotification={addNotification}
                    showProgress={showProgress}
                    hideProgress={hideProgress}
                    idUser={idUser}
                    ref={(c) => {
                        this.CutAllSubmissionModal = c;
                    }}
                    reFetch={this._fetchDataSubmission}
                />
                <ModalPopup
                    type="upload"
                    ref={(c) => {
                        this.confirmAjukanGrabfood = c;
                    }}
                    confirmText="Sudah"
                    cancelText="Belum"
                    customCancelHandler={() => this.gotoCreateGrabfoodMerchant()}
                    confirmHandle={() => this.gotoSubmissionGrab()}
                    headerTitle="Konfirmasi"
                >
                    Apakah anda sudah memiliki akun Grabfood Merchant?
                </ModalPopup>
            </div>
        );
    }
}
