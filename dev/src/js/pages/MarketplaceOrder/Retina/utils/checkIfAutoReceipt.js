import { PROVIDER_TYPE } from '~/components/marketplace/enums';

// karena belum ada patokan dari response api untuk menentukan
// apakah sebuah transaksi memiliki resi otomatis atau manual
// sementara aku buat untuk compare berdasarkan jasa deliverynya dulu
// TODO -> update check berdasarkan patokan dari response api ketika sudah tersedia
const checkIfAutoReceipt = (providerId, deliveryServiceName) => {
    switch (providerId) {
        case PROVIDER_TYPE.TOKOPEDIA: {
            switch (String(deliveryServiceName).toLowerCase()) {
                case 'wahana':
                case 'pos indonesia':
                case 'tiki':
                    return false;
                default:
                    return true;
            }
        }
        case PROVIDER_TYPE.SHOPEE:
        default:
            return true;
    }
};

export default checkIfAutoReceipt;
