import React, {
  useState, useEffect, useMemo, useRef, useCallback,
} from 'react';
import {
  FormHelper, FormGroup, PageDialog, Flex, Paper, Box, FormLabel, InputSelect, InputText, DialogClose,
  Button, Text, ToastContext, InputNumber, TableOfContent, InputSelectTag, InputDatePicker,
  InputTimePicker, Heading, InputTextArea, EditableTable, Paragraph, InputGroup,
  InputPrefix, Tooltip, Separator, IconButton, ModalDialog, Upload,
} from '@majoo-ui/react';
import { DownloadOutline, EllipsisHorizontalOutline, PlusOutline, TrashOutline } from '@majoo-ui/icons';
import { useForm, Controller } from 'react-hook-form';

import * as yup from 'yup';
import moment from 'moment';
import { yupResolver } from '@hookform/resolvers/yup';
import { isEmpty, omit } from 'lodash';
import { Trans, useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { TOKEN_UPLOAD } from '~/pages/Promotion/BasePromoV2/retina/DetailDialog/utils';
import { clearSession } from '~/services/api/session.util';
import { TRANSACTION_STATUS, TRANSACTION_TYPE } from './enum';
import {
  createCashReceipts, createCashDisbursement, updateCashReceipt, updateCashDisbursement,
} from '../../../data/accounting';
import { catchError, checkFileType, downloadFileHelper, FILE_TYPES, formatCurrency, getFileNameFromUrl, handleOpenFile, isImageURL } from '../../../utils/helper';
import {
  CustomConfirmModal, CustomConfirmCancel,
} from './Modal';
import { useMediaQuery } from '../../../utils/useMediaQuery';
import DetailAccountOptions from './DetailAccountOptionsModal';
import { colors } from '../../../stitches.config';
import ActivityLogSidebar from '../../../components/activityLog/ActivityLogSidebar';
import ActivityLogModal from '../../../components/activityLog/ActivityLogModal';
import { UPLOAD_FILE_ADDRESS } from '../TransferList/components/FormModal';

const wrap = (str) => {
  if (str) {
    return `(${str})`;
  }
  return '';
};

const TOC_CONTENT = (type, t) => [
  {
    id: 'section-one',
    title: type === TRANSACTION_TYPE.ADD_RECEIPT_TRANSACTION ? t('toc.one.revenue') : t('toc.one.expense'),
  },
  {
    id: 'section-two',
    title: type === TRANSACTION_TYPE.ADD_RECEIPT_TRANSACTION ? t('toc.two.revenue') : t('toc.two.expense'),
  },
];

const secondarySchema = t => yup.object().shape({
  jumlah: yup.number().required(t('form.secondaryAccount.zero')).typeError(t('form.secondaryAccount.zero')),
  deskripsi: yup.string(),
  akunting_no: yup.string(),
});

const schema = t => yup.object().shape({
  outlet_id: yup.number().required(t('form.outlet.errors.required')).typeError(t('form.outlet.errors.required')),
  kode_transaksi: yup.string(),
  tgl_transaksi: yup.string().transform(date => moment(date).format('YYYY-MM-DD HH:mm:ss')),
  status: yup.number(),
  keterangan: yup.string(),
  akun_utama: yup.string().required(t('form.account.errors.required')),
  akun_sekunder: yup.array().of(secondarySchema(t)).min(1, t('form.secondaryAccount.less')).required(t('form.secondaryAccount.less')),
});

const CashTransactionForm = (props) => {
  const {
    open, onOpenChange, outletOptions, accountOptions, accountDetailOptions, transactionType, onSuccess, transactionDetail = {}, mode, onDelete, addToast,
    activityLog, handleFetchActivityLog,
  } = props;

  const { t, ready } = useTranslation(['Keuangan/formTransaction', 'translation']);

  const [activeId, setActiveId] = useState();
  const parentScrollContainerRef = useRef(null);
  const isMobile = useMediaQuery('(max-width: 992px)');
  const [openConfirmCancel, setOpenConfirmCancel] = useState(false);
  const [openConfirmCreate, setOpenConfirmCreate] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [form, setForm] = useState({});
  const [openDetailAccountOptions, setOpenDetailAccountOptions] = useState(false);
  const [tableData, setTableData] = useState(transactionDetail.akun_sekunder || []);
  const [total, setTotal] = useState(0);
  const isDetail = mode === 'DETAIL';
  const isAddNew = isEmpty(transactionDetail);
  const isDraft = transactionDetail.status === TRANSACTION_STATUS.DRAFT;
  const [disabledAccount, setDisabledAccount] = useState([]);
  const isReceipt = transactionType === TRANSACTION_TYPE.ADD_RECEIPT_TRANSACTION;
  const [openOption, setOpenOption] = useState(false);
  const [isOpenActivityLog, setIsOpenActivityLog] = useState(false);
  const [isDisabled, setDisabled] = useState(true);

  const {
    register, formState: { errors, isValid }, clearErrors, setValue, reset, getValues, unregister, watch, handleSubmit, control, trigger, setFocus,
  } = useForm({
    mode: 'all',
    resolver: yupResolver(schema(t)),
    defaultValues: {
      outlet_id: String(transactionDetail.outlet_id) || '',
      kode_transaksi: transactionDetail.kode_transaksi || '',
      tgl_transaksi: transactionDetail.tgl_transaksi ? moment(transactionDetail.tgl_transaksi, 'YYYY-MM-DD HH:mm:ss').toDate() : new Date(),
      status: transactionDetail.status || 1,
      keterangan: transactionDetail.keterangan || '',
      akun_utama: transactionDetail.akun_utama || '',
      akun_sekunder: !isEmpty(transactionDetail.akun_sekunder)
        ? transactionDetail.akun_sekunder.map(m => ({ ...m, jumlah: isReceipt ? m.kredit : m.debet })) : [],
      no_transaksi: transactionDetail.no_transaksi,
    },
  });

  const [files, setFiles] = useState([]);
  const [fileError, setFileError] = useState('');

  const handleFileChange = ({ file }) => {
    if (file) {
      if (file.type && (file.type.includes('xlsx')
        || file.type.includes('spreadsheet')
        || file.type.includes('pdf')
        || file.type.includes('jpg')
        || file.type.includes('png')
        || file.type.includes('xls')
        || file.type.includes('jpeg'))) {
        const fileSize = (file.size / 1024 / 1024).toFixed(1);
        if (fileSize > 1) {
          setFileError(t('form.attachFileError'));
          return;
        }
        setFiles([file]);
        setFileError('');
      } else {
        setFiles([]);
        setFileError(t('error.formatUnsupported', { ns: 'translation' }));
      }
    } else {
      setFiles([]);
    }
  };

  useEffect(() => {
    const subscription = watch(({ akun_sekunder }) => {
      if (Array.isArray(akun_sekunder)) {
        setTotal(akun_sekunder.reduce((prev, current) => prev + Number(current.jumlah || 0), 0));
      } else {
        setTotal(0);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch]);

  useEffect(() => {
    if (!isDetail) {
      if (getValues('outlet_id') !== '' && getValues('akun_utama') !== '' && tableData.length > 0) {
        setDisabled(false);
      }
    }
  }, [isDetail, getValues('outlet_id'), getValues('akun_utama'), tableData]);

  const handleError = (err) => {
    const keys = Object.keys(err);
    if (keys.length) {
      const el = document.getElementById(keys[0]);
      setFocus(keys[0]);
      if (el) el.scrollIntoView();
    }
  };

  const create = async (_payload) => {
    const payload = {
      ..._payload,
      akun_sekunder: _payload.akun_sekunder.map(m => omit(m, ['debet', 'kredit'])),
    };
    const isUpdate = payload.no_transaksi;
    const _isDraft = payload.status === 2;
    const msg = () => {
      const flag = isReceipt ? 'revenue' : 'expense';
      if (isUpdate) {
        return (
          <Trans t={t} i18nKey={`toast.edit.${flag}`}>
            {{ code: wrap(payload.kode_transaksi) }}
          </Trans>
        );
      }
      if (payload.kode_transaksi) {
        if (_isDraft) {
          return (
            <Trans t={t} i18nKey={`toast.addDraft.${flag}`}>
              {{ code: wrap(payload.kode_transaksi) }}
            </Trans>
          );
        }
        return (
          <Trans t={t} i18nKey={`toast.add.${flag}`}>
            {{ code: wrap(payload.kode_transaksi) }}
          </Trans>
        );
      }
      if (_isDraft) {
        return (
          <Trans t={t} i18nKey={`toast.addDraftEmpty.${flag}`} />
        );
      }
      return (
        <Trans t={t} i18nKey={`toast.addEmpty.${flag}`} />
      );
    };
    const failedMsg = () => {
      const flag = isReceipt ? 'revenue' : 'expense';
      if (payload.kode_transaksi) {
        if (_isDraft) {
          return (
            <Trans t={t} i18nKey={`toast.failedAddDraft.${flag}`}>
              {{ code: wrap(payload.kode_transaksi) }}
            </Trans>
          );
        }
        return (
          <Trans t={t} i18nKey={`toast.failedAdd.${flag}`}>
            {{ code: wrap(payload.kode_transaksi) }}
          </Trans>
        );
      }
      if (_isDraft) {
        return (
          <Trans t={t} i18nKey={`toast.failedAddDraftEmpty.${flag}`} />
        );
      }
      return (
        <Trans t={t} i18nKey={`toast.failedAddEmpty.${flag}`} />
      );
    };
    setIsSubmitting(true);
    try {
      if (isUpdate) {
        const res = isReceipt ? await updateCashReceipt(payload) : await updateCashDisbursement(payload);
        if (!res.data) throw new Error(res.msg);
      } else {
        const res = isReceipt ? await createCashReceipts(payload) : await createCashDisbursement(payload);
        if (!res.data) throw new Error(res.msg);
      }
      addToast({
        title: t('toast.success', { ns: 'translation' }),
        description: msg(),
        variant: 'success',
      });
      reset();
      setOpenConfirmCreate(false);
      onOpenChange(false);
      onSuccess();
    } catch (e) {
      if (catchError(e) === 'no transaksi telah digunakan') {
        addToast({
          title: t('toast.notice'),
          description: t('toast.failedDuplicate'),
          variant: 'failed',
        });
      } else {
        addToast({
          title: t('toast.error', { ns: 'translation' }),
          description: failedMsg(),
          variant: 'failed',
        });
      }
    } finally {
      setIsSubmitting(false);
      setOpenConfirmCreate(false);
    }
  };

  const handleAddRow = (checkList) => {
    const cache = getValues('akun_sekunder');
    const _isEmpty = cache === undefined;
    unregister('akun_sekunder');
    setTableData(() => checkList
      .map((item, index) => {
        if (_isEmpty) {
          return {
            ...item,
            jumlah: '',
            deskripsi: '',
          };
        }
        const match = cache.find(c => c.akunting_no === item.akunting_no) || {};
        const result = { ...item, jumlah: match.jumlah || '', deskripsi: match.deskripsi || '' };
        setValue(`akun_sekunder.${index}`, {
          akunting_no: item.akunting_no || '',
          jumlah: match.jumlah || '',
          deskripsi: match.deskripsi || '',
        });
        return result;
      }));
    setDisabledAccount(checkList.map(m => m.akunting_no));
    clearErrors('akun_sekunder');
  };

  const handleDeleteRow = (id) => {
    const cache = getValues('akun_sekunder');
    unregister('akun_sekunder');
    setTableData(row => row
      .filter(item => item.akunting_no !== id)
      .map((item, index) => {
        const match = cache.find(c => c.akunting_no === item.akunting_no);
        const result = { ...item, jumlah: match.jumlah, deskripsi: match.deskripsi };
        setValue(`akun_sekunder.${index}`, {
          akunting_no: item.akunting_no,
          jumlah: match.jumlah,
          deskripsi: match.deskripsi,
        });
        return result;
      }));
  };

  const columns = useMemo(
    () => [
      {
        Header: t('header.name'),
        accessor: 'akunting_no',
        width: 150,
        isMobileHeader: true,
        Cell: (instance) => {
          const { value, row } = instance;
          const match = accountDetailOptions.find(f => f.number === value);
          setValue(`akun_sekunder.${row.index}.akunting_no`, value);
          let nameAccount = value;
          if (match) {
            nameAccount = match.name;
          }
          return (
            <Box css={{ maxWidth: 150 }}>
              <Paragraph isTruncated>{nameAccount}</Paragraph>
            </Box>
          );
        },
      },
      {
        Header: t('header.desc'),
        accessor: 'deskripsi',
        width: '170px',
        Cell: (instance) => {
          const { row, value } = instance;
          const field = `akun_sekunder.${row.index}.deskripsi`;
          return isDetail ? (
            <Tooltip side="top" align="start" label={getValues(field)}>
              <InputText css={{ backgroundColor: isDetail ? '$formDisable' : '$white' }} readOnly={isDetail} size="sm" {...register(field)} placeholder={t('descPlaceholder')} />
            </Tooltip>
          ) : (
            <InputText css={{ backgroundColor: isDetail ? '$formDisable' : '$white' }} readOnly={isDetail} size="sm" {...register(field)} placeholder={t('descPlaceholder')} />
          );
        },
      },
      {
        Header: t('header.amount'),
        accessor: 'jumlah',
        width: '170px',
        Cell: (instance) => {
          const { row } = instance;
          const { original } = row;
          const field = `akun_sekunder.${row.index}.jumlah`;
          const invalid = Array.isArray(errors.akun_sekunder) && !!errors.akun_sekunder[row.index] && !!errors.akun_sekunder[row.index].jumlah;
          const _getValue = () => {
            if (getValues(field)) {
              return getValues(field);
            }
            if (original.kredit) {
              return original.kredit;
            }
            if (original.debet) {
              return original.debet;
            }
            return 0;
          };
          const defaultValue = _getValue();
          const [num, setNum] = useState(defaultValue);
          return (
            <InputGroup css={{ backgroundColor: isDetail ? '$formDisable' : '$white' }} readOnly={isDetail} size="sm" isInvalid={invalid}>
              <InputPrefix>Rp</InputPrefix>
              <InputNumber
                readOnly={isDetail}
                {...register(field)}
                size="sm"
                onValueChange={({ value: val }) => {
                  setNum(val);
                  setValue(field, val);
                }}
                value={num}
                placeholder="0"
                decimalScale={2}
                isNumericString
                allowNegative={false}
                onFocus={() => {
                  if (defaultValue === 0 || defaultValue === '0') {
                    setNum('');
                    setValue(field, '');
                  }
                }}
                onBlur={() => {
                  if (defaultValue === '') {
                    setNum(0);
                    setValue(field, 0);
                  }
                }}
              />
            </InputGroup>
          );
        },
      },
      {
        Header: ' ',
        width: 48,
        hidden: isDetail,
        isAction: true,
        Cell: (c) => {
          const { row } = c;
          return (
            <Box css={{ '@md': { display: tableData.length === 1 && 'none' } }}>
              <EditableTable.Remove
                onRemove={() => handleDeleteRow(row.original.number)}
              />
            </Box>
          );
        },
      },
    ],
    [tableData.length, errors, isDetail],
  );

  const [outlet, setOutlet] = useState(outletOptions.find(f => f.value === String(transactionDetail.outlet_id)));
  const [time, setTime] = useState(moment(getValues('tgl_transaksi')));
  const [account, setAccount] = useState(accountOptions.find(f => f.value === transactionDetail.akun_utama));

  const handleClose = (val) => {
    unregister('akun_sekunder');
    if (!val) {
      if (isDetail) onOpenChange(false);
      setOpenConfirmCancel(true);
    }
  };

  const onCancel = () => {
    reset();
    setOpenConfirmCancel(false);
    onOpenChange(false);
  };

  const handleCreate = (payload) => {
    setValue('status', TRANSACTION_STATUS.NEW);
    setOpenConfirmCreate(true);
    setForm({ ...payload, status: TRANSACTION_STATUS.NEW });
  };

  const handleSaveDraft = (payload) => {
    setValue('status', TRANSACTION_STATUS.DRAFT);
    setOpenConfirmCreate(true);
    setForm({ ...payload, status: TRANSACTION_STATUS.DRAFT });
  };

  const handleSaveDraftDirect = (payload) => {
    setValue('status', TRANSACTION_STATUS.DRAFT);
    create({ ...payload, status: TRANSACTION_STATUS.DRAFT });
  };

  const onConfirm = () => {
    create(form);
  };

  const getTitleMessage = () => {
    if (isReceipt) {
      if (isDetail) {
        return t('title.revenue.detail');
      }
      if (isDraft) {
        return t('title.revenue.edit');
      }
      return t('title.revenue.add');
    }
    if (isDetail) {
      return t('title.expense.detail');
    }
    if (isDraft) {
      return t('title.expense.edit');
    }
    return t('title.expense.add');
  };

  useEffect(() => {
    if (transactionDetail && transactionDetail.attachment_url) {
      const attachmentUrl = transactionDetail.attachment_url;
      setFiles([{
        name: getFileNameFromUrl(attachmentUrl),
        url: attachmentUrl,
      }]);
    }
  }, [transactionDetail]);


  return (
    <React.Fragment>
      <PageDialog open={open} onOpenChange={handleClose}>
        <PageDialog.Title>
          {getTitleMessage()}
        </PageDialog.Title>
        <PageDialog.Content ref={parentScrollContainerRef} wrapperSize="lg" css={{ overflow: 'auto' }}>
          <Box
            css={{
              display: 'grid',
              gridTemplateColumns: '1fr',
              height: '100%',
              width: '100%',
              position: 'relative',
              '@md': {
                gridTemplateColumns: '312px 1fr',
                gap: '$cozy',
                '@media (orientation: portrait)': {
                  gridTemplateColumns: '1fr',
                }
              },
            }}
          >
            <Box
              css={{
                position: 'sticky',
                top: !isMobile ? 40 : 0,
                height: 'fit-content',
                zIndex: '$sticky',
              }}
            >
              <TableOfContent
                activeId={activeId}
                isMobile={isMobile}
                isDialog
                content={TOC_CONTENT(transactionType, t)}
                css={{
                  '& > div': {
                    width: !isMobile ? '100%' : 'unset',
                    // display: 'flex',
                  },
                }}
              />
              {(isDetail || isDraft) && (
                <Box
                  css={{
                    marginTop: '$spacing-05',
                    padding: 20,
                    '@md': {
                      padding: 0,
                      minWidth: 311,
                      marginBottom: '$spacing-06',
                    },
                  }}
                >
                  <ActivityLogSidebar
                    onClickButton={() => setIsOpenActivityLog(true)}
                    lastUpdateDateTime={(activityLog.list && !!activityLog.list.length) && activityLog.list[0].date}
                  />
                </Box>
              )}
            </Box>
            <Box
              as="form"
              id="transaction-income"
              css={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                gap: '$compact',
                '@md': {
                  gap: '$comfortable',
                  alignItems: 'center',
                },
                '& > div, & > div > div': {
                  all: 'inherit',
                },
                '& > div > div': {
                  all: 'inherit',
                },
              }}
            >
              <TableOfContent.Page
                offsetBottom={0}
                scrollThrottle={80}
                onUpdateCallback={(id) => {
                  setActiveId(id);
                }}
                parentScrollContainerRef={parentScrollContainerRef}
              >
                <Paper
                  id="section-one"
                  responsive
                  css={{
                    padding: '$compact',
                    display: 'grid',
                    gap: '$cozy',
                    width: '100%',
                    '@md': {
                      padding: '$cozy',
                    },
                  }}
                >
                  <Box css={{ '@md': { marginBottom: '$spacing-03' } }}>
                    <Heading
                      as="h3"
                      heading="pageTitle"
                    >
                      {isReceipt ? t('title.revenue.section') : t('title.expense.section')}
                    </Heading>
                  </Box>
                  <FormGroup responsive="input" id="outlet_id">
                    <FormLabel htmlFor="outlet_id" variant="required">Outlet</FormLabel>
                    <Controller
                      name="outlet_id"
                      control={control}
                      render={() => (
                        <InputSelect
                          readOnly={isDetail}
                          placeholder={t('placeholder.select', { ns: 'translation' })}
                          id="outlet_id"
                          option={outletOptions}
                          value={outlet}
                          onChange={(val) => {
                            setOutlet(val);
                            setValue('outlet_id', val.value);
                            trigger('outlet_id');
                            if (errors.outlet_id) {
                              clearErrors('outlet_id');
                            }
                          }}
                          isInvalid={!!errors.outlet_id}
                        />
                      )}
                    />
                    {errors.outlet_id && (
                      <FormHelper
                        error
                      >
                        {errors.outlet_id.message}
                      </FormHelper>
                    )}
                  </FormGroup>
                  <FormGroup responsive="input">
                    <FormLabel htmlFor="kode_transaksi" variant="required">{t('form.transactionNumber.label')}</FormLabel>
                    <InputText
                      readOnly={isDetail}
                      maxLength={100}
                      id="kode_transaksi"
                      {...register('kode_transaksi')}
                      placeholder={t('form.transactionNumber.placeholder')}
                      isInvalid={!!errors.kode_transaksi}
                    />
                    <FormHelper>{t('form.transactionNumber.subLabel')}</FormHelper>
                    {errors.kode_transaksi && (
                      <FormHelper
                        error
                      >
                        {errors.kode_transaksi.message}
                      </FormHelper>
                    )}
                  </FormGroup>
                  <FormGroup responsive="input" id="akun_utama">
                    <FormLabel
                      htmlFor="akun_utama"
                      variant="required"
                    >
                      {isReceipt ? t('form.account.target') : t('form.account.source')}
                    </FormLabel>
                    <Controller
                      name="akun_utama"
                      control={control}
                      render={() => (
                        <InputSelect
                          readOnly={isDetail}
                          placeholder={t('placeholder.select', { ns: 'translation' })}
                          id="akun_utama"
                          option={accountOptions}
                          value={account}
                          onChange={(val) => {
                            setAccount(val);
                            setValue('akun_utama', val.value);
                            trigger('akun_utama');
                            if (errors.akun_utama) {
                              clearErrors('akun_utama');
                            }
                          }}
                          isInvalid={!!errors.akun_utama}
                          disabledOptions={disabledAccount}
                        />
                      )}
                    />
                    {errors.akun_utama && (
                      <FormHelper
                        error
                      >
                        {errors.akun_utama.message}
                      </FormHelper>
                    )}
                  </FormGroup>
                  <FormGroup responsive="input">
                    <FormLabel css={{ '& > span': { fontSize: '14px !important', fontWeight: '600 !important' } }} htmlFor="tanggal" variant="required">{t('form.transactionDate.label')}</FormLabel>
                    <Flex gap={3} css={{ '& > *': { flex: 1 }, flexDirection: 'column', '@md': { flexDirection: 'row' } }}>
                      <FormGroup>
                        <Text variant="label" color="primary">{t('form.transactionDate.date')}</Text>
                        <InputDatePicker readOnly={isDetail} size="lg" defaultValue={getValues('tgl_transaksi')} onChange={val => setValue('tgl_transaksi', moment(val).set({ hour: time.get('hour'), minute: time.get('minute') }).toDate())} />
                      </FormGroup>
                      <FormGroup>
                        <Text variant="label" color="primary">{t('form.transactionDate.time')}</Text>
                        <InputTimePicker readOnly={isDetail} size="lg" value={time} onChange={(val) => { setTime(val); setValue('tgl_transaksi', moment(getValues('tgl_transaksi')).set({ hour: val.get('hour'), minute: val.get('minute') }).toDate()); }} />
                      </FormGroup>
                    </Flex>
                  </FormGroup>
                </Paper>
                <Box css={{ padding: '0px $compact', '@md': { display: 'none' } }}>
                  <Separator css={{ '@md': { display: 'none' } }} />
                </Box>
                <Paper
                  id="section-two"
                  responsive
                  css={{
                    padding: '$compact',
                    display: 'grid',
                    width: '100%',
                    gap: '$compact',
                    '@md': {
                      padding: '$cozy',
                      gap: '$cozy',
                    },
                  }}
                >
                  <Box css={{ '@md': { marginBottom: '$spacing-03' } }}>
                    <Heading
                      id="2"
                      as="h3"
                      heading="pageTitle"
                    >
                      {isReceipt ? (
                        t('title.revenue.section2')
                      ) : t('title.expense.section2')}
                    </Heading>
                  </Box>
                  <FormGroup responsive="button">
                    <FormLabel css={{ '& > span': { fontSize: '14px !important', fontWeight: '600 !important' } }} htmlFor="tanggal" variant="required">{t('form.details.label')}</FormLabel>
                    {!isDetail && (
                      <Button
                        size="sm"
                        leftIcon={(
                          <Box css={{ position: 'relative', top: 3 }}>
                            <PlusOutline color="currentColor" />
                          </Box>
                        )}
                        type="button"
                        buttonType="secondary"
                        onClick={() => setOpenDetailAccountOptions(true)}
                        css={{
                          width: 'fit-content',
                          alignItems: 'center',
                          justifySelf: 'flex-end',
                          display: 'none',
                          '@md': {
                            display: 'flex',
                          }
                        }}
                      >
                        {t('form.details.select')}
                      </Button>
                    )}
                    {errors.kode_transaksi && (
                      <FormHelper
                        error
                      >
                        {errors.kode_transaksi.message}
                      </FormHelper>
                    )}
                  </FormGroup>
                  <Box>
                    <Box id="akun_sekunder">
                      <EditableTable
                        key={tableData.length}
                        columns={columns}
                        data={tableData}
                        rowHeight={56}
                        css={{ '& td > div': { minHeight: tableData.length === 0 && 64, '@md': { minHeight: tableData.length === 0 && 132 } } }}
                        emptyDataMessage={t('emptyTable')}
                      />
                    </Box>
                    <Separator css={{ ml: 'auto', maxWidth: 596, mb: '$compact' }} />
                    {!isDetail && (
                      <Button
                        size="sm"
                        leftIcon={(
                          <Box css={{ position: 'relative', top: 3 }}>
                            <PlusOutline color="currentColor" />
                          </Box>
                        )}
                        type="button"
                        buttonType="secondary"
                        onClick={() => setOpenDetailAccountOptions(true)}
                        css={{
                          alignItems: 'center',
                          justifySelf: 'flex-end',
                          width: '100%',
                          display: 'flex',
                          '@md': {
                            display: 'none',
                          }
                        }}
                      >
                        Pilih Akun
                      </Button>
                    )}
                    {errors.kode_transaksi && (
                      <FormHelper
                        error
                      >
                        {errors.kode_transaksi.message}
                      </FormHelper>
                    )}
                    <Flex justify="end">
                      <Flex justify="between" css={{ width: '100%', my: '$spacing-03', '@md': { width: 400 } }}>
                        <Heading as="h4" heading="pageTitle">Total</Heading>
                        <Heading as="h4" heading="pageTitle">{formatCurrency(total, { minimumFractionDigits: total === 0 ? 0 : 2 })}</Heading>
                      </Flex>
                    </Flex>
                    {errors.akun_sekunder && (
                      <FormHelper
                        error
                      >
                        {errors.akun_sekunder.message || errors.akun_sekunder.filter(m => !!m.jumlah.message)[0].jumlah.message}
                      </FormHelper>
                    )}
                  </Box>
                  <FormGroup responsive="input">
                    <FormLabel htmlFor="keterangan">{t('form.remarks.label')}</FormLabel>
                    <InputTextArea
                      readOnly={isDetail}
                      id="keterangan"
                      placeholder={isReceipt ? t('form.remarks.placeholder.revenue') : t('form.remarks.placeholder.revenue')}
                      {...register('keterangan')}
                    />
                  </FormGroup>
                  <FormGroup responsive="input">
                    <FormLabel htmlFor="file">
                      {t('form.attachFile')}
                      <br />
                      <br />
                      <Text variant="caption">
                        {t('form.attachFileDescription')}
                      </Text>
                    </FormLabel>
                    <Box css={{ flex: 1 }}>
                      <Controller
                        control={control}
                        name="attachment_url"
                        render={({ field: { onChange } }) => (
                          <Upload
                            multiple={false}
                            max={1}
                            listType={
                              files &&
                                files.length > 0 &&
                                ((files[0].url && checkFileType(files[0].url, FILE_TYPES.IMAGES)) ||
                                  (files[0].name &&
                                    checkFileType(files[0].name, FILE_TYPES.IMAGES)))
                                ? 'picture'
                                : 'file'
                            }
                            showCropper={false}
                            accept=".jpg,.jpeg,.png,.pdf,.xlsx,.xls"
                            fileList={files}
                            onChange={handleFileChange}
                            action={UPLOAD_FILE_ADDRESS}
                            token={TOKEN_UPLOAD}
                            maxSizeMB={5}
                            customBodyRequest={file => ({ userfile: file })}
                            resKey="item_image_path"
                            onSuccess={val => {
                              if (val.item_image_path) {
                                onChange(val.item_image_path);
                              } else {
                                handleFileChange({});
                              }
                            }}
                            customPreviewIcon={<DownloadOutline color={colors.white} />}
                            onRemove={() => {
                              onChange('');
                              handleFileChange({});
                            }}
                            onError={(error, response) => {
                              if (error) {
                                addToast({
                                  title: t('toast.somethingWrong', 'Terjadi Kesalahan', { ns: 'translation' }),
                                  description: catchError(response),
                                  variant: 'failed',
                                  position: 'top-right',
                                  dismissAfter: 3000,
                                });
                                if (error.message === 'Unauthorized') {
                                  setTimeout(() => {
                                    clearSession();
                                  }, 1000);
                                }
                              }
                            }}
                            onPreviewIconClick={file => {
                              if (file.url) {
                                downloadFileHelper(file.url);
                              }
                            }}
                            errorMessage={fileError}
                            disabled={transactionDetail.status === TRANSACTION_STATUS.NEW || transactionDetail.status === TRANSACTION_STATUS.VOID}
                          />
                        )}
                      />
                    </Box>
                  </FormGroup>
                </Paper>
              </TableOfContent.Page>
            </Box>
          </Box>
        </PageDialog.Content>
        <PageDialog.Footer
          css={{
            justifyContent: 'center',
            height: 'auto',
            padding: '$spacing-05',
            borderTop: '1px solid $gray150',
            '@lg': {
              height: 64,
              padding: '12px 24px',
              borderTop: 'none',
              gap: '$compact',
            },
          }}
        >
          <Box css={{
            display: 'none',
            '@lg': {
              display: 'block',
              width: 312,
            },
          }}
          />
          <Box
            css={{
              display: 'flex',
              width: '100%',
              gap: '$compact',
              justifyContent: 'space-between',
              flex: 1,
              '@lg': {
                maxWidth: 982,
                display: 'flex',
              },
            }}
          >
            <Box css={{ display: 'none', '@lg': { display: 'flex', gap: '10px' } }}>
              {isDraft && !isDetail ? (
                <Button buttonType="negative-secondary" css={{ minWidth: 'unset', width: 40, padding: 0 }} onClick={onDelete}>
                  <TrashOutline color={colors.iconRed} />
                </Button>
              ) : (<Box css={{ display: 'none', '@lg': { display: 'block' } }} />)}
              {(isDraft || isAddNew) && !isDetail ? (
                <Button type="button" buttonType="secondary" onClick={handleSubmit(handleSaveDraft, handleError)} disabled={isDisabled}>
                  {t('label.saveAsDraft', { ns: 'translation' })}
                </Button>
              ) : (<Box css={{ display: 'none', '@lg': { display: 'block' } }} />)}
            </Box>
            {isDetail ? null : (
              <Box css={{ display: 'block', m: 'auto', '@lg': { display: 'none' } }}>
                <IconButton onClick={() => setOpenOption(true)}>
                  <EllipsisHorizontalOutline color={colors.iconSecondary} />
                </IconButton>
              </Box>
            )}
            <Flex gap={5} css={{ flex: 1, '@lg': { flex: 'unset' } }}>
              {isDetail ? (
                <DialogClose asChild>
                  <Button
                    size="md"
                    buttonType="ghost"
                    type="button"
                    css={{ flex: 1, '@lg': { flex: 'unset' } }}
                  >
                    {t('label.close', { ns: 'translation' })}
                  </Button>
                </DialogClose>
              ) : (
                <React.Fragment>
                  <DialogClose asChild>
                    <Button
                      size="md"
                      buttonType="ghost"
                      type="button"
                      css={{ flex: 1, '@lg': { flex: 'unset' } }}
                    >
                      {t('label.cancel', { ns: 'translation' })}
                    </Button>
                  </DialogClose>
                  <Button
                    size="md"
                    type="button"
                    css={{ flex: 1, '@lg': { flex: 'unset' } }}
                    onClick={handleSubmit(handleCreate, handleError)}
                    disabled={isDisabled}
                  >
                    {t('label.save', { ns: 'translation' })}
                  </Button>
                </React.Fragment>
              )}
            </Flex>
          </Box>
        </PageDialog.Footer>
      </PageDialog>
      <CustomConfirmCancel
        title={isReceipt ? t('modal.cancel.revenue.title') : t('modal.cancel.expense.title')}
        open={openConfirmCancel}
        onOpenChange={setOpenConfirmCancel}
        onConfirm={handleSubmit(handleSaveDraftDirect, () => { setOpenConfirmCancel(false); })}
        onCancel={onCancel}
        transactionType={transactionType}
        t={t}
      />
      <CustomConfirmModal
        t={t}
        open={openConfirmCreate}
        onOpenChange={setOpenConfirmCreate}
        onConfirm={onConfirm}
        isSubmitting={isSubmitting}
        title={isReceipt ? t('modal.save.revenue.title') : t('modal.save.expense.title')}
        message={getValues('status') === 2 ? (
          <Trans t={t} i18nKey={`modal.save.${isReceipt ? 'revenue' : 'expense'}.descriptionDraft`}>{{ code: wrap(getValues('kode_transaksi')) }}</Trans>
        ) : (
          <Trans t={t} i18nKey={`modal.save.${isReceipt ? 'revenue' : 'expense'}.description`}>{{ code: wrap(getValues('kode_transaksi')) }}</Trans>
        )}
      />
      <DetailAccountOptions
        key={`account-list-${openDetailAccountOptions}`}
        selectedAccount={account}
        open={openDetailAccountOptions}
        onOpenChange={setOpenDetailAccountOptions}
        accountDetailOptions={accountDetailOptions}
        title={t('selectAccount')}
        onSubmit={handleAddRow}
        checkedItems={tableData.reduce((prev, current) => { Object.assign(prev, { [current.akunting_no]: true }); return prev; }, {})}
        t={t}
      />
      <ModalDialog open={openOption} onOpenChange={setOpenOption}>
        <ModalDialog.Title>
          {t('label.nextOption', { ns: 'translation' })}
        </ModalDialog.Title>
        <ModalDialog.Content>
          <Box
            css={{
              display: 'flex',
              flexDirection: 'column',
              gap: '$compact',
            }}
          >
            {isDraft && !isDetail ? (
              <Box onClick={() => { onDelete(); setOpenOption(false); }}>
                <Paragraph color="primary">
                  {t('label.delete', { ns: 'translation' })}
                </Paragraph>
              </Box>
            ) : null}
            {(isDraft || isAddNew) && !isDetail ? (
              <Box onClick={() => { handleSubmit(handleSaveDraft, handleError)(); setOpenOption(false); }}>
                <Paragraph color="primary">
                  {t('label.saveAsDraft', { ns: 'translation' })}
                </Paragraph>
              </Box>
            ) : null}
          </Box>
        </ModalDialog.Content>
      </ModalDialog>
      <ActivityLogModal
        open={isOpenActivityLog}
        handleOpen={setIsOpenActivityLog}
        activityLog={activityLog}
        handleFetchActivityLog={() => handleFetchActivityLog(transactionDetail)}
      />
    </React.Fragment>
  );
};

CashTransactionForm.propTypes = {
  activityLog: PropTypes.shape({
    list: PropTypes.arrayOf(PropTypes.shape({
      description: PropTypes.string.isRequired,
      date: PropTypes.string.isRequired,
    })),
    last: PropTypes.shape({
      description: PropTypes.string.isRequired,
      date: PropTypes.string,
    }),
  }),
};

CashTransactionForm.defaultProps = {
  activityLog: {
    list: [],
    last: {},
  },
};

export default CashTransactionForm;
