/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable no-nested-ternary */
import React, {
  useEffect, useState, useContext, useCallback,
} from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { isEmpty, isObject, isEqual } from 'lodash';
import XLSX from 'xlsx/dist/xlsx.core.min';

import {
  Box,
  Button,
  Flex,
  FormLabel,
  FormHelper,
  Heading,
  ModalDialog,
  ModalDialogContent,
  ModalDialogFooter,
  ModalDialogTitle,
  InputText,
  Text,
  Upload,
  ToastContext,
  InputResourceList,
  Tooltip,
} from '@majoo-ui/react';
import {
  DownloadOutline,
  PlusOutline,
  TrashOutline,
  CircleInfoOutline,
} from '@majoo-ui/icons';
import { foundations } from '@majoo-ui/core';

import {
  Divider, findDuplicateElements, getRowValue, matchMediaChecker, MATCH_MEDIA_TYPE, MobileAdvanceOption, StockQuantityLabel,
} from './utils';
import * as inventoryApi from '../../../../data/inventories';
import { catchError, fileObjectToBinary, formatDecimal } from '../../../../utils/helper';

const { iconSecondary, iconDisable } = foundations.colors;

const schema = translate => yup.object().shape({
  serialNumbers: yup.array().of(
    yup.object().shape({
      value: yup.string().required(translate('detailpage.snbnDialog.serialNumberEmpty', 'Mohon Lengkapi Serial Number', { ns: 'Penjualan/Inventori/purchaseInvoice' })),
    }),
  ),
});

const SerialNumberDialog = ({
  isOpen,
  onClose,
  title,
  stock,
  onSave,
  serial_number,
  isNegativeCase = false, negativeCaseType = 'plus',
  stock_opname_detail_actual: stockActual,
  stock_opname_detail_registered: stockSystem,
  stockPage = '',
  // Stok Entry form data id Needed
  id: item_id,
  formData,
  translate: t,
  // List data editable SNBN from stock entry
  checkedSNBN,
  paymentStatus, // get paymentStatus from stock entry
  originalData,
  ...item
}) => {
  const translate = (key, defaultValue, ns = {}) => (t ? t(key, defaultValue, ns) : defaultValue);
  const {
    handleSubmit,
    formState: { errors },
    setValue,
    clearErrors,
    setError,
    control,
    watch,
    register,
  } = useForm({
    resolver: yupResolver(schema(translate)),
    defaultValues: {
      serialNumbers: [],
    },
  });

  const { addToast } = useContext(ToastContext);

  const { fields, append, remove } = useFieldArray({ control, name: 'serialNumbers' });

  const [serialNumbers] = watch(['serialNumbers']);

  const [isMobile, setIsMobile] = useState(!matchMediaChecker(MATCH_MEDIA_TYPE.MD));
  const [page, setPage] = useState(0);
  const [serialNumberList, setSerialNumberList] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isMoreSerial, setMoreSerial] = useState(true);
  const [filterSerialList, setFilterSerialList] = useState([]);
  const [enableSerialList, setEnableSerialList] = useState([]);
  const [serialNumberItems, setSerialNumberItems] = useState({
    items: [],
    itemSelected: [],
    itemNotSelected: [],
  });
  const [file, setFile] = useState(null);
  const [uploadError, setUploadError] = useState(false);
  const [snCaseFlag, setSnCaseFlag] = useState('plus');
  const [firstLoad, setFirstLoad] = useState(true);


  const hasPurchaseShipmentsRef = formData && formData.purchaseShipments && formData.purchaseShipments.length > 0;


  const handleSerialNumberItems = (value, name) => {
    setSerialNumberItems({ ...serialNumberItems, [name]: value });
  };

  const onSubmit = useCallback(async (data, submitType = false) => {
    setIsLoading(true);
    const { serialNumbers } = data;
    let errorMessage = '';

    clearErrors('serialNumbers');

    // Validation based on stock draft(false) / save(true)
    if (submitType === true) {
      if (serialNumbers.length < stock) {
        errorMessage = translate('detailpage.snbnDialog.serialNumberMissing', `Mohon lengkapi Serial Number sesuai dengan stok diterima (Kurang ${formatDecimal(stock - serialNumbers.length)} Serial Number)`, { quantity: formatDecimal(stock - serialNumbers.length), ns: 'Penjualan/Inventori/purchaseInvoice' });
      }

      if (serialNumbers.length > stock) {
        errorMessage = translate('detailpage.snbnDialog.serialNumberExcess', `Mohon lengkapi Serial Number sesuai dengan stok diterima (Lebih ${formatDecimal(serialNumbers.length - stock)} Serial Number)`, { quantity: formatDecimal(serialNumbers.length - stock), ns: 'Penjualan/Inventori/purchaseInvoice' });
      }

      if (errorMessage) {
        setError('serialNumbers', { type: 'discrepancySerialNumberLength', message: errorMessage });
        setIsLoading(false);
        return;
      }
    }

    const cleanedSerialNumbers = serialNumbers.filter(({ value }) => value).map(({ value }) => value);
    const duplicatedElements = findDuplicateElements(cleanedSerialNumbers);
    if (duplicatedElements.length) {
      setError('serialNumbers', { type: 'duplicateSerialNumber', message: translate('detailpage.snbnDialog.serialNumberDuplicated', 'Terdapat Serial Number yang sama', { ns: 'Penjualan/Inventori/purchaseInvoice' }) });
      serialNumbers.forEach(({ value }, index) => {
        if (duplicatedElements.includes(value)) {
          setError(`serialNumbers.${index}.value`, { type: 'duplicateSerialNumber', message: translate('detailpage.snbnDialog.serialNumberDuplicated', 'Terdapat Serial Number yang sama', { ns: 'Penjualan/Inventori/purchaseInvoice' }) });
        }
      });
      setIsLoading(false);
      return;
    }

    if (snCaseFlag === 'minus') {
      onSave({
        ...(serial_number && { ...serial_number }),
        is_reserved: submitType,
        serial_no: serialNumbers.map(({ value, label }) => ({ value, label })),
        case_flag: snCaseFlag,
      });
      setIsLoading(false);
    } else {
      // default stockPage are '', below block code are for Stock Entry / Stock Opname Plus Difference
      try {
        // payload by stock page value
        const stockOpnamePlusPayload = {
          item_id: Number(item.itemId),
          outlet_id: Number(item.idOutlet),
          ...(item.transactionNo ? { transaction_no: item.transactionNo } : {}),
          transaction_type: item.transactionType,
          is_negative: false,
        };

        const stockEntryPayload = {
          item_id: Number(item_id),
          outlet_id: formData && Number(formData.outletId),
          ...(formData && formData.transactionNumber ? { transaction_no: formData.transactionNumber } : {}),
          transaction_type: formData && Number(formData.stockSourceType),
        };

        const originalSerialNumbers = (originalData
          && originalData.serial_number
          && originalData.serial_number.serial_no
          && originalData.serial_number.serial_no.length > 0 ? originalData.serial_number.serial_no : []) || [];

        const payload = {
          ...(stockPage === 'opname' ? { ...stockOpnamePlusPayload } : { ...stockEntryPayload }),
          list: serialNumbers.filter(snItem => !originalSerialNumbers.includes(snItem.value)).map(({ value }) => value),
        };

        if (payload.list && payload.list.length > 0) {
          const { data: duplicatedSerialNumbers } = await inventoryApi.checkSNBNValidationV1(payload);

          if (duplicatedSerialNumbers && duplicatedSerialNumbers.length > 0 && !hasPurchaseShipmentsRef) {
            setError('serialNumbers', { type: 'duplicateSerialNumberFromDatabase', message: translate('detailpage.snbnDialog.serialNumberBeenUsed', 'Serial Number telah digunakan', { ns: 'Penjualan/Inventori/purchaseInvoice' }) });
            serialNumbers.forEach(({ value }, index) => {
              if (duplicatedSerialNumbers.includes(value)) {
                setError(`serialNumbers.${index}.value`, { type: 'duplicateSerialNumberFromDatabase', message: translate('detailpage.snbnDialog.serialNumberBeenUsed', 'Serial Number telah digunakan', { ns: 'Penjualan/Inventori/purchaseInvoice' }) });
              }
            });
            setIsLoading(false);
            return;
          }
        }

        if (snCaseFlag === 'plus' && (paymentStatus === 1 || hasPurchaseShipmentsRef) && (checkedSNBN && checkedSNBN.length > 0)) {
          const formattedSNItem = serialNumbers.reduce((filtered, sn, index) => {
            if (!serial_number.serial_no.includes(sn.value)) {
              const snItem = {
                current_serial_number: serial_number.serial_no[index],
                serial_number: sn.value,
              };
              filtered.push(snItem);
            }
            return filtered;
          }, []);

          const updateStockEntrySNPayload = { ...stockEntryPayload, sn_item: formattedSNItem };

          let resUpdatedSN = null;
          if (formattedSNItem.length > 0 && !hasPurchaseShipmentsRef) {
            resUpdatedSN = await inventoryApi.updateEditableSnbn(updateStockEntrySNPayload);
            if (resUpdatedSN.data) return;
          }
        }
        onSave({
          ...(serial_number && { ...serial_number }),
          is_reserved: submitType,
          serial_no: serialNumbers.map(({ value }) => value),
          is_draft: !submitType,
          case_flag: snCaseFlag,
        });

        setIsLoading(false);
      } catch (err) {
        addToast({
          title: translate('toast.error', 'Gagal!', { ns: 'translation' }),
          description: catchError(err),
          variant: 'failed',
        });
        setIsLoading(false);
      }
    }
  }, [stock, formData, item_id, snCaseFlag]);

  const handleExportTemplate = useCallback(async () => {
    const payload = {};
    if (snCaseFlag === 'minus') {
      const { itemId, idOutlet } = item;

      payload.item_id = itemId;
      payload.outlet_id = idOutlet;
    }

    try {
      const { data, msg } = await inventoryApi.downloadSNTemplateV1(payload, snCaseFlag === 'minus');
      if (!data) {
        throw new Error(msg);
      }

      window.open(data.file_url, '_blank');
    } catch (err) {
      addToast({
        title: translate('toast.error', 'Gagal!', { ns: 'translation' }),
        description: catchError(err),
        variant: 'failed',
      });
    }
  }, [snCaseFlag, item]);

  const handleImportTemplate = useCallback(async ({ file }) => {
    clearErrors('serialNumbers');
    setUploadError(false);

    try {
      const convertFileToBinary = await fileObjectToBinary(file);
      if (!convertFileToBinary) {
        throw new Error(translate('error.unchoosedFile', 'Mohon pilih file terlebih dahulu', { ns: 'translation' }));
      }
      if (!['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/wps-office.xlsx'].includes(file.type)) {
        setUploadError(true);
        setError('serialNumbers', { type: 'fileFormatError', message: translate('detailpage.snbnDialog.fileFormatInvalid', 'Format berkas tidak didukung, hanya mendukung format .xls atau .xlsx', { ns: 'Penjualan/Inventori/purchaseInvoice' }) });
        return;
      }

      const workbook = XLSX.read(convertFileToBinary, { type: 'binary' });
      const sheetName = workbook.SheetNames[0];
      const rowObjectArray = XLSX.utils.sheet_to_row_object_array(workbook.Sheets[sheetName]);

      const templateMandatoryColumn = ['Serial Number'];
      const sheetColumn = XLSX.utils.sheet_to_row_object_array(workbook.Sheets[sheetName], { header: 1, range: 'A1:Z1' });

      let columnMandatoryCompartment = !isEqual(templateMandatoryColumn, sheetColumn[0]);
      if (['opname', 'wasted'].includes(stockPage) && snCaseFlag === 'minus') {
        columnMandatoryCompartment = !isEqual(templateMandatoryColumn[0], sheetColumn[0][0]);
      }

      if (columnMandatoryCompartment) {
        setUploadError(true);
        setError('serialNumbers', { type: 'templateError', message: translate('detailpage.snbnDialog.invalidTemplate', 'Template tidak sesuai, gunakan Template yang disediakan.', { ns: 'Penjualan/Inventori/purchaseInvoice' }) });
        return;
      }

      const serialNumbers = [];
      rowObjectArray.forEach((roa) => {
        const value = getRowValue(roa, 'Serial Number');
        if (value) {
          serialNumbers.push(value);
        }
      });

      if (serialNumbers.length === 0) {
        setUploadError(true);
        setError('serialNumbers', { type: 'emptyData', message: translate('detailpage.snbnDialog.serialNumberEmptyList', 'Mohon lengkapi serial number, harap koreksi dan silakan impor ulang.', { ns: 'Penjualan/Inventori/purchaseInvoice' }) });
        return;
      }

      if (['opname', 'wasted'].includes(stockPage) && snCaseFlag === 'minus') {
        await fetchSelectedSN(serialNumbers, serialNumberList);
      } else {
        serialNumbers.forEach(sn => append({ value: sn }));
      }

      setFile(file);
    } catch (err) {
      setFile(null);
      addToast({
        title: translate('toast.error', 'Gagal!', { ns: 'translation' }),
        description: catchError(err),
        variant: 'failed',
      });
    }
  }, [stock, snCaseFlag, serialNumberList]);

  const getEnabledSerialItems = useCallback(
    () => {
      const idSerialNumbers = serialNumberList.map(item => ({ value: item.label, hasTransaction: item.hasTransaction }));
      const idSerialNumberAdd = serialNumberItems.itemSelected.map(item => ({ value: item.label, hasTransaction: item.hasTransaction }));
      let enabledSerialListChecker = false;
      // default stockPage is 'opname'
      if (stockPage === 'wasted') {
        enabledSerialListChecker = idSerialNumberAdd.length === stockActual;
      } else {
        enabledSerialListChecker = snCaseFlag === 'minus' ? idSerialNumberAdd.length === (stockSystem - stockActual) : idSerialNumberAdd.length === stockActual;
      }
      // if idSerialNumber length equal with excess length, then set selected serial as enabled, and other disabled
      // else set all enabled
      const enabledSerialListCheckerItems = enabledSerialListChecker ? [...idSerialNumberAdd] : [...idSerialNumbers, ...idSerialNumberAdd];
      const parsedEnabledSerialList = enabledSerialListCheckerItems.filter(idsna => !idsna.hasTransaction).map(idsnaz => idsnaz.value);
      setEnableSerialList(parsedEnabledSerialList);
    },
    [serialNumberList, serialNumberItems.itemSelected],
  );

  const fetchSNBNAvailability = useCallback(
    async () => {
      const {
        itemId,
        idOutlet,
        transactionNo,
        transactionType,
        case_flag,
      } = item;

      const payload = {
        item_id: Number(itemId),
        outlet_id: Number(idOutlet),
        transaction_no: transactionNo,
        transaction_type: transactionType,
        is_negative: case_flag === 'minus',
        list: serial_number.serial_no.map(serialNo => (isObject(serialNo) ? serialNo.label : serialNo)),
      };

      try {
        const { data, error } = await inventoryApi.checkSNBNValidationV1(payload);
        if (error) {
          throw new Error(error);
        }
        return data;
      } catch (error) {
        addToast({
          variant: 'failed',
          title: translate('toast.somethingWrong', 'Terjadi Kesalahan', { ns: 'translation' }),
          description: catchError(error),
          dismissAfter: 3000,
        });
      }
    },
    [item],
  );

  // Set Selected Serial Number on First Load
  const fetchSelectedSN = async (serialNumberData, serialNumberListData) => {
    if (isNegativeCase && negativeCaseType === 'minus' && serialNumberData.length > 0) {
      // Set populated selected serial
      let populateSelectedSerial = [];
      const snbnCheckedList = item.transactionNo !== '' ? await fetchSNBNAvailability() : [];
      // If not listed in snbnCheckedList, then set by avail sn
      const availableSN = serialNumberData.filter(sn => (isObject(sn) ? !snbnCheckedList.includes(sn.serial_no) : !snbnCheckedList.includes(sn)));
      // then check by serial number response by its transaction number, if had same with availableSN
      populateSelectedSerial = stockPage === 'wasted'
        ? serialNumberListData.filter(sr => availableSN.find(sn => (isObject(sn) ? sn.label === sr.label : sn === sr.label)))
        : serialNumberListData.filter(sr => serialNumberData.find(sn => (isObject(sn) ? sn.label === sr.label : sn === sr.label)));
      // then set checked
      setSerialNumberItems(prevSerialNumberItem => ({
        ...prevSerialNumberItem,
        itemSelected: populateSelectedSerial,
      }));
    }
  };

  // Check current serial number data availability
  const currentSelectedSNSetter = useCallback(
    async () => {
      const unavailableSN = await fetchSNBNAvailability();
      const available = serial_number.serial_no.filter(arr => !unavailableSN.includes(arr));
      // if available
      if (available.length) {
        return available.map(serialItem => ({
          value: serialItem,
          label: serialItem,
          serial_no: serialItem,
        }));
      }
      return [];
    },
    [item, serial_number.serial_no],
  );

  // for set Serial Number list on input resource list
  const fetchSNByItemId = async ({ query, index }) => {
    const { idOutlet, itemId } = item;
    const payload = {
      outlet_id: idOutlet,
      item_id: itemId,
      page: index !== undefined ? index : page,
      limit: 10,
      sort_by: 'id',
      sort_type: 'desc',
      search_column_name: 'serial_no',
    };

    if (query) {
      Object.assign(payload, { search_keyword: query });
    }

    if (!isMoreSerial && index === undefined) return;

    setIsLoading(true);
    try {
      const response = await inventoryApi.getSerialNumberListByItem(payload);

      let serialNumbersRes = [];
      if (query !== undefined && (index === 0 || page === 0)) {
        serialNumbersRes = response.data.map(itemRes => ({
          // NOTE: set value with unique serial number
          value: itemRes.serial_no,
          label: itemRes.serial_no,
          hasTransaction: itemRes.stock_on_hand === 0 // if stock_on_hand equal 0
            ? !((
              serial_number.serial_no.length > 0
              && serial_number.serial_no.includes(itemRes.serial_no)
            )) // if serial looped item sn number not same with serial number on transaction, then set true
            : false, // if stock_on_hand not equal 0
          ...((itemRes.stock_on_hand === 0
            ? (
              serial_number.serial_no.length > 0
              && serial_number.serial_no.includes(itemRes.serial_no)
            ) ? {}
              : {
                icon: () => (
                  <Tooltip label={translate('detailpage.snbnDialog.serialNumberTransUsed', 'Serial Number ini sudah digunakan untuk transaksi', { ns: 'Penjualan/Inventori/purchaseInvoice' })} side="right">
                    <CircleInfoOutline size={20} color={iconDisable} />
                  </Tooltip>
                ),
              }
            : {}
          )),
        }));
      } else {
        const filteredSNSelectedIncludes = serialNumberItems.itemSelected.map(({ label }) => label);
        serialNumbersRes = [
          ...serialNumberList,
          ...response.data.map(itemRes => ({
            value: itemRes.serial_no,
            label: itemRes.serial_no,
            hasTransaction: itemRes.stock_on_hand === 0 // if stock_on_hand equal 0
              ? !((
                filteredSNSelectedIncludes.length > 0
                && filteredSNSelectedIncludes.includes(itemRes.serial_no)
              )) // if serial looped item sn number not same with serial number on transaction, then set true
              : false, // if stock_on_hand not equal 0
            ...((itemRes.stock_on_hand === 0
              ? (
                filteredSNSelectedIncludes.length > 0
                && filteredSNSelectedIncludes.includes(itemRes.serial_no)
              ) ? {}
                : {
                  icon: () => (
                    <Tooltip label={translate('detailpage.snbnDialog.serialNumberTransUsed', 'Serial Number ini sudah digunakan untuk transaksi', { ns: 'Penjualan/Inventori/purchaseInvoice' })} side="right">
                      <CircleInfoOutline size={20} color={iconDisable} />
                    </Tooltip>
                  ),
                }
              : {}
            )),
          })),
        ];
      }

      let populateDataSerialNumberList = [];
      // Run For First Load Only
      if (firstLoad) {
        const tempSelectedSN = await currentSelectedSNSetter();
        setSerialNumberItems(prevSerialNumberItem => ({
          ...prevSerialNumberItem,
          itemSelected: tempSelectedSN.map((pSD) => {
            const foundedData = serialNumbersRes.find(snRes => snRes.label === pSD.serial_no);
            if (foundedData) {
              return ({
                ...foundedData,
                serial_no: foundedData.label,
              });
            }
            return ({
              value: pSD.serial_no,
              label: pSD.serial_no,
              serial_no: pSD.serial_no,
              hasTransaction: false,
            });
          }),
        }));
        populateDataSerialNumberList = serialNumbersRes.filter(
          snResUnselected => (!serial_number.serial_no.includes(snResUnselected.label)
            ? snResUnselected : tempSelectedSN.map((pSD) => {
              const foundedData = serialNumbersRes.find(snRes => snRes.label === pSD.serial_no);
              if (foundedData) {
                return ({
                  ...foundedData,
                  serial_no: foundedData.label,
                  label: foundedData.label,
                });
              }
            })),
        );

        setFirstLoad(false);
      } else {
        const filteredSNLabel = serialNumberItems.itemSelected.map(filteredSNObj => filteredSNObj);
        populateDataSerialNumberList = serialNumbersRes.filter(
          snResUnselected => (!filteredSNLabel.map(snItem => snItem.label).includes(snResUnselected.label)
            ? snResUnselected : filteredSNLabel.map((pSD) => {
              const foundedData = serialNumbersRes.find(snRes => snRes.value === pSD.value);
              if (foundedData) {
                return ({
                  ...foundedData,
                  label: foundedData.label,
                });
              }
            })),
        );
      }
      // auto select when list serial equal 1 and query exist
      if (populateDataSerialNumberList.length === 1 && query) {
        handleSerialNumberItems([...serialNumberItems.itemSelected, populateDataSerialNumberList[0]], 'itemSelected');
      }
      // set list of serial numbers
      setSerialNumberList(populateDataSerialNumberList);
      setPage(response.meta.current_page + 1);
      setMoreSerial(serialNumbersRes.length < response.meta.total);
    } catch (error) {
      setIsLoading(false);
      addToast({
        variant: 'failed',
        title: translate('toast.somethingWrong', 'Terjadi Kesalahan', { ns: 'translation' }),
        description: catchError(error),
        dismissAfter: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSerial = useCallback(
    ({ query, index }) => {
      fetchSNByItemId({ query, index });
    },
    [page, serialNumberList, isMoreSerial, serialNumberItems],
  );

  const handleAddSerialNumber = useCallback(() => {
    if (errors && errors.serialNumbers && errors.serialNumbers.type === 'discrepancySerialNumberLength') {
      clearErrors('serialNumbers');
    }

    append({ value: '' });
  }, [errors]);

  const populateNegativeCaseMinusItem = useCallback(
    (serialSelected) => {
      setValue('serialNumbers', [...serialSelected]);
    },
    [serialNumberItems.items],
  );

  const handleSelectedSerialItem = useCallback(
    () => {
      const items = [...serialNumberItems.items, ...serialNumberItems.itemSelected];
      const filterSerialListTemp = items.filter((filterSerialItem, index, data) => index === data.findIndex(serial => serial.value === filterSerialItem.value));
      setSerialNumberItems({ ...serialNumberItems, items: [...filterSerialListTemp] });
    },
    [serialNumberItems.itemSelected],
  );

  // Set Filter Serial List
  useEffect(() => {
    setFilterSerialList(serialNumberList);
  }, [serialNumberList]);

  useEffect(() => {
    getEnabledSerialItems();
  }, [serialNumberList, serialNumberItems.itemSelected]);

  useEffect(() => {
    setSerialNumberItems({
      ...serialNumberItems,
      items: serialNumberItems.items.filter(item => !serialNumberItems.itemNotSelected.map(deleted => deleted).includes(item.value)),
      itemSelected: serialNumberItems.itemSelected.filter(item => !serialNumberItems.itemNotSelected.map(deleted => deleted).includes(item.value)),
    });
  }, [serialNumberItems.itemNotSelected]);

  useEffect(() => {
    if (serialNumberItems.itemSelected.length > 0) {
      handleSelectedSerialItem();
    }
  }, [serialNumberItems.itemSelected]);

  useEffect(() => {
    // trigger setValue serialNumbers only when negative case with minus excess value
    if (isNegativeCase && negativeCaseType === 'minus') populateNegativeCaseMinusItem(serialNumberItems.items);
    if (serialNumberItems.items.length !== 0) {
      const filterSerial = serialNumberList.filter(serialNumberItem => !serialNumberItems.items.map(itemOfSerialNumberItems => itemOfSerialNumberItems.value).includes(serialNumberItem.value));
      setFilterSerialList(filterSerial);
    }
  }, [serialNumberList, serialNumberItems.items]);

  useEffect(() => {
    if (serial_number && !isNegativeCase && item.case_flag === 'plus' && negativeCaseType === item.case_flag) {
      if (checkedSNBN && checkedSNBN.length > 0) {
        setValue('serialNumbers', checkedSNBN.map(sn => ({ value: sn.serial_number, isDisabled: !sn.is_editable || false })));
      } else if (Object.keys(serial_number).length > 0 && serial_number.serial_no && serial_number.serial_no.length > 0 && checkedSNBN.length === 0) {
        setValue('serialNumbers', serial_number.serial_no.map(sn => ({ value: sn, isDisabled: false })));
      }
    }
  }, [serial_number, isNegativeCase, negativeCaseType, item.case_flag, checkedSNBN]);

  useEffect(() => {
    setIsMobile(!matchMediaChecker(MATCH_MEDIA_TYPE.MD));
  }, [matchMediaChecker(MATCH_MEDIA_TYPE.MD)]);

  useEffect(() => {
    // if isNegativeCase is true & negativeCaseType is minus
    if (isNegativeCase && negativeCaseType === 'minus') {
      // set local caseFlag based on negativeCaseType
      setSnCaseFlag(negativeCaseType);
      // then fetch serial
      fetchSerial({ index: 0 });
    } else {
      setIsLoading(false);
    }
    return () => {
      setFirstLoad(true);
    };
  }, []);

  // render
  return (
    <ModalDialog
      open={isOpen}
      onOpenChange={isLoading ? () => { } : () => onClose()}
      layer="$modal"
      modal
      isMobile={isMobile}
      width={!isMobile ? '473px' : '100%'}
      onPointerDownOutside={e => e.preventDefault()}
    >
      <ModalDialogTitle>
        {title}
      </ModalDialogTitle>
      <ModalDialogContent
        css={{
          overflow: 'auto',
          '@md': {
            maxHeight: 'calc((100vh - 64px) - 184px)',
          },
        }}
      >
        <Flex
          direction="column"
          gap={isMobile ? 6 : 5}
        >
          {!isMobile ? (
            <Flex direction="column">
              <Flex justify="beetwen">
                <Flex direction="column" gap={5}>
                  <Flex direction="column" gap={2}>
                    <Heading as="h6" heading="sectionSubTitle" color="primary">
                      {translate('detailpage.snbnDialog.importLabel', 'Impor Template', { ns: 'Penjualan/Inventori/purchaseInvoice' })}
                    </Heading>
                    <Text
                      color="secibdart"
                      css={{
                        fontSize: 12,
                        fontWeight: 400,
                        lineHeight: '16px',
                        width: '80%',
                      }}
                    >
                      {translate('detailpage.snbnDialog.importNote', 'Silakan ekspor template terlebih dahulu jika belum memiliki template', { ns: 'Penjualan/Inventori/purchaseInvoice' })}
                    </Text>
                  </Flex>
                  <Upload
                    fileList={file ? [{ name: file.name, url: file.url }] : []}
                    accept=".xls, .xlsx"
                    listType="file"
                    max={1}
                    height={140}
                    width={140}
                    css={{
                      '&:nth-child(1)': {
                        background: '$gray50',
                      },
                    }}
                    onChange={handleImportTemplate}
                    onRemove={() => { setFile(null); remove(fields); }}
                    errorMessage={uploadError ? ' ' : undefined}
                    disabled={isLoading}
                  />
                </Flex>
                <Flex>
                  <Button
                    css={{ fontWeight: 'bold', flex: 1 }}
                    leftIcon={<DownloadOutline size={24} color="currentColor" />}
                    buttonType="secondary"
                    onClick={handleExportTemplate}
                    size="sm"
                    disabled={isLoading}
                  >
                    {translate('detailpage.snbnDialog.exportButton', 'Ekspor Template', { ns: 'Penjualan/Inventori/purchaseInvoice' })}
                  </Button>
                </Flex>
              </Flex>
              {
                (stockPage === 'opname' || stockPage === 'wasted') && !isEmpty(errors) && errors.serialNumbers && errors.serialNumbers.type && errors.serialNumbers.type === 'templateError' ? (
                  <FormHelper
                    error
                    css={{ marginTop: '$spacing-03 !important' }}
                  >
                    {errors.serialNumbers.message}
                  </FormHelper>
                ) : null
              }
            </Flex>
          ) : null}

          {!isMobile ? (
            <Divider />
          ) : null}

          <Flex direction="column" gap={5}>
            {
              isNegativeCase && negativeCaseType === 'minus' ? (
                <Flex justify="end">
                  <StockQuantityLabel
                    stockPage={stockPage}
                    systemStock={stockSystem}
                    actualStock={stockActual}
                    selectedItem={serialNumberItems.itemSelected}
                    quantityLabel={translate('detailpage.snbnDialog.stockQuantity', 'Jumlah Stok:', { ns: 'Penjualan/Inventori/purchaseInvoice' })}
                  />
                </Flex>
              ) : (
                <Flex justify="between">
                  <Heading as="h6" heading="sectionSubTitle" color="primary">
                    {translate('detailpage.snbnDialog.serialNumberList', 'Daftar Serial Number', { ns: 'Penjualan/Inventori/purchaseInvoice' })}
                  </Heading>
                  <FormLabel>
                    {translate('detailpage.snbnDialog.stockQuantity', 'Jumlah Stok:', { ns: 'Penjualan/Inventori/purchaseInvoice' })}
                    &nbsp;
                    <span style={errors && errors.serialNumbers && errors.serialNumbers.type === 'discrepancySerialNumberLength' ? { color: 'red', fontWeight: 'bold' } : null}>{serialNumbers.length}</span>
                    /
                    {formatDecimal(stock)}
                  </FormLabel>
                </Flex>
              )
            }
            <form
              id="sn-dialog-form"
              onSubmit={handleSubmit(onSubmit)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                }
              }}
            >
              {
                isNegativeCase && negativeCaseType === 'minus' ? (
                  <Flex justify="center">
                    <InputResourceList
                      counterFooterText={translate('label.itemSelected', 'item dipilih', { ns: 'translation' })}
                      isLoading={isLoading}
                      data={filterSerialList}
                      enabledItems={enableSerialList}
                      value={serialNumberItems.items}
                      onItemCheck={(selectedItem) => {
                        handleSerialNumberItems([...serialNumberItems.itemSelected, selectedItem], 'itemSelected');
                      }}
                      onItemUncheck={(unselectedItem) => {
                        handleSerialNumberItems(
                          [...serialNumberItems.itemNotSelected, unselectedItem.value],
                          'itemNotSelected',
                        );
                        setFilterSerialList(current => [unselectedItem, ...current]);
                      }}
                      fetchData={fetchSerial}
                      onQueryChange={query => fetchSerial({ query, index: 0 })}
                      css={{
                        width: '100%',
                        'div:nth-child(2)': {
                          position: 'relative',
                          zIndex: 9999,
                        },
                      }}
                    />
                  </Flex>
                ) : (
                  <Flex
                    direction="column"
                    css={{
                      gap: 12,
                    }}
                  >
                    {/* <Flex align="center">
                      <Text
                        color="secondary"
                        css={{
                          fontSize: 12,
                          fontWeight: 400,
                          lineHeight: '16px',
                        }}
                      >
                        {translate('detailpage.snbnDialog.barcodeNote', 'Ingin scan barcode Serial Number?', { ns: 'Penjualan/Inventori/purchaseInvoice' })}
                      </Text>
                      <Button
                        buttonType="ghost"
                        size="sm"
                        css={{
                          mx: 4,
                          '& div': {
                            display: 'flex',
                          },
                        }}
                        leftIcon={<BarcodeFilled color="#04C99E" />}
                      >
                      {translate('detailpage.snbnDialog.scanBarcodeButton', 'Scan Sekarang', { ns: 'Penjualan/Inventori/purchaseInvoice' })}
                      </Button>
                    </Flex> */}

                    <Flex direction="column" gap={4}>
                      <Flex
                        direction="column"
                        gap={4}
                      >
                        {
                          fields.map((item, index) => (
                            <Flex gap={3}>
                              <InputText
                                {...register(`serialNumbers.${index}.value`)}
                                placeholder={`${translate('placeholder.example', 'Contoh: ', { ns: 'translation' })}ABC-1011E`}
                                isInvalid={!!errors && !!errors.serialNumbers && !!errors.serialNumbers[index]}
                                autoComplete="off"
                                disabled={isLoading || item.isDisabled}
                              />
                              <Box
                                onClick={() => {
                                  if (!hasPurchaseShipmentsRef) {
                                    remove(index);
                                  }
                                }}
                                css={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  cursor: 'pointer',
                                }}
                                disabled={isLoading}
                              >
                                <TrashOutline color={iconSecondary} />
                              </Box>
                            </Flex>
                          ))
                        }
                      </Flex>
                      {
                        !isNegativeCase && fields.length < stock ? (
                          <Button
                            buttonType="secondary"
                            leftIcon={(
                              <PlusOutline
                                size={24}
                                color={
                                  serialNumbers
                                    && serialNumbers.some(serialNumberItem => serialNumberItem.value === '')
                                    ? '#A5ABAB' : '#04C99E'}
                              />
                            )}
                            onClick={handleAddSerialNumber}
                            disabled={(serialNumbers && serialNumbers.some(item => item.value === '')) || isLoading}
                          >
                            {translate('detailpage.snbnDialog.addSerialNumber', 'Tambah Serial Number', { ns: 'Penjualan/Inventori/purchaseInvoice' })}
                          </Button>
                        ) : null
                      }
                      {
                        !isEmpty(errors) && errors.serialNumbers ? (
                          <FormHelper
                            error
                            css={{ marginTop: '$spacing-03 !important' }}
                          >
                            {Array.isArray(errors.serialNumbers) && fields.length > 0 && fields.some(item => item.value === '') ? translate('detailpage.snbnDialog.serialNumberEmpty', 'Mohon Lengkapi Serial Number', { ns: 'Penjualan/Inventori/purchaseInvoice' }) : errors.serialNumbers.message}
                          </FormHelper>
                        ) : null
                      }
                    </Flex>
                  </Flex>
                )
              }
            </form>
          </Flex>
        </Flex>
      </ModalDialogContent>
      <ModalDialogFooter
        css={{
          display: 'flex',
          gap: '$compact',
          justifyContent: paymentStatus !== 1 ? 'space-between' : 'flex-end',
        }}
      >
        {(!isMobile && paymentStatus !== 1) && (
          <Button
            css={{ '@md': { flex: 'initial' } }}
            buttonType="secondary"
            size="sm"
            onClick={() => handleSubmit(data => onSubmit(data, false))()}
            disabled={isLoading || fields.length === 0}
          >
            {translate('label.saveAsDraft', 'Simpan Draf', { ns: 'translation' })}
          </Button>
        )}
        <Flex gap={4} css={isMobile ? { width: '100%' } : {}}>
          {
            isMobile ? (
              <Button
                css={{ flex: 1 }}
                buttonType="secondary"
                size="md"
                onClick={() => handleSubmit(data => onSubmit(data, false))()}
                disabled={isLoading || fields.length === 0}
              >
                {translate('label.saveAsDraft', 'Simpan Draf', { ns: 'translation' })}
              </Button>
            ) : (
              <Button
                css={{ '@md': { flex: 'initial' } }}
                buttonType="ghost"
                size="sm"
                onClick={onClose}
                disabled={isLoading}
              >
                {translate('label.cancel', 'Batal', { ns: 'translation' })}
              </Button>
            )
          }
          <Button
            css={{ flex: 1, '@md': { flex: 'initial' } }}
            aria-label="Close"
            size={isMobile ? 'md' : 'sm'}
            disabled={
              isNegativeCase && negativeCaseType === 'minus'
                ? stockPage === 'wasted'
                  ? serialNumberItems.itemSelected.length !== stockActual
                  : (stockSystem - serialNumberItems.itemSelected.length !== stockActual || isLoading)
                : (fields.length === 0 || isLoading)
            }
            onClick={() => handleSubmit(data => onSubmit(data, true))()}
          >
            {translate('label.save', 'Simpan', { ns: 'translation' })}
          </Button>
        </Flex>
      </ModalDialogFooter>
    </ModalDialog>
  );
};

export default SerialNumberDialog;
