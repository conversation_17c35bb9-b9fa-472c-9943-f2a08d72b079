import React, { useState } from 'react';
import { uniqBy } from 'lodash';
import {
  Text,
  Box,
  Flex,
  Badge,
  InputNumber,
  InputTextArea,
  InputText,
  InputGroup,
  InputSuffix,
  InputSelect,
  EditableTable,
} from '@majoo-ui/react';
import { Controller } from 'react-hook-form';
import { currency } from '../../../../utils/helper';
import BigProductName from '../../../../components/retina/BigProductName';
import { PO_STATUS } from '../utils/enum';

const StyledSummaryText = ({ children }) => (
  <Text color="primary" css={{ fontSize: 14, fontWeight: 400 }}>{children}</Text>
);

const reformattedCurrency = ({ value, decimal = false }) => (currency({ value, decimal }).includes('-') ? `-${currency({ value, decimal }).replace('-', '')}` : currency({ value, decimal }));

const tableColumns = ({
  translate, control, setValue, isCanEdit, getValues, mainFormData, isMobile,
  removePODetail,
}) => [
    {
      Header: translate('editableTableSection.item'),
      accessor: 'itemName',
      isMobileHeader: true,
      Cell: ({ value, row: { original } }) => (original.itemVariants ? (
        <Box>
          <BigProductName
            name={value}
            css={{ fontWeight: 400, wordBreak: 'break-word' }}
            color="primary"
            type="detail"
            maxLength={80}
          />
          <Flex gap={2} css={{ marginTop: 4 }}>
            {original.itemVariants.map(variant => (
              <Badge
                badgeVariant="outline"
                colorScheme="primary"
                css={{ padding: '4px 8px', borderColor: '$gray150' }}
              >
                {variant}
              </Badge>
            ))}
          </Flex>
        </Box>
      ) : (
        <BigProductName
          name={value}
          css={{ fontWeight: 400, wordBreak: 'break-word' }}
          color="primary"
          type="detail"
          maxLength={80}
        />
      )),
    },
    {
      Header: translate('editableTableSection.quantity'),
      accessor: 'itemQty',
      Cell: ({ value, row: { index, original } }) => {
        const [inputValue, setInputValue] = useState(value);

        return (
          <Controller
            control={control}
            name={`poDetails.${index}.itemQty`}
            render={({ field: { onChange }, fieldState: { error } }) => (
              <InputNumber
                size="sm"
                onValueChange={({ floatValue }) => {
                  const val = floatValue || 0;
                  setInputValue(val);
                  const itemDiscount = (getValues(`poDetails.${index}.itemDiscount`) || original.itemDiscount) / 100;
                  const totalPrice = val * (getValues(`poDetails.${index}.itemPrice`) || original.itemPrice);
                  onChange(val);
                  setValue(`poDetails.${index}.itemTotalPrice`, totalPrice - (totalPrice * itemDiscount));
                }}
                value={inputValue}
                allowNegative={false}
                decimalScale={2}
                disabled={!isCanEdit}
                isInvalid={!!error}
              />
            )}
          />
        );
      },
    },
    {
      Header: translate('editableTableSection.unit'),
      accessor: 'itemUnit',
      Cell: ({ value, row: { index, original } }) => ((original.itemUnitOpts.length && !mainFormData.productRequest.length) ? (
        <Controller
          control={control}
          name={`poDetails.${index}.itemUnit`}
          render={({ field: { onChange, value, ref } }) => {
            const unitOpt = uniqBy(original.itemUnitOpts, 'no');
            return (
              <InputSelect
                ref={ref}
                size="sm"
                valueSelector="no"
                option={unitOpt}
                value={unitOpt.find(item => String(item.name) === String(value))}
                onChange={(option) => {
                  const selectedItem = unitOpt.find(item => String(item.name) === String(option.name));
                  onChange(selectedItem.name);
                  setValue(`poDetails.${index}.itemUnitNo`, selectedItem.no);
                  setValue(`poDetails.${index}.itemPrice`, selectedItem.purchase_price);
                  setValue(`poDetails.${index}.itemTotalPrice`, selectedItem.purchase_price * (getValues(`poDetails.${index}.itemQty`) || original.itemQty));
                }}
                disabled={!isCanEdit}
              />
            );
          }}
        />
      ) : (
        <InputText
          size="sm"
          value={value}
          disabled
        />
      )),
    },
    {
      Header: translate('editableTableSection.unitPrice'),
      accessor: 'itemPrice',
      Cell: ({ row: { index, original } }) => (
        <Controller
          control={control}
          name={`poDetails.${index}.itemPrice`}
          render={({ field: { onChange, value, ref } }) => (
              <InputNumber
                ref={ref}
                size="sm"
                decimalScale={2}
                prefix="Rp "
                placeholder="Rp 0"
                allowNegative={false}
                onValueChange={({ floatValue }) => {
                  const val = floatValue || 0;
                  const itemDiscount = (getValues(`poDetails.${index}.itemDiscount`) || original.itemDiscount) / 100;
                  const totalPrice = val * (getValues(`poDetails.${index}.itemQty`) || original.itemQty);
                  onChange(val);
                  setValue(`poDetails.${index}.itemTotalPrice`, totalPrice - (totalPrice * itemDiscount));
                }}
                value={value}
                disabled={!isCanEdit}
                isAllowed={({ floatValue }) => floatValue === undefined || floatValue >= 0}
              />
            )}
        />
      ),
    },
    {
      Header: translate('editableTableSection.discount'),
      accessor: 'itemDiscount',
      Cell: ({ row: { index, original } }) => (
        <Controller
          control={control}
          name={`poDetails.${index}.itemDiscount`}
          render={({ field: { onChange, value, ref } }) => (
            <InputGroup size="sm" disabled={!isCanEdit}>
              <InputNumber
                ref={ref}
                size="sm"
                decimalScale={1}
                placeholder="0"
                allowNegative={false}
                onValueChange={({ floatValue }) => {
                  const val = floatValue || 0;
                  const totalPrice = (getValues(`poDetails.${index}.itemQty`) || original.itemQty) * (getValues(`poDetails.${index}.itemPrice`) || original.itemPrice);
                  onChange(val);
                  setValue(`poDetails.${index}.itemTotalPrice`, totalPrice - ((val * totalPrice) / 100));
                }}
                value={value}
                isAllowed={({ floatValue }) => floatValue === undefined || floatValue <= 100}
                disabled={!isCanEdit}
              />
              <InputSuffix>%</InputSuffix>
            </InputGroup>
          )}
        />
      ),
    },
    {
      Header: translate('editableTableSection.total'),
      accessor: 'itemTotal',
      Cell: ({ row: { index } }) => (
        <Controller
          control={control}
          name={`poDetails.${index}.itemTotalPrice`}
          render={({ field: { value } }) => (
              <InputNumber
                size="sm"
                prefix="Rp "
                placeholder="Rp 0"
                value={value}
                decimalScale={2}
                disabled
              />
            )}
        />
      ),
    },
    ...(mainFormData && (+mainFormData.poStatus === PO_STATUS.WAITING || +mainFormData.poStatus === PO_STATUS.DRAFT)) && !isMobile ? [{
      Header: '',
      accessor: 'remove',
      width: 34,
      Cell: ({ row: { index } }) => (
        <EditableTable.Remove
          css={{ marginLeft: 10, '@2xl': { marginLeft: 20 } }}
          onRemove={() => removePODetail(index)}
        />
      ),
    }] : [],
    {
      Header: translate('editableTableSection.note'),
      accessor: 'itemDescription',
      isSubRow: true,
      verticalAlign: 'top',
      Cell: ({ row: { index, original } }) => (
        <Controller
          control={control}
          name={`poDetails.${index}.itemDescription`}
          render={({ field: { onChange, value, ref } }) => (
            <InputTextArea
              ref={ref}
              value={value}
              css={{
                width: (mainFormData && (+mainFormData.poStatus === PO_STATUS.WAITING || +mainFormData.poStatus === PO_STATUS.DRAFT)) && !isMobile ? 'calc(100% - 44px)' : '100%',
              }}
              onChange={e => onChange(e.target.value)}
              disabled={!isCanEdit}
            />
          )}
        />
      ),
    },
    ...(mainFormData && (+mainFormData.poStatus === PO_STATUS.WAITING || +mainFormData.poStatus === PO_STATUS.DRAFT)) && isMobile ? [{
      Header: '',
      accessor: 'remove',
      width: 34,
      Cell: ({ row: { index } }) => (
        <EditableTable.Remove
          css={{ marginLeft: 10, '@2xl': { marginLeft: 20 } }}
          onRemove={() => removePODetail(index)}
        />
      ),
    }] : [],
  ];

export {
  StyledSummaryText,
  reformattedCurrency,
  tableColumns,
};
