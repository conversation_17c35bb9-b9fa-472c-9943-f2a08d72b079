import React, { Fragment, useEffect, useState } from 'react';
import {
  Paper,
  Heading,
  Box,
  Flex,
  InputSelect,
  InputText,
  InputDatePicker,
  InputSwitch,
  InputNumber,
  Separator,
  Button,
  FormHelper,
  MemoizedEditableTable,
  Paragraph,
} from '@majoo-ui/react';
import { useForm, Controller, useFieldArray } from 'react-hook-form';
import moment from 'moment';

import { resolver } from './resolver';
import { PO_STATUS } from '../utils/enum';
import { useTranslationHook } from '../lang/lang.utils';
import {
  StyledSummaryText, reformattedCurrency, tableColumns,
} from './detailPurchaseOrderPageUtils';
import {
  StyledFormLabelBox, StyledFieldBox, StyledFormLabel, GeneratedTagStatus,
} from '../utils';
import {
  discountOptions, generateDiscountValue, generateTaxValue, taxOptions,
  generateSubtotalPoDetails,
  generateTotal,
} from '../Step/PurchaseDetail/purchaseDetailUtils';
import { currency } from '../../../../utils/helper';
import { accountType } from '../../../../config/enum';
import { getAccountType } from '../../../../utils/savedLocalStorage';
import SelectProductModalDialog from '../Step/PurchaseDetail/SelectProductModalDialog';

const DetailPurchaseOrderPage = ({
  t,
  currentLang,
  accountOptions,
  supplierOptions,
  outletOptions,
  mainFormData,
  setMainFormData,
  setShowSavePurchaseOrderDialog,
  setShowPrintDetailPurchaseOrderDialog,
  setShowSendEmailDialog,
  isMobile,
  isCanEdit,
  openRestrictionModal,
  categoryOptions,
  priceSetting,
}) => {
  const translateDetail = value => t(`pageDialog.purchaseOrderDetailsSection.${value}`);
  const translate = value => t(`pageDialog.detailPurchaseOrderPageSection.${value}`);
  const { LANG_DATA, TransComponent, LANG_KEY } = useTranslationHook();
  const [subtotal, setSubtotal] = useState(0);
  const [discount, setDiscount] = useState(0);
  const [tax, setTax] = useState(0);
  const [remaining, setRemaining] = useState(0);
  const [paymentAccount, setPaymentAccount] = useState('-');
  const isDraft = mainFormData && Number(mainFormData.poStatus) === PO_STATUS.DRAFT;
  const isValidUser = [accountType.ADVANCE, accountType.ENTERPRISE, accountType.PRIME, accountType.PRIMEPLUS].includes(getAccountType());
  const [showSelectProductModalDialog, setShowSelectProductModalDialog] = useState(false);

  const {
    handleSubmit,
    control,
    setValue,
    getValues,
    watch,
    formState: { errors },
    clearErrors,
    setError,
  } = useForm(resolver(LANG_DATA));

  const { remove: removePODetail } = useFieldArray({ control, name: 'poDetails' });

  const formData = Object.keys(getValues()).length > 0 ? getValues() : { poDetails: [] };

  const onSubmit = (data) => {
    if (data.poDownPayment && data.poDownPaymentNominal <= 0) {
      setError('poDownPaymentNominal', {
        type: 'custom',
        message: LANG_DATA.PAGE_DIALOG.PURCHASE_ORDER_DETAILS_SECTION.DOWN_PAYMENT_SECTION.AMOUNT_MIN,
      });
    } else if (remaining >= 0) {
      setMainFormData(prevState => ({ ...prevState, ...data }));
      setShowSavePurchaseOrderDialog(true);
    }
  };

  useEffect(() => {
    if (mainFormData) {
      Object.entries(mainFormData).forEach(([key, value]) => {
        setValue(key, value);
      });
    }
  }, [mainFormData]);

  useEffect(() => {
    if (formData && Array.isArray(formData.poDetails) && formData.poDetails.length > 0) {
      const currSubtotal = generateSubtotalPoDetails(formData.poDetails, formData.poTax);
      const currDiscount = generateDiscountValue({
        poDiscount: formData.poDiscount,
        poDiscountValue: formData.poDiscountValue,
        subtotal: currSubtotal,
        poTax: formData.poTax,
      });
      const currTax = generateTaxValue({
        subtotal: currSubtotal,
        discount: currDiscount,
        poTax: formData.poTax,
      });
      const total = generateTotal({
        subtotal: currSubtotal,
        discount: currDiscount,
        tax: currTax,
        additionalCosts: formData.poAdditionalCosts,
      });

      setSubtotal(currSubtotal);
      setDiscount(currDiscount);
      setTax(currTax);
      setValue('poTotal', total);
      setValue('poGrandTotal', total);
      setRemaining(total - watch('poDownPaymentNominal'));
      setPaymentAccount(formData.poDownPaymentAccount);
    }
  }, [JSON.stringify(watch('poDetails', formData.poDetails))]);

  const handleSelectProducts = (products) => {
    const poDetails = getValues('poDetails');
    setValue('poDetails', [...poDetails, ...products]);
    clearErrors('poDetails');
  };

  return (
    <Flex direction="column" gap={5}>
      <Paper>
        <Flex justify="between" gap={3} css={{ flexDirection: 'column', '@lg': { flexDirection: 'row' }}}>
          <Flex align="center" css={{ gap: '$cozy' }}>
            <Heading as="h6" heading="sectionSubTitle" color="primary">
              Status
            </Heading>
            {mainFormData ? (
              <GeneratedTagStatus value={mainFormData.poStatus} lang={currentLang} />
            ) : null}
          </Flex>
          <Paragraph css={{ color: '$textSecondary' }}>
            <TransComponent i18nKey={LANG_KEY.LAST_UPDATED_BY} context={!(getValues('updatedDate') && getValues('updatedBy')) ? 'empty' : null}>
              {{ date: getValues('updatedDate') || '' }}
              {{ by: getValues('updatedBy') || '' }}
            </TransComponent> 
          </Paragraph>
        </Flex>
      </Paper>
      <Paper padding="26px 24px">
        <form id="edit-detail-po" onSubmit={handleSubmit(onSubmit)}>
          {!isCanEdit ? (
            <Heading as="h4" heading="pageTitle" color="primary">
              {translate('title')}
            </Heading>
          ) : null}
          <Flex
            css={{
              marginTop: 16,
              '@md': { marginTop: 34 },
            }}
            gap={6}
            direction="column"
          >
            <Flex
              css={{
                flexDirection: 'column',
                gap: 8,
                '@md': {
                  flexDirection: 'row',
                  gap: 0,
                },
              }}
            >
              <StyledFormLabelBox>
                <StyledFormLabel variant="required">{translate('outletList')}</StyledFormLabel>
              </StyledFormLabelBox>
              <StyledFieldBox>
                <InputSelect
                  option={outletOptions}
                  placeholder="Pilih Outlet"
                  readOnly
                  value={outletOptions.find(item => mainFormData && item.value === mainFormData.outletId)}
                />
              </StyledFieldBox>
            </Flex>

            <Flex
              css={{
                flexDirection: 'column',
                gap: 8,
                '@md': {
                  flexDirection: 'row',
                  gap: 0,
                },
              }}
            >
              <StyledFormLabelBox>
                <StyledFormLabel>{translate('purchaseOrderNumber')}</StyledFormLabel>
              </StyledFormLabelBox>
              <StyledFieldBox>
                <InputText
                  id="poNumber"
                  placeholder={LANG_DATA.PAGE_DIALOG.PURCHASE_ORDER_INFORMATION_SECTION.PURCHASE_ORDER_NUMBER}
                  value={watch('poNumber')}
                  onChange={e => setValue('poNumber', e.target.value)}
                  disabled={!isCanEdit}
                />
              </StyledFieldBox>
            </Flex>

            <Flex
              css={{
                flexDirection: 'column',
                gap: 8,
                '@md': {
                  flexDirection: 'row',
                  gap: 0,
                },
              }}
            >
              <StyledFormLabelBox>
                <StyledFormLabel variant="required">{translate('supplier')}</StyledFormLabel>
              </StyledFormLabelBox>
              <StyledFieldBox>
                <InputSelect
                  id="poSupplierId"
                  option={supplierOptions}
                  value={supplierOptions.find(item => item.value === watch('poSupplierId'))}
                  onChange={({ value }) => setValue('poSupplierId', value)}
                  disabled={!isCanEdit}
                  search
                />
              </StyledFieldBox>
            </Flex>
            <Flex
              css={{
                flexDirection: 'column',
                gap: 8,
                '@md': {
                  flexDirection: 'row',
                  gap: 0,
                },
              }}
            >
              <StyledFormLabelBox>
                <StyledFormLabel variant="required">{t('translation:label.createdBy')}</StyledFormLabel>
              </StyledFormLabelBox>
              <StyledFieldBox>
                <InputText value={getValues('createdBy')} disabled />
              </StyledFieldBox>
            </Flex>
            <Flex
              css={{
                flexDirection: 'column',
                gap: 8,
                '@md': {
                  flexDirection: 'row',
                  gap: 0,
                },
              }}
            >
              <StyledFormLabelBox>
                <StyledFormLabel variant="required">{t('translation:label.createdDate')}</StyledFormLabel>
              </StyledFormLabelBox>
              <StyledFieldBox>
                <InputDatePicker
                  id="createdDate"
                  type="single"
                  size="lg"
                  value={getValues('createdDate')}
                  disabled
                  showTimePicker
                />
              </StyledFieldBox>
            </Flex>
            <Flex
              css={{
                flexDirection: 'column',
                gap: 8,
                '@md': {
                  flexDirection: 'row',
                  gap: 0,
                },
              }}
            >
              <StyledFormLabelBox>
                <StyledFormLabel variant="required">{translate('purchaseOrderDate')}</StyledFormLabel>
              </StyledFormLabelBox>
              <StyledFieldBox>
                <InputDatePicker
                  key={watch('poDate')}
                  id="poDate"
                  type="single"
                  size="lg"
                  defaultValue={new Date(watch('poDate'))}
                  onChange={(value) => {
                    setValue('poDate', moment(value).format('YYYY-MM-DD HH:mm:ss'));
                  }}
                  maxDate={new Date()}
                  disabled={!isCanEdit}
                  isInvalid={!!errors.poDate}
                  showTimePicker
                />
              </StyledFieldBox>
            </Flex>
          </Flex>

          <Separator css={{ marginTop: 16, marginBottom: 24 }} />

          {(mainFormData && (+mainFormData.poStatus === PO_STATUS.WAITING || +mainFormData.poStatus === PO_STATUS.DRAFT)) && (
            <Flex
              justify="between"
              css={{
                marginBottom: 16,
                '@md': { marginBottom: 32 },
              }}
            >
              <Heading as="h6" heading="sectionSubTitle" color="primary">
                {translateDetail('itemDetails')}
              </Heading>
              <Button
                type="button"
                css={{
                  '@md': {
                    width: 150,
                    fontWeight: 600,
                  },
                }}
                buttonType="secondary"
                size="sm"
                onClick={() => setShowSelectProductModalDialog(true)}
              >
                {translateDetail('setItemButton')}
              </Button>
            </Flex>
          )}

          <MemoizedEditableTable
            columns={tableColumns({
              translate, control, setValue, isCanEdit, getValues, mainFormData, isMobile,
              removePODetail,
            })}
            data={formData.poDetails || []}
            css={{ marginBottom: 24 }}
            isMobileLayout={isMobile}
          />
          {errors.poDetails && typeof errors.poDetails === 'object' ? (
            <FormHelper css={{ marginBottom: 24 }} error>{errors.poDetails.message}</FormHelper>
          ) : null}
          {errors.poDetails && Array.isArray(errors.poDetails) ? (
            <FormHelper css={{ marginBottom: 24 }} error>{translateDetail('editableTableSection.quantityValidation')}</FormHelper>
          ) : null}

          <Flex>
            <StyledFormLabelBox>
              <StyledFormLabel>
                {LANG_DATA.PAGE_DIALOG.PURCHASE_ORDER_DETAILS_SECTION.DOWN_PAYMENT_SECTION.TITLE}
              </StyledFormLabel>
            </StyledFormLabelBox>
            <StyledFieldBox>
              <Flex direction="column" gap={5} css={{ alignItems: 'end', '@md': { alignItems: 'start' } }}>
                <Controller
                  name="poDownPayment"
                  control={control}
                  render={({ field: { value, onChange } }) => (
                    <InputSwitch
                      dataOnLabel="ON"
                      dataOffLabel="OFF"
                      checked={value}
                      onCheckedChange={(val) => {
                        if (isValidUser) {
                          onChange(val);
                          if (val) {
                            setValue('poDownPaymentAccount', paymentAccount !== '-' ? paymentAccount : '');
                          } else {
                            clearErrors('poDownPaymentAccount');
                            setValue('poDownPaymentAccount', '-');
                          }
                        } else {
                          openRestrictionModal();
                        }
                      }}
                      disabled={!isCanEdit}
                    />
                  )}
                />
              </Flex>
            </StyledFieldBox>
          </Flex>
          {isValidUser && watch('poDownPayment') ? (
            <Flex css={{ marginTop: 16 }}>
              <Box css={{ display: 'none', '@md': { width: '25%', display: 'flex' } }} />
              <Flex
                align="start"
                css={{
                  flexDirection: 'column',
                  gap: 16,
                  width: '100%',
                  '@md': {
                    flexDirection: 'row',
                    gap: 8,
                    width: '75%',
                  },
                }}
              >
                <Flex direction="column" gap={4} css={{ width: '100%', '@md': { width: '50%' } }}>
                  <StyledFormLabel variant="required">
                    {LANG_DATA.PAGE_DIALOG.PURCHASE_ORDER_DETAILS_SECTION.DOWN_PAYMENT_SECTION.PAYMENT_ACCOUNT}
                  </StyledFormLabel>
                  <InputSelect
                    value={accountOptions.find(val => String(val.value) === String(watch('poDownPaymentAccount')))}
                    onChange={({ value }) => {
                      clearErrors('poDownPaymentAccount');
                      setValue('poDownPaymentAccount', value);
                      setPaymentAccount(value);
                    }}
                    option={accountOptions}
                    placeholder={LANG_DATA.PLACEHOLDER_SELECT}
                    isInvalid={!!errors.poDownPaymentAccount}
                    disabled={!isCanEdit}
                  />
                  {errors.poDownPaymentAccount ? (
                    <FormHelper error>{errors.poDownPaymentAccount.message}</FormHelper>
                  ) : null}
                </Flex>
                <Flex direction="column" gap={4} css={{ width: '100%', '@md': { width: '50%' } }}>
                  <StyledFormLabel variant="required">
                    {LANG_DATA.PAGE_DIALOG.PURCHASE_ORDER_DETAILS_SECTION.DOWN_PAYMENT_SECTION.AMOUNT}
                  </StyledFormLabel>
                  <InputNumber
                    prefix="Rp "
                    placeholder="Rp 0"
                    onValueChange={({ floatValue }) => {
                      if (floatValue) clearErrors('poDownPaymentNominal');
                      setValue('poDownPaymentNominal', floatValue);
                      setRemaining(formData.poGrandTotal - (floatValue || 0));
                    }}
                    allowNegative={false}
                    decimalScale={2}
                    defaultValue={watch('poDownPaymentNominal')}
                    isAllowed={({ floatValue }) => floatValue === undefined || floatValue >= 0}
                    isInvalid={!!errors.poDownPaymentNominal || remaining < 0}
                    disabled={!isCanEdit}
                  />
                  {errors.poDownPaymentNominal ? (
                    <FormHelper error>{errors.poDownPaymentNominal.message}</FormHelper>
                  ) : null}
                  {remaining < 0 ? (
                    <FormHelper error>{LANG_DATA.PAGE_DIALOG.PURCHASE_ORDER_DETAILS_SECTION.DOWN_PAYMENT_SECTION.AMOUNT_EXCEED}</FormHelper>
                  ) : null}
                </Flex>
              </Flex>
            </Flex>
          ) : null}

          {isValidUser ? <Separator css={{ margin: '20px 0px' }} /> : null}

          <Flex
            css={{
              flexDirection: 'column',
              gap: 16,
              '@md': {
                flexDirection: 'row',
                gap: 0,
              },
            }}
          >
            <Box
              css={{
                width: '100%',
                '@md': { width: '60%' },
              }}
            >
              <Heading as="h6" heading="sectionSubTitle" color="primary">
                {translate('detailSection.totalPrice')}
              </Heading>
            </Box>
            <Box
              css={{
                width: '100%',
                '@md': { width: '40%' },
              }}
            >
              <Flex justify="between" css={{ marginBottom: 16 }}>
                <StyledSummaryText>{translate('detailSection.itemSubtotal')}</StyledSummaryText>
                <StyledSummaryText>
                  {currency({ value: subtotal, decimal: true })}
                </StyledSummaryText>
              </Flex>

              {formData.poDiscountValue !== undefined ? (
                <Flex justify="between" css={{ marginBottom: 16 }}>
                  <StyledSummaryText>
                    {discountOptions(currentLang, formData.poDiscountValue).find(item => item.value === formData.poDiscount).name}
                  </StyledSummaryText>
                  <StyledSummaryText>
                    {currency({ value: -1 * discount, decimal: discount % 1 !== 0 })}
                  </StyledSummaryText>
                </Flex>
              ) : null}

              {formData.poTax ? (
                <Flex justify="between" css={{ marginBottom: 16 }}>
                  <StyledSummaryText>
                    {taxOptions(currentLang).find(item => item.value === formData.poTax).name}
                  </StyledSummaryText>
                  <StyledSummaryText>
                    {reformattedCurrency({ value: tax, decimal: tax % 1 !== 0 })}
                  </StyledSummaryText>
                </Flex>
              ) : null}

              {Array.isArray(formData.poAdditionalCosts) ? formData.poAdditionalCosts.map(({ name, value }) => (
                <Flex justify="between" css={{ marginBottom: 16 }}>
                  <StyledSummaryText>{name}</StyledSummaryText>
                  <StyledSummaryText>{currency({ value, decimal: value % 1 !== 0 })}</StyledSummaryText>
                </Flex>
              )) : null}

              <Separator css={{ marginBottom: 16 }} />

              <Flex justify="between" css={{ marginBottom: 16 }}>
                <Heading as="h6" heading="sectionSubTitle" color="primary">
                  {translate('detailSection.totalPrice')}
                </Heading>
                <Heading as="h6" heading="sectionSubTitle" color="primary">
                  {reformattedCurrency({
                    value: formData.poGrandTotal || 0,
                    decimal: formData.poGrandTotal % 1 !== 0 || false,
                  })}
                </Heading>
              </Flex>

              {isValidUser && watch('poDownPayment') ? (
                <Fragment>
                  <Flex justify="between" css={{ marginBottom: 16 }}>
                    <Heading as="h6" heading="sectionSubTitle" color="primary">
                      {LANG_DATA.PAGE_DIALOG.DETAIL_PURCHASE_ORDER_PAGE_SECTION.DETAIL_SECTION.DOWN_PAYMENT}
                    </Heading>
                    <Heading as="h6" heading="sectionSubTitle" color={remaining < 0 ? 'red' : 'primary'}>
                      {reformattedCurrency({
                        value: (-1 * watch('poDownPaymentNominal')),
                        decimal: watch('poDownPaymentNominal') % 1 !== 0,
                      })}
                    </Heading>
                  </Flex>
                  <Separator css={{ marginBottom: 16 }} />
                  <Flex justify="between" css={{ marginBottom: 16 }}>
                    <Heading as="h6" heading="sectionSubTitle" color="primary">
                      {LANG_DATA.PAGE_DIALOG.DETAIL_PURCHASE_ORDER_PAGE_SECTION.DETAIL_SECTION.REMAINING_BILLS}
                    </Heading>
                    <Heading as="h6" heading="sectionSubTitle" color={remaining < 0 ? 'red' : 'primary'}>
                      {reformattedCurrency({
                        value: remaining,
                        decimal: remaining % 1 !== 0,
                      })}
                    </Heading>
                  </Flex>
                </Fragment>
              ) : null}
            </Box>
          </Flex>

          <Box>
            <Separator css={{ margin: '20px 0px' }} />
            <Flex justify="end">
              <Box css={{ width: '100%', '@md': { width: '75%' } }}>
                <Flex gap={3} direction={isMobile ? 'column' : 'row'}>
                  {!isDraft ? (
                    <Button
                      css={{ width: isMobile ? '100%' : '50%' }}
                      onClick={(e) => {
                        e.preventDefault();
                        setShowSendEmailDialog(true);
                      }}
                    >
                      {translate('sendButton')}
                    </Button>
                  ) : null}
                  <Button
                    css={{ width: isDraft || isMobile ? '100%' : '50%' }}
                    buttonType="secondary"
                    onClick={(e) => {
                      e.preventDefault();
                      setShowPrintDetailPurchaseOrderDialog(true);
                    }}
                  >
                    {translate('printButton')}
                  </Button>
                </Flex>
              </Box>
            </Flex>
          </Box>
        </form>

        {showSelectProductModalDialog ? (
          <SelectProductModalDialog
            t={t}
            currentLang={currentLang}
            open={showSelectProductModalDialog}
            setOpen={setShowSelectProductModalDialog}
            categoryOptions={categoryOptions}
            productIds={Object.fromEntries(watch('poDetails').map(poDetail => [String(poDetail.itemId), true]))}
            handleSelectProducts={handleSelectProducts}
            outletId={mainFormData ? mainFormData.outletId : ''}
            isMobile={isMobile}
            priceSetting={priceSetting}
          />
        ) : null}
      </Paper>
    </Flex>
  );
};

export default DetailPurchaseOrderPage;
