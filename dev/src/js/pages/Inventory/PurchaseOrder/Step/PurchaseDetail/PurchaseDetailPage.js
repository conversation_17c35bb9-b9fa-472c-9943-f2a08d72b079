import React, {
  Fragment, useState, useEffect, useRef,
} from 'react';
import { useForm, useField<PERSON><PERSON>y, Controller } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  Box,
  Flex,
  Heading,
  Separator,
  InputSelect,
  InputNumber,
  FormGroup,
  FormLabel,
  Button,
  InputText,
  FormHelper,
  Accordion,
  AccordionItem,
  AccordionContent,
  AccordionTrigger,
  Text,
  InputSwitch,
} from '@majoo-ui/react';
import { RemoveOutline } from '@majoo-ui/icons';
import { foundations } from '@majoo-ui/core';

import PageWrapperPaper from '../PageWrapperPaper';
import { GeneratedTagStatus, reformattedCurrency } from '../../utils';
import { useTranslationHook } from '../../lang/lang.utils';

import {
  StyledFormLabel,
  StyledSummaryText,
  StyledSummaryBoldText,
  purchaseDetailTableColumns,
  discountOptions,
  taxOptions,
  generateSubtotalPoDetails,
  generateTaxValue,
  generateTotal,
  generateDiscountValue,
  generateDefaultValues,
  MemoizedEditableTable,
  roundTotalValue,
} from './purchaseDetailUtils';

import SelectProductModalDialog from './SelectProductModalDialog';

import { currency } from '../../../../../utils/helper';
import { FORM_TYPES } from '../../../../../enum/form';
import { getAccountType } from '../../../../../utils/savedLocalStorage';
import { accountType } from '../../../../../config/enum';

const FORM_ID = 'purchase-detail-page';

const { iconSecondary, textPrimary } = foundations.colors;

const schema = ({
  itemsValidation, quantityValidation, totalPriceValidation, LANG_DATA,
}) => yup.object().shape({
  poDetails: yup.array().min(1, itemsValidation).of(
    yup.object().shape({
      itemId: yup.number(),
      itemQty: yup.number().min(0.01, quantityValidation).required(quantityValidation),
      itemPrice: yup.number(),
      itemDescription: yup.string(),
      itemUnit: yup.string(),
      itemName: yup.string(),
      itemVariants: yup.array().nullable().optional(),
      itemTotalPrice: yup.number(),
      itemDiscount: yup.number().optional(),
    }),
  ),
  poDiscount: yup.string(),
  poDiscountValue: yup.number(),
  poTax: yup.string(),
  poTaxValue: yup.number(),
  poAdditionalCosts: yup.array().of(
    yup.object().shape({
      name: yup.string(),
      value: yup.number(),
    }),
  ),
  poTotal: yup.number(),
  poGrandTotal: yup.number().min(0, totalPriceValidation),
  poStatus: yup.number(),
  poDownPayment: yup.boolean(),
  poDownPaymentNominal: yup.number().required(LANG_DATA.PAGE_DIALOG.PURCHASE_ORDER_DETAILS_SECTION.DOWN_PAYMENT_SECTION.AMOUNT_NULL),
  poDownPaymentAccount: yup.string().required(LANG_DATA.PAGE_DIALOG.PURCHASE_ORDER_DETAILS_SECTION.DOWN_PAYMENT_SECTION.PAYMENT_ACCOUNT_NULL),
});

const PurchaseDetail = ({
  t,
  currentLang,
  categoryOptions,
  accountOptions,
  mainFormData,
  setMainFormData,
  setShowSavePurchaseOrderDialog,
  outletId,
  formType,
  isMobile,
  openRestrictionModal,
  priceSetting,
}) => {
  const translate = value => t(`pageDialog.purchaseOrderDetailsSection.${value}`);
  const { LANG_DATA } = useTranslationHook();

  const {
    handleSubmit, setValue, watch, formState: { errors }, clearErrors, control, setError,
  } = useForm({
    resolver: yupResolver(schema({
      itemsValidation: translate('itemsValidation'),
      quantityValidation: translate('editableTableSection.quantityValidation'),
      totalPriceValidation: translate('totalPriceValidation'),
      LANG_DATA,
    })),
    defaultValues: generateDefaultValues(mainFormData),
  });

  const {
    poDiscount, poDiscountValue, poTax, poStatus, poDetails,
    poDownPayment, poDownPaymentNominal, poDownPaymentAccount,
  } = watch();

  const { remove: removePODetail } = useFieldArray({ control, name: 'poDetails' });
  const {
    fields: poAdditionalCosts,
    append: appendPOAdditionalCost,
    update: updatePOAdditionalCost,
    remove: removePOAdditionalCost,
  } = useFieldArray({ control, name: 'poAdditionalCosts' });

  const [showSelectProductModalDialog, setShowSelectProductModalDialog] = useState(false);
  const [subtotal, setSubtotal] = useState(generateSubtotalPoDetails(poDetails));
  const [discount, setDiscount] = useState(generateDiscountValue({
    poDiscount, poDiscountValue, subtotal, poTax,
  }));
  const [tax, setTax] = useState(generateTaxValue({ subtotal, discount, poTax }));
  const [total, setTotal] = useState(generateTotal({
    subtotal, discount, tax, additionalCosts: poAdditionalCosts,
  }));
  const [remaining, setRemaining] = useState(0);
  const [paymentAccount, setPaymentAccount] = useState('-');
  const [accordionValue, setAccordionValue] = useState('');

  const formGroupRef = useRef(null);
  const purchaseDetailTableColumnsRef = useRef(purchaseDetailTableColumns({
    control,
    setValue,
    translate,
    removePODetail,
    isMobile,
    mainFormData,
  }));

  const onSubmit = (data) => {
    if (poDownPayment && poDownPaymentNominal <= 0) {
      setError('poDownPaymentNominal', {
        type: 'custom',
        message: LANG_DATA.PAGE_DIALOG.PURCHASE_ORDER_DETAILS_SECTION.DOWN_PAYMENT_SECTION.AMOUNT_MIN,
      });
    } else if (remaining >= 0) {
      setMainFormData(prevState => ({ ...prevState, ...data }));
      setShowSavePurchaseOrderDialog(true);
    }
  };

  const handleSelectProducts = (products) => {
    setValue('poDetails', [...poDetails, ...products]);
    clearErrors('poDetails');
  };

  const handleAddPOAdditionalCost = () => {
    const newAdditionalCost = {
      name: '',
      value: 0,
    };

    appendPOAdditionalCost(newAdditionalCost);
  };

  const handleUpdatePOAdditionalCost = (index, key, value) => {
    const poAdditionalCost = poAdditionalCosts[index];
    poAdditionalCost[key] = value;

    updatePOAdditionalCost(index, poAdditionalCost);
  };

  useEffect(() => {
    const subTot = generateSubtotalPoDetails(poDetails, poTax);
    setSubtotal(subTot);
    setValue('poTotal', total);
  }, [JSON.stringify(poDetails), poTax]);

  useEffect(() => {
    setDiscount(generateDiscountValue({
      poDiscount, poDiscountValue, subtotal, poTax,
    }));
  }, [poDiscount, poDiscountValue, subtotal, poTax]);

  useEffect(() => {
    setTax(generateTaxValue({ subtotal, discount, poTax }));
  }, [subtotal, discount, poTax]);

  useEffect(() => {
    const total = generateTotal({
      subtotal, discount, tax, additionalCosts: poAdditionalCosts,
    });
    setTotal(total);
    setValue('poGrandTotal', total);
    setRemaining(total - (poDownPayment ? Math.abs(poDownPaymentNominal) : 0));

    if (total >= 0) {
      clearErrors('poGrandTotal');
    }
  }, [subtotal, discount, tax, poAdditionalCosts, poDownPayment, poDownPaymentNominal]);

  useEffect(() => {
    purchaseDetailTableColumnsRef.current = purchaseDetailTableColumns({
      control,
      setValue,
      translate,
      removePODetail,
      isMobile,
      mainFormData,
    });
  }, [isMobile]);

  return (
    <Box>
      <PageWrapperPaper isMobile={isMobile}>
        <form
          id={FORM_ID}
          onSubmit={handleSubmit(onSubmit)}
          onKeyPress={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
            }
          }}
        >
          <Heading as="h4" heading="pageTitle" color="primary">
            {t('pageDialog.navContents.purchaseOrderDetails')}
          </Heading>

          <Box
            css={{
              marginTop: 16,
              '@md': { marginTop: 34 },
            }}
          >
            <Flex
              justify="between"
              css={{
                marginBottom: 16,
                '@md': { marginBottom: 32 },
              }}
            >
              <Heading as="h6" heading="sectionSubTitle" color="primary">
                {translate('itemDetails')}
              </Heading>
              <Button
                type="button"
                css={{
                  display: 'none',
                  '@md': {
                    width: 200,
                    fontWeight: 600,
                    display: 'block',
                  },
                }}
                buttonType="secondary"
                size="sm"
                onClick={() => setShowSelectProductModalDialog(true)}
              >
                {translate('setItemButton')}
              </Button>
            </Flex>

            <MemoizedEditableTable
              columns={purchaseDetailTableColumnsRef.current}
              data={poDetails}
              isMobile={isMobile}
            />

            <Button
              type="button"
              css={{
                display: 'block',
                width: '100%',
                '@md': { display: 'none' },
              }}
              buttonType="secondary"
              size="md"
              onClick={() => setShowSelectProductModalDialog(true)}
            >
              {translate('setItemButton')}
            </Button>

            {errors.poDetails && typeof errors.poDetails === 'object' ? (
              <FormHelper error>{errors.poDetails.message}</FormHelper>
            ) : null}

            {errors.poDetails && Array.isArray(errors.poDetails) ? (
              <FormHelper error>{translate('editableTableSection.quantityValidation')}</FormHelper>
            ) : null}

            {isMobile ? (
              <Separator css={{ marginTop: 20 }} />
            ) : null}

            <Flex
              css={{
                marginTop: 20,
                flexDirection: 'column',
                gap: 16,
                '@md': {
                  flexDirection: 'row',
                  gap: 0,
                },
              }}
            >
              <Box
                css={{
                  width: '100%',
                  '@md': { width: '30%' },
                }}
              >
                <Heading as="h6" heading="sectionSubTitle" color="primary">
                  {translate('discountSection.title')}
                </Heading>
              </Box>
              <Box
                css={{
                  width: '100%',
                  '@md': { width: '70%' },
                }}
              >
                <Flex
                  css={{
                    flexDirection: 'column',
                    gap: 16,
                    '@md': {
                      flexDirection: 'row',
                      gap: 8,
                    },
                  }}
                >
                  <FormGroup
                    css={{
                      width: '100%',
                      '@md': { width: '50%' },
                    }}
                  >
                    <StyledFormLabel>{translate('discountSection.type')}</StyledFormLabel>
                    <InputSelect
                      onChange={({ value }) => {
                        setValue('poDiscount', value);
                        setValue('poDiscountValue', 0);
                      }}
                      option={discountOptions(currentLang)}
                      placeholder={currentLang === 'id' ? 'Pilih' : 'Select'}
                      value={discountOptions(currentLang).find(
                        item => item.value === poDiscount,
                      )}
                      css={{
                        width: formGroupRef.current ? formGroupRef.current.clientWidth : '100%',
                      }}
                    />
                  </FormGroup>
                  {poDiscount === 'percent' ? (
                    <Flex
                      gap={3}
                      css={{
                        width: '100%',
                        '@md': { width: '50%' },
                      }}
                    >
                      <FormGroup
                        css={{
                          width: '100%',
                          '@md': { width: '50%' },
                        }}
                      >
                        <FormLabel>
                          <StyledFormLabel>{translate('discountSection.magnitude')}</StyledFormLabel>
                        </FormLabel>
                        <InputNumber
                          suffix="%"
                          placeholder="0%"
                          onValueChange={({ floatValue }) => setValue('poDiscountValue', floatValue || 0)}
                          allowNegative={false}
                          defaultValue={poDiscountValue}
                          isAllowed={({ floatValue }) => floatValue === undefined || floatValue <= 100}
                          decimalScale={1}
                        />
                      </FormGroup>

                      <FormGroup
                        css={{
                          width: '100%',
                          '@md': { width: '50%' },
                        }}
                      >
                        <FormLabel>
                          <StyledFormLabel>{translate('discountSection.amount')}</StyledFormLabel>
                        </FormLabel>
                        <InputNumber
                          prefix="Rp "
                          placeholder="Rp 0"
                          value={currency({ value: discount, type: '', decimal: discount % 1 !== 0 })}
                          allowedDecimalSeparators
                          disabled
                        />
                      </FormGroup>
                    </Flex>
                  ) : (
                    <FormGroup
                      css={{
                        width: '100%',
                        '@md': { width: '50%' },
                      }}
                    >
                      <FormLabel>
                        <StyledFormLabel>{translate('discountSection.amount')}</StyledFormLabel>
                      </FormLabel>
                      <InputNumber
                        prefix={poDiscount === 'nominal' ? 'Rp ' : ''}
                        suffix={poDiscount === 'percent' ? '%' : ''}
                        placeholder={poDiscount === 'nominal' ? 'Rp 0' : '0%'}
                        onValueChange={({ floatValue }) => setValue('poDiscountValue', floatValue || 0)}
                        allowNegative={false}
                        defaultValue={poDiscountValue}
                        isAllowed={({ floatValue }) => floatValue === undefined || (poDiscount === 'percent' ? floatValue <= 100 : floatValue >= 0)}
                        decimalScale={0}
                      />
                    </FormGroup>
                  )}
                </Flex>
              </Box>
            </Flex>

            <Separator css={{ margin: '20px 0px' }} />

            <Flex
              css={{
                flexDirection: 'column',
                gap: 16,
                '@md': {
                  flexDirection: 'row',
                  gap: 0,
                },
              }}
            >
              <Box
                css={{
                  width: '100%',
                  '@md': { width: '30%' },
                }}
              >
                <Heading as="h6" heading="sectionSubTitle" color="primary">
                  {translate('taxSection.title')}
                </Heading>
              </Box>
              <Box
                css={{
                  width: '100%',
                  '@md': { width: '70%' },
                }}
              >
                <Flex
                  css={{
                    flexDirection: 'column',
                    gap: 16,
                    '@md': {
                      flexDirection: 'row',
                      gap: 8,
                    },
                  }}
                >
                  <FormGroup
                    css={{
                      width: '100%',
                      '@md': { width: '50%' },
                    }}
                  >
                    <StyledFormLabel>{translate('taxSection.type')}</StyledFormLabel>
                    <InputSelect
                      onChange={({ value }) => setValue('poTax', value)}
                      option={taxOptions(currentLang)}
                      placeholder={currentLang === 'id' ? 'Pilih' : 'Select'}
                      value={taxOptions(currentLang).find(item => item.value === poTax)}
                      css={{
                        width: formGroupRef.current ? formGroupRef.current.clientWidth : '100%',
                      }}
                    />
                  </FormGroup>
                  <FormGroup
                    ref={formGroupRef}
                    css={{
                      width: '100%',
                      '@md': { width: '50%' },
                    }}
                  >
                    <StyledFormLabel>{translate('taxSection.amount')}</StyledFormLabel>
                    <InputNumber
                      placeholder="Rp 0"
                      disabled
                      prefix="Rp "
                      value={currency({ value: tax, type: '', decimal: tax % 1 !== 0 })}
                    />
                  </FormGroup>
                </Flex>
              </Box>
            </Flex>

            <Separator css={{ margin: '20px 0px' }} />

            <Flex
              css={{
                flexDirection: 'column',
                gap: 16,
                '@md': {
                  flexDirection: 'row',
                  gap: 0,
                },
              }}
            >
              <Box
                css={{
                  width: '100%',
                  '@md': { width: '30%' },
                }}
              >
                <Heading as="h6" heading="sectionSubTitle" color="primary">
                  {translate('otherExpensesSection.title')}
                </Heading>
              </Box>

              <Box
                css={{
                  display: 'none',
                  '@md': {
                    display: 'block',
                    width: '70%',
                  },
                }}
              >
                {poAdditionalCosts.map(({ name, value }, index) => (
                  <Flex gap={3} css={{ marginBottom: 16 }}>
                    <FormGroup css={{ width: '47%' }}>
                      <StyledFormLabel>{translate('otherExpensesSection.name')}</StyledFormLabel>
                      <InputText
                        onChange={event => handleUpdatePOAdditionalCost(index, 'name', event.target.value)}
                        value={name}
                      />
                    </FormGroup>
                    <FormGroup css={{ width: '47%' }}>
                      <StyledFormLabel>{translate('otherExpensesSection.amount')}</StyledFormLabel>
                      <InputNumber
                        prefix="Rp "
                        placeholder="Rp 0"
                        value={value}
                        onValueChange={({ floatValue }) => handleUpdatePOAdditionalCost(index, 'value', floatValue)}
                        decimalScale={0}
                      />
                    </FormGroup>
                    <Flex css={{ width: '6%' }} align="end" justify="end">
                      <Box
                        css={{ cursor: 'pointer' }}
                        onClick={() => removePOAdditionalCost(index)}
                      >
                        <RemoveOutline color={iconSecondary} />
                      </Box>
                    </Flex>
                  </Flex>
                ))}

                <Button
                  type="button"
                  buttonType="secondary"
                  css={{ width: '100%' }}
                  onClick={handleAddPOAdditionalCost}
                >
                  {translate('otherExpensesSection.addButton')}
                </Button>
              </Box>

              <Box
                css={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 16,
                  '@md': { display: 'none' },
                }}
              >
                <Box
                  css={{
                    display: 'block',
                    background: '#fff',
                    boxShadow: '0px 1px 4px rgba(0, 0, 0, 0.04)',
                    borderRadius: 4,
                    '@md': {
                      display: 'none',
                    },
                  }}
                >
                  <Accordion type="single" value={accordionValue} onValueChange={setAccordionValue} css={{ width: '100%' }}>
                    {poAdditionalCosts.map(({ name, value }, index) => (
                      <AccordionItem value={`additional-cost-${index}`}>
                        <AccordionTrigger
                          arrowColor={textPrimary}
                          css={{
                            padding: 16,
                            borderBottom: index === poAdditionalCosts.length - 1 && accordionValue !== `additional-cost-${index}` ? 'none' : '1px solid #ECEFF1',
                            '& > svg': {
                              margin: '0px !important',
                            },
                          }}
                        >
                          <Text color="primary" css={{ fontSize: 14, fontWeight: 600 }}>
                            {currentLang === 'id' ? `Biaya ${index + 1}` : `Cost ${index + 1}`}
                          </Text>
                        </AccordionTrigger>
                        <AccordionContent>
                          <Flex
                            direction="column"
                            gap={5}
                            css={{
                              padding: 16,
                              borderBottom: index < poAdditionalCosts.length - 1 && accordionValue === `additional-cost-${index}` ? '1px solid #ECEFF1' : 'none',
                            }}
                          >
                            <FormGroup>
                              <StyledFormLabel>{translate('otherExpensesSection.name')}</StyledFormLabel>
                              <InputText
                                onChange={event => handleUpdatePOAdditionalCost(index, 'name', event.target.value)}
                                value={name}
                              />
                            </FormGroup>
                            <FormGroup>
                              <StyledFormLabel>{translate('otherExpensesSection.amount')}</StyledFormLabel>
                              <InputNumber
                                prefix="Rp "
                                placeholder="Rp 0"
                                value={value}
                                onValueChange={({ floatValue }) => handleUpdatePOAdditionalCost(index, 'value', floatValue)}
                                decimalScale={0}
                              />
                            </FormGroup>
                            <Flex align="end" justify="end">
                              <Box
                                css={{ cursor: 'pointer' }}
                                onClick={() => removePOAdditionalCost(index)}
                              >
                                <RemoveOutline color={iconSecondary} />
                              </Box>
                            </Flex>
                          </Flex>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </Box>
                <Button
                  type="button"
                  buttonType="secondary"
                  css={{ width: '100%' }}
                  onClick={handleAddPOAdditionalCost}
                >
                  {translate('otherExpensesSection.addButton')}
                </Button>
              </Box>
            </Flex>

            <Separator css={{ margin: '20px 0px' }} />

            <Flex gap={5}>
              <Flex
                align={poDownPayment ? 'start' : 'center'}
                css={{ width: '100%', '@md': { width: '30%' } }}
              >
                <Heading as="h6" heading="sectionSubTitle" color="primary">
                  {LANG_DATA.PAGE_DIALOG.PURCHASE_ORDER_DETAILS_SECTION.DOWN_PAYMENT_SECTION.TITLE}
                </Heading>
              </Flex>
              <Flex direction="column" css={{ width: '100%', '@md': { width: '70%' } }}>
                <Flex justify={poDownPayment && !isMobile ? 'start' : 'end'}>
                  <Controller
                    name="poDownPayment"
                    control={control}
                    render={({ field: { value, onChange } }) => (
                      <InputSwitch
                        dataOnLabel="ON"
                        dataOffLabel="OFF"
                        checked={value}
                        onCheckedChange={(val) => {
                          if ([accountType.ADVANCE, accountType.ENTERPRISE, accountType.PRIME, accountType.PRIMEPLUS].includes(getAccountType())) {
                            onChange(val);
                            if (val) {
                              setValue('poDownPaymentAccount', paymentAccount !== '-' ? paymentAccount : '');
                            } else {
                              clearErrors('poDownPaymentAccount');
                              setValue('poDownPaymentAccount', '-');
                            }
                          } else {
                            openRestrictionModal(true);
                          }
                        }}
                      />
                    )}
                  />
                </Flex>
              </Flex>
            </Flex>
            {poDownPayment ? (
              <Flex gap={5} css={{ marginTop: 16 }}>
                <Box css={{ display: 'none', '@md': { width: '30%', display: 'flex' } }} />
                <Flex
                  align="start"
                  css={{
                    flexDirection: 'column',
                    gap: 16,
                    width: '100%',
                    '@md': {
                      flexDirection: 'row',
                      gap: 8,
                      width: '70%',
                    },
                  }}
                >
                  <FormGroup css={{ width: '100%', '@md': { width: '50%' } }}>
                    <FormLabel variant="required" css={{ color: '$textPrimary' }}>
                      {LANG_DATA.PAGE_DIALOG.PURCHASE_ORDER_DETAILS_SECTION.DOWN_PAYMENT_SECTION.PAYMENT_ACCOUNT}
                    </FormLabel>
                    <InputSelect
                      value={accountOptions.find(val => String(val.value) === String(poDownPaymentAccount))}
                      onChange={({ value }) => {
                        clearErrors('poDownPaymentAccount');
                        setValue('poDownPaymentAccount', value);
                        setPaymentAccount(value);
                      }}
                      option={accountOptions}
                      placeholder={LANG_DATA.PLACEHOLDER_SELECT}
                      css={{ width: formGroupRef.current ? formGroupRef.current.clientWidth : '100%', maxWidth: 'initial' }}
                      isInvalid={!!errors.poDownPaymentAccount}
                    />
                    {errors.poDownPaymentAccount ? (
                      <FormHelper error>{errors.poDownPaymentAccount.message}</FormHelper>
                    ) : null}
                  </FormGroup>
                  {/* UANG MUKA */}
                  <FormGroup ref={formGroupRef} css={{ width: '100%', '@md': { width: '50%' } }}>
                    <FormLabel variant="required" css={{ color: '$textPrimary' }}>
                      {LANG_DATA.PAGE_DIALOG.PURCHASE_ORDER_DETAILS_SECTION.DOWN_PAYMENT_SECTION.AMOUNT}
                    </FormLabel>

                    <InputNumber
                      prefix="Rp "
                      placeholder="Rp 0"
                      onValueChange={({ floatValue }) => {
                        if (floatValue) clearErrors('poDownPaymentNominal');
                        setValue('poDownPaymentNominal', floatValue);
                        setRemaining(total - (floatValue || 0));
                      }}
                      decimalScale={2}
                      allowNegative={false}
                      defaultValue={poDownPaymentNominal}
                      isAllowed={({ floatValue }) => floatValue === undefined || floatValue >= 0}
                      isInvalid={!!errors.poDownPaymentNominal || remaining < 0}
                    />
                    {errors.poDownPaymentNominal ? (
                      <FormHelper error>{errors.poDownPaymentNominal.message}</FormHelper>
                    ) : null}
                    {remaining < 0 ? (
                      <FormHelper error>{LANG_DATA.PAGE_DIALOG.PURCHASE_ORDER_DETAILS_SECTION.DOWN_PAYMENT_SECTION.AMOUNT_EXCEED}</FormHelper>
                    ) : null}
                  </FormGroup>

                </Flex>
              </Flex>
            ) : null}

            <Separator css={{ margin: '20px 0px' }} />

            <Flex
              css={{
                flexDirection: 'column',
                gap: 16,
                '@md': {
                  flexDirection: 'row',
                  gap: 0,
                },
              }}
            >
              <Box
                css={{
                  width: '100%',
                  '@md': { width: '30%' },
                }}
              >
                <Heading as="h6" heading="sectionSubTitle" color="primary">
                  {translate('detailSection.totalPrice')}
                </Heading>
              </Box>
              <Box
                css={{
                  width: '100%',
                  '@md': { width: '70%' },
                }}
              >
                <Flex justify="between" css={{ marginBottom: 16 }}>
                  <StyledSummaryText>{translate('detailSection.itemSubtotal')}</StyledSummaryText>
                  <StyledSummaryText>
                    {currency({ value: subtotal, decimal: true })}
                  </StyledSummaryText>
                </Flex>

                <Flex justify="between" css={{ marginBottom: 16 }}>
                  <StyledSummaryText>
                    {discountOptions(currentLang, poDiscountValue).find(item => item.value === poDiscount).name}
                  </StyledSummaryText>
                  <StyledSummaryText>{`${currency({ value: -1 * discount, decimal: discount % 1 !== 0 })}`}</StyledSummaryText>
                </Flex>

                <Flex justify="between" css={{ marginBottom: 16 }}>
                  <StyledSummaryText>
                    {taxOptions(currentLang).find(item => item.value === poTax).name}
                  </StyledSummaryText>
                  <StyledSummaryText>
                    {reformattedCurrency({ value: tax, decimal: tax % 1 !== 0 })}
                  </StyledSummaryText>
                </Flex>

                {/* need confirmation to PO about name == '' */}
                {poAdditionalCosts.map(({ name, value }) => (
                  <Flex justify="between" css={{ marginBottom: 16 }}>
                    <StyledSummaryText>{name}</StyledSummaryText>
                    <StyledSummaryText>{currency({ value })}</StyledSummaryText>
                  </Flex>
                ))}

                <Separator css={{ marginBottom: 16 }} />

                <Flex justify="between" css={{ marginBottom: 16 }}>
                  <StyledSummaryBoldText>{translate('detailSection.totalPrice')}</StyledSummaryBoldText>
                  <StyledSummaryBoldText>
                    {reformattedCurrency({ value: roundTotalValue(total), decimal: roundTotalValue(total) % 1 !== 0 })}
                  </StyledSummaryBoldText>
                </Flex>

                {poDownPayment ? (
                  <Fragment>
                    <Flex justify="between" css={{ marginBottom: 16 }}>
                      <StyledSummaryBoldText>
                        {LANG_DATA.PAGE_DIALOG.PURCHASE_ORDER_DETAILS_SECTION.DETAIL_SECTION.DOWN_PAYMENT}
                      </StyledSummaryBoldText>
                      <StyledSummaryBoldText color={remaining < 0 ? 'red' : 'black'}>
                        {reformattedCurrency({ value: -1 * roundTotalValue(poDownPaymentNominal || 0), decimal: roundTotalValue(poDownPaymentNominal || 0) % 1 !== 0 })}
                      </StyledSummaryBoldText>
                    </Flex>

                    <Separator css={{ marginBottom: 16 }} />

                    <Flex justify="between" css={{ marginBottom: 16 }}>
                      <StyledSummaryBoldText>
                        {LANG_DATA.PAGE_DIALOG.PURCHASE_ORDER_DETAILS_SECTION.DETAIL_SECTION.REMAINING_BILLS}
                      </StyledSummaryBoldText>
                      <StyledSummaryBoldText color={remaining < 0 ? 'red' : 'black'}>
                        {`
                          ${remaining < 0 ? '-' : ''}
                          ${reformattedCurrency({
                            value: roundTotalValue(remaining),
                            decimal: roundTotalValue(remaining) % 1 !== 0,
                          })}
                        `}
                      </StyledSummaryBoldText>
                    </Flex>
                  </Fragment>
                ) : null}

                {errors.poGrandTotal ? (
                  <FormHelper error>{errors.poGrandTotal.message}</FormHelper>
                ) : null}
              </Box>
            </Flex>

            {formType !== FORM_TYPES.CREATE ? (
              <Box>
                <Separator css={{ margin: '20px 0px' }} />

                <Flex>
                  <Box css={{ width: '30% ' }}>
                    <Heading as="h6" heading="sectionSubTitle" color="primary">
                      Status
                    </Heading>
                  </Box>
                  <Box css={{ width: '70% ' }}>
                    <GeneratedTagStatus value={poStatus} lang={currentLang} />
                  </Box>
                </Flex>

                <Separator css={{ margin: '20px 0px' }} />
              </Box>
            ) : null}
          </Box>
        </form>
      </PageWrapperPaper>

      {showSelectProductModalDialog ? (
        <SelectProductModalDialog
          t={t}
          currentLang={currentLang}
          open={showSelectProductModalDialog}
          setOpen={setShowSelectProductModalDialog}
          categoryOptions={categoryOptions}
          productIds={Object.fromEntries(poDetails.map(poDetail => [String(poDetail.itemId), true]))}
          handleSelectProducts={handleSelectProducts}
          outletId={outletId}
          isMobile={isMobile}
          priceSetting={priceSetting}
        />
      ) : null}
    </Box>
  );
};

export default PurchaseDetail;
