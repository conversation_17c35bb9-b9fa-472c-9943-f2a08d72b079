import React, { useContext } from 'react';
import { FormContext } from '../utils';
import CustomInputNumber from '../../../../../components/retina/CustomInputNumber';

export default ({
    cell, onBlur, onChange, readOnly,
}) => {
    const { row, value } = cell;
    const { getValues } = useContext(FormContext);

    const handleBlurInput = () => {
        if (readOnly) return;

        const products = getValues('products');
        const product = products[row.index];

        onBlur('purchasePrice', product.purchasePrice, row.index);
    };

    const reformatValue = String(value);

    return (
        <CustomInputNumber
            allowEmptyFormatting
            allowNegative={false}
            decimalScale={0}
            isNumericString
            size="sm"
            onBlur={handleBlurInput}
            value={reformatValue}
            onValueChange={(val) => { if (!readOnly) onChange('purchasePrice', val.floatValue, row.index); }}
            isCurrency
            {...{ readOnly }}
        />
    );
};
