import React, { useContext } from 'react';
import { InputGroup, InputSuffix } from '@majoo-ui/react';
import { FormContext } from '../utils';
import CustomInputNumber from '../../../../../components/retina/CustomInputNumber';

export default ({
    cell, onBlur, onChange, readOnly,
}) => {
    const { row, value } = cell;
    const { getValues } = useContext(FormContext);

    const handleBlurInput = () => {
        if (readOnly) return;

        const products = getValues('products');
        const product = products[row.index];

        onBlur('discount', product.discount, row.index);
    };

    const reformatValue = String(value);

    return (
        <InputGroup>
            <CustomInputNumber
                allowEmptyFormatting
                allowNegative={false}
                decimalScale={0}
                isNumericString
                size="sm"
                onBlur={handleBlurInput}
                value={reformatValue}
                onValueChange={(val) => { if (!readOnly) onChange('discount', val.floatValue, row.index); }}
                isCurrency
                {...{ readOnly }}
            />
            <InputSuffix>%</InputSuffix>
        </InputGroup>
    );
};
