import React from 'react';
import { InputRadio, DateColumn } from '@majoo-ui/react';


export const columns = LANG_DATA => ([
    {
        Header: LANG_DATA.FORM.TRANSACTION_NUMBER,
        accessor: 'note_number',
        Cell: ({ row: { original } }) => <InputRadio label={original.note_number} value={original.id} />,
    },
    {
        Header: LANG_DATA.FORM.ORDER_DATE,
        accessor: 'date',
        Cell: DateColumn,
    },
    {
        Header: LANG_DATA.FORM.SUPPLIER,
        accessor: 'supplier_name',
    },
]);
