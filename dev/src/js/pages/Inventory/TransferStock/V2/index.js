import React, {
  memo, useState, useContext,
  useEffect, lazy, useCallback,
  useRef, Fragment
} from 'react';
import PropTypes from 'prop-types';
// import queryString from 'query-string';
import * as yup from 'yup';
import moment from 'moment';
import { debounce, get } from 'lodash';
import { useForm } from 'react-hook-form';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { useTranslation, Trans } from 'react-i18next';
import {
  Paper, Box, Flex, Heading, Button, Paragraph, Separator,
  InputSearchbox, InputDatePicker, Table, ADVANCE_PICKER,
  ToastContext, PageDialog, PageDialogTitle, PageDialogContent,
  PageDialogFooter, AlertDialogFunction, TableOfContent, Text,
  FormGroup, FormLabel, FormHelper, InputRadioGroup, InputRadio,
} from '@majoo-ui/react';
import { PlusOutline, CalendarOutline, TrashOutline } from '@majoo-ui/icons';
import { foundations } from '@majoo-ui/core';
import { yupResolver } from '@hookform/resolvers/yup';

import LastUpdateHeader from '~/components/retina/Status/LastUpdateHeader';
import ExportBanner from '~/components/retina/export/ExportBanner';
import * as outletActions from '../../../../data/outlets/actions';
import * as outletSelectors from '../../../../data/outlets/selectors';
import * as productApi from '../../../../data/product';
import * as inventoryApi from '../../../../data/inventories';
import * as popupProduct from '../../../../utils/popupProduct';
import * as settingApi from '../../../../data/settings';
import { catchError, formatDate } from '../../../../utils/helper';
// import usePrevious from '../../../../utils/usePrevious';

import CoreHoc from '../../../../core/CoreHOC';
import {
  newTableColumns,
  tableContent,
  tableContentView,
  stockSourceType,
  renderStatus,
} from './table';
import AlertDraftSN from './Modals/alertDraftSN';
import { BannerText, FavoriteWrapper, TooltipGuidance } from '../../../../components/retina';
import ExportButton from '../../components/ExportButton';

import { useMediaQuery } from '../../../../utils/useMediaQuery';
import { stockSourceEnum } from './enum';

export const createNomor = () => `TRF/${moment().format('YYMMDD/')}0001`;

export const KirimStokContext = React.createContext();
// eslint-disable-next-line import/no-cycle
const KirimStokView = lazy(() => import('./KirimStokViewEdit'));

const KirimStok = ({
  actions, userWhitelist, outletList, filterBranch: selectedBranch, calendar: defaultCalendar, assignCalendar, menuPrivilege, ...props
}) => {
  const { t, i18n } = useTranslation(['Penjualan/Inventori/MutasiOutlet/kirimStok', 'translation']);
  const { isShowProgress, showProgress, hideProgress } = props;
  const { addToast } = useContext(ToastContext);
  const alertDraftSNRef = useRef();
  const parentScrollContainerRef = useRef(null);
  const [loadingTable, setLoadingTable] = useState(false);
  const [totalData, settotalData] = useState(0);
  const [pageIndex, setpageIndex] = useState(0);
  const [currentPageSize, setCurrentPageSize] = useState(10);
  const [tableData, setTableData] = useState([]);
  const [rowInfo, setrowInfo] = useState({});
  const [tempValue, setTempValue] = useState({});
  const [calendar, setCalendar] = useState({
    start: moment(defaultCalendar.start, 'DD-MM-YYYY').toDate(),
    end: moment(defaultCalendar.end, 'DD-MM-YYYY').toDate(),
  });
  const [openModal, setopenModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [outletOption, setOutletOption] = useState([]);
  const [activeId, setActiveId] = useState();
  const [limitationStock, setLimitationStock] = useState(false);
  const [validationStock, setValidationStock] = useState({
    raw: {
      stockReachMinimum: false,
      outOfStock: false,
      stockIsMinus: false,
    },
    product: {
      stockReachMinimum: false,
      outOfStock: false,
      stockIsMinus: false,
    },
  });
  const [productInventoryLimitation, setProductInventoryLimitation] = useState([]);
  const [showExportBanner, setShowExportBanner] = useState(false);
  const { colors } = foundations;

  const schema = yup.object().shape({
    form: yup.object().shape({
      stockSource: yup.string().required(t('pageDialog.errors.stockSource', 'Mohon pilih Sumber Stok')),
      nomor: yup.string().nullable().optional(),
      cabang_asal: yup.string().required(t('pageDialog.errors.cabang_asal', 'Mohon pilih outlet asal')),
      cabang_tujuan: yup.string().required(t('pageDialog.errors.cabang_tujuan', 'Mohon pilih outlet tujuan')),
      produk: yup.array().min(1, t('pageDialog.errors.produk', 'Mohon pilih produk')),
    }),
  });

  const {
    handleSubmit,
    register,
    getValues,
    setValue,
    formState,
    watch,
    control,
    clearErrors,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      form: {
        produk: [],
      },
    },
  });

  // const prevBranch = usePrevious(selectedBranch);
  const isMobile = useMediaQuery('(max-width: 767px)');
  const isView = 'mode' in rowInfo && rowInfo.mode === 'view';
  const isEdit = 'mode' in rowInfo && rowInfo.mode === 'edit';

  const isInvalidSN = watch('form.produk').filter(x => x.is_sn === 1 && x.qty !== (x.selected_serial_numbers || []).length).length > 0;
  // TO DO: isInvalidBN (masih asses QA & PO)
  const isButtonEnabled = watch('form.cabang_asal') !== '' && watch('form.cabang_tujuan') !== '' && watch('form.produk').length > 0 && !isInvalidSN;

  const isCanVoid = 'data' in rowInfo && (rowInfo.data && (rowInfo.data.status === '1' || rowInfo.data.status_name.toLowerCase() === 'in transit'));
  // const isReferenceStock = queryString.parse(window.location.search).is_reference;

  const lang = i18n.language;

  const formMode = useCallback(() => {
    let retval = '';
    switch (rowInfo.mode) {
      case 'view':
        retval = t('mainpage.detail', 'Detail Mutasi Stok');
        break;
      case 'edit':
        retval = t('mainpage.edit', 'Ubah Mutasi Stok');
        break;
      default:
        retval = t('mainpage.add', 'Tambah Mutasi Stok');
        break;
    }
    return retval;
  }, [rowInfo, lang]);

  const toogleModal = useCallback(() => {
    setopenModal(prev => !prev);
  });

  const _onFetch = async (state = { pageSize: 10 }) => {
    let notifValue = null;
    setLoadingTable(true);

    const {
      pageSize,
      pageIndex: page,
      pages,
      date,
      keyword,
      sortAccessor,
      sortDirection,
    } = state;

    if (pageSize) setCurrentPageSize(pageSize);
    const formattedDate = rawDate => moment(rawDate).format('YYYY-MM-DD');
    const newPage = page || 0;

    const payload = {
      start_date: formattedDate(calendar.start),
      end_date: formattedDate(calendar.end),
      per_page: pageSize || currentPageSize,
      page: newPage + 1,
      keyword: searchQuery,
      type: 1,
    };

    if (date && date.start !== '') {
      Object.assign(payload, {
        start_date: formattedDate(date.start),
      });
    }

    if (date && date.end !== '') {
      Object.assign(payload, {
        end_date: formattedDate(date.end),
      });
    }

    if (keyword || keyword === '') {
      Object.assign(payload, { keyword });
    }

    // get sort params
    if (sortAccessor && sortDirection) {
      Object.assign(payload, {
        sort_by: sortAccessor,
        sort_type: String(sortDirection).toLowerCase(),
      });
    }

    // get filter params
    if (selectedBranch) {
      Object.assign(payload, { outlet_id: selectedBranch });
    }

    let err = null,
      data = [],
      pageCount = pages > -1 ? pages : -1;

    try {
      showProgress();
      const res = await inventoryApi.getTransferStockV1(payload);
      hideProgress();
      if (res.status || res.data) {
        setLoadingTable(false);
        ({
          data,
          meta: { last_page: pageCount },
        } = res);

        setTableData(data);
        setpageIndex(res.meta.current_page - 1);
        settotalData(res.meta.total);
      }
      setLoadingTable(false);
    } catch (e) {
      setLoadingTable(false);
      err = e;
      notifValue = {
        title: t('mainpage.toast.failedFetch', 'Gagal mengambil data'),
        message: catchError(err),
        variant: 'failed',
        dismissAfter: 2000,
      };
    }

    if (notifValue) {
      addToast(notifValue);
    }

    return { data, pageCount, err };
  };

  const fetchCategoryList = async () => {
    const result = await productApi.getKategoriProduk();

    if (result.status) {
      const categoryList = result.data.map(x => Object.assign({}, x, {
        id: x.id_category_item,
        name: x.category_item_name,
      }));

      return categoryList.map(x => [x.id, x.name]);
    }

    return [];
  };

  const fetchProductInventoryLimitation = async () => {
    try {
      const resLimitation = await settingApi.getProductInvetoryLimitation();
      if (resLimitation && resLimitation.data) {
        const { pos_sales_and_invoice: posSales, send_stock: sendStock, stock_production: stockPo } = resLimitation.data;
        setLimitationStock(String(sendStock) === '1');
        return [posSales, sendStock, stockPo];
      }
      return [];
    } catch (_) {
      return [];
    }
  };

  const fetchOutletData = async () => {
    const { outlet } = actions;
    try {
      const outlets = await outlet.getOutlet({ is_list: 1 });
      const outletOpt = outlets.map(({ id, name }) => ({ value: id, name }));

      setOutletOption(outletOpt);
    } catch (_err) {
      addToast({
        title: t('translation:toast.error', 'Gagal!'),
        description: t('mainpage.toast.somethingWrong', 'Terjadi Kesalahan!'),
        variant: 'failed',
      });
    } finally {
      hideProgress();
    }
  };

  const _onFetchProduk = async (state) => {
    const {
      itemType, category_id, search, page,
    } = state;
    // const { cabang_asal: cabangAsal } = this.state;
    const data = await popupProduct.fetchData(
      state,
      {
        page: page || 0,
        search: search || '',
        id_outlet: getValues('form.stockSource') === stockSourceEnum.REQUEST ? getValues('form.cabang_tujuan') : getValues('form.cabang_asal'),
        item_type: itemType,
        category_id: category_id || '',
        cogs: 1,
        item_have_recipe: 2,
      },
      true,
    );

    return data;
  };

  const fetchValidationStockFromSettingProductAndInventory = async () => {
    const { data } = await inventoryApi.getSettingProductAndInventory();

    if (data) {
      const { raw_material_stock_monitoring: raw, product_stock_monitoring: product } = data;

      const rawStockReachMinimum = Boolean(raw.stock_reach_minimum.status) && raw.stock_reach_minimum.components.includes('send_stock');
      const rawOutOfStock = Boolean(raw.out_of_stock.status) && raw.out_of_stock.components.includes('send_stock');
      const rawStockIsMinus = Boolean(raw.stock_is_minus.status) && raw.stock_is_minus.components.includes('send_stock');

      const productStockReachMinimum = Boolean(product.stock_reach_minimum.status) && product.stock_reach_minimum.components.includes('send_stock');
      const productOutOfStock = Boolean(product.out_of_stock.status) && product.out_of_stock.components.includes('send_stock');
      const productStockIsMinus = Boolean(product.stock_is_minus.status) && product.stock_is_minus.components.includes('send_stock');

      setValidationStock({
        raw: {
          stockReachMinimum: rawStockReachMinimum,
          outOfStock: rawOutOfStock,
          stockIsMinus: rawStockIsMinus,
        },
        product: {
          stockReachMinimum: productStockReachMinimum,
          outOfStock: productOutOfStock,
          stockIsMinus: productStockIsMinus,
        },
      });
    }
  };

  const callEditViewHandler = useCallback(async ({ data }) => {
    const { idPermission } = props;
    const fetchCategory = fetchCategoryList();
    const fetchDetailv1 = inventoryApi.getDetailTransferStockv1({
      id: data.id,
    });

    let unitsByItems = [];

    showProgress();
    try {
      const filterCategory = await fetchCategory;
      const inventoryLimit = await fetchProductInventoryLimitation();

      setProductInventoryLimitation(inventoryLimit);
      const responsev1 = await fetchDetailv1;
      if (data.type !== 'view') {
        const arrayOfItemsId = responsev1.data.detail.map(item => item.item_refno);
        const payloadItemUnits = {
          outlets: [data.outlet_sender_refno],
          items: arrayOfItemsId,
        };
        const resUnits = await productApi.getUnitsByItemsV15(payloadItemUnits);
        unitsByItems = resUnits.data;
      }

      if (responsev1.status) {
        const resData = responsev1.data;

        const inventoryProduct = await popupProduct.fetchData(
          {
            page: 0,
            perPage: resData.length,
            sort: 'sku ASC',
          },
          {
            item_type: 0,
            cogs: 1,
            item_have_recipe: 2,
            id_outlet: resData.request_stock_id ? resData.outlet_receiver_refno : resData.outlet_sender_refno,
            item_id: resData.detail.map(item => item.item_refno).join(','),
          },
          true, true, false, true,
        );

        const updatedDetail = resData.detail.map((detail) => {
          const product = inventoryProduct.data.find(item => String(item.id) === String(detail.item_refno));
          return {
            ...detail,
            item_stock: product ? product.item_stock : null,
            item_stock_alert: product ? product.item_stock_alert : null,
          };
        });

        let selectedProductIds = {};
        const produk = updatedDetail.map((item, index) => ({
          ...responsev1.data.detail[index],
          detail_id: item.id,
          id: item.item_refno,
          name: item.item_name,
          qty: item.quantity,
          remaining_stock: (item.quantity - item.stock_received - item.stock_rejected),
          temp_stock: item.item_stock,
          item_stock: item.item_stock,
          item_stock_alert: item.item_stock_alert,
          unit_no: item.item_unit_number,
          satuan: item.item_unit_name,
          unit_name: item.item_unit_name,
          sku: item.item_sku,
          variant: item.variants || [],
          unit_opts: unitsByItems.length ? (get(unitsByItems.find(unitByItem => unitByItem.item_id === item.item_refno), 'units') || [{ name: item.item_unit_name, no: item.item_unit_number }]) : [{ name: item.item_unit_name, no: item.item_unit_number }],
          requestStockStatus: item.request_stock_status ? item.request_stock_status : '',
          is_sn: item.is_serial_number,
          selected_serial_numbers: item.serial_number ? item.serial_number.serial_no : [],
          is_bn: item.is_batch_number,
          selected_batch_numbers: item.batch_number ? item.batch_number.data.map(x => ({ value: x.batch_no, stock: x.stock, exp: x.expired_date })) : [],
        }));

        if (produk.length > 0) {
          selectedProductIds = Object.fromEntries(produk.map(item => [Number(item.id), true]));
        }

        const result = {
          id_tf: resData.id,
          type: data.type,
          nomor: resData.transfer_stock_number,
          statusTS: resData.status,
          branchOptionFrom: outletList.filter(outlet => outlet.id_cabang.length > 0).map(outlet => ({
            value: outlet.id_cabang,
            name: outlet.cabang_name,
          })),
          branchOptionTo: idPermission !== '1' ? outletOption.filter(({ value }) => String(value) !== String(resData.cabang_pengirim)) : outletOption,
          cabang_asal: resData.outlet_sender_refno,
          cabang_tujuan: resData.outlet_receiver_refno,
          keterangan: resData.note,
          waktu: moment(resData.transfer_stock_date).format('HH:mm'),
          tgl: moment(resData.transfer_stock_date).format('DD/MM/YYYY'),
          tgl_kirim: moment().format('DD/MM/YYYY'),
          waktu_kirim: moment().format('HH:mm'),
          user: resData.created_by_name,
          produk,
          stockSource: resData.request_stock_id ? stockSourceEnum.REQUEST : stockSourceEnum.MANUAL,
          requestStockId: resData.request_stock_id,
          requestNo: resData.request_stock_number,
          msg: {
            nomor: '',
            cabang_asal: '',
            cabang_tujuan: '',
            waktu: '',
            produk: '',
          },
          showProductPopup: false,
          itemList: [],
          filterCategory,
          isBranchFromDisabled: idPermission !== '1',
          isRoleWareHouse: idPermission === '5',
          selectedProductIds,
          updatedBy: resData.updated_by_name,
          updatedDate: resData.updated_at,
        };
        setTempValue(result);
        setValue('form', result);
        setopenModal(true);
      }
    } catch (error) {
      addToast({
        title: 'Gagal!',
        description: t('mainpage.toast.somethingWrong', 'Terjadi Kesalahan!'),
        variant: 'failed',
      });
    } finally {
      hideProgress();
    }
  }, [outletList]);

  const callCreateHandler = useCallback(async () => {
    const { idPermission, idCabang } = props;
    const fetchCategory = fetchCategoryList();

    showProgress();

    const filterCategory = await fetchCategory;
    const inventoryLimit = await fetchProductInventoryLimitation();
    setProductInventoryLimitation(inventoryLimit);

    const initialForm = {
      id_tf: '',
      type: 'create',
      nomor: '',
      statusTS: '',
      branchOptionFrom: outletList.filter(outlet => outlet.id_cabang.length > 0).map(outlet => ({
        value: outlet.id_cabang,
        name: outlet.cabang_name,
      })),
      branchOptionTo: idPermission !== '1' ? outletOption.filter(({ value }) => String(value) !== String(selectedBranch || idCabang)) : outletOption,
      cabang_asal: idPermission !== '1' ? selectedBranch || idCabang : '',
      cabang_tujuan: '',
      keterangan: '',
      waktu: moment().format('HH:mm'),
      tgl: moment().format('DD/MM/YYYY'),
      tgl_kirim: moment().format('DD/MM/YYYY'),
      waktu_kirim: moment().format('HH:mm'),
      user: '',
      produk: [],
      msg: {
        nomor: '',
        cabang_asal: '',
        cabang_tujuan: '',
        waktu: '',
        produk: '',

      },
      showProductPopup: false,
      itemList: [],
      filterCategory,
      isBranchFromDisabled: idPermission !== '1',
      stockSource: '1',
      requestStockId: '',
      requestNo: '',
      isRoleWareHouse: idPermission === '5',
    };

    setTempValue(initialForm);
    setValue('form', initialForm);
    hideProgress();
    setopenModal(true);
  }, [outletList, outletOption]);

  const onChangeCalendar = async (value) => {
    setCalendar({ start: value[0], end: value[1] });
    assignCalendar(moment(value[0]).format('DD-MM-YYYY'), moment(value[1]).format('DD-MM-YYYY'), null, null);
    await _onFetch({ date: { start: value[0], end: value[1] } });
  };

  const onSearch = debounce(async (keyword) => {
    setSearchQuery(keyword);
    await _onFetch({ keyword });
  }, 500);

  const confirmRemoveHandler = async (callback) => {
    const { id_tf } = getValues('form');
    const payload = id_tf !== '' ? { id_tf } : {};
    showProgress();

    inventoryApi.deleteTransferStockV1(payload)
      .then((res) => {
        if (res.error === null) {
          _onFetch();
          addToast({
            title: t('toast.success', { ns: 'translation' }),
            description: (
              <Trans
                t={t}
                i18nKey="mainpage.toast.deleteSucced"
                values={{ nomor: rowInfo.data.transfer_stock_number }} // existing code -> transfer_stock_no
              />
            ),
            variant: 'success',
            dismissAfter: 2000,
          });
        } else {
          callback();
          addToast({
            title: t('toast.error', { ns: 'translation' }),
            description: (
              <Trans
                t={t}
                i18nKey="mainpage.toast.deleteFailed"
                values={{ nomor: rowInfo.data.transfer_stock_number }}
              />
            ),
            variant: 'failed',
            dismissAfter: 2000,
          });
        }
      })
      .catch((err) => {
        addToast({
          title: t('mainpage.toast.somethingWrong', 'Terjadi Kesalahan!'),
          description: catchError(err),
          variant: 'failed',
        });
      })
      .finally(() => {
        hideProgress();
        toogleModal();
      });
  };

  const confirmVoidHandler = async (callback) => {
    const { id_tf } = getValues('form');
    const payload = id_tf !== '' ? { id: id_tf } : {};
    showProgress();

    inventoryApi.voidTransferStockV1(payload)
      .then((res) => {
        if (res.error === null) {
          _onFetch();
          addToast({
            title: t('toast.success', { ns: 'translation' }),
            description: (
              <Trans
                t={t}
                i18nKey="mainpage.toast.voidSucced"
                values={{ nomor: rowInfo.data.transfer_stock_number }}
              />
            ),
            variant: 'success',
            dismissAfter: 2000,
          });
        } else {
          callback();
          addToast({
            title: t('toast.error', { ns: 'translation' }),
            description: (
              <Trans
                t={t}
                i18nKey="mainpage.toast.voidFailed"
                values={{ nomor: rowInfo.data.transfer_stock_number }}
              />
            ),
            variant: 'failed',
            dismissAfter: 2000,
          });
        }
      })
      .catch((err) => {
        addToast({
          title: t('mainpage.toast.somethingWrong', 'Terjadi Kesalahan!'),
          description: catchError(err),
          variant: 'failed',
        });
      })
      .finally(() => {
        hideProgress();
        toogleModal();
      });
  };

  const onEditViewAction = (mode, data) => {
    setrowInfo({ mode, data });
    if (mode === 'edit' || mode === 'view') {
      callEditViewHandler({ data: { ...data, type: mode } });
    } else {
      callCreateHandler();
    }
  };

  const saveDataHandler = async (data, action, callback) => {
    showProgress();
    const {
      id_tf,
      produk,
      cabang_asal,
      cabang_tujuan,
      tgl,
      waktu,
      tgl_kirim: tglKirim,
      waktu_kirim: waktuKirim,
      keterangan,
      nomor,
      user,
      requestStockId,
    } = data.form;
    if (userWhitelist.isWhitelistPopUp) {
      if (callback) {
        return callback();
      }
      return 0;
    }

    let isContainZeroPrice = false;
    const hasDraftItemSerialNumber = false;

    const detail = [];
    for (let index = 0; index < produk.length; index++) {
      const item = produk[index];
      if (!parseFloat(item.qty, 10)) {
        isContainZeroPrice = true;
      }
      if ((item && item.draft_serial_number && item.draft_serial_number.length > 0) || (item && item.draft_batch_number && item.draft_batch_number.length > 0)) {
        alertDraftSNRef.current.openDialog();
        hideProgress();
        return false;
      }

      if ((item.requestStockStatus !== 2) && ((!!item.is_sn && !item.selected_serial_numbers) || (!!item.is_bn && !item.selected_batch_numbers))) {
        addToast({
          title: t('translation:toast.error', 'Gagal!'),
          description: t('pageDialog.invalidSerialNumberAmount'),
          variant: 'failed',
        });
        hideProgress();
        return false;
      }
      if (Number(item.qty) > Number(item.item_stock) && !!limitationStock) { // existing condition --> productInventoryLimitation.includes(1)
        addToast({
          title: t('translation:toast.error', 'Gagal!'),
          description: t('pageDialog.invalidMaxStockLimit'),
          variant: 'failed',
        });
        hideProgress();
        return false;
      }

      if (requestStockId && item.requestStockStatus === '') {
        addToast({
          title: t('translation:toast.error', 'Gagal!'),
          description: t('pageDialog.errors.requestStockStatus'),
          variant: 'failed',
        });
        hideProgress();
        return false;
      }

      detail.push({
        detail_id: Number(item.detail_id),
        item_id: Number(item.id),
        item_qty: Number(item.qty),
        unit_no: item.unit_no,
        ...(requestStockId) && { request_stock_status: item.requestStockStatus },
        ...(!!item.is_sn || !!item.is_serial_number) && Array.isArray(item.selected_serial_numbers) && (item.requestStockStatus !== 2) && {
          serial_number: {
            is_reserved: action !== 'draft',
            serial_no: item.selected_serial_numbers,
          },
        },
        ...(!!item.is_bn || !!item.is_batch_number) && Array.isArray(item.selected_batch_numbers) && (item.requestStockStatus !== 2) && {
          batch_number: {
            is_reserved: action !== 'draft',
            data: item.selected_batch_numbers.map(x => ({
              batch_no: x.value,
              stock: x.stock,
              ...x.exp && { expired_date: moment(x.exp).format('YYYY-MM-DD HH:mm:ss') },
            })),
          },
        },
        ...(!!item.is_bn || !!item.is_batch_number) && get(item, 'batch_number.data') && item.batch_number.data.length > 0 && {
          batch_number: {
            ...item.batch_number,
            is_reserved: action !== 'draft',
          },
        },
        ...(!!item.is_sn || !!item.is_serial_number) && get(item, 'serial_number.serial_no') && Array.isArray(item.serial_number.serial_no) && {
          serial_number: {
            is_reserved: action !== 'draft',
            serial_no: item.serial_number.serial_no,
          },
        },
      });
    }

    if (isContainZeroPrice) {
      hideProgress();
      addToast({
        variant: 'failed',
        title: 'Error!',
        description: t('mainpage.toast.zeroQuantity', 'Kuantitas barang tidak boleh 0'),
        dismissAfter: 2000,
      });

      if (callback) {
        return callback();
      }
      return 0;
    }

    const payload = {
      nomor,
      keterangan,
      tgl: `${formatDate(requestStockId ? tglKirim : tgl, 'yyyy-mm-dd')} ${requestStockId ? waktuKirim : waktu}:00`,
      cabang_asal: Number(cabang_asal),
      cabang_tujuan: Number(cabang_tujuan),
      detail,
      status: action === 'draft' ? 2 : 1,
      user: Number(user),
      request_stock_id: requestStockId === '' ? null : requestStockId,
      lang: lang || 'id',
    };

    if (data.form.type === 'create') {
      inventoryApi.createTransferStockV1(payload)
        .then((response) => {
          if (response.error === null) {
            _onFetch();
            addToast({
              title: t('toast.success', { ns: 'translation' }),
              description: action === 'draft'
                ? <Trans t={t} i18nKey="mainpage.toast.saveDraft" values={{ nomor }} />
                : <Trans t={t} i18nKey="mainpage.toast.save" values={{ nomor }} />,
              variant: 'success',
              dismissAfter: 2000,
            });
            toogleModal();
          } else {
            callback();
            addToast({
              title: t('toast.error', { ns: 'translation' }),
              description: action === 'draft'
                ? <Trans t={t} i18nKey="mainpage.toast.failedSaveDraft" values={{ nomor }} />
                : <Trans t={t} i18nKey="mainpage.toast.failedSave" values={{ nomor }} />,
              variant: 'failed',
              dismissAfter: 2000,
            });
          }
        })
        .catch((err) => {
          addToast({
            title: t('toast.error', { ns: 'translation' }),
            description: catchError(err),
            variant: 'failed',
            values: {
              menu: t('pageDialog.title', 'Mutasi Stok'),
            },
          });
        })
        .finally(hideProgress);
    } else {
      payload.id_tf = String(id_tf);
      inventoryApi.updateTransferStockV1(payload)
        .then((response) => {
          if (response.error === null) {
            _onFetch();
            addToast({
              title: t('toast.success', { ns: 'translation' }),
              description: action === 'draft'
                ? <Trans t={t} i18nKey="mainpage.toast.updateDraft" values={{ nomor }} />
                : <Trans t={t} i18nKey="mainpage.toast.update" values={{ nomor }} />,
              variant: 'success',
              dismissAfter: 2000,
            });
            toogleModal();
          } else {
            callback();
            addToast({
              title: t('toast.error', { ns: 'translation' }),
              description: action === 'draft'
                ? <Trans t={t} i18nKey="mainpage.toast.failedUpdateDraft" values={{ nomor }} />
                : <Trans t={t} i18nKey="mainpage.toast.failedUpdate" values={{ nomor }} />,
              variant: 'failed',
              dismissAfter: 2000,
            });
          }
        })
        .catch((err) => {
          addToast({
            title: t('toast.error', { ns: 'translation' }),
            description: catchError(err),
            variant: 'failed',
            values: {
              menu: t('pageDialog.title', 'Mutasi Stok'),
            },
          });
        })
        .finally(hideProgress);
    }
    return 0;
  };

  const submitDraft = handleSubmit(() => {
    const data = getValues();
    saveDataHandler(data, 'draft');
  });

  const cancelModal = () => {
    if (isView) {
      setopenModal(prev => !prev);
      return;
    }
    const cancelDialog = new AlertDialogFunction({
      title: t('mainpage.dialog.cancelTitle',
        `Batal ${isEdit ? 'Ubah' : 'Tambah'} Kirim stok`,
        { mode: isEdit ? t('mainpage.edit', 'Ubah') : t('mainpage.add', 'Tambah') }),
      description: (
        <Trans t={t} i18nKey="mainpage.dialog.cancelDesc">
          Membatalkan
          {' '}
          {isEdit ? <span>{{ mode: t('mainpage.dialog.editmode') }}</span> : <span>{{ mode: t('mainpage.dialog.addmode') }}</span>}
          {' '}
          akan menghapus seluruh data yang telah diinput dan tidak dapat dibatalkan. Lanjutkan?
        </Trans>
      ),
      dialogType: 'primary',
      labelConfirm: t('mainpage.dialog.btn.saveDraf', 'Simpan Draf'),
      labelCancel: t('label.continue', { ns: 'translation' }),
      onConfirm: submitDraft,
      onCancel: () => {
        clearErrors();
        setopenModal(prev => !prev);
      },
    });
    cancelDialog.show();
  };

  const deleteActionModal = () => {
    const dialog = new AlertDialogFunction({
      title: t('mainpage.dialog.deleteTitle'),
      css: { width: 422 },
      description: (
        <Trans
          t={t}
          i18nKey="mainpage.dialog.deleteDesc"
          values={{ nomor: rowInfo.data.transfer_stock_number }} // existing code -> lang === 'en' ? rowInfo.data.transfer_stock_number : rowInfo.data.id_transfer_stock
        />
      ),
      labelConfirm: t('label.continue', { ns: 'translation' }),
      labelCancel: t('label.cancel', { ns: 'translation' }),
      dialogType: 'negative',
      onConfirm: () => confirmRemoveHandler(),
    });
    dialog.show();
  };

  const voidActionModal = () => {
    const dialog = new AlertDialogFunction({
      title: t('mainpage.dialog.voidTitle'),
      css: { width: 422 },
      description: (
        <Trans
          t={t}
          i18nKey="mainpage.dialog.voidDesc"
          values={{ nomor: rowInfo.data.transfer_stock_number }}
        />
      ),
      labelConfirm: t('label.continue', { ns: 'translation' }),
      labelCancel: t('label.cancel', { ns: 'translation' }),
      dialogType: 'negative',
      onConfirm: () => confirmVoidHandler(),
    });
    dialog.show();
  };

  const saveDraftModal = (data) => {
    const dialog = new AlertDialogFunction({
      title: t('mainpage.dialog.saveTitle'),
      css: { width: 422 },
      description: (
        <Trans
          t={t}
          i18nKey="mainpage.dialog.saveDrafDesc"
          values={{ nomor: data.form.nomor }} // existing code: data.form.id_tf || data.form.nomor.split('/')[1]
        />
      ),
      disabledButton: isShowProgress,
      labelConfirm: t('label.continue', { ns: 'translation' }),
      labelCancel: t('label.cancel', { ns: 'translation' }),
      dialogType: 'primary',
      onConfirm: () => saveDataHandler(data, 'draft'),
    });
    dialog.show();
  };

  const saveCompleteModal = (data) => {
    const dialog = new AlertDialogFunction({
      title: t('mainpage.dialog.saveTitle'),
      css: { width: 422 },
      description: (
        <Trans
          t={t}
          i18nKey="mainpage.dialog.saveDesc"
          values={{ nomor: data.form.nomor }} // existing code: nomor: data.form.id_tf || data.form.nomor.split('/')[1]
        />
      ),
      disabledButton: isShowProgress,
      labelConfirm: t('label.continue', { ns: 'translation' }),
      labelCancel: t('label.cancel', { ns: 'translation' }),
      dialogType: 'primary',
      onConfirm: () => saveDataHandler(data, 'complete'),
    });
    dialog.show();
  };

  const onFailed = () => { };

  const checkValidationStock = useCallback(() => {
    const { raw, product } = validationStock;
    const invalidStocks = [];
    getValues('form.produk').forEach(({ item_stock, item_stock_alert, jenis_produk }) => {
      const validation = jenis_produk === 'Bahan' ? raw : product;
      let invalidStock = false;

      switch (true) {
        case Number(item_stock) < Number(item_stock_alert) && Number(item_stock) > 0 && validation.stockReachMinimum:
          invalidStock = true;
          break;
        case Number(item_stock) === 0 && validation.outOfStock:
          invalidStock = true;
          break;
        case Number(item_stock) < 0 && validation.stockIsMinus:
          invalidStock = true;
          break;
        default:
          invalidStock = false;
          break;
      }

      invalidStocks.push(invalidStock);
    });

    return invalidStocks.every(item => item === false);
  }, [validationStock, getValues('form.produk')]);

  useEffect(async () => {
    await _onFetch({ branch: selectedBranch });
  }, [selectedBranch]);

  useEffect(() => {
    fetchOutletData();
    fetchValidationStockFromSettingProductAndInventory();
  }, []);

  const handleExport = async () => {
    const startDate = moment(calendar.start, 'DD/MM/YYYY');
    const endDate = moment(calendar.end, 'DD/MM/YYYY');

    const diffRangeMonth = moment(endDate).diff(startDate, 'months');
    if (diffRangeMonth > 0) {
      addToast({
        title: t('translation:toast.error'),
        description: t('mainpage.toast.errorLimit1Month'),
        variant: 'failed',
      });
      return;
    }

    const today = moment();
    const diffMonthWithToday = moment(today).diff(startDate, 'months');
    if (diffMonthWithToday > 12) {
      addToast({
        title: t('translation:toast.error'),
        description: t('mainpage.toast.errorLimit1Year'),
        variant: 'failed',
      });
      return;
    }

    try {
      showProgress();
      const payload = {
        start_date: startDate.format('YYYY-MM-DD'),
        end_date: endDate.format('YYYY-MM-DD'),
        ...searchQuery && { search: searchQuery },
        ...selectedBranch && { outlet_id: +selectedBranch },
        lang,
      };
      await inventoryApi.exportTransferStock(payload);

      setShowExportBanner(true)
    } catch (error) {
      addToast({
        title: t('translation:toast.error'),
        description: catchError(error),
        variant: 'failed',
      });
    } finally {
      hideProgress();
    }
  }

  return (
    <Fragment>
      <Box css={{ ...showExportBanner && { marginBottom: '$spacing-05' } }}>
        <ExportBanner
          showExportBanner={showExportBanner}
          onDismiss={() => setShowExportBanner(false)}
        />
      </Box>
      <Paper css={{ padding: '16px 24px', mb: 10 }}>
        <div id="a-dialog" />
        <Flex css={{ mb: 19 }} justify="between">
          <Box>
            <FavoriteWrapper>
              <Heading onClick={() => { i18n.changeLanguage(i18n.language === 'id' ? 'en' : 'id'); }} heading="pageTitle">{t('mainpage.heading', 'Kirim Stok')}</Heading>
              <TooltipGuidance />
            </FavoriteWrapper>
            <Flex align="center" css={{ gap: 13, mt: 8 }}>
              <CalendarOutline />
              <Paragraph>
                {`${moment(calendar.start).locale(lang).format('DD MMMM YYYY')} - ${moment(calendar.end).locale(lang).format('DD MMMM YYYY')}`}
              </Paragraph>
            </Flex>
          </Box>
          <Flex direction="row" gap={3}>
            <ExportButton
              title={t('mainpage.exportTitle', 'Ekspor Kirim Stok')}
              onExport={handleExport}
              calendar={calendar}
            />
            {menuPrivilege.isCanCreate && (
              <Button
                onClick={() => onEditViewAction('create')}
                size="sm"
                leftIcon={<PlusOutline size="24" color={colors.white} />}
              >
                {t('mainpage.btnAdd', 'Tambah Kirim Stok')}
              </Button>
            )}
          </Flex>
        </Flex>
        <BannerText />
        <Separator />
        <Flex align="center" css={{ gap: 16, mt: 20, mb: 23 }}>
          <Box>
            <InputSearchbox onChange={onSearch} placeholder={t('mainpage.searchBox', 'Cari ...')} />
          </Box>
          <InputDatePicker
            type={ADVANCE_PICKER}
            date={[calendar.start, calendar.end]}
            onChange={onChangeCalendar}
            css={{
              width: '258px',
            }}
          />
        </Flex>
        <Separator />
        <Table
          // TODO: remove this double click event, another event on column meta
          // onRowClick={(row) => {
          //   const { original: { status } } = row;
          //   if (String(status) === '2' && menuPrivilege.isCanUpdate) {
          //     onEditViewAction('edit', row.original);
          //   } else {
          //     onEditViewAction('view', row.original);
          //   }
          // }}
          headerCss={{
            color: '$textPrimary',
            fontSize: '$section-sub-title',
            fontWeight: 600,
            lineHeight: '16px',
            letterSpacings: '$section-sub-title',
          }}
          isLoading={loadingTable}
          css={{ marginBlock: 20 }}
          columns={newTableColumns(onEditViewAction, t, menuPrivilege.isCanUpdate)}
          data={tableData}
          totalData={totalData}
          pageIndex={pageIndex}
          fetchData={_onFetch}
          searchQuery={searchQuery}
        />
        <KirimStokContext.Provider
          value={{
            _onFetchProduk,
            popupProduct,
            openModal,
            setTempValue,
            tempValue,
            rowInfo,
            props,
            addToast,
            translation: { t, i18n },
            useFormData: {
              handleSubmit,
              register,
              getValues,
              setValue,
              formState,
              watch,
              control,
              clearErrors,
            },
            validationStock,
          }}
        >
          {openModal && (
            <PageDialog
              isMobile={isMobile}
              open={openModal}
              modal
              css={{ zIndex: '$modal' }}
              onOpenChange={cancelModal}
            >
              <PageDialogTitle>{formMode()}</PageDialogTitle>
              <PageDialogContent ref={parentScrollContainerRef} wrapperSize="lg">
                <Box
                  css={{
                    display: 'grid',
                    height: '100%',
                    width: '100%',
                    position: 'relative',
                    gridTemplateColumns: '1fr',
                    '@md': { gridTemplateColumns: '1fr 4fr', gap: '$compact' },
                  }}
                >
                  <React.Suspense fallback={null}>
                    <Box
                      css={{
                        '@sm': {
                          position: 'sticky',
                          top: 0,
                          padding: 0,
                          backgroundColor: 'white',
                        },
                        '@md': {
                          position: 'unset',
                          top: 'unset',
                          backgroundColor: 'unset',
                        },
                      }}
                    >
                      <TableOfContent
                        isMobile={isMobile}
                        css={{ position: 'sticky', top: 0 }}
                        activeId={activeId}
                        isDialog
                        content={!isView ? tableContent(t) : tableContentView(t)}
                      />
                    </Box>
                    <Box
                      css={{
                        width: '100%',
                        maxWidth: 982,
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '$compact',
                        '& > div, & > div > div': { all: 'inherit' },
                        '& > div > div': { all: 'inherit' },
                      }}
                    >
                      <TableOfContent.Page
                        offsetBottom={0}
                        scrollThrottle={80}
                        onUpdateCallback={setActiveId}
                        parentScrollContainerRef={parentScrollContainerRef}
                      >
                        {rowInfo.mode !== 'create' && rowInfo?.data?.id && (
                          <Paper responsive css={{ padding: 24 }}>
                            <Flex justify="between" gap={3} css={{ flexDirection: 'column', '@lg': { flexDirection: 'row' } }}>
                              <Flex align="center" css={{ gap: '$cozy' }}>
                                <Heading as="h6" heading="sectionSubTitle" color="primary">
                                  Status
                                </Heading>
                                {renderStatus(rowInfo?.data?.status_name, rowInfo?.data?.status, t)}
                              </Flex>
                              <LastUpdateHeader
                                t={t}
                                tKey="translation:label.lastUpdatedBy"
                                updatedDate={tempValue?.updatedDate}
                                updatedBy={tempValue?.updatedBy}
                              />
                            </Flex>
                          </Paper>
                        )}
                        <Box id="source">
                          <Paper responsive css={{ padding: 24 }}>
                            <Heading
                              as="h3"
                              heading="pageTitle"
                              css={{ fontSize: '$page-title !important', mb: '$spacing-07 !important' }}
                            >
                              {t('pageDialog.content.source', 'Sumber Stok')}
                            </Heading>
                            <FormGroup>
                              <Flex
                                align="start"
                                css={{
                                  flexDirection: 'column',
                                  gap: '$spacing-03',
                                  mb: '$spacing-05',
                                  '@md': {
                                    gap: '$cozy',
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                  },
                                }}
                              >
                                <FormLabel
                                  variant="required"
                                  css={{
                                    alignItems: 'start !important',
                                    '& > span': {
                                      color: '$textPrimary',
                                      fontSize: '$label',
                                      fontWeight: 500,
                                      lineHeight: '$label',
                                      letterSpacings: '$label',
                                      '@md': {
                                        color: '$textPrimary',
                                        fontSize: '$section-sub-title',
                                        fontWeight: 600,
                                        lineHeight: '$section-sub-title',
                                        letterSpacings: '$section-sub-title',
                                        width: 175,
                                      },
                                    },
                                  }}
                                >
                                  {t('pageDialog.label.source', 'Pilih Sumber')}
                                </FormLabel>
                                <Flex direction="column" gap={3} css={{ width: '100%', '@md': { width: '70%' } }}>
                                  <InputRadioGroup
                                    defaultValue={getValues('form.stockSource')}
                                    onValueChange={(val) => {
                                      setTempValue(prev => ({ ...prev, stockSource: val, produk: [] }));
                                      setValue('form.stockSource', val);
                                      setValue('form.produk', []);
                                      clearErrors();
                                    }}
                                    css={{
                                      display: 'flex',
                                      flexDirection: 'column',
                                      gap: '10px',
                                    }}
                                  >
                                    {stockSourceType(t).map(x => (
                                      <Box
                                        key={x.id}
                                        css={{
                                          border: `1px solid ${x.id === getValues('form.stockSource') ? '$primary500' : '$gray300'}`,
                                          padding: '8px',
                                          borderRadius: '8px',
                                        }}
                                      >
                                        <InputRadio key={x.id} id={x.id} value={x.id} label={x.label} disabled={isView} />
                                        <Text variant="caption">{x.description}</Text>
                                      </Box>
                                    ))}
                                  </InputRadioGroup>
                                  {(formState.errors.form && formState.errors.form.stockSource) && (
                                    <FormHelper error>{formState.errors.form.stockSource.message}</FormHelper>
                                  )}
                                </Flex>
                              </Flex>
                            </FormGroup>
                          </Paper>
                        </Box>
                        <Box id="detail">
                          <KirimStokView />
                        </Box>
                      </TableOfContent.Page>
                    </Box>
                  </React.Suspense>
                </Box>
              </PageDialogContent>
              <PageDialogFooter>
                <Flex
                  justify={(!isView || isCanVoid) ? 'between' : 'end'}
                  css={{ width: 1230, mx: 'auto', gap: 16 }}
                >
                  {isCanVoid && (
                    <Flex css={{ gap: 16, ml: (!isMobile && isCanVoid) && '230px' }}>
                      <Button
                        onClick={voidActionModal}
                        buttonType="negative"
                      >
                        Void
                      </Button>
                    </Flex>
                  )}
                  {!isView && (
                    <React.Fragment>
                      <Flex css={{ gap: 16, ml: (!isMobile && !isView && !isCanVoid) && '230px' }}>
                        {menuPrivilege.isCanDelete && isEdit && (
                          <React.Fragment>
                            <Button
                              onClick={deleteActionModal}
                              css={{
                                width: 40,
                                minWidth: 'unset',
                                padding: 'unset',
                              }}
                              buttonType="negative-secondary"
                            >
                              <TrashOutline color={colors.textRed} />
                            </Button>
                          </React.Fragment>
                        )}
                        <Button
                          disabled={!isButtonEnabled}
                          onClick={handleSubmit(saveDraftModal, onFailed)}
                          buttonType="secondary"
                        >
                          {t('pageDialog.btnAction.saveDraft', 'Simpan Draf')}
                        </Button>
                      </Flex>
                    </React.Fragment>
                  )}
                  <Flex justify="end" css={{ gap: 16 }}>
                    {isView ? (
                      <Button onClick={toogleModal} buttonType="ghost">
                        {t('pageDialog.btnAction.back', 'Kembali')}
                      </Button>
                    ) : (
                      <React.Fragment>
                        <Button onClick={cancelModal} buttonType="ghost">
                          {t('pageDialog.btnAction.cancel', 'Batal')}
                        </Button>
                        <Button disabled={!isButtonEnabled} onClick={handleSubmit(saveCompleteModal, onFailed)}>
                          {t('pageDialog.btnAction.save', 'Simpan')}
                        </Button>
                      </React.Fragment>
                    )}
                  </Flex>
                </Flex>
              </PageDialogFooter>
            </PageDialog>
          )}
          <AlertDraftSN ref={alertDraftSNRef} />
        </KirimStokContext.Provider>
      </Paper>
    </Fragment>
  );
};

KirimStok.propTypes = {
  userWhitelist: PropTypes.shape(),
  actions: PropTypes.shape(),
  isShowProgress: PropTypes.bool,
  showProgress: PropTypes.func,
  hideProgress: PropTypes.func,
  outletList: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  filterBranch: PropTypes.string,
  calendar: PropTypes.shape({
    start: PropTypes.string,
    end: PropTypes.string,
  }),
  assignCalendar: PropTypes.func,
};

KirimStok.defaultProps = {
  userWhitelist: {},
  actions: {},
  isShowProgress: false,
  showProgress: () => { },
  hideProgress: () => { },
  filterBranch: '',
  calendar: {
    start: moment().startOf('month').toDate(),
    end: moment().endOf('month').toDate(),
  },
  assignCalendar: () => { },
};

const mapStateToProps = state => ({
  userWhitelist: state.popUpHppReducer,
  menuPrivilege: state.layouts.detailPrivilege,
  outletList: outletSelectors.getListBranchByPrivilege(state.branch.list, state.users.strap.permissionId),
});

const mapDispatchToProps = dispatch => ({
  actions: {
    outlet: bindActionCreators(outletActions, dispatch),
    product: bindActionCreators(productApi, dispatch),
  },
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(CoreHoc(memo(KirimStok)));
