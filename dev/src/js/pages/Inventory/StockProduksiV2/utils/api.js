import React from 'react';
import { Box } from '@majoo-ui/react';
import { Trans } from 'react-i18next';
import { catchError, joinListString } from '../../../../utils/helper';
import {
    getStokProduksiList,
    getDetailProductTemplateV1,
    getTemplateStokProduksiList,
    createProductionStockV1,
    updateProductionStockV1,
    deleteProductionStockV1,
    getSettingProductAndInventory,
    getDetailProductionStockV1,
} from '../../../../data/inventories';
import { getOutletV3 } from '../../../../data/outlets';
import { toMoment } from './helper';
import {
    itemType,
    statusEnum,
    toastStyle,
    nameSpaceTranslation as ns,
} from '../settings/constant';
import { getProductInvetoryLimitation } from '../../../../data/settings';

export const fetchStockProduksi = async (stateTable = {}, contextData, onSuccessfully) => {
    const {
        calendar, filterBranch, searchKeyword, showProgress, hideProgress, addToast, t,
    } = contextData;

    const {
        pageSize, pageIndex = 0, sortAccessor = 'date', sortDirection = 'desc',
    } = stateTable;

    const payload = {
        limit: stateTable.pageLimit || pageSize || 10,
        page: pageIndex + 1,
        start: toMoment(calendar.start).format('YYYY-MM-DD'),
        end: toMoment(calendar.end).format('YYYY-MM-DD'),
        ...(sortAccessor && sortDirection) && {
            order_type: sortDirection.toUpperCase(),
            order_by: sortAccessor,
        },
        ...(searchKeyword !== '') && { search: searchKeyword },
        id_outlet: filterBranch ? Number(filterBranch) : null,
    };

    try {
        showProgress();
        const { data, meta: { total, current_page: currentPage } } = await getStokProduksiList(payload);

        onSuccessfully({
            tableData: data,
            totalData: total,
            currentPage: currentPage - 1,
        });
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: ns.translation }),
            description: (
                <Box css={toastStyle.md}>
                    {t('toast.failed.fetchProductionStock')}
                </Box>
            ),
            variant: 'failed',
            preventDuplicate: true,
        });
    } finally {
        hideProgress();
    }
};

export const fetchOutlet = async (contextData, onSuccessfully) => {
    const {
        showProgress, hideProgress, addToast, t,
    } = contextData;

    try {
        showProgress();
        const { data } = await getOutletV3();
        onSuccessfully(data);
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: ns.translation }),
            description: (
                <Box css={toastStyle.md}>
                    {t('toast.failed.fetchOutlet')}
                </Box>
            ),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const fetchTemplateProduksiDetail = async (contextData, onSuccessfully, id, outletId) => {
    const {
        showProgress, hideProgress, addToast, t,
    } = contextData;

    const payload = { id };
    if (outletId) {
        payload.id_outlet = outletId;
    }

    try {
        showProgress();
        // const { data: { status, is_template_can_change_stock } } = await getTemplateStokProduksiDetailV15(payload); // OLD API VERSION
        const response = await getDetailProductTemplateV1(payload);
        onSuccessfully({
            stokSumberList: productSelector(response.data.material_details),
            stokHasilList: productSelector(response.data.finished_product_details),
            status: response.status,
            settingProductionStock: String(response.data.is_template_can_change_stock),
        });
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: ns.translation }),
            description: (
                <Box css={toastStyle.md}>
                    {t('toast.failed.fetchTemplateDetail')}
                </Box>
            ),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const payloadStokSumber = (isCogs, idOutlet) => ({
    ...(idOutlet && { id_outlet: idOutlet }),
    per_page: 10,
    ...(isCogs) && { cogs: 1 },
    item_type: itemType.RAW_MATERIAL,
    path: ['stock', 'popup', 'multiunit'].join(','),
});

export const payloadStokHasil = (isCogs, idOutlet) => ({
    ...(idOutlet && { id_outlet: idOutlet }),
    per_page: 10,
    ...(isCogs) && { cogs: 1 },
    path: ['multiunit', 'hide-serialnumber', 'hide-batchnumber'].join(','),
    item_type: itemType.ALL,
});

const productSelector = (products) => {
    if (Array.isArray(products)) {
        return products.map(item => ({
            harga: String(item.price),
            id: String(item.id),
            item_name: item.item_name,
            qty: String(item.qty),
            sku: item.sku,
            unit_name: item.unit_name,
            unit_no: item.unit_no,
            variants: item.variants,
            stock: item.stock_system,
            temp_stock: item.stock_system || 0,
        }));
    }
    return [];
};

export const fetchStockProduksiDetail = async (contextData, onSuccessfully, payload, form = {}) => {
    const {
        showProgress, hideProgress, addToast, t,
    } = contextData;

    try {
        showProgress();

        const response = await getDetailProductionStockV1(payload);
        const stockSource = response.data.material_details;
        const stockFinished = response.data.finished_product_details;

        /* If using endpoint mayang */
        // response = await getStokProduksiDetail(payload);
        // stockSource = response.data.detail_bahan;
        // stockFinished = response.data.detail_produk_jadi;
        // }

        onSuccessfully({
            stokSumberList: productSelector(stockSource),
            stokHasilList: productSelector(stockFinished),
        });
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: ns.translation }),
            description: (
                <Box css={toastStyle.md}>
                    {t('toast.failed.fetchProductionStockDetail')}
                </Box>
            ),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const postStokProduksi = async (contextData, onSuccessfully, payload) => {
    const {
        showProgress, hideProgress, addToast, t, lang,
    } = contextData;

    try {
        showProgress();
        const { error, message } = await createProductionStockV1(payload);
        if (error !== null) {
            throw new Error(message);
        }

        onSuccessfully();
    } catch (err) {
        addToast({
            title: t('toast.error', { ns: ns.translation }),
            description: catchError(err),
            variant: 'failed',
            values: {
                item_name: err.cause && err.cause.data ? joinListString(err.cause.data.item_name || [], lang, lang === 'en') : '',
                menu: t('menu', 'Produksi Stok'),
            },
        });
    } finally {
        hideProgress();
    }
};

export const editStokProduksi = async (contextData, onSuccessfully, payload) => {
    const {
        showProgress, hideProgress, addToast, t,
    } = contextData;

    try {
        showProgress();
        const { error, message } = await updateProductionStockV1(payload);
        if (error !== null) {
            throw new Error(message);
        }

        onSuccessfully();
    } catch (err) {
        addToast({
            title: t('toast.error', { ns: ns.translation }),
            description: catchError(err),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const removeStokProduksi = async (contextData, onSuccessfully, { nomor, ...payload }) => {
    const {
        showProgress, hideProgress, addToast, t,
    } = contextData;

    try {
        showProgress();
        const { error, message } = await deleteProductionStockV1(payload);
        if (error !== null) {
            throw new Error(message);
        }

        onSuccessfully();
    } catch (err) {
        addToast({
            title: t('toast.error', { ns: ns.translation }),
            description: (
                <Box css={toastStyle.xl}>
                    <Trans t={t} i18nKey="toast.failed.removeProductionStock">
                        {{ number: nomor }}
                    </Trans>
                </Box>
            ),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const fetchSettingProductAndInventory = async (propsData, onSuccessfully) => {
    const { showProgress, hideProgress, addToast } = propsData;

    try {
        showProgress();
        const { data } = await getSettingProductAndInventory();
        const { data: inventoryLimitationData } = await getProductInvetoryLimitation();

        let isHaveValidationStockReachMinimum = false;
        let isHaveValidationOutOfStock = false;
        let isHaveValidationStockIsMinus = false;

        let isHaveValidationOutOfStockPOS = false;
        let isHaveValidationOutOfStockSendStock = false;
        let isHaveValidationOutOfStockStockProduction = false;

        if (data) {
            const { raw_material_stock_monitoring: { stock_reach_minimum, out_of_stock, stock_is_minus } } = data;

            isHaveValidationStockReachMinimum = Boolean(stock_reach_minimum.status) && stock_reach_minimum.components.includes('stock_production');
            isHaveValidationOutOfStock = Boolean(out_of_stock.status) && out_of_stock.components.includes('stock_production');
            isHaveValidationStockIsMinus = Boolean(stock_is_minus.status) && stock_is_minus.components.includes('stock_production');
        }

        if (inventoryLimitationData) {
            const { pos_sales_and_invoice, send_stock, stock_production } = inventoryLimitationData;
            isHaveValidationOutOfStockPOS = pos_sales_and_invoice === 1;
            isHaveValidationOutOfStockSendStock = send_stock === 1;
            isHaveValidationOutOfStockStockProduction = stock_production === 1;
        }

        onSuccessfully({
            isHaveValidationStockReachMinimum,
            isHaveValidationOutOfStock,
            isHaveValidationStockIsMinus,
            isHaveValidationOutOfStockPOS,
            isHaveValidationOutOfStockSendStock,
            isHaveValidationOutOfStockStockProduction,
        });
    } catch (err) {
        addToast({
            title: t('toast.error', { ns: ns.translation }),
            description: catchError(err),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};
