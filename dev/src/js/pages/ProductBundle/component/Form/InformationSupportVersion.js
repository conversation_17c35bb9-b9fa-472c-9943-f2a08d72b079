import React from 'react';
import {
    <PERSON>,
    <PERSON>lex,
    Banner,
    <PERSON>,
    BannerDescription,
} from '@majoo-ui/react';
import { useTranslationHook } from '../../utils/lang.utils';

const InformationSupportVersion = () => {
    const { LANG_DATA } = useTranslationHook();

    return (
        <Banner variant="info">
            <Flex direction="column" gap={2} css={{ width: '100%' }}>
                <Box css={{ marginBottom: 4 }}>
                    <BannerDescription css={{ whiteSpace: 'normal' }}>
                        {LANG_DATA.FORM.INFORMATIONSUPPORTVERSION.DESCRIPTION}
                    </BannerDescription>
                </Box>
                <Flex justify="end" css={{ width: '100%' }}>
                    <Link css={{ color: '$primary500' }} href={`${process.env.PORTAL_BASE_URL}/hubungi-kami`} target="_blank">
                        {LANG_DATA.FORM.INFORMATIONSUPPORTVERSION.CONTACT}
                    </Link>
                </Flex>
            </Flex>
        </Banner>
    );
};

export default InformationSupportVersion;
