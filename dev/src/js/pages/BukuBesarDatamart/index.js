import React, {
    useEffect,
    useState,
    useContext,
    useMemo,
} from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import {
    Flex,
    Paragraph,
    Paper,
    Box,
    Text,
    Heading,
    Button,
    Separator,
    ToastContext,
    InputDatePicker,
    InputSelectTag,
    Pagination,
    Table,
    AlertDialog,
} from '@majoo-ui/react';
import moment from 'moment';
import { isEqual } from 'lodash';
import { Trans, useTranslation } from 'react-i18next';
import { toMoment } from '../CashBook/TransferList/utils/helper';
import { getListAkunReportDatamart } from '../../data/bukuBesar';
import { requestExportReportDatamart, requestExportReportDatamartV1 } from '../../data/accounting';
import { catchError, resetDateRangeHelper } from '../../utils/helper';
import CoreHOC from '../../core/CoreHOC';
import InfiniteScrollTable from './components/InfiniteScrollTable';
import { tableMetaBukuBesar } from './utils/utils';
import { getOutletData } from '../../data/outlets/selectors';
import { useMediaQuery } from '../../utils/useMediaQuery';
import { FavoriteWrapper, TooltipGuidance } from '../../components/retina';
import ExportReport from '../../components/retina/export/ExportReport';
import { ns } from './variables';

const BukuBesarDatamart = (props) => {
    const {
        showProgress,
        hideProgress,
        calendar,
        filterBranch,
        assignCalendar,
        outletList,
        addNotification,
    } = props;

    const { t, i18n: { language } } = useTranslation([ns.report, ns.generalLedger, ns.translation], { useSuspense: false });
    const menuName = language === 'en' ? 'General Ledger' : 'Buku Besar';
    const [loading, setLoading] = useState(true);
    const [optionAccount, setOptionAccount] = useState([]);
    const [valueOptionAccount, setValueOptionAccount] = useState([]);
    const [totalData, setTotalData] = useState(0);
    const [tableData, setTableData] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [limit, setLimit] = useState(2);
    const [prevFilter, setPrevFilter] = useState({});
    const [dialogInfoExport, setDialogInfoExport] = useState({
        isDialog: false,
        state: {
            title: '',
            description: '',
            btnCloseOnly: false,
        },
    });

    const outlet = useMemo(() => outletList.find(data => Number(data.id_cabang) === Number(filterBranch), [outletList, filterBranch]));

    const isMobile = useMediaQuery('(max-width: 767px)');
    const { addToast } = useContext(ToastContext);

    const handleChangeOption = (data) => {
        setValueOptionAccount(data);
    };

    const handlePageChange = async (incomingPage = 1, incomingLimit = limit) => {
        try {
            setLoading(true);
            const payload = {
                outlet_id: filterBranch || 0,
                page: incomingLimit !== limit ? 1 : incomingPage,
                start_date: moment(calendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                end_date: moment(calendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                account_no_list: valueOptionAccount.length === 0 ? '' : valueOptionAccount.join(','),
                per_page: incomingLimit,
            };
            const { data: dataPage, meta: { page, total } } = await getListAkunReportDatamart(payload);
            setTableData(dataPage);
            setTotalData(total);
            setLimit(incomingLimit);
            setCurrentPage(page);
        } catch (error) {
            addToast({
                title: t('translation:toast.error'),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            setLoading(false);
        }
    };

    const fetchAccountList = async () => {
        try {
            setLoading(true);
            const payload = {
                outlet_id: filterBranch,
                start_date: moment(calendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                end_date: moment(calendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                is_all: true,
            };
            const { data } = await getListAkunReportDatamart(payload);

            const selectAll = data.map(item => item.account.no);
            const listAccountOpt = data.map(item => ({
                id: item.account.no,
                name: `(${item.account.code}) ${item.account.name}`,
            }));
            setOptionAccount(listAccountOpt);
            handleChangeOption(selectAll);
            if (data.length === 0) {
                setTotalData(0);
                setTableData([]);
                setLoading(false);
            }
        } catch (error) {
            setLoading(false);
            if (Number(error.status) === 524) {
                addToast({
                    title: t('translation:toast.error'),
                    description: (
                        <Trans t={t} i18nKey="toast.failed.fetchGeneralLedger" ns={ns.generalLedger}>
                            Data terlalu besar, silakan kurangi
                            <br />
                            periode tanggal atau filter peroutlet
                        </Trans>
                    ),
                    variant: 'failed',
                });
            } else {
                addToast({
                    title: t('translation:toast.error'),
                    description: catchError(error),
                    variant: 'failed',
                });
            }
        }
    };

    const handleChangeDateRange = (value) => {
        const { newStartDate, newEndDate, isForceReset } = resetDateRangeHelper(value[0], value[1], 90);
        if (isForceReset) {
            addToast({
                title: t('toast.errorMaxDate', 'Terjadi Kesalahan!', { ns: 'translation' }),
                description: t('toast.errorMaxDateRange', 'Maksimum rentang waktu yang dapat dipilih: 3 bulan', { ns: 'translation' }),
                variant: 'pending',
            });
            assignCalendar(newStartDate, newEndDate);
        } else {
            assignCalendar(value[0], value[1]);
        }
    };

    const downloadLaporan = async (type = 'xlsx') => {
        setLoading(true);

        const payload = {
            report_name: 'accounting_ledger',
            report_format: type,
            parameters: {
                accounting_date_min: moment(calendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                accounting_date_max: moment(calendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                ...(valueOptionAccount.length && ({ account_no_list: valueOptionAccount })),
                ...(filterBranch && ({ outlet_id: Number(filterBranch) || 0 })),
            },
        };

        try {
            await requestExportReportDatamartV1(payload);

            setDialogInfoExport({
                isDialog: true,
                state: {
                    title: (
                        <Trans
                            t={t}
                            i18nKey="dialog.labelExport"
                            defaults="Ekspor Laporan Buku Besar"
                            values={{ menu: menuName }}
                        />
                    ),
                    description: (
                        <Trans
                            t={t}
                            i18nKey="dialog.descriptionExport"
                            defaults="Mohon menunggu, sistem sedang memproses Laporan <strong>Buku Besar</strong>. Anda akan menerima pemberitahuan notifikasi dan email saat data siap untuk diunduh."
                            components={{ strong: <b /> }}
                            values={{ menu: menuName }}
                        />
                    ),
                    btnCloseOnly: false,
                },
            });
        } catch (error) {
            if (error.cause && error.cause.status && error.cause.status.code) {
                if (Number(error.cause.status.code) === 422002) {
                    setDialogInfoExport({
                        isDialog: true,
                        state: {
                            title: (
                                <Trans
                                    t={t}
                                    i18nKey="label.exportReport"
                                    defaults="Ekspor Laporan"
                                />
                            ),
                            description: error.cause.status.message,
                            btnCloseOnly: true,
                        },
                    });
                    return;
                }
            }

            const message = catchError(error);
            addToast({
                title: t('translation:toast.error'),
                description: message,
                variant: 'failed',
                position: 'top-right',
            });
        } finally {
            setLoading(false);
        }
    };

    const responsivePaper = {
        '@sm': {
            padding: 0,
            backgroundColor: 'transparent',
            boxShadow: 'none',
        },
        '@md': {
            padding: '$spacing-05 0px',
            backgroundColor: '$white',
            boxShadow: '0px 2px 12px #********',
        },
    };

    useEffect(() => {
        if (loading) {
            showProgress();
        } else {
            hideProgress();
        }
    }, [loading]);

    useEffect(() => {
        if (optionAccount.length) handlePageChange();
    }, [valueOptionAccount]);

    useEffect(() => {
        const filter = { filterBranch, calendar };
        if (!isEqual(prevFilter, filter)) {
            fetchAccountList();
            setPrevFilter({ filterBranch, calendar });
        }
    }, [filterBranch, calendar]);

    return (
        <Flex
            direction="column"
            css={{ gap: '$comfortable', '@md': { marginBottom: '28px' } }}
        >
            <Paper css={responsivePaper}>
                <Box css={{ '@sm': { height: 'auto' }, '@md': { height: '100%' } }}>
                    <Flex justify="between" css={{ '@md': { padding: '$spacing-03 $spacing-06 $spacing-05 $spacing-06' } }}>
                        <Box>
                            <Flex gap={3} align="center">
                                <Heading
                                    as="h1"
                                    heading="pageTitle"
                                    css={{ marginBottom: '$spacing-02', '@md': { marginBottom: '12px' } }}
                                >
                                    {t('titleSection.title', 'Laporan Buku Besar', { ns: ns.generalLedger })}
                                </Heading>
                                <Text as="span" css={{ fontSize: '14px', fontWeight: '300' }}> ({t('label.inRupiah', { ns: 'translation', defaultValue: 'dalam Rupiah' })})</Text>
                                <TooltipGuidance />
                            </Flex>
                            <Flex align="center" css={{ gap: '$spacing-03', marginTop: '$spacing-02' }}>
                                <Text as="b" css={{ color: '$textSecondary' }}>
                                    {!outlet ? t('label.allOutlets', { ns: 'translation', defaultValue: 'Semua Outlet' }) : outlet.cabang_name}
                                </Text>
                            </Flex>
                        </Box>
                        {!isMobile && (
                            <ExportReport
                                onExport={downloadLaporan}
                                calendar={calendar}
                                title={t('titleSection.title', 'Laporan Buku Besar', { ns: ns.generalLedger })}
                                type="multi"
                                css={{
                                    '@sm': {
                                        display: 'none',
                                    },
                                    '@md': {
                                        display: 'inline-flex',
                                    },
                                }}
                            />
                        )}
                    </Flex>
                </Box>
                <Box css={{ padding: '0px $spacing-06' }}>
                    <Separator
                        css={{ '@sm': { display: 'none' }, '@md': { display: 'block' } }}
                    />
                </Box>
                <Box css={{ margin: '$spacing-03 $spacing-05' }}>
                    <Flex align="start" css={{ '@sm': { flexDirection: 'column' }, '@md': { flexDirection: 'row', gap: '16px' } }}>
                        <InputDatePicker
                            css={{
                                width: '258px',
                                '@sm': {
                                    width: '100%',
                                    padding: '12px 0',
                                },
                                '@md': {
                                    width: '258px',
                                },
                            }}
                            type="advance"
                            onChange={handleChangeDateRange}
                            date={[toMoment(calendar.start).toDate(), toMoment(calendar.end).toDate()]}
                        />
                        <InputSelectTag
                            key={String(valueOptionAccount.length)}
                            showSelectAll
                            onChange={handleChangeOption}
                            placeholder={t('filterSection.selectAccount', 'Pilih Nama Akun', { ns: ns.generalLedger })}
                            size="sm"
                            css={{
                                width: '290px',
                                '@sm': {
                                    width: '100%',
                                    padding: '12px 0',
                                },
                                '@md': {
                                    width: '258px',
                                },
                            }}
                            value={valueOptionAccount}
                            option={optionAccount}
                            selectAllLabel={t('filterSection.selectAll', 'Pilih Semua', { ns: ns.generalLedger })}
                            showCounter
                            labelCounter={t('filterSection.labelCounter', 'Akun Terpilih', { ns: ns.generalLedger, count: valueOptionAccount.length })}
                        />
                        {isMobile && (
                            <ExportReport
                                onExport={downloadLaporan}
                                calendar={calendar}
                                title={t('titleSection.title', 'Laporan Buku Besar', { ns: ns.generalLedger })}
                                type="multi"
                                css={{
                                    '@sm': {
                                        display: 'inline-flex',
                                        width: '100%',
                                    },
                                    '@md': {
                                        display: 'none',
                                    },
                                }}
                            />
                        )}
                    </Flex>
                </Box>
                <Box css={{ padding: '0px $spacing-05' }}>
                    <Separator
                        css={{ '@sm': { display: 'none' }, '@md': { display: 'block' } }}
                    />
                </Box>

                {/* Empty data table */}
                {!totalData && (
                    <Box
                        css={{
                            padding: '0px $spacing-05',
                            '& > div > div:nth-child(2)': {
                                display: !totalData ? 'block' : 'none',
                            },
                        }}
                    >
                        <Table
                            columns={tableMetaBukuBesar(t)}
                            data={[]}
                            totalData={totalData}
                            isLoading={loading}
                            hidePagination
                            hideDataInfo
                            css={{ overflowX: 'hidden' }}
                        />
                    </Box>
                )}

                {/* Content Infinite Scroll Table */}
                {tableData.map((item, index) => (
                    <React.Fragment>
                        <Box css={{ padding: '0px $spacing-05' }}>
                            <InfiniteScrollTable
                                t={t}
                                key={item.account.no}
                                item={item}
                                calendar={calendar}
                                filterBranch={filterBranch}
                                indexTable={index}
                            />
                        </Box>
                        {index + 1 !== tableData.length && (
                            <Separator css={{ margin: '$spacing-06 0px' }} />
                        )}
                    </React.Fragment>
                ))}

                {/* Custom Pagination */}
                {!!totalData && (
                    <Pagination
                        currentPage={currentPage}
                        limit={limit}
                        onLimitChange={incomingLimit => handlePageChange(currentPage, incomingLimit)}
                        onPageChange={incomingPage => handlePageChange(incomingPage, limit)}
                        totalData={totalData}
                        rowsPerPageOptions={[1, 2, 3, 5, 10]}
                        css={{
                            padding: '$spacing-04 0',
                            '@md': { padding: '$spacing-06 $spacing-07' },
                        }}
                    />
                )}
            </Paper>
            <AlertDialog
                isMobile={isMobile}
                onConfirm={() => {
                    setDialogInfoExport(prev => ({ ...prev, isDialog: false }));
                    addNotification({
                        title: t('toast.success', { ns: 'translation', defaultValue: 'Berhasil!' }),
                        message: (
                            <Trans
                                t={t}
                                i18nKey="toast.successRequestExport"
                                components={{ strong: <b /> }}
                                values={{ menu: menuName }}
                            >
                                Laporan
                                {' '}
                                <strong>Buku Besar</strong>
                                {' '}
                                dalam proses ekspor
                            </Trans>
                        ),
                        level: 'success',
                    });
                }}
                onCancel={() => setDialogInfoExport(prev => ({ ...prev, isDialog: false }))}
                open={dialogInfoExport.isDialog}
                title={dialogInfoExport.state.title}
                description={dialogInfoExport.state.description}
                labelConfirm={(
                    <Trans
                        t={t}
                        ns={ns.translation}
                        i18nKey="label.confirm"
                        defaults="Oke, Mengerti"
                    />
                )}
                labelCancel={(
                    <Trans
                        t={t}
                        ns={ns.translation}
                        i18nKey="label.close"
                        defaults="Tutup"
                    />
                )}
                css={{
                    width: isMobile ? 'unset' : '422px',
                }}
                actionButtonProps={{ size: 'md' }}
                cancelButtonProps={{ size: 'md' }}
                singleButton={!dialogInfoExport.state.btnCloseOnly}
                hideActionButton={dialogInfoExport.state.btnCloseOnly}
            />
        </Flex>
    );
};

BukuBesarDatamart.propTypes = {
    addNotification: PropTypes.func,
    t: PropTypes.func,
    i18n: PropTypes.shape({
        language: PropTypes.string,
    }),
    filterBranch: PropTypes.string,
    showProgress: PropTypes.func,
    hideProgress: PropTypes.func,
    assignCalendar: PropTypes.func,
    calendar: PropTypes.shape({
        start: PropTypes.string,
        end: PropTypes.string,
    }).isRequired,
    router: PropTypes.shape({
        push: PropTypes.func,
    }),
    location: PropTypes.shape({
        pathname: PropTypes.string,
    }),
    notificationSystem: PropTypes.shape({
        addNotification: PropTypes.func,
    }),
    outletList: PropTypes.arrayOf(PropTypes.object),
};

BukuBesarDatamart.defaultProps = {
    addNotification: () => { },
    t: () => { },
    i18n: { language: 'id' },
    filterBranch: '',
    showProgress: () => { },
    hideProgress: () => { },
    assignCalendar: () => { },
    router: null,
    location: null,
    notificationSystem: null,
    outletList: [],
};

const mapStateToProps = state => ({
    namaUser: state.user.profile.user_name,
    listCabang: state.branch.list,
    outletList: getOutletData(),
});

export default connect(mapStateToProps, null)(CoreHOC(BukuBesarDatamart));
