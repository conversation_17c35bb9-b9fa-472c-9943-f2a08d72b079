import React from 'react';
import { Paper, Separator } from '@majoo-ui/react';
import { connect } from 'react-redux';
import CoreHOC from '~/core/CoreHOC';
import { useMediaQuery } from '~/utils/useMediaQuery';
import SalesDashboardProvider from './context/SalesDashboardProvider';
import layoutsSelector from '../../data/layouts/layouts.selector';

import TitleSection from './components/TitleSection';
import FilterSection from './components/FilterSection';
import SummarySection from './components/SummarySection';
import ChartSection from './components/ChartSection';
import WidgetGroup from './components/WidgetGroup';

const SalesDashboard = (props) => {
    const isMobile = useMediaQuery('(max-width: 767px)');
    return (
        <SalesDashboardProvider parentProps={props}>
            <Paper responsive style={{ marginBottom: 20, padding: isMobile ? 0 : 20 }}>
                <TitleSection />
                <Separator css={{ mt: 10, mb: 10 }} />
                <FilterSection />
                <Separator css={{ mt: 10, mb: 10 }} />
                <SummarySection />
                <Separator css={{ mt: 10, mb: 10 }} />
                <ChartSection />
            </Paper>
            <WidgetGroup />
        </SalesDashboardProvider>
    );
};

const mapStateToProps = state => ({
    sidebarMenus: layoutsSelector.getVisibleNav(state.layouts),
});

export default connect(mapStateToProps)(CoreHOC(SalesDashboard));
