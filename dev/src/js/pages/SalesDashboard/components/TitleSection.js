import React, { useContext } from 'react';
import { Box, Flex, Heading } from '@majoo-ui/react';
import { BannerText, FavoriteWrapper } from '~/components/retina';

import SalesDashboardContext from '../context/SalesDashboardContext';

const TitleSection = () => {
    const { t, lastUpdate } = useContext(SalesDashboardContext);

    return (
        <Flex
            justify="between"
            css={{
                flexDirection: 'column',
                alignItems: 'start',
                '@md': {
                    alignItems: 'center',
                    flexDirection: 'row',
                }
            }}
            gap={3}
        > 
            <Box>
                <FavoriteWrapper>
                    <Heading
                        as="h1"
                        heading="pageTitle"
                    >
                        {t('title', 'Penjualan')}
                    </Heading>
                </FavoriteWrapper>
                <Box css={{ fontSize: 12 }}>
                    {`${t('lastUpdate', 'Diperbarui')} ${lastUpdate}`}
                </Box>
                <BannerText css={{ mb: 0 }} />
            </Box>
        </Flex>
    )
};

export default TitleSection;
