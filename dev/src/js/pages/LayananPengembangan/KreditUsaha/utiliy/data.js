import moment from 'moment';
import update from 'immutability-helper';
import { get } from 'lodash';

import { STATUS_LIST } from '../config/modalwirausaha.config';

export const STATUS_SAVE = {
    DRAFT: 0,
    SAVE: 39,
};

const setPayloadBTN = (res, data) => {
    const newData = update(data, {
        agreement: { $set: res.agreement || false },
        kreditName: { $set: res.kredit_provider || '' },
        kreditIdProvider: { $set: res.kredit_id_provider || 0 },
        kreditBunga: { $set: res.kredit_bunga || 0 },
        kreditAngsuran: { $set: res.kredit_angsuran || 0 },
        kreditTenor: { $set: res.kredit_tenor || 0 },
        kreditTotal: { $set: res.kredit_total || 0 },
        statusPermohonan: { $set: res.status_permohonan || STATUS_LIST.DRAFT },
        dataPemohon: {
            pemohonReferensiSales: { $set: res.pemohon_referensi_sales || '' },
            pemohonCabangKreditur: { $set: res.cabang_kreditur || '' },
            pemohonNama: { $set: res.pemohon_nama || '' },
            pemohonKtp: { $set: String(res.pemohon_ktp) || '' },
            pemohonTempatLahir: { $set: res.pemohon_tempat_lahir || '' },
            pemohonTglLahir: { $set: res.pemohon_tgl_lahir !== '0000-00-00' ? moment(res.pemohon_tgl_lahir, 'YYYY-MM-DD').format('DD/MM/YYYY') : moment().subtract(20, 'years').format('DD/MM/YYYY') },
            pemohonKelamin: { $set: res.pemohon_kelamin || '' },
            pemohonStatusPernikahan: { $set: Number(res.pemohon_status) === 1 || Number(res.pemohon_status) === 0 ? String(res.pemohon_status) : '' },
            pemohonNoTlpHp: { $set: res.pemohon_no_tlp_hp || '' },
            pemohonSurel: { $set: res.pemohon_email || '' },
            pemohonAlamat: { $set: res.domisili_alamat || '' },
            pemohonProvinsi: { $set: res.domisili_provinsi || '' },
            pemohonKota: { $set: res.domisili_kota || '' },
            pemohonIdProvinsi: { $set: String(res.domisili_id_provinsi) || '' },
            pemohonIdKota: { $set: String(res.domisili_id_kota) || '' },
            pemohonKecamatan: { $set: res.domisili_kecamatan || '' },
            pemohonDesa: { $set: res.domisili_kelurahan || '' },
            pemohonRt: { $set: res.domisili_rt || '' },
            pemohonRw: { $set: res.domisili_rw || '' },
            pemohonKodePos: { $set: res.domisili_kode_pos || '' },
            pemohonStatusTempatTinggal: { $set: Number(res.domisili_kepemilikan) || 0 },
            pasanganNama: { $set: res.kontak_darurat_nama_pasangan || '' },
            pasanganKtp: { $set: res.kontak_darurat_nomor_ktp_pasangan || '' },
            pasanganTempatLahir: { $set: res.kontak_darurat_tempat_lahir_pasangan || '' },
            pasanganTglLahir: { $set: res.kontak_darurat_tanggal_lahir_pasangan && res.kontak_darurat_tanggal_lahir_pasangan !== '0000-00-00' ? moment(res.kontak_darurat_tanggal_lahir_pasangan, 'YYYY-MM-DD').format('DD/MM/YYYY') : moment().subtract(20, 'years').format('DD/MM/YYYY') },
            pasanganNoHp: { $set: res.kontak_darurat_no_tlp || '' },
        },
        dataUsaha: {
            usahaSektorUsaha: { $set: res.usaha_sektor || '' },
            usahaLamaUsaha: { $set: String(res.usaha_duration) || '' },
            usahaOmsetPerBulan: { $set: String(res.usaha_omset) || '' },
            usahaNamaTempatUsaha: { $set: res.usaha_nama || '' },
            usahaAlamatUsaha: { $set: res.usaha_alamat || '' },
            usahaIdProvinsi: { $set: String(res.usaha_id_provinsi === 0 ? '' : res.usaha_id_provinsi) || '' },
            usahaIdKota: { $set: String(res.usaha_id_kota === 0 ? '' : res.usaha_id_kota) || '' },
            usahaProvinsi: { $set: res.usaha_provinsi || '' },
            usahaKota: { $set: res.usaha_kota || '' },
            usahaKecamatan: { $set: res.usaha_kecamatan || '' },
            usahaKelurahan: { $set: res.usaha_kelurahan || '' },
            usahaRt: { $set: res.usaha_rt || '' },
            usahaRw: { $set: res.usaha_rw || '' },
            usahaKodePos: { $set: res.usaha_kode_pos || '' },
            usahaTujuanPengajuan: { $set: res.kredit_tujuan || '' },
            usahaAlasanDetailPengajuan: { $set: res.kredit_tujuan_detail || '' },
            usahaStatusKepemilikanTempatUsaha: { $set: String(res.usaha_status === 0 ? '' : res.usaha_status) || '' },
        },
        dataLampiran: {
            lampiranFotoPemohon: { $set: res.lampiran_foto_pemohon || { filename: '', path: '', thumbnail: '' } },
            lampiranFotoKtpPemohon: { $set: res.lampiran_identitas || { filename: '', path: '', thumbnail: '' } },
            lampiranFotoNpwpPemohon: { $set: res.lampiran_npwp || { filename: '', path: '', thumbnail: '' } },
            lampiranFotoizinUsaha: { $set: res.lampiran_ijin_usaha || { filename: '', path: '', thumbnail: '' } },
        },
    });
    return newData;
};

const generatePayloadBTN = (data, statusSave) => {
    const {
        agreement,
        statusPermohonan,
        kreditName,
        kreditIdProvider,
        kreditBunga,
        kreditAngsuran,
        kreditTenor,
        kreditTotal,
        dataPemohon: {
            pemohonReferensiSales,
            pemohonCabangKreditur,
            pemohonNama,
            pemohonKtp,
            pemohonTempatLahir,
            pemohonTglLahir,
            pemohonKelamin,
            pemohonStatusPernikahan,
            pemohonNoTlpHp,
            pemohonSurel,
            pemohonAlamat,
            pemohonProvinsi,
            pemohonKota,
            pemohonIdProvinsi,
            pemohonIdKota,
            pemohonKecamatan,
            pemohonDesa,
            pemohonRt,
            pemohonRw,
            pemohonKodePos,
            pemohonStatusTempatTinggal,
            pasanganNama,
            pasanganKtp,
            pasanganTempatLahir,
            pasanganTglLahir,
            pasanganNoHp,
        },
        dataUsaha: {
            usahaSektorUsaha,
            usahaLamaUsaha,
            usahaOmsetPerBulan,
            usahaNamaTempatUsaha,
            usahaAlamatUsaha,
            usahaIdProvinsi,
            usahaIdKota,
            usahaProvinsi,
            usahaKota,
            usahaKecamatan,
            usahaKelurahan,
            usahaRt,
            usahaRw,
            usahaKodePos,
            usahaTujuanPengajuan,
            usahaAlasanDetailPengajuan,
            usahaStatusKepemilikanTempatUsaha,
        },
        dataLampiran: {
            lampiranFotoPemohon,
            lampiranFotoKtpPemohon,
            npwp,
            izinUsaha,
        },
    } = data;

    const payloadData = {
        agreement,
        kredit_angsuran: kreditAngsuran,
        kredit_bunga: kreditBunga,
        kredit_id_provider: kreditIdProvider,
        kredit_name: kreditName,
        kredit_tenor: kreditTenor,
        kredit_total: kreditTotal,
        cabang_kreditur: pemohonCabangKreditur,
        pemohon_referensi_sales: pemohonReferensiSales,
        pemohon_ktp: pemohonKtp,
        pemohon_nama: pemohonNama,
        pemohon_email: pemohonSurel,
        pemohon_no_tlp_hp: pemohonNoTlpHp,
        pemohon_tempat_lahir: pemohonTempatLahir,
        pemohon_tgl_lahir: moment(pemohonTglLahir, 'DD/MM/YYYY').format('YYYY-MM-DD'),
        pemohon_kelamin: pemohonKelamin,
        pemohon_status: String(pemohonStatusPernikahan),
        domisili_alamat: pemohonAlamat,
        domisili_id_kota: pemohonIdKota,
        domisili_id_provinsi: pemohonIdProvinsi,
        domisili_kota: pemohonKota,
        domisili_provinsi: pemohonProvinsi,
        domisili_kecamatan: pemohonKecamatan,
        domisili_kelurahan: pemohonDesa,
        domisili_rt: pemohonRt,
        domisili_rw: pemohonRw,
        domisili_kode_pos: pemohonKodePos,
        domisili_kepemilikan: Number(pemohonStatusTempatTinggal),
        kontak_darurat_nama_pasangan: pasanganNama,
        kontak_darurat_nomor_ktp_pasangan: pasanganKtp,
        kontak_darurat_tempat_lahir_pasangan: pasanganTempatLahir,
        kontak_darurat_tanggal_lahir_pasangan: moment(pasanganTglLahir, 'DD/MM/YYYY').format('YYYY-MM-DD'),
        kontak_darurat_no_tlp: pasanganNoHp,
        usaha_alamat: usahaAlamatUsaha,
        usaha_rt: usahaRt,
        usaha_rw: usahaRw,
        usaha_kecamatan: usahaKecamatan,
        usaha_kelurahan: usahaKelurahan,
        usaha_id_kota: usahaIdKota,
        usaha_id_provinsi: usahaIdProvinsi,
        usaha_kota: usahaKota,
        usaha_provinsi: usahaProvinsi,
        usaha_kode_pos: usahaKodePos,
        usaha_status: Number(usahaStatusKepemilikanTempatUsaha),
        usaha_duration: usahaLamaUsaha,
        usaha_nama: usahaNamaTempatUsaha,
        usaha_sektor: usahaSektorUsaha,
        usaha_omset: usahaOmsetPerBulan,
        plafon_kur: kreditTotal,
        kredit_tujuan: usahaTujuanPengajuan,
        kredit_tujuan_detail: usahaAlasanDetailPengajuan,
        lampiran_identitas: lampiranFotoKtpPemohon,
        lampiran_foto_pemohon: lampiranFotoPemohon,
        lampiran_npwp: npwp,
        lampiran_ijin_usaha: izinUsaha,
        jangka_waktu_kur: kreditTenor,
        status_permohonan: statusSave === STATUS_SAVE.DRAFT ? STATUS_SAVE.DRAFT : STATUS_SAVE.SAVE,
    };
    return payloadData;
};

const generatePayloadBNI = (data, statusSave) => {
    const {
        agreement,
        statusPermohonan,
        kreditName,
        kreditTujuan,
        kreditIdProvider,
        kreditBunga,
        kreditAngsuran,
        dataPelanggan: {
            pemohonReferensiSales,
            pemohonNama,
            pemohonNoTlpHp,
            pemohonKelamin,
            pemohonKtp,
            pemohonTglLahir,
            pemohonSurel,
        },
        dataUsaha: {
            usahaJenisBadanUsaha,
            usahaJenisSubBadanUsaha,
            pertanyaanPenghasilanPerBulan,
            kreditTotal,
            kreditTenor,
            alamatUsaha,
            provinsiUsaha,
            kotaUsaha,
            kecamatanUsaha,
            kelurahanUsaha,
            kodePosUsaha,
            description,
        },
        dataLampiran: {
            lampiranIdentitas,
            lampiranFotoOutlet,
            fotoUsahaLainnya,
        },
    } = data;

    const payloadData = {
        agreement,
        kredit_angsuran: kreditAngsuran,
        kredit_bunga: kreditBunga,
        kredit_id_provider: kreditIdProvider,
        kredit_name: kreditName,
        kredit_tenor: kreditTenor,
        kredit_total: kreditTotal,
        kredit_tujuan: kreditTujuan,
        pemohon_kelamin: pemohonKelamin,
        pemohon_ktp: pemohonKtp,
        pemohon_nama: pemohonNama,
        pemohon_no_tlp_hp: pemohonNoTlpHp,
        pemohon_email: pemohonSurel,
        pemohon_referensi_sales: pemohonReferensiSales,
        pemohon_tgl_lahir: moment(pemohonTglLahir, 'DD/MM/YYYY').format('YYYY-MM-DD'),
        status_permohonan: statusSave === STATUS_SAVE.DRAFT ? STATUS_SAVE.DRAFT : STATUS_SAVE.SAVE,
        usaha_jenis_badan_usaha: usahaJenisBadanUsaha,
        usaha_jenis_sub_badan_usaha: usahaJenisSubBadanUsaha,
        pertanyaan_penghasilan_per_bulan: pertanyaanPenghasilanPerBulan,
        lampiran_identitas: lampiranIdentitas,
        lampiran_foto_outlet: lampiranFotoOutlet,
        usaha_alamat: alamatUsaha,
        usaha_provinsi: provinsiUsaha,
        usaha_kota: kotaUsaha,
        usaha_kecamatan: kecamatanUsaha,
        usaha_kelurahan: kelurahanUsaha,
        usaha_kode_pos: kodePosUsaha,
        deskripsi: description,
        foto_usaha_lainnya: [fotoUsahaLainnya],
    };
    return payloadData;
};

const getProvinsiKotaName = (listProvinsi, idProvinsi, idKota) => {
    const dataProvinsi = listProvinsi.find(x => x.id === idProvinsi);
    const dataKota = dataProvinsi !== undefined ? dataProvinsi.kota.find(x => x.id === idKota) : undefined;

    return {
        provinsiName: dataProvinsi !== undefined ? dataProvinsi.name : '',
        kotaName: dataKota !== undefined ? dataKota.name : '',
    };
};

const checkInappropriateFormat = value => !(/(?!^\s+$)^.*$/).test(value);

const getMergeRekening = (bankName, rekName, rekNumber) => (`${bankName}-${rekName}-${rekNumber}`);

const generatePayload = (form, status, listProvinsi) => {
    const {
        dataUsaha, dataPemohon, dataKredit, dataLampiran, dataRekeningUsaha,
    } = form;

    const pemohonProvinsiKota = getProvinsiKotaName(listProvinsi, dataPemohon.idProvinsi, dataPemohon.idKota);
    const usahaProvinsiKota = getProvinsiKotaName(listProvinsi, dataUsaha.idProvinsi, dataUsaha.idKota);

    return {
        pemohon_referensi_sales: dataPemohon.salesCode,
        pemohon_nama: dataPemohon.namaPemohon,
        pemohon_email: dataPemohon.email,
        pemohon_ktp: Number(dataPemohon.noKTP),
        pemohon_npwp: Number(dataPemohon.NPWP),
        pemohon_no_tlp_hp: dataPemohon.noTelp,
        pemohon_no_tlp_rumah: dataPemohon.noTelpRumah,
        pemohon_kelamin: dataPemohon.jenisKelamin,
        pemohon_status: dataPemohon.statusPernikahan,
        pemohon_ibu_kandung: '',
        pemohon_tgl_lahir: moment(dataPemohon.tglLahir, 'DD/MM/YYYY').format('YYYY-MM-DD'),
        pemohon_id_kota_lahir: 0,
        pemohon_kota_lahir: dataPemohon.kotaLahir,
        pemohon_id_provinsi_lahir: 0,
        pemohon_provinsi_lahir: dataPemohon.provinsiLahir,
        pemohon_id_negara_lahir: 0,
        pemohon_negara_lahir: dataPemohon.negaraLahir,

        domisili_alamat: dataPemohon.alamatDomisili,
        domisili_kode_pos: '',
        domisili_rt: '',
        domisili_rw: '',
        domisili_id_kota: Number(dataPemohon.idKota),
        domisili_kota: pemohonProvinsiKota.kotaName,
        domisili_id_provinsi: Number(dataPemohon.idProvinsi),
        domisili_provinsi: pemohonProvinsiKota.provinsiName,
        domisili_kecamatan: '',
        domisili_kepemilikan: dataPemohon.statusKepemilikanRumah,
        domisili_lama_menetap: '',
        domisili_masa_berlaku: moment(dataPemohon.masaBerlaku, 'DD/MM/YYYY').format('YYYY-MM-DD'),

        usaha_nama: dataUsaha.nameUsaha,
        usaha_jenis_permohonan: dataUsaha.jenisPermohonan,
        usaha_id_outlet: Number(dataUsaha.selectedOutlet),
        usaha_status: dataUsaha.statusKepemilikanUsaha,
        usaha_masa_berlaku: moment(dataUsaha.masaBerlaku, 'DD/MM/YYYY').format('YYYY-MM-DD'),
        usaha_alamat: dataUsaha.addressUsaha,
        usaha_id_kota: Number(dataUsaha.idKota),
        usaha_kota: usahaProvinsiKota.kotaName,
        usaha_id_provinsi: Number(dataUsaha.idProvinsi),
        usaha_provinsi: usahaProvinsiKota.provinsiName,
        usaha_duration: Number(dataUsaha.lamaUsaha) || 0,
        usaha_jenis_badan_usaha: dataUsaha.jenisBadanUsaha,

        kredit_name: dataKredit.providerName,
        kredit_tujuan: dataKredit.tujuanKredit,
        kredit_total: parseFloat(dataKredit.kebutuhan),
        kredit_id_provider: Number(dataKredit.providerId),
        kredit_bunga: parseFloat(dataKredit.bunga) || 0,
        kredit_tenor: parseInt(dataKredit.tenor, 10),
        kredit_angsuran: Number(dataKredit.angsuran),

        rekening_nama: dataRekeningUsaha.atasNama,
        rekening_nama_bank: dataRekeningUsaha.namaBank,
        rekening_no: dataRekeningUsaha.nomorRekening,
        rekening_cabang: dataRekeningUsaha.cabang,

        lampiran_identitas: dataLampiran.ktp,
        lampiran_npwp: dataLampiran.npwp,
        lampiran_kk: dataLampiran.kartuKeluarga,
        lampiran_identitas_pasangan: dataLampiran.ktpPasangan,
        lampiran_ijin_usaha: dataLampiran.izinUsaha,
        lampiran_foto_outlet: dataLampiran.fotoOutlet,
        lampiran_rekening_koran: dataLampiran.rekening,
        lampiran_cover_buku_tabungan: dataLampiran.fotoBukuTabungan,
        lampiran_akta_perusahaan: dataLampiran.aktaPendirianUsaha,
        lampiran_ktp_pendiri_usaha: dataLampiran.ktpPengurus,
        lampiran_npwp_perusahaan: dataLampiran.npwpPerusahaan,
        status_permohonan: status === STATUS_SAVE.DRAFT ? STATUS_SAVE.DRAFT : STATUS_SAVE.SAVE,
    };
};

const generatePayloadCommbank = (form, status, jenisUsaha, listProvinsi) => {
    const {
        dataUsaha, dataAlamatDomisili, dataPemohon, dataKredit, dataRekening, dataAlamatKtp, dataEmergencyContact,
    } = form;

    const {
        rekBankSatu, rekBankDua, rekBankTiga,
    } = dataRekening;

    const alamatDomisiliProvinsiKota = getProvinsiKotaName(listProvinsi, dataAlamatDomisili.idProvinsi, dataAlamatDomisili.idKota);
    const alamatKtpProvinsiKota = getProvinsiKotaName(listProvinsi, dataAlamatKtp.idProvinsi, dataAlamatKtp.idKota);

    const rekeningNo = getMergeRekening(rekBankSatu.bankName, rekBankSatu.rekName, rekBankSatu.rekNumber);
    const rekeningNoDua = getMergeRekening(rekBankDua.bankName, rekBankDua.rekName, rekBankDua.rekNumber);
    const rekeningNoTiga = getMergeRekening(rekBankTiga.bankName, rekBankTiga.rekName, rekBankTiga.rekNumber);

    const pemohonProvinsiKota = getProvinsiKotaName(listProvinsi, dataPemohon.idProvinsi, dataPemohon.idKota);
    const usahaProvinsiKota = getProvinsiKotaName(listProvinsi, dataUsaha.idProvinsi, dataUsaha.idKota);

    return {
        pemohon_referensi_sales: dataPemohon.salesCode,
        pemohon_nama: dataPemohon.namaPemohon,
        pemohon_email: dataPemohon.email,
        pemohon_ktp: Number(dataPemohon.noKTP),
        pemohon_npwp: Number(dataPemohon.NPWP),
        pemohon_no_tlp_hp: dataPemohon.noTelp,
        pemohon_no_tlp_rumah: dataPemohon.noTelpRumah,
        pemohon_kelamin: dataPemohon.jenisKelamin,
        pemohon_status: dataPemohon.statusPernikahan,
        pemohon_ibu_kandung: dataPemohon.namaIbu,
        pemohon_tgl_lahir: moment(dataPemohon.tglLahir, 'DD/MM/YYYY').format('YYYY-MM-DD'),
        pemohon_kota_lahir: dataPemohon.kotaLahir,

        domisili_alamat: dataAlamatDomisili.alamat,
        domisili_kode_pos: dataAlamatDomisili.kodePos,
        domisili_rt: dataAlamatDomisili.RT,
        domisili_rw: dataAlamatDomisili.RW,
        domisili_id_kota: Number(dataAlamatDomisili.idKota) || 0,
        domisili_kota: alamatDomisiliProvinsiKota.kotaName,
        domisili_id_provinsi: Number(dataAlamatDomisili.idProvinsi) || 0,
        domisili_provinsi: alamatDomisiliProvinsiKota.provinsiName,
        domisili_kecamatan: dataAlamatDomisili.kecamatan,
        domisili_kepemilikan: dataAlamatDomisili.kepemilikan,
        domisili_lama_menetap: Number(dataAlamatDomisili.lamaMenetap),

        ktp_alamat: dataAlamatKtp.alamat,
        ktp_kode_pos: dataAlamatKtp.kodePos,
        ktp_rt: dataAlamatKtp.RT,
        ktp_rw: dataAlamatKtp.RW,
        ktp_id_kota: Number(dataAlamatKtp.idKota) || 0,
        ktp_kota: alamatKtpProvinsiKota.kotaName,
        ktp_id_provinsi: Number(dataAlamatKtp.idProvinsi) || 0,
        ktp_provinsi: alamatKtpProvinsiKota.provinsiName,
        ktp_kecamatan: dataAlamatKtp.kecamatan,
        ktp_kepemilikan: dataAlamatKtp.kepemilikan,
        ktp_lama_menetap: Number(dataAlamatKtp.lamaMenetap),

        kontak_darurat_nama: dataEmergencyContact.name,
        kontak_darurat_tempat_lahir: dataEmergencyContact.birthPlace,
        kontak_darurat_tgl_lahir: moment(dataEmergencyContact.tglLahir, 'DD/MM/YYYY').format('YYYY-MM-DD'),
        kontak_darurat_ibu_kandung: dataEmergencyContact.ibuKandung,
        kontak_darurat_no_tlp: dataEmergencyContact.noTlp,
        kontak_darurat_status: dataEmergencyContact.statusKerabat,

        usaha_nama: dataUsaha.nameUsaha,
        usaha_domain: dataUsaha.domainStore,
        usaha_kode: Number(dataUsaha.sektorUsaha),
        usaha_sektor: Number(dataUsaha.sektorUsaha) !== 0 ? get(jenisUsaha.find(x => Number(x.id_jenis_usaha) === Number(dataUsaha.sektorUsaha)), 'jenis_usaha_name') : '',
        usaha_sektor_ekonomi: dataUsaha.sektorEkonomi,
        usaha_alamat: dataUsaha.addressUsaha,
        usaha_id_kota: Number(dataUsaha.idKota) || 0,
        usaha_kota: usahaProvinsiKota.kotaName,
        usaha_id_provinsi: Number(dataUsaha.idProvinsi) || 0,
        usaha_provinsi: usahaProvinsiKota.provinsiName,
        usaha_rt: dataUsaha.rtUsaha,
        usaha_rw: dataUsaha.rwUsaha,
        usaha_kecamatan: dataUsaha.kecamatanUsaha,
        usaha_kode_pos: dataUsaha.kodePosUsaha,
        usaha_jenis_badan_usaha: dataUsaha.jenisBadanUsaha,
        usaha_asal_pengirimian: dataUsaha.asalPengiriman,
        usaha_tgl_mulai_usaha: '',
        usaha_tgl_berdiri: '',
        usaha_duration: 0,

        pertanyaan_apakah_online: String(dataUsaha.sumberPendapatan),
        pertanyaan_pekerjaan: dataUsaha.jobs,
        pertanyaan_tempat_perkerjaan: dataUsaha.workPlace,
        pertanyaan_penghasilan_per_bulan: parseFloat(dataUsaha.penghasilanBulan) || 0,
        pertanyaan_pinjaman_yang_dimiliki: dataUsaha.pinjamanDimiliki,
        pertanyaan_total_pinjaman: parseFloat(dataUsaha.totalPinjaman) || 0,
        pertanyaan_cicilan_pinjaman_perbulan: parseFloat(dataUsaha.totalCicilan) || 0,
        pertanyaan_sisa_pinjaman: parseFloat(dataUsaha.totalNominalSisa) || 0,

        kredit_name: dataKredit.providerName,
        kredit_tujuan: dataKredit.tujuanKredit,
        kredit_total: parseFloat(dataKredit.kebutuhan),
        kredit_id_provider: Number(dataKredit.providerId),
        kredit_bunga: parseFloat(dataKredit.bunga) || 0,
        kredit_tenor: parseInt(dataKredit.tenor, 10),
        kredit_angsuran: Number(dataKredit.angsuran),

        rekening_no: rekeningNo,
        rekening_no_dua: rekeningNoDua,
        rekening_no_tiga: rekeningNoTiga,
        status_permohonan: status === STATUS_SAVE.DRAFT ? STATUS_SAVE.DRAFT : STATUS_SAVE.SAVE,
    };
};

const generatePayloadBRI = (form, status) => {
    const {
        dataPemohon, dataKredit, dataUsaha, dataRekeningUsaha, dataLampiran,
    } = form;


    return {
        biaya_rumah_tangga: dataUsaha.biayaRumahTangga,
        biaya_usaha: dataUsaha.biayaUsaha,

        domisili_id_provinsi: 0,
        domisili_kecamatan: dataPemohon.kecamatanDomisili,
        domisili_kelurahan: dataPemohon.kelurahanDomisili,
        domisili_kepemilikan: dataPemohon.statusKepemilikanRumah,
        domisili_kode_pos: dataPemohon.kodePosDomisili,
        domisili_kota: dataPemohon.kotaDomisili,
        domisili_provinsi: dataPemohon.provinsiDomisili,

        foto_usaha_lainnya: dataLampiran.fotoUsahaLainnya,

        jenis_usaha_ecommerce: dataUsaha.ecommerceType,

        jumlah_rekening_bank_lain: dataRekeningUsaha.jumlahRekeningBankLain,
        jumlah_rekening_bri: dataRekeningUsaha.jumlahRekening,

        kontak_darurat_nama_pasangan: dataPemohon.pasanganName,
        kontak_darurat_nomor_ktp_pasangan: dataPemohon.pasanganNIK,
        kontak_darurat_tanggal_lahir_pasangan: moment(dataPemohon.pasanganDateBirth, 'DD/MM/YYYY').format('YYYY-MM-DD'),
        kontak_darurat_tempat_lahir_pasangan: dataPemohon.tempatLahirPasangan,

        kredit_name: dataKredit.providerName,
        kredit_tujuan: dataKredit.tujuanKredit,
        kredit_total: parseFloat(dataKredit.kebutuhan),
        kredit_id_provider: Number(dataKredit.providerId),
        kredit_bunga: parseFloat(dataKredit.bunga) || 0,
        kredit_tenor: parseInt(dataKredit.tenor, 10),
        kredit_angsuran: Number(dataKredit.angsuran),

        ktp_alamat: dataPemohon.alamatKtp,
        ktp_kecamatan: dataPemohon.kecamatanKtp,
        ktp_kelurahan: dataPemohon.kelurahanKtp,
        ktp_kode_pos: dataPemohon.kodePosKtp,
        ktp_kota: dataPemohon.kotaKtp,
        ktp_provinsi: dataPemohon.provinsiKtp,

        lampiran_foto_outlet: dataLampiran.fotoOutlet,
        lampiran_foto_selfie_ktp: dataLampiran.ktpSelfie,
        lampiran_identitas: dataLampiran.ktp,
        lampiran_ijin_usaha: dataLampiran.izinUsaha,
        lampiran_npwp: dataLampiran.npwp,
        lampiran_rekening_koran: dataLampiran.rekening,

        pemohon_agama: dataPemohon.agama,
        pemohon_email: dataPemohon.email,
        pemohon_ibu_kandung: dataPemohon.namaIbu,
        pemohon_id_kota_lahir: 0,
        pemohon_kelamin: dataPemohon.jenisKelamin,
        pemohon_kota_lahir: dataPemohon.kotaLahir,
        pemohon_ktp: dataPemohon.noKTP,
        pemohon_nama: dataPemohon.namaPemohon,
        pemohon_no_tlp_hp: dataPemohon.noTelp,
        pemohon_pendidikan: dataPemohon.pendidikan,
        pemohon_referensi_sales: dataPemohon.salesCode,
        pemohon_status: dataPemohon.statusPernikahan,
        pemohon_tgl_lahir: moment(dataPemohon.tglLahir, 'DD/MM/YYYY').format('YYYY-MM-DD'),

        pertanyaan_cicilan_pinjaman_perbulan: dataUsaha.totalCicilan,
        pertanyaan_pekerjaan: dataPemohon.jobs !== '4' ? dataPemohon.jobs : dataPemohon.jobsFreeText,
        pertanyaan_penghasilan_per_bulan: dataUsaha.penghasilanBulan,

        rating_toko: dataUsaha.storeRating,
        rekening_no: dataRekeningUsaha.nomorRekening,
        saldo_simpanan_bulan_ini: dataRekeningUsaha.saldoSimpanan,
        memiliki_internet_banking: dataRekeningUsaha.internetBanking,

        tanggal_mulai_tinggal: moment(dataPemohon.dateStarStay, 'DD/MM/YYYY').format('YYYY-MM-DD'),
        tingkat_sukses_transaksi: dataUsaha.successTransaction,

        usaha_domain: dataUsaha.domainStore,
        usaha_tgl_berdiri: moment(dataUsaha.tglMulaiUsaha, 'DD/MM/YYYY').format('YYYY-MM-DD'),
        nama_ecommerce: dataUsaha.namaEcommerce,
        usaha_status: dataUsaha.statusKepemilikanUsaha,
        usaha_jenis_badan_usaha: dataUsaha.jenisBadanUsaha,

        status_permohonan: status === STATUS_SAVE.DRAFT ? STATUS_SAVE.DRAFT : STATUS_SAVE.SAVE,
    };
};

const stringDate = date => moment(date, 'DD-MM-YYYY').format('DD MMM YYYY');

export {
    stringDate,
    setPayloadBTN,
    generatePayloadBTN,
    generatePayloadBNI,
    generatePayload,
    generatePayloadCommbank,
    generatePayloadBRI,
    checkInappropriateFormat,
};
