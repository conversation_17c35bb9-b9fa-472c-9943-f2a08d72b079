import React, { useContext, useState } from 'react';
import PropTypes from 'prop-types';
import { ToastContext } from '@majoo-ui/react';
import { useTranslation } from 'react-i18next';
import { nameSpaceTranslation as ns } from '../settings/constant';

const setValueContext = (parentProps) => {
    const { addToast } = useContext(ToastContext);
    const [openConfirmation, setOpenConfirmation] = useState(false);
    const [modalConfirmation, setModalConfirmation] = useState({
        type: 'primary',
        title: '',
        description: '',
        buttons: {
            confirmButton: { label: '', action: () => {} },
            cancelButton: { label: '', action: () => {} },
        },
    });
    const { t, ready, i18n } = useTranslation([ns.incomeStatement, ns.statement, ns.translation], { useSuspense: false });
    const lang = i18n.language;

    const contextData = {
        ...parentProps,
        addToast,
        modalConfirmation,
        openConfirmation,
        ready,
        lang,
    };

    const handleSetCalendar = (val) => {
        parentProps.assignCalendar(val[0], val[1]);
    };

    const handleOpenConfirmationModal = ({
        type, title, description, buttons,
    }) => {
        setOpenConfirmation(true);
        setModalConfirmation({
            type,
            title,
            description,
            buttons,
        });
    };

    const handleCloseConfirmationModal = () => {
        setOpenConfirmation(false);
    };

    return {
        ...contextData,
        handleSetCalendar,
        handleOpenConfirmationModal,
        handleCloseConfirmationModal,
        setOpenConfirmation,
        t,
    };
};

export const ProfitLossContext = React.createContext();

const ProfitLossContextProvider = ({ children, parentProps }) => {
    const value = setValueContext(parentProps);

    return (
        <ProfitLossContext.Provider {...{ value }}>
            {children}
        </ProfitLossContext.Provider>
    );
};

ProfitLossContextProvider.propTypes = {
    children: PropTypes.element.isRequired,
    parentProps: PropTypes.shape({}),
};

ProfitLossContextProvider.defaultProps = {
    parentProps: {},
};

export default ProfitLossContextProvider;
