import React, { useContext, useState } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { ToastContext } from '@majoo-ui/react';
import { statusEnum } from '../settings/constant';

const setValueContext = (parentProps) => {
    const { addToast } = useContext(ToastContext);
    const [openConfirmation, setOpenConfirmation] = useState(false);
    const [modalConfirmation, setModalConfirmation] = useState({
        type: 'primary',
        title: '',
        description: '',
        buttons: {
            confirmButton: { label: '', action: () => { } },
            cancelButton: { label: '', action: () => { } },
        },
    });
    const [statusFilter, setStatusFilter] = useState(statusEnum.LANGSUNG);
    const [openFilterModal, setOpenFilterModal] = useState(false);
    const [filterReport, setFilterReport] = useState({
        showToast: false,
        isFiltered: false,
        compared: '',
        outlet: [],
        date: [moment(parentProps.calendar.start, 'DD/MM/YYYY').toDate(), moment(parentProps.calendar.end, 'DD/MM/YYYY').toDate()],
        totalPeriod: '',
        period: '',
        sort: 'start-end',
    });

    const contextData = {
        ...parentProps,
        addToast,
        modalConfirmation,
        openConfirmation,
        statusFilter,
        openFilterModal,
        filterReport,
    };

    const handleSetCalendar = (val) => {
        parentProps.assignCalendar(val[0], val[1]);
    };

    const handleOpenConfirmationModal = ({
        type, title, description, buttons,
    }) => {
        setOpenConfirmation(true);
        setModalConfirmation({
            type,
            title,
            description,
            buttons,
        });
    };

    const handleCloseConfirmationModal = () => {
        setOpenConfirmation(false);
    };
    
    const handleOpenFilterModal = () => {
        setOpenFilterModal(true);
    };

    const handleCloseFilterModal = () => {
        setOpenFilterModal(false);
    };

    const handleResetFilterModal = () => {
        setFilterReport({
            showToast: false,
            isFiltered: false,
            compared: '',
            outlet: [],
            date: [moment(parentProps.calendar.start, 'DD/MM/YYYY').toDate(), moment(parentProps.calendar.end, 'DD/MM/YYYY').toDate()],
            totalPeriod: '',
            period: '',
            sort: 'start-end',
        });
    };

    const handleChangeFilterModal = (key, value) => {
        setFilterReport(current => ({ ...current, [key]: value }));
        // if (key === 'date') handleSetCalendar(value); // Next improvement
    };

    return {
        ...contextData,
        handleSetCalendar,
        handleOpenConfirmationModal,
        handleCloseConfirmationModal,
        setOpenConfirmation,
        setStatusFilter,
        handleOpenFilterModal,
        handleCloseFilterModal,
        handleChangeFilterModal,
        handleResetFilterModal,
    };
};

export const ArusKasContext = React.createContext();

const ArusKasContextProvider = ({ children, parentProps }) => {
    const value = setValueContext(parentProps);

    return (
        <ArusKasContext.Provider {...{ value }}>
            {children}
        </ArusKasContext.Provider>
    );
};

ArusKasContextProvider.propTypes = {
    children: PropTypes.element.isRequired,
    parentProps: PropTypes.shape({}),
};

ArusKasContextProvider.defaultProps = {
    parentProps: {},
};

export default ArusKasContextProvider;
