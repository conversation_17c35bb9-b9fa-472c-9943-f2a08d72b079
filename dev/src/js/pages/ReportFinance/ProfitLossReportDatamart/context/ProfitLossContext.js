import React, { useContext, useState } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { ToastContext } from '@majoo-ui/react';

const setValueContext = (props) => {
    const { addToast } = useContext(ToastContext);
    const [openConfirmation, setOpenConfirmation] = useState(false);
    const [modalConfirmation, setModalConfirmation] = useState({
        type: 'primary',
        title: '',
        description: '',
        buttons: {
            confirmButton: { label: '', action: () => { } },
            cancelButton: { label: '', action: () => { } },
        },
    });
    const [openFilterModal, setOpenFilterModal] = useState(false);
    const [filterReport, setFilterReport] = useState({
        isFiltered: false,
        compared: '',
        outlet: [],
        date: [moment(props.calendar.start, 'DD/MM/YYYY').toDate(), moment(props.calendar.end, 'DD/MM/YYYY').toDate()],
        totalPeriod: '',
        period: '',
        sort: 'start-end',
    });

    const contextData = {
        ...props,
        addToast,
        modalConfirmation,
        openConfirmation,
        openFilterModal,
        filterReport,
    };

    const handleSetCalendar = (val) => {
        props.assignCalendar(val[0], val[1]);
    };

    const handleOpenConfirmationModal = ({
        type, title, description, buttons,
    }) => {
        setOpenConfirmation(true);
        setModalConfirmation({
            type,
            title,
            description,
            buttons,
        });
    };

    const handleCloseConfirmationModal = () => {
        setOpenConfirmation(false);
    };

    const handleOpenFilterModal = () => {
        setOpenFilterModal(true);
    };

    const handleCloseFilterModal = () => {
        setOpenFilterModal(false);
    };

    const handleResetFilterModal = () => {
        setFilterReport({
            isFiltered: false,
            compared: '',
            outlet: [],
            date: [moment(props.calendar.start, 'DD/MM/YYYY').toDate(), moment(props.calendar.end, 'DD/MM/YYYY').toDate()],
            totalPeriod: '',
            period: '',
            sort: 'start-end',
        });
    };

    const handleChangeFilterModal = (key, value) => {
        setFilterReport(current => ({ ...current, [key]: value }));
        if (key === 'date') handleSetCalendar(value);
    };

    return {
        ...contextData,
        handleSetCalendar,
        handleOpenConfirmationModal,
        handleCloseConfirmationModal,
        setOpenConfirmation,
        handleOpenFilterModal,
        handleCloseFilterModal,
        handleChangeFilterModal,
        handleResetFilterModal,
    };
};

export const ProfitLossContext = React.createContext();

const ProfitLossContextProvider = (props) => {
    const { children } = props;
    const value = setValueContext(props);
    return (
        <ProfitLossContext.Provider {...{ value }}>
            {children}
        </ProfitLossContext.Provider>
    );
};

ProfitLossContextProvider.propTypes = {
    children: PropTypes.element.isRequired,
    props: PropTypes.shape({}),
};

ProfitLossContextProvider.defaultProps = {
    props: {},
};

export default ProfitLossContextProvider;
