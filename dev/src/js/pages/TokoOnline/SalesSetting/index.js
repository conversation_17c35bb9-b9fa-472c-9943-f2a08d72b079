import React, {
    Fragment, useContext, useEffect, useState,
} from 'react';
import PropTypes from 'prop-types';
import { Trans, useTranslation } from 'react-i18next';
import {
    Box, Button, Flex, Heading, Paper, Paragraph, Separator, ToastContext,
} from '@majoo-ui/react';

import CoreHOC from '../../../core/CoreHOC';
import PremiumDomain from './components/PremiumDomain';
import SelectOutlet from './components/SelectOutlet';
import SelectTimezone from './components/SelectTimezone';
import ProductPromo from './components/ProductPromo';
import PaymentType from './components/PaymentType';
import BankAccount from './components/BankAccount';
import Loading from './components/general/Loading';
import ServiceSetting from './components/ServiceSetting';
import ToggleActive from './components/ToggleActive';
import BannerUnactivatedOnlineShop from '../components/BannerUnactivatedOnlineShop';
import UnactivatedOnlineShopDialog from '../components/UnactivatedOnlineShopDialog';
import ActivatedOnlineShopPageDialog from '../components/ActivatedOnlineShopPageDialog';
import SuccessActivatedOnlineShopDialog from '../components/SuccessActivatedOnlineShopDialog';
import AccountingIntegration from './components/general/AccountingIntegration';
import DynamicModalContent from './components/general/DynamicModalContent';
import { catchError } from '../../../utils/helper';
import { getOutlet } from '../../../data/outlets';
import { getAccountInfo, getListSubmissionProfile } from '../../../data/users';
import { bankAccountWeborder } from '../../../data/users/selectors';
import { EATERY_SUB_TYPE, EATERY_TYPE, TABS_SERVICE_TYPE } from './components/ServiceSetting/utils';
import { styled } from '../../../stitches.config';
import { PERMISSIONS } from '../../../constants/permissions';
import {
    getDashboardWebstore,
    getDetailMerchant,
    getOptionCourier,
    getOrderSettingV12,
    getPremiumDomainStatus,
    updateOrderSetting,
    getMdrData,
} from '../../../data/webOrder';
import {
    activeOutlets,
    COURIER_OUTLET_TYPE,
    DELIVERY_INIT_STATE,
    DINE_IN_TYPE,
    INTEGRATION_STATUS,
    LIST_TIMEZONE,
    PAYMENT_TIME,
    REKENING_STATUS,
    removeZeroPhoneNumber,
    setSelectedCourier,
    sortingOutlets,
} from './utils';
import { BannerText, FavoriteWrapper, TooltipGuidance } from '../../../components/retina';
import { useMediaQuery } from '../../../utils/useMediaQuery';

const FooterContent = styled(Flex, {
    backgroundColor: '#fff',
    bottom: 0,
    marginTop: 12,
    padding: '16px 0',
    position: 'sticky',
});

const SalesSetting = ({ showProgress, hideProgress, router }) => {
    const [loadingChangeOutlet, setLoadingChangeOutlet] = useState(true);
    const [idOutlet, setIdOutlet] = useState('');
    const [weborderNumber, setWeborderNumber] = useState('');
    const [weblinkUri, setWeblinkUri] = useState('');
    const [listOutlet, setListOutlet] = useState([]);
    const [serviceTypeActive, setServiceTypeActive] = useState([]);
    const [dineInType, setDineInType] = useState('');
    const [paymentTime, setPaymentTime] = useState('');
    const [formDelivery, setFormDelivery] = useState(DELIVERY_INIT_STATE);
    const [isSetAttribute, setIsSetAttribute] = useState(false);
    const [addressDeliveryStatus, setAddressDeliveryStatus] = useState(false);
    const [rawListCourier, setRawListCourier] = useState([]);
    const [listCourier, setListCourier] = useState([]);
    const [paymentType, setPaymentType] = useState([]);
    const [listRekening, setListRekening] = useState([]);
    const [bankSelected, setBankSelected] = useState('');
    const [timezoneSelected, setTimezoneSelected] = useState();
    const [isDisabledButton, setIsDisabledButton] = useState(true);
    const [loadingSave, setLoadingSave] = useState(false);
    const [isInactiveWebstore, setIsInactiveWebstore] = useState(true);
    const [popupConfirmInactive, setPopupConfirmInactive] = useState(false);
    const [pageDialogActivation, setPageDialogActivation] = useState(false);
    const [popupActivatedSuccessfully, setPopupActivatedSuccessfully] = useState(false);
    const [popupVolumeWeight, setPopupVolumeWeight] = useState(false);
    const [popupAddressDelivery, setPopupAddressDelivery] = useState(false);
    const [isNotAdmin, setIsNotAdmin] = useState(true);
    const [confirmSaveSetting, setConfirmSaveSetting] = useState(false);
    const [isActiveChannel, setIsActiveChannel] = useState(true);
    const [merchantInfo, setMerchantInfo] = useState({
        outlet_name: '',
        majoo_email: '',
        weblink: '',
    });
    const [premiumDomainInfo, setPremiumDomainInfo] = useState({
        status: null,
        domain: '',
    });
    const [mdrSetting, setMdrSetting] = useState();

    const { addToast } = useContext(ToastContext);
    const { t } = useTranslation(['TokoOnline/TokoOnline/salesSetting', 'translation']);
    const isMobile = useMediaQuery('(max-width: 767px)');

    const handleFetchDashboard = async () => {
        try {
            const res = await getDashboardWebstore();
            if (!res.status) throw new Error(res.message);
            const { data_integration: dataIntegrations } = res;
            if (INTEGRATION_STATUS.INTEGRATED.includes(dataIntegrations.status_integrasi)) {
                setIsInactiveWebstore(false);
            }
            setWeborderNumber(dataIntegrations.weborder_number);
            setIdOutlet(String(dataIntegrations.id_outlet));
            setWeblinkUri(res.merchant_weblink_uri);
        } catch (error) {
            addToast({
                title: 'Failed get dashboard webstore',
                description: catchError(error),
                variant: 'failed',
            });
        }
    };

    const handleFetchAccount = async () => {
        try {
            const res = await getAccountInfo();
            if (!res.status) throw new Error(res.msg);
            if (res.data.hak_akses_id === PERMISSIONS.ADMIN) {
                setIsNotAdmin(false);
            }
        } catch (error) {
            addToast({
                title: 'Failed get info account',
                description: catchError(error),
                variant: 'failed',
            });
        }
    };

    const handleSetSelectedKurir = () => {
        if (rawListCourier.length === 0) return;
        const listCourierNew = setSelectedCourier(listCourier, rawListCourier);
        setListCourier(listCourierNew);
    };

    useEffect(() => {
        if (rawListCourier.length > 0) handleSetSelectedKurir();
    }, [rawListCourier]);

    const handleFetchOptionCourier = async () => {
        try {
            const res = await getOptionCourier();
            const { data: resData } = res;
            const errorMessage = t('errors.optionCourier', 'Tidak ada opsi kurir yang tersedia');
            if (!resData || resData.length <= 0) throw new Error(errorMessage);
            const listData = resData.map(courier => ({
                name: courier.name,
                code: courier.code,
                image: courier.logo,
                subOption: courier.service_types.map(sub => ({ ...sub, isChecked: false })),
                desc: '',
                isChecked: false,
            }));
            setListCourier(listData);
        } catch (error) {
            addToast({
                title: 'Failed get courier option',
                description: catchError(error),
                variant: 'failed',
            });
        }
    };

    const handleFetchMdr = async () => {
        try {
            const res = await getMdrData();
            setMdrSetting((res.data || []).find(x => x.app_name.toLowerCase() === 'dashboard'))
        } catch (error) {
            addToast({
                title: t('translation:toast.error'),
                description: catchError(error),
                variant: 'failed',
            });
        }
    };

    useEffect(() => {
        handleFetchAccount();
        handleFetchDashboard();
        handleFetchPremiumDomain();
        handleFetchMerchantInfo();
        handleFetchMdr();
    }, [popupActivatedSuccessfully]);

    const handleCheckStatusAddress = (exListOutlet) => {
        const selectedOutlet = exListOutlet.find(outlet => outlet.id === idOutlet);
        if (selectedOutlet) {
            setAddressDeliveryStatus(!!selectedOutlet.address);
        }
    };

    const handleFetchOutlet = async () => {
        try {
            const res = await getOutlet({ show_weborder_number: 1 });
            if (!res.status) throw new Error(res.msg);
            const { data } = res;
            const outlets = activeOutlets(data);
            const sortedOutlet = sortingOutlets(isInactiveWebstore ? data : outlets);
            setListOutlet(sortedOutlet);
            handleCheckStatusAddress(sortedOutlet);
        } catch (error) {
            setLoadingChangeOutlet(false);
            addToast({
                title: 'Failed get list outlet',
                description: catchError(error),
                variant: 'failed',
            });
        }
    };

    const handleFetchOrderSetting = async (rekeningData) => {
        try {
            setLoadingChangeOutlet(true);
            const { data, message } = await getOrderSettingV12(idOutlet);
            if (!data) throw new Error(message);
            const {
                account_number: accountNumber,
                payment_type: resPaymentType,
                eatery_type: eateryType,
                dine_eatery_sub_type: eaterySubType,
                dinein_type: resDineInType,
                payment_time: resPaymentTime,
                shipper_address: shipperAddress,
                shipper_detail: shipperDetail,
                list_courier: listUserCourier,
                outlet_courier: outletCourier,
                is_set_attribute: resIsSetAttribute,
                timezone: resTimezone,
                dinein_takeaway: dineInTakeAway,
                active_channel: activeChannel,
            } = data;

            const newServiceTypeActive = [];
            if (eateryType.includes(EATERY_TYPE.DINE_IN) && eaterySubType.includes(EATERY_SUB_TYPE.DINE_IN)) newServiceTypeActive.push(TABS_SERVICE_TYPE.DINE_IN);
            if (eateryType.includes(EATERY_TYPE.DELIVERY)) newServiceTypeActive.push(TABS_SERVICE_TYPE.DELIVERY);
            if (eaterySubType.includes(EATERY_SUB_TYPE.TAKE_IT_HOME)) newServiceTypeActive.push(TABS_SERVICE_TYPE.TAKE_IT_HOME);
            if (dineInTakeAway) newServiceTypeActive.push(TABS_SERVICE_TYPE.DINE_IN_TAKE_AWAY);

            const addressLatitude = shipperAddress.coordinate.latitude;
            const addressLongitude = shipperAddress.coordinate.longitude;

            let formDeliveryService = {
                senderName: shipperDetail.name || '',
                senderPhone: removeZeroPhoneNumber(shipperDetail.phone_number),
                selectedCourier: listUserCourier ? listUserCourier.map(courier => ({ ...courier, service_types: courier.service_types_code })) : [],
                outletCourierStatus: !!outletCourier.status,
                addressManual: shipperAddress.name || '',
                note: shipperAddress.description || '',
                koordinat: addressLatitude !== '' && addressLongitude !== '' && addressLatitude && addressLongitude ? `${addressLatitude},${addressLongitude}` : '',
                postalCode: shipperAddress.postal_code,
            };

            if (outletCourier.setup) {
                formDeliveryService = {
                    ...formDeliveryService,
                    outletCourierType: outletCourier.setup.type || COURIER_OUTLET_TYPE.WEIGHT,
                    outletCourierMaxDelivery: outletCourier.setup.max_delivery || 0,
                    outletCourierCost: outletCourier.setup.cost || 0,
                    outletCourierEstimation: outletCourier.setup.estimation === null ? '' : outletCourier.setup.estimation,
                    outletCourierEstimationMax: outletCourier.setup.estimation_max === null ? '' : outletCourier.setup.estimation,
                    outletCourierNextDay: !!outletCourier.setup.next_day,
                };
            }

            const selectedAccountNumber = rekeningData.find(item => item.id === accountNumber);
            setServiceTypeActive([TABS_SERVICE_TYPE.E_MENU, ...newServiceTypeActive]);
            setDineInType(resDineInType ? String(resDineInType) : DINE_IN_TYPE.DYNAMIC);
            setPaymentTime(resPaymentTime ? String(resPaymentTime) : PAYMENT_TIME.PAY_LATER);
            setTimezoneSelected(resTimezone);
            setBankSelected(selectedAccountNumber ? selectedAccountNumber.id : '');
            setPaymentType(resPaymentType || []);
            setFormDelivery(formDeliveryService);
            setRawListCourier(listUserCourier);
            setIsSetAttribute(resIsSetAttribute);
            // 1 toko online
            setIsActiveChannel(activeChannel === 1);
        } catch (error) {
            addToast({
                title: 'Failed get order setting',
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            setLoadingChangeOutlet(false);
            hideProgress();
        }
    };

    const handleFetchRekening = async () => {
        const payload = {
            id_outlet: idOutlet,
            status: REKENING_STATUS.APPROVED,
        };
        try {
            const res = await getListSubmissionProfile(payload);
            if (!res.status) throw new Error(res.msg);
            const rekeningData = bankAccountWeborder(res.data);
            setListRekening(rekeningData);
        } catch (error) {
            setLoadingChangeOutlet(false);
            addToast({
                title: 'Failed get account number',
                description: catchError(error),
                variant: 'failed',
            });
        }
    };

    const handleFetchMainData = async () => {
        handleFetchOutlet();
        if (isInactiveWebstore) {
            setLoadingChangeOutlet(false);
            hideProgress();
            return;
        }
        handleFetchRekening();
    };

    useEffect(() => {
        if (idOutlet) {
            handleFetchOptionCourier();
            handleFetchMainData();
            setIsDisabledButton(true);
        }
    }, [idOutlet]);

    useEffect(() => {
        if (!idOutlet || !listOutlet || !listRekening) return;
        const selectedOutlet = listOutlet.find(outletItem => outletItem.id === idOutlet);
        if (selectedOutlet) handleFetchOrderSetting(listRekening);
        else if (listOutlet.length) setIdOutlet(listOutlet[0].id);
        else {
            hideProgress();
        }
    }, [idOutlet, JSON.stringify(listOutlet), JSON.stringify(listRekening)]);

    const handleCheckedPaymentType = (id) => {
        setIsDisabledButton(false);
        setPaymentType((prevState) => {
            if (prevState.includes(id)) return prevState.filter(payment => payment !== id);
            return [...prevState, id];
        });
    };

    const handleActiveService = (id) => {
        if (isInactiveWebstore) {
            setPopupConfirmInactive(true);
            return;
        }
        const isExist = serviceTypeActive.includes(id);
        if (id === TABS_SERVICE_TYPE.DELIVERY && !isSetAttribute && !isExist) {
            setPopupVolumeWeight(true);
            return;
        }
        setIsDisabledButton(false);
        setServiceTypeActive((prevState) => {
            if (prevState.includes(id)) {
                if (id === TABS_SERVICE_TYPE.DINE_IN) return prevState.filter(type => (type !== id && type !== TABS_SERVICE_TYPE.DINE_IN_TAKE_AWAY));
                return prevState.filter(type => type !== id);
            }
            return [...prevState, id];
        });
    };

    const handleChangeFormDelivery = (field, value) => {
        setIsDisabledButton(false);
        setFormDelivery(prevState => ({
            ...prevState,
            [field]: value,
            ...(field === 'outletCourierNextDay' && {
                outletCourierEstimation: value ? 0 : '',
                outletCourierEstimationMax: value ? 0 : '',
            }),
        }));
    };

    const handleToastError = (title, description) => {
        addToast({
            title,
            description,
            variant: 'failed',
        });
    };

    const handleResetState = () => {
        setServiceTypeActive([]);
        setDineInType('');
        setPaymentTime('');
        setFormDelivery(DELIVERY_INIT_STATE);
        setAddressDeliveryStatus(false);
        setRawListCourier([]);
        setListCourier([]);
        setPaymentType([]);
        setListRekening([]);
        setBankSelected('');
        setTimezoneSelected();
    };

    const handleChangeOutlet = (id) => {
        const selectedOutlet = listOutlet.find(outlet => outlet.id === id);
        if (!selectedOutlet) {
            handleToastError('Terjadi kesalahan', 'Gagal mengganti outlet, silakan coba lagi.');
            return;
        }
        handleResetState();
        setIdOutlet(id);
        setWeborderNumber(selectedOutlet.weborderNumber);
    };

    const handleChangeTimezone = (id) => {
        setTimezoneSelected(id);
        setIsDisabledButton(false);
    };

    const handleSaveOrderSetting = async ({ outletCourier }) => {
        const shipperDetail = {
            name: formDelivery.senderName,
            phone_number: formDelivery.senderPhone,
        };

        const koordinat = formDelivery.koordinat.split(',');

        const shipperAddress = {
            name: formDelivery.addressManual,
            description: formDelivery.note,
            provice_id: '',
            city_id: '',
            subdistric_id: '',
            village_id: '',
            postal_code: formDelivery.postalCode,
            coordinate: {
                latitude: koordinat[0] || '',
                longitude: koordinat[1] || '',
            },
        };

        const isDineInActive = serviceTypeActive.includes(TABS_SERVICE_TYPE.DINE_IN);
        const isTakeItHomeActive = serviceTypeActive.includes(TABS_SERVICE_TYPE.TAKE_IT_HOME);
        const isDeliveryActive = serviceTypeActive.includes(TABS_SERVICE_TYPE.DELIVERY);
        const isDineInTakeAwayActive = serviceTypeActive.includes(TABS_SERVICE_TYPE.DINE_IN_TAKE_AWAY);

        const eateryType = [];
        const eaterySubType = [];
        if (isDineInActive) {
            eateryType.push(EATERY_TYPE.DINE_IN);
            eaterySubType.push(EATERY_SUB_TYPE.DINE_IN);
        }
        if (isTakeItHomeActive) {
            eateryType.push(EATERY_TYPE.DINE_IN);
            eaterySubType.push(EATERY_SUB_TYPE.TAKE_IT_HOME);
        }
        if (isDeliveryActive) {
            eateryType.push(EATERY_TYPE.DELIVERY);
        }

        const payload = {
            no: weborderNumber,
            outlet_id: idOutlet,
            eatery_type: Array.from(new Set(eateryType)).toString(),
            eatery_sub_type: Array.from(new Set(eaterySubType)).toString(),
            account_number: bankSelected,
            shipper_detail: shipperDetail,
            shipper_address: shipperAddress,
            list_courier: formDelivery.selectedCourier,
            dinein_type: isDineInActive ? dineInType : '',
            payment_time: parseInt(paymentTime, 10),
            outlet_courier: outletCourier.status ? outletCourier : null,
            payment_type: paymentType,
            timezone: timezoneSelected,
            dinein_takeaway: isDineInTakeAwayActive,
            active_channel: isActiveChannel ? 1 : 2,
        };

        try {
            setLoadingSave(true);
            await updateOrderSetting(payload);
            addToast({
                title: t('toast.success', { ns: 'translation', defaultValue: 'Berhasil!' }),
                description: (
                    <Paragraph paragraph="longContentRegular">
                        <Trans t={t} i18nKey="toast.saveSetting" defaultValue="<strong>Pengaturan Penjualan</strong> berhasil disimpan" />
                    </Paragraph>
                ),
                variant: 'success',
            });
            handleFetchMainData();
            setIsDisabledButton(true);
            setConfirmSaveSetting(false);
        } catch (error) {
            addToast({
                title: t('toast.somethingWrong', { ns: 'translation', defaultValue: 'Terjadi Kesalahan' }),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            setLoadingSave(false);
        }
    };

    const handlePrepareSave = () => {
        const isDineInActive = serviceTypeActive.includes(TABS_SERVICE_TYPE.DINE_IN);
        const isTakeItHomeActive = serviceTypeActive.includes(TABS_SERVICE_TYPE.TAKE_IT_HOME);
        const isDeliveryActive = serviceTypeActive.includes(TABS_SERVICE_TYPE.DELIVERY);
        const dineInTypeActive = dineInType === DINE_IN_TYPE.STATIC || dineInType === DINE_IN_TYPE.DYNAMIC;

        if (!isDineInActive && !isTakeItHomeActive && !isDeliveryActive) {
            handleToastError(t('errors.serviceSettingTitle', 'Error Pengaturan Layanan'), t('errors.serviceSettingDescription', 'Anda belum pilih tipe order, pilih minimal 1 tipe order'));
            return;
        }

        if (isDineInActive && !dineInType) {
            handleToastError(t('errors.dineInTitle', 'Error Pesan di Tempat'), t('errors.dineInType', 'Anda belum memilih tipe makan di tempat'));
        }

        if (isDineInActive && dineInTypeActive && paymentTime === '') {
            handleToastError(t('errors.dineInTitle', 'Error Pesan di Tempat'), t('errors.dineInPaymentTime', 'Anda belum memilih waktu pembayaran'));
        }

        const formSelectedCourier = formDelivery.selectedCourier;

        if ((formSelectedCourier.length === 0 || !formSelectedCourier) && isDeliveryActive) {
            handleToastError(t('errors.courierServiceTitle', 'Error Kurir Ekspedisi'), t('errors.courierServiceDescription', 'Anda belum memilih 1 ekspedisi apapun, pilih minimal 1 ekpedisi'));
            return;
        }

        if (paymentType.length === 0) {
            handleToastError(t('errors.paymentSettingTitle', 'Error Pengaturan Pembayaran'), t('errors.paymentSettingDescription', 'Anda belum pilih tipe order, pilih minimal 1 tipe order'));
            return;
        }

        if (isDeliveryActive && !addressDeliveryStatus) {
            setPopupAddressDelivery(true);
            return;
        }

        const outletCourier = {
            status: formDelivery.outletCourierStatus,
            setup: {
                type: formDelivery.outletCourierType,
                max_delivery: parseInt(formDelivery.outletCourierMaxDelivery, 10),
                cost: parseInt(formDelivery.outletCourierCost, 10),
                estimation: formDelivery.outletCourierEstimation,
                estimation_max: formDelivery.outletCourierEstimationMax,
                next_day: formDelivery.outletCourierNextDay,
            },
        };

        if (outletCourier.status) {
            if (outletCourier.setup.max_delivery === 0 && outletCourier.setup.type === COURIER_OUTLET_TYPE.WEIGHT) {
                handleToastError(t('errors.outletCourierTitle', 'Error Kurir Outlet'), t('errors.outletCourierMaxWeight', 'Masukkan batas berat pengiriman'));
                return;
            }

            if (outletCourier.setup.max_delivery === 0 && outletCourier.setup.type === COURIER_OUTLET_TYPE.DISTANCE) {
                handleToastError(t('errors.outletCourierTitle', 'Error Kurir Outlet'), t('errors.outletCourierMaxDistance', 'Masukkan batas jarak maksimum pengiriman'));
                return;
            }

            if (outletCourier.setup.estimation === '' || outletCourier.setup.estimation_max === '') {
                handleToastError(t('errors.outletCourierTitle', 'Error Kurir Outlet'), t('errors.outletCourierEstimation', 'Pilih durasi estimasi pesanan sampai terlebih dahulu'));
                return;
            }

            if (parseInt(outletCourier.setup.estimation_max, 10) < parseInt(outletCourier.setup.estimation, 10)) {
                handleToastError(t('errors.outletCourierTitle', 'Error Kurir Outlet'), t('errors.outletCourierFailedEstimation', 'Estimasi pesanan sampai tidak boleh lebih besar dari nilai kedua'));
                return;
            }
        }

        handleSaveOrderSetting({ outletCourier });
    };

    const handleFetchPremiumDomain = async () => {
        try {
            const res = await getPremiumDomainStatus();
            if (!res.status) throw new Error(res.message);
            setPremiumDomainInfo(res.data);
        } catch (error) {
            addToast({
                title: 'Failed get premium domain status',
                description: catchError(error),
                variant: 'failed',
            });
        }
    };

    const handleFetchMerchantInfo = async () => {
        try {
            const res = await getDetailMerchant();
            if (!res.data) throw new Error(res.message);
            setMerchantInfo(res.data);
        } catch (error) {
            addToast({
                title: 'Gagal get merchant info',
                description: catchError(error),
                variant: 'failed',
            });
        }
    };

    const outletName = listOutlet.find(outlet => outlet.id === idOutlet);

    return (
        <Box css={{ marginTop: '-$spacing-06', '@lg': { marginTop: 0 } }}>
            {isInactiveWebstore && (
                <Box css={{ marginBottom: 24 }}>
                    <BannerUnactivatedOnlineShop setShowActivatedOnlineShopPageDialog={setPageDialogActivation} />
                </Box>
            )}
            <Paper>
                <FavoriteWrapper>
                    <Heading as="h1" heading="pageTitle">{t('titleSection.title', 'Pengaturan Penjualan')}</Heading>
                    <TooltipGuidance />
                </FavoriteWrapper>
                <BannerText css={{ mb: 0 }} />
                <Separator css={{ marginTop: 20 }} />
                {!loadingChangeOutlet && (
                    <ToggleActive
                        value={isActiveChannel}
                        onChange={(val) => {
                            setIsActiveChannel(val);
                            setIsDisabledButton(false);
                        }}
                        disabled={isInactiveWebstore || isNotAdmin}
                        isMobile={isMobile}
                    />
                )}
                <SelectOutlet
                    disabled={loadingChangeOutlet || isInactiveWebstore}
                    idOutlet={idOutlet}
                    listOutlet={listOutlet}
                    onChange={handleChangeOutlet}
                    isMobile={isMobile}
                />
                {loadingChangeOutlet ? <Loading text={`${t('loading.outletDetail', 'Sedang memuat data outlet')} ${outletName ? outletName.name : ''}`} /> : (
                    <Fragment>
                        <SelectTimezone
                            disabled={isInactiveWebstore || isNotAdmin}
                            timezoneSelected={timezoneSelected}
                            listTimezone={LIST_TIMEZONE(t)}
                            onChange={handleChangeTimezone}
                            isMobile={isMobile}
                        />
                        <PremiumDomain
                            domainInfo={premiumDomainInfo}
                            merchantInfo={merchantInfo}
                            isDisabled={isInactiveWebstore || isNotAdmin}
                            isMobile={isMobile}
                            t={t}
                        />
                        <ProductPromo isInactiveWebstore={isInactiveWebstore} router={router} isNotAdmin={isNotAdmin} />
                        <ServiceSetting
                            isNotAdmin={isNotAdmin}
                            serviceTypeActive={serviceTypeActive}
                            idOutlet={idOutlet}
                            dineInType={dineInType}
                            paymentTime={paymentTime}
                            formDelivery={formDelivery}
                            listCourier={listCourier}
                            onChangeActiveService={handleActiveService}
                            onChangeFormDelivery={handleChangeFormDelivery}
                            onChangeDineInType={(value) => {
                                setDineInType(value);
                                setIsDisabledButton(false);
                            }}
                            onChangePaymentTime={(value) => {
                                setPaymentTime(value);
                                setIsDisabledButton(false);
                            }}
                            isMobile={isMobile}
                        />
                        <PaymentType
                            disabledSwitch={isInactiveWebstore || isNotAdmin}
                            paymentType={paymentType}
                            onCheckedChange={handleCheckedPaymentType}
                            isMobile={isMobile}
                            paymentTime={paymentTime}
                            mdrSetting={mdrSetting}
                        />
                        <BankAccount
                            disabled={isInactiveWebstore || isNotAdmin}
                            listRekening={listRekening}
                            bankSelected={bankSelected}
                            onChange={(value) => {
                                setBankSelected(value);
                                setIsDisabledButton(false);
                            }}
                            isMobile={isMobile}
                        />
                    </Fragment>
                )}
                <FooterContent justify="end">
                    <Button
                        size="lg"
                        disabled={isDisabledButton || isNotAdmin}
                        onClick={() => setConfirmSaveSetting(true)}
                        {...(isMobile && { block: true })}
                    >
                        {t('label.save', { ns: 'translation', defaultValue: 'Simpan' })}
                    </Button>
                </FooterContent>
            </Paper>

            <UnactivatedOnlineShopDialog
                open={popupConfirmInactive}
                setOpen={setPopupConfirmInactive}
                setShowActivatedOnlineShopPageDialog={setPageDialogActivation}
            />

            <ActivatedOnlineShopPageDialog
                open={pageDialogActivation}
                setOpen={setPageDialogActivation}
                outletId={idOutlet}
                showProgress={showProgress}
                hideProgress={hideProgress}
                router={router}
                setShowSuccessActivatedOnlineShopDialog={setPopupActivatedSuccessfully}
            />

            <SuccessActivatedOnlineShopDialog
                open={popupActivatedSuccessfully}
                setOpen={setPopupActivatedSuccessfully}
                mainLink={weblinkUri}
            />

            {!isInactiveWebstore && <AccountingIntegration />}

            <DynamicModalContent
                open={popupVolumeWeight}
                title={t('modal.setVolumeWeight.title', 'Atur Volume dan Berat')}
                labelCancel={t('label.cancel', { ns: 'translation', defaultValue: 'Batal' })}
                labelConfirm={t('modal.setVolumeWeight.confirmButton', 'Atur Produk')}
                onOpenChange={setPopupVolumeWeight}
                onConfirm={() => {
                    window.open('/item', '_blank');
                    setPopupVolumeWeight(false);
                }}
            >
                <Paragraph paragraph="longContentRegular" color="primary">
                    <Trans t={t} i18nKey="modal.setVolumeWeight.description" defaultValue="Belum dapat mengaktifkan layanan <strong>Pengiriman</strong>. Atur volume dan berat produk yang ditampilkan pada toko online untuk mengaktifkan layanan <strong>Pengiriman</strong>." />
                </Paragraph>
            </DynamicModalContent>

            <DynamicModalContent
                open={popupAddressDelivery}
                title={t('modal.failedSetDelivery.title', 'Atur Alamat Pengiriman')}
                labelCancel={t('label.cancel', { ns: 'translation', defaultValue: 'Batal' })}
                labelConfirm={t('label.continue', { ns: 'translation', defaultValue: 'Batal' })}
                onOpenChange={setPopupAddressDelivery}
                onConfirm={() => {
                    window.open(`/pengaturan-bisnis/cabang/detail/${idOutlet}`);
                    setPopupAddressDelivery(false);
                }}
            >
                <Paragraph paragraph="longContentRegular" color="primary">
                    <Trans t={t} i18nKey="modal.failedSetDelivery.description" defaultValue="Gagal menyimpan pengaturan layanan <strong>Pengiriman</strong>. Atur alamat dan lokasi outlet untuk proses pengambilan produk. Lanjutkan?" />
                </Paragraph>
            </DynamicModalContent>

            <DynamicModalContent
                open={confirmSaveSetting}
                title={t('modal.saveSetting.title', 'Simpan Pengaturan')}
                labelCancel={t('label.cancel', { ns: 'translation', defaultValue: 'Batal' })}
                labelConfirm={loadingSave ? t('modal.saveSetting.saving', 'Menyimpan...') : t('label.continue', { ns: 'translation', defaultValue: 'Ya, Lanjutkan' })}
                disabled={loadingSave}
                onConfirm={handlePrepareSave}
                onOpenChange={value => (loadingSave ? {} : setConfirmSaveSetting(value))}
            >
                <Paragraph paragraph="longContentRegular" color="primary">
                    <Trans t={t} i18nKey="modal.saveSetting.description" defaultValue="<strong>Pengaturan Penjualan</strong> akan disimpan dan tampil sesuai dengan pengaturan yang telah dilakukan. Lanjutkan?" />
                </Paragraph>
            </DynamicModalContent>
        </Box>
    );
};

SalesSetting.propTypes = {
    showProgress: PropTypes.func,
    hideProgress: PropTypes.func,
    router: PropTypes.shape({}),
};

SalesSetting.defaultProps = {
    showProgress: () => {},
    hideProgress: () => {},
    router: {},
};

export default CoreHOC(SalesSetting);
