import React, { forwardRef, useImperativeHandle, useState } from 'react';
import PropTypes from 'prop-types';
import { 
    <PERSON><PERSON>, 
    Flex, 
    PageDialog, 
    PageDialogContent, 
    PageDialogFooter, 
    PageDialogTitle,
} from '@majoo-ui/react';
import { useMediaQuery } from '../../../../../utils/useMediaQuery';
import moment from 'moment';
import { registerPremiumDomain } from '../../../../../data/webOrder';
import PremiumDomainForm from './PremiumDomainForm';
import TermsAndCondition from './TermsAndCondition';
import { PREMIUM_DOMAIN_STEP } from '../../utils';


const PremiumDomainPageDialog = forwardRef((props, ref) => {
    const [isHasDomain, setIsHasDomain] = useState(false);
    const [isShowDialog, setIsShowDialog] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isDisabled, setIsDisabled] = useState(false);
    const [step, setStep] = useState(PREMIUM_DOMAIN_STEP.TnC);
    const { t } = props;
    const isMobile = useMediaQuery('(max-width: 766px)');

    const openDialog = (hasDomain = true) => {
        setIsHasDomain(hasDomain);
        setStep(PREMIUM_DOMAIN_STEP.TnC);
        setIsShowDialog(true);
    };

    const closeDialog = () => {
        setIsShowDialog(false);
    };

    useImperativeHandle(ref, () => {
        return {
            openDialog,
            closeDialog,
        }
    });

    const handleSubmitPengajuanDomain = async (payload) => {
        if (payload.ns_setup_date) payload.ns_setup_date = moment(payload.ns_setup_date).format('YYYY-MM-DD HH:mm:ss');
        try { 
            setIsLoading(true);
            await registerPremiumDomain(payload);
            setTimeout(() => {
                window.location.reload();
            }, 500);
        } catch (error) {
            addToast({
                title: t('toast.somethingWrong', { ns: 'translation', defaultValue: 'Terjadi Kesalahan' }),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <PageDialog 
            modal
            open={isShowDialog}
            layer={1}
            size="full"
            onOpenChange={closeDialog}
            isMobile={isMobile}
        >
            <PageDialogTitle>
                {t('modal.registerPremiumDomain.title', 'Daftar Premium Domain')}
            </PageDialogTitle>
            <PageDialogContent wrapperSize="lg">
                {step === PREMIUM_DOMAIN_STEP.TnC && (
                    <TermsAndCondition 
                        isMobile={isMobile} 
                        setDisabledButton={setIsDisabled}
                        saveForm={() => setStep(PREMIUM_DOMAIN_STEP.FORM)} 
                        closeForm={closeDialog}
                        t={t}
                    />
                )}
                {step === PREMIUM_DOMAIN_STEP.FORM && (
                    <PremiumDomainForm
                        isMobile={isMobile}
                        isLoading={isLoading}
                        isHasDomain={isHasDomain}
                        saveForm={handleSubmitPengajuanDomain}
                        {...props}
                    />
                )}
            </PageDialogContent>
            <PageDialogFooter
                css={{
                    display: 'flex',
                    gap: '$compact',
                    justifyContent: 'center',
                    padding: '12px 24px',
                }}
            >
                <Flex
                    css={{
                        justifyContent: 'flex-end',
                        '@sm': {
                            width: '100%',
                        },
                        '@md': {
                            width: '100%',
                            margin: 'auto',
                        },
                    }}
                >
                    <Button
                        buttonType="ghost"
                        onClick={closeDialog}
                        css={ isMobile ? { marginRight: 28, width: '50%' } : { marginRight: 16 }}
                    >
                        {step === PREMIUM_DOMAIN_STEP.TnC ? t('label.back', { ns: 'translation', defaultValue: 'Kembali' }) : t('label.cancel', { ns: 'translation', defaultValue: 'Batal' })}
                    </Button>
                    <Button
                        type='submit'
                        form={step}
                        aria-label="Close"
                        variant="green"
                        onClick={() => { }}
                        disabled={isLoading || isDisabled}
                        css={isMobile ? { width: '50%' } : { marginRight: 3 }}
                    >
                        {step === PREMIUM_DOMAIN_STEP.TnC ? t('label.nextThen', { ns: 'translation', defaultValue: 'Selanjutnya' }) : t('label.submit', { ns: 'translation', defaultValue: 'Submit' })}
                    </Button>
                </Flex>
            </PageDialogFooter>
       </PageDialog>
    );
});

PremiumDomainPageDialog.propTypes = {
    weblink: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired, 
    email: PropTypes.string.isRequired,
    t: PropTypes.func,
};

PremiumDomainPageDialog.defaultProps = {
    t: () => { },
};

export default PremiumDomainPageDialog;
