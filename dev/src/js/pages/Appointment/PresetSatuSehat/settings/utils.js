/* eslint-disable react/prop-types */
import React from 'react';
import {
  Box, RowMenuColumn, Text,
  InputCheckbox, TagStatus, InputText,
  IconButton, DropdownMenu, DropdownMenuTrigger,
  DropdownMenuContent, DropdownMenuItem,
  DropdownMenuSeparator, Flex,
  InputSelect, InputNumber, Upload,
  FormGroup, FormLabel,
  InputDatePicker,
  InputCounter,
  FormHelper,
  InputTimePicker,
} from '@majoo-ui/react';
import { CaretDownOutline, CaretUpOutline, EditOutline, EllipsisHorizontalOutline } from '@majoo-ui/icons';
import { DateTimeColumn } from '../../../../components/retina';
import { SATUSEHAT_FORM_NAME, TABLE_TYPE } from './enum';
import StatusColumn from '~/components/retina/table/StatusColumn';
import { isNullOrUndefined } from '~/utils/helper';

// STYLING //
export const responsivePaper = {
  '@sm': {
    padding: 0,
    backgroundColor: 'transparent',
    boxShadow: 'none',
  },
  '@md': {
    padding: '$spacing-05',
    backgroundColor: '$white',
    boxShadow: '0px 2px 12px #00000014',
  },
};

export const responsiveFieldLabel = {
  alignItems: 'start !important',
  '& > span': {
    color: '$textPrimary',
    fontSize: '12px',
    fontWeight: 500,
    lineHeight: '16px',
    letterSpacings: '$section-sub-title',
    '@md': {
      fontWeight: 600,
      fontSize: '$section-sub-title',
      lineHeight: '$section-sub-title',
    },
  },
};

export const responsiveForm = {
  display: 'flex',
  flexDirection: 'column',
  margin: '10px 0 20px',
  justifyContent: 'space-between',
  alignItems: 'start',
  gap: '$compact',
  '@md': { flexDirection: 'row' },
};

export const responsiveLabel = {
  width: '100%',
  marginBottom: '10px',
  '@md': { width: '30%' },
};

export const responsiveField = {
  width: '100%',
  '@md': { width: '70%' },
};

export const responsiveFormAdditional = {
  display: 'flex',
  flexDirection: 'column',
  gap: '24px',
};

export const tableInformation = {
  border: '10px solid #FFFFFF',
  backgroundColor: '#FAFAFA',
  color: 'black',
};

export const tableColumnInformation = {
  borderRight: '10px solid #FFFFFF',
  width: '30%',
  padding: '8px',
};

export const tableValueInformation = {
  borderLeft: '10px solid #FFFFFF',
  width: '70%',
  padding: '8px',
};

export const iconMasterData = {
  border: '1px solid #C0C4C4',
  padding: '7px 16px',
  borderRadius: '8px',
};
// STYLING //

// FUNCTION //
export const defaultParams = {
  pageIndex: 0,
  pageSize: null,
  sortAccessor: undefined,
  sortDirection: 'ASC',
  filterSearch: undefined,
};

export const createFormDataMaster = (data = null) => {
  const dtForm = data.master_data_formulir.map(item => ({
    id: item.id,
    formName: item.name,
    isRepeat: !!item.is_repeat,
    isActive: !!item.active,
    field: item.field.map(x => ({
      id: x.id,
      choices: Array.isArray(x.choices) && x.choices.length > 0 ? x.choices.map(choice => ({
        name: choice,
      })) : [],
      isPrintReceipt: !!x.is_print_receipt,
      isRequired: !!x.is_required,
      isShowPos: !!x.is_show_pos,
      isSearch: !!x.is_search,
      name: x.name,
      type: x.type.toString(),
      urgency: x.urgency.toString(),
      isActive: !!x.active,
      is_show_form: isNullOrUndefined(x.is_show_form) ? 1 : x.is_show_form,
    })),
  }));

  return {
    id: data ? data.master_data.id : '',
    typeDataMaster: data ? data.master_data.type_data_master : 0,
    categoryDataMaster: data ? data.master_data.category_data_master.toString() : '',
    namecategoryDataMaster: data ? data.master_data.category_data_master_name.toString() : '',
    nameDataMaster: data ? data.master_data.name_data_master : '',
    iconDataMaster: data ? data.master_data.icon_data_master : '',
    iconDataMasterImage: data ? data.master_data.icon_data_master_image : '',
    isFormPos: data ? !!data.master_data.is_form_pos : false,
    isDuplicate: data ? !!data.master_data.is_duplicate : false,
    idDataMasterDuplicate: data ? data.master_data.id_data_master_duplicate || '' : '',
    isActive: data ? !!data.master_data.active : false,
    isFormRepeat: data ? String(data.master_data.is_repeat) === '1' : true,
    dataForm: dtForm,
  };
};

export const createDataDuplicate = (data = null) => {
  let totalField = 0;
  const dtForm = data.master_data_formulir.map((item, idxForm) => ({
    id: `form-${idxForm + 1}`,
    formName: item.name,
    // isRepeat: !!item.is_repeat,
    isActive: !!item.active,
    field: item.field.map((x) => {
      totalField += 1;
      return {
        id: `field-${totalField}`,
        choices: Array.isArray(x.choices) && x.choices.length > 0 ? x.choices.map(choice => ({
          name: choice,
        })) : [],
        isPrintReceipt: !!x.is_print_receipt,
        isRequired: !!x.is_required,
        isShowPos: !!x.is_show_pos,
        name: x.name,
        type: x.type.toString(),
        urgency: x.urgency.toString(),
        isActive: !!x.active,
      };
    }),
  }));

  return { dtForm, totalField };
};

export const initFormDataMaster = () => {
  const initForm = {
    id: '',
    typeDataMaster: 0,
    categoryDataMaster: '',
    namecategoryDataMaster: '',
    nameDataMaster: '',
    iconDataMaster: 0,
    iconDataMasterImage: '',
    isFormRepeat: true,
    isFormPos: false,
    isDuplicate: false,
    idDataMasterDuplicate: '',
    isActive: true,
    dataForm: [{
      id: 'form-1',
      formName: '',
      isRepeat: false,
      isActive: true,
      field: [],
    }],
  };
  return initForm;
};

export const initFormDataField = (countField) => {
  const initForm = {
    id: `field-${countField}`,
    choices: [{ name: '' }, { name: '' }],
    isPrintReceipt: false,
    isRequired: false,
    isShowPos: false,
    name: '',
    type: '',
    urgency: '1',
    isActive: true,
    is_show_form: 1,
  };
  return initForm;
};

export const createPayload = (data) => {
  const dtMaster = {
    id: data.id,
    type_data_master: data.typeDataMaster,
    category_data_master: data.categoryDataMaster,
    name_category_data_master: data.namecategoryDataMaster,
    name_data_master: data.nameDataMaster,
    icon_data_master: data.iconDataMaster,
    is_form_pos: data.isFormPos,
    is_duplicate: data.isDuplicate,
    id_data_master_duplicate: data.idDataMasterDuplicate || '',
    active: data.isActive,
    is_repeat: data.isFormRepeat ? 1 : 0,
  };

  const dtForm = data.dataForm.map(item => ({
    id: item.id,
    name: item.formName,
    // is_repeat: item.isRepeat,
    active: item.isActive,
    field: item.field.map(x => ({
      id: x.id,
      choices: x.choices.map(choice => (choice.name)),
      is_print_receipt: !!x.isPrintReceipt,
      is_required: x.isRequired,
      is_show_pos: x.isShowPos,
      is_search: x.isSearch,
      name: x.name,
      type: Number(x.type),
      urgency: Number(x.urgency),
      active: x.isActive,
      is_show_form: isNullOrUndefined(x.is_show_form) ? 1 : x.is_show_form,
    })),
  }));

  return {
    master_data: dtMaster,
    master_data_formulir: dtForm,
  };
};

export const CODE_TYPE_FIELD = {
  SELECTION_INPUT: '1',
  SINGLE_CHOICE: '2',
  MULTIPLE_CHOICE: '3',
  SHORT_TEXT: '4',
  LONG_PARAGRAPH: '5',
  DATE: '6',
  INPUT_NUMBER: '7',
  QUANTITY_INPUT: '8',
  FILE_UPLOADER: '9',
  NUMBER_BASED_AUTOMATIC_GENERATE: '10',
  TIME_BASED_AUTOMATIC_GENERATE: '11',
  SIGNATURE: '12',
  TIME: '13',
};

export const getFieldComponent = (field) => {
  switch (field.type) {
    case CODE_TYPE_FIELD.SELECTION_INPUT:
      return (
        <InputSelect
          placeholder="Pilih"
          size="lg"
          disabled
          option={[]}
        />
      );
    case CODE_TYPE_FIELD.SINGLE_CHOICE:
      return (
        <InputSelect
          placeholder="Pilih"
          size="lg"
          disabled
          option={[]}
        />
      );
    case CODE_TYPE_FIELD.MULTIPLE_CHOICE: {
      const choices = field.choices || [];
      return (
        <Flex gap={11}>
          <Flex direction="column" gap={2}>
            {choices.slice(0, Math.ceil(choices.length / 2)).map((choice) => (
              <InputCheckbox key={choice.name} checked={false} disabled label={choice.name} />
            ))}
          </Flex>
          <Flex direction="column" gap={2}>
            {choices.slice(Math.ceil(choices.length / 2)).map((choice) => (
              <InputCheckbox key={choice.name} checked={false} disabled label={choice.name} />
            ))}
          </Flex>
        </Flex>
      );
    }
    case CODE_TYPE_FIELD.SHORT_TEXT:
      return (
        <FormGroup>
          <FormLabel count={0} maxCount={40} />
          <InputText
            placeholder="Input di sini"
            size="lg"
            disabled
          />
        </FormGroup>
      );
    case CODE_TYPE_FIELD.LONG_PARAGRAPH:
      return (
        <FormGroup>
          <FormLabel count={0} maxCount={500} />
          <InputText
            placeholder="Input di sini"
            size="lg"
            disabled
          />
        </FormGroup>
      );
    case CODE_TYPE_FIELD.DATE:
      return (
        <InputDatePicker
          placeholder="Pilih tanggal"
          size="lg"
          disabled
        />
      );
    case CODE_TYPE_FIELD.INPUT_NUMBER:
      return (
        <InputNumber
          placeholder="Input di sini"
          size="lg"
          disabled
        />
      );
    case CODE_TYPE_FIELD.QUANTITY_INPUT:
      return (
        <InputCounter css={{ width: 200 }} disabled />
      );
    case CODE_TYPE_FIELD.FILE_UPLOADER:
      return (
        <Upload fileList={[]} disabled />
      );
    case CODE_TYPE_FIELD.NUMBER_BASED_AUTOMATIC_GENERATE:
      return (
        <FormGroup>
          <InputText
            placeholder="Input di sini"
            size="lg"
            disabled
            value="000001"
          />
          <FormHelper>Otomatis diinput oleh sistem</FormHelper>
        </FormGroup>
      );
    case CODE_TYPE_FIELD.TIME_BASED_AUTOMATIC_GENERATE:
      return (
        <FormGroup>
          <InputText
            placeholder="Input di sini"
            size="lg"
            disabled
            value="000001"
          />
          <FormHelper>Otomatis diinput oleh sistem</FormHelper>
        </FormGroup>
      );
    case CODE_TYPE_FIELD.SIGNATURE:
      return (
        <Upload fileList={[]} disabled placeholder="Tanda tangan disini" width="100%" css={{ width: '100%' }} />
      );
    case CODE_TYPE_FIELD.TIME:
      return (
        <InputTimePicker placeholder="Pilih waktu" disabled />
      );
    default:
      return (
        <InputText
          placeholder={field.name}
          size="lg"
          disabled
        />
      );
  }
};

export const getType = (type) => {
  switch (type) {
    case '1':
      return 'Selection Input';
    case '2':
      return 'Single Select';
    case '3':
      return 'Multiple Choice';
    case '4':
      return 'Short Text';
    case '5':
      return 'Long Paragraph';
    case '6':
      return 'date';
    case '7':
      return 'Input Number';
    case '8':
      return 'Quantity Input';
    case '9':
      return 'File Uploader';
    case '10':
      return 'Number Based Automatic Generate';
    case '11':
      return 'Time Based Automatic Generate';
    case '12':
      return 'Signature';
    case '13':
      return 'Time';
    default:
      return '';
  }
};

export const getIdForm = (idType) => {
  switch (idType) {
    case 1:
      return 'masterForm';
    case 2:
      return 'dataForm';
    case 3:
      return 'fieldForm';
    default:
      return '';
  }
};
// FUNCTION //

// TABLE //
export const tableColumn = (onChangeRowMenu, t) => [
  {
    Header: t('table.columns.dataType', 'Tipe Formulir'),
    accessor: 'data_type',
    Cell: (cell) => {
      const { value } = cell;
      const dt = value !== null ? value : '';
      return (
        <Box>
          {dt === 1 ? 'Data Tambahan' : 'Data Kepemilikan'}
        </Box>
      );
    },
    isMobileHeader: true,
  },
  {
    Header: t('table.columns.masterData', 'Nama Formulir'),
    accessor: 'name',
    Cell: (cell) => {
      const { value } = cell;
      const dt = value !== '' ? value : '-';
      return (
        <Text variant="placeholder" as="p" color="primary">{dt}</Text>
      );
    },
  },
  {
    Header: t('table.columns.formData', 'Section'),
    accessor: 'data_formulir',
    unsortable: true,
    colWidth: 149,
    Cell: (cell) => {
      const { value } = cell;
      const dt = value !== '' ? value : '-';
      return (
        <Text
          variant="placeholder"
          as="p"
          color="primary"
          isTruncated
          css={{
            '-webkit-line-clamp': 2,
            '-webkit-box-orient': 'vertical',
            display: '-webkit-box',
            whiteSpace: 'normal',
          }}
        >
          {dt}
        </Text>
      );
    },
  },
  {
    Header: t('table.columns.fieldCount', 'Jumlah Data'),
    accessor: 'data_formulir_field',
    unsortable: true,
    Cell: (cell) => {
      const { value } = cell;
      const dt = value !== '' ? value : '-';
      return (
        <Text variant="placeholder" as="p" color="primary">{dt}</Text>
      );
    },
  },
  {
    Header: 'Status',
    accessor: 'active',
    unsortable: true,
    Cell: (cell) => {
      const { value } = cell;
      return (
        <StatusColumn value={value} />
      );
    },
  },
  {
    Header: t('table.columns.lastUpdate', 'Terakhir Diperbaharui'),
    accessor: 'updated_at',
    Cell: (cell) => {
      const { value } = cell;
      return (
        <DateTimeColumn value={value} customFormat="DD MMM YYYY, HH:mm:ss" />
      );
    },
  },
  {
    id: 'menu',
    Header: () => null,
    unsortable: true,
    Cell: ({ row: { original } }) => (
      <DropdownMenu align="end">
        <DropdownMenuTrigger asChild>
          <IconButton><EllipsisHorizontalOutline /></IconButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {original.name === SATUSEHAT_FORM_NAME.OBSERVASI || original.name === SATUSEHAT_FORM_NAME.TINDAKAN ? (
            <React.Fragment>
              <DropdownMenuItem onClick={() => {
                if (String(original.active) === '0') {
                  onChangeRowMenu(original, 'activate');
                } else {
                  onChangeRowMenu(original, 'deactivate');
                }
              }}>
                {t(`label.${String(original.active) === '0' ? 'activate' : 'deactivate'}`, { ns: 'translation' })}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
            </React.Fragment>
          ) : null}
          <DropdownMenuItem
            onClick={() => {
              const { id } = original;
              if (id) onChangeRowMenu(original, 'edit');
            }}
          >
            {t('label.edit', { ns: 'translation' }, 'Ubah')}
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => {
              const { id } = original;
              if (id) onChangeRowMenu(original, 'detail');
            }}
          >
            {t('label.detail', { ns: 'translation' }, 'Lihat Detail')}
          </DropdownMenuItem>
          <DropdownMenuSeparator />
        </DropdownMenuContent>
      </DropdownMenu>
    ),
  },
];

export const tableField = (t, onOpenDetail, move) => [
  {
    id: 'id',
    Header: 'No',
    accessor: 'id',
    isToggled: false,
    unsortable: true,
    Cell: ({ row: { index } }) => (index + 1),
  },
  {
    id: 'name',
    Header: t('fieldTable.columns.dataName', 'Nama Data'),
    accessor: 'name',
    isToggled: false,
    unsortable: true,
  },
  {
    id: 'type',
    Header: t('fieldTable.columns.dataType', 'Tipe Data'),
    accessor: 'type',
    isMobileHeader: true,
    unsortable: true,
    Cell: (cell) => {
      const { value } = cell;
      const label = getType(value);
      return (
        <Text>{label}</Text>
      );
    },
  },
  {
    id: 'isRequired',
    Header: t('fieldTable.columns.required', 'Wajib Diisi'),
    accessor: 'isRequired',
    unsortable: true,
    Cell: (cell) => {
      const { value } = cell;
      const dt = value;
      return (
        <InputCheckbox checked={dt} disabled />
      );
    },
  },
  // {
  //   id: 'isShowPos',
  //   Header: t('fieldTable.columns.showInPos', 'Tampil di POS'),
  //   accessor: 'isShowPos',
  //   unsortable: true,
  //   Cell: (cell) => {
  //     const { value } = cell;
  //     const dt = value;
  //     return (
  //       <InputCheckbox checked={dt} disabled />
  //     );
  //   },
  // },
  // {
  //   id: 'isSearch',
  //   Header: t('fieldTable.columns.canSearch', 'Dapat Dicari'),
  //   accessor: 'isSearch',
  //   unsortable: true,
  //   Cell: (cell) => {
  //     const { value } = cell;
  //     const dt = value;
  //     return (
  //       <InputCheckbox checked={dt} disabled />
  //     );
  //   },
  // },
  // {
  //   id: 'isPrintReceipt',
  //   Header: t('fieldTable.columns.printReceipt', 'Cetak Struk'),
  //   accessor: 'isPrintReceipt',
  //   unsortable: true,
  //   Cell: (cell) => {
  //     const { value } = cell;
  //     const dt = value;
  //     return (
  //       <InputCheckbox checked={dt} disabled />
  //     );
  //   },
  // },
  // {
  //   Header: t('fieldTable.columns.dataUrgence', 'Informasi Data'),
  //   accessor: 'urgency',
  //   unsortable: true,
  //   Cell: (cell) => {
  //     const { value } = cell;
  //     const dt = value !== null ? value : '';
  //     let label;
  //     let type;
  //     switch (dt) {
  //       case '1':
  //         label = 'Normal';
  //         type = 'default';
  //         break;
  //       case '2':
  //         label = 'Penting';
  //         type = 'pending';
  //         break;
  //       case '3':
  //         label = 'Prioritas';
  //         type = 'new';
  //         break;
  //       default:
  //         break;
  //     }
  //     return (
  //       <TagStatus type={type}>
  //         {label}
  //       </TagStatus>
  //     );
  //   },
  // },
  // ...(typeof move !== 'undefined' ? [(
  //   {
  //     id: 'move',
  //     Header: t('fieldTable.columns.sort', 'Sortir'),
  //     unsortable: true,
  //     Cell: (cell) => {
  //       const { row, rows } = cell;
  //       const lastIndex = rows.length - 1;
  //       return (
  //         <Flex gap={2}>
  //           <IconButton
  //             buttonType="secondary"
  //             disabled={row.index === 0}
  //             onClick={(e) => {
  //               e.preventDefault();
  //               move(row.index, row.index - 1);
  //             }}
  //           >
  //             <CaretUpOutline />
  //           </IconButton>
  //           <IconButton
  //             buttonType="secondary"
  //             disabled={row.index === lastIndex}
  //             onClick={(e) => {
  //               e.preventDefault();
  //               move(row.index, row.index + 1);
  //             }}
  //           >
  //             <CaretDownOutline />
  //           </IconButton>
  //         </Flex>
  //       );
  //     },
  //     isAction: true,
  //   }
  // )] : []),
  ...(typeof onOpenDetail !== 'undefined' ? [(
    {
      id: 'eye',
      Header: () => null,
      unsortable: true,
      Cell: (cell) => {
        const dt = cell.row.original;
        return (
          <IconButton
            buttonType="secondary"
            onClick={(e) => {
              e.preventDefault();
              onOpenDetail(dt);
            }}
          >
            <EditOutline />
          </IconButton>
        );
      },
      isAction: true,
    }
  )] : []),
];

export const URGENCY_LIST = [
  {
    id: 'urgency-1',
    value: '1',
    label: 'form.label.dataUrgencySetting.option.normal',
    iconColor: '',
  },
  {
    id: 'urgency-2',
    value: '2',
    label: 'form.label.dataUrgencySetting.option.urgent',
    iconColor: '#FE7F2D',
  },
  {
    id: 'urgency-3',
    value: '3',
    label: 'form.label.dataUrgencySetting.option.priority',
    iconColor: '#0197F4',
  },
];

// TABLE //
