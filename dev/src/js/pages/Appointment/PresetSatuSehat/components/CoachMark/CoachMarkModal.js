import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { AlertDialog } from '@majoo-ui/react';
import PresetSatuSehatContext from '../../context/PresetSatuSehatContext';

export default function CoachMarkModal({ open, onCancel, onConfirm }) {
    const { isMobile, t } = useContext(PresetSatuSehatContext);

    return (
        <AlertDialog
            open={open}
            title={t('title', 'Judul')}
            description={t('desc', 'Desc')}
            labelCancel={t('cancel', 'Batal')}
            labelConfirm={t('confirm', 'Ya')}
            isMobile={isMobile}
            onCancel={onCancel}
            onConfirm={onConfirm}
        />
    );
}

CoachMarkModal.propTypes = {
    open: PropTypes.bool.isRequired,
    onCancel: PropTypes.func.isRequired,
    onConfirm: PropTypes.func.isRequired,
};
