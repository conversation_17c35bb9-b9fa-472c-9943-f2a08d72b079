/* eslint-disable camelcase */
/* eslint-disable react/prop-types */
import React, { useCallback, useEffect, useState, useContext } from 'react';
import {
    Box,
    Flex,
    Grid,
    Separator,
    Heading,
    TagStatus,
    InputSelect,
    Table,
    RowMenuColumn,
    FormLabel,
    InputSelectTag,
    InputNumber,
    InputText,
    InputTextArea,
    InputDatePicker,
    InputCounter,
    InputTimePicker,
    FormGroup,
    FormHelper,
    InputSignature,
    InputCheckbox,
    CheckboxGroup,
    ToastContext,
} from '@majoo-ui/react';
import { uniqBy } from 'lodash';
import PropTypes from 'prop-types';
import moment from 'moment';
import { catchError, dataURItoBlob } from '~/utils/helper';
import { uploadImage } from '~/data/upload';
import useDidMountEffect from '~/utils/useDidMountEffect';
import {
    RESERVATION_STATUS,
    RESERVATION_STATUS_LABEL,
    RESERVATION_FORM_STATUS,
} from './enum';
import { FavoriteWrapper, NullableColumn, SalesInformation, TooltipGuidance } from '../../../components/retina';
import {
    responsiveField,
    responsiveFieldLabel,
    responsiveForm,
    responsiveLabel,
    tokenUpload,
    uploadImageAddress,
    PreviewImageDialog,
    DATA_FIELD_LIST,
} from '../../Customer/CustomerList/settings/utils';
import UploadComponent from '../../Customer/CustomerList/components/uploadComponent';
import { useMasterData } from '../context/MasterDataContext';

export const responsivePaper = {
    '@sm': {
        padding: 0,
        backgroundColor: 'transparent',
        boxShadow: 'none',
    },
    '@md': {
        padding: '$spacing-05',
        backgroundColor: '$white',
        boxShadow: '0px 2px 12px #00000014',
    },
};

export const actionType = {
    FORM: 'form',
    DETAIL: 'detail',
    OWNERSHIP_HISTORY: 'ownershipHistory',
    ADDITIONAL_HISTORY: 'additionalHistory',
};

/**
 * @component
 *
 * @param {Date} date Date for current month text
 * @param {String} percentage percentage comparison
 * @period {('jam'|'hari')} period
 * */
export const TitleSection = ({ date, ...props }) => {
    const { t } = props;
    return (
        <Box
            css={{
                marginBottom: '$spacing-03',
                '@sm': { padding: 0, height: 'auto' },
                '@md': { padding: '0px $spacing-03' },
            }}
        >
            <FavoriteWrapper css={{ marginBottom: '$spacing-03' }}>
                <Heading as="h1" heading="pageTitle">
                    {t('title', 'Appointment')}
                </Heading>
                <TooltipGuidance />
            </FavoriteWrapper>
        </Box>
    );
};

TitleSection.propTypes = {
    t: PropTypes.func,
    lang: PropTypes.string,
};

TitleSection.defaultProps = {
    t: string => string,
    lang: 'id',
};

export const SummarySection = ({ dataSummary, t, isMobile }) => {
    const {
        total,
        new: waitingConfirmation,
        waiting_check_in: notCheckIn,
        check_in: waitingProcess,
        not_present: notPresent,
        ongoing,
        canceled,
        completed,
    } = dataSummary;

    return (
        <Flex
            css={{
                flexDirection: 'column',
                marginBottom: '$cozy',
                gap: '$spacing-06',
                '@sm': {
                    flexDirection: 'column',
                    padding: '$compact $spacing-03 0px $spacing-03',
                    overflowX: 'scroll',
                    scrollbarWidth: 0,
                    '&::-webkit-scrollbar': { width: 0, height: 0 },
                    marginBottom: '$compact',
                    gap: 0,
                },
            }}
        >
            <Grid
                css={{
                    width: '$full',
                    margin: '$spacing-05 0',
                    rowGap: '$spacing-05',
                    columnGap: '$spacing-06',
                    alignItems: 'start',
                    'grid-auto-rows': '1fr',
                    '& > div': {
                        borderBottom: '1px solid $bgBorder',
                    },
                    '@sm': {
                        'grid-template-columns': 'repeat(2, 1fr)',
                    },
                    '@md': {
                        'grid-template-columns': 'repeat(4, 1fr)',
                        '& > div:not(:last-child)': {
                            borderRight: '1px solid $bgBorder',
                        },
                        '& > div': {
                            borderBottom: 'none',
                        },
                    },
                }}
            >
                <Box css={{ '@md': { marginBottom: '$spacing-05' }, height: '$full' }}>
                    <SalesInformation
                        size={isMobile ? 'sm' : 'lg'}
                        percentage={0}
                        total={total}
                        label={t('label.summary.customerTotal')}
                        labelInfoTooltip={t('label.summaryTooltip.customerTotal')}
                        transparentBadge
                        isReverse
                        isNewSize
                        isDashboard
                    />
                </Box>
                <Box css={{ '@md': { marginBottom: '$spacing-05' }, height: '$full' }}>
                    <SalesInformation
                        size={isMobile ? 'sm' : 'lg'}
                        percentage={0}
                        total={waitingConfirmation}
                        label={t('label.summary.waitingConfirmation')}
                        labelInfoTooltip={t('label.summaryTooltip.waitingConfirmation')}
                        transparentBadge
                        isReverse
                        isNewSize
                        isDashboard
                    />
                </Box>
                <Box css={{ '@md': { marginBottom: '$spacing-05' }, height: '$full' }}>
                    <SalesInformation
                        size={isMobile ? 'sm' : 'lg'}
                        percentage={0}
                        total={notCheckIn}
                        label={t('label.summary.notCheckinYet')}
                        labelInfoTooltip={t('label.summaryTooltip.notCheckinYet')}
                        transparentBadge
                        isReverse
                        isNewSize
                        isDashboard
                    />
                </Box>
                <Box css={{ '@md': { marginBottom: '$spacing-05' }, height: '$full' }}>
                    <SalesInformation
                        size={isMobile ? 'sm' : 'lg'}
                        percentage={0}
                        total={waitingProcess}
                        label={t('label.summary.waitingToProcess')}
                        labelInfoTooltip={t('label.summaryTooltip.waitingToProcess')}
                        transparentBadge
                        isReverse
                        isNewSize
                        isDashboard
                    />
                </Box>
            </Grid>
            <Separator css={{ display: 'none', '@md': { display: 'block' } }} />
            <Grid
                css={{
                    width: '$full',
                    margin: '$spacing-05 0',
                    rowGap: '$spacing-05',
                    columnGap: '$spacing-06',
                    alignItems: 'start',
                    'grid-auto-rows': '1fr',
                    '& > div': {
                        borderBottom: '1px solid $bgBorder',
                    },
                    '@sm': {
                        'grid-template-columns': 'repeat(2, 1fr)',
                    },
                    '@md': {
                        'grid-template-columns': 'repeat(4, 1fr)',
                        '& > div:not(:last-child)': {
                            borderRight: '1px solid $bgBorder',
                        },
                        '& > div': {
                            borderBottom: 'none',
                        },
                    },
                }}
            >
                <Box css={{ '@md': { marginBottom: '$spacing-05' }, height: '$full' }}>
                    <SalesInformation
                        size={isMobile ? 'sm' : 'lg'}
                        percentage={0}
                        total={ongoing}
                        label={t('label.summary.ongoingReservation')}
                        labelInfoTooltip={t('label.summaryTooltip.ongoingReservation')}
                        transparentBadge
                        isReverse
                        isNewSize
                        isDashboard
                    />
                </Box>
                <Box css={{ '@md': { marginBottom: '$spacing-05' }, height: '$full' }}>
                    <SalesInformation
                        size={isMobile ? 'sm' : 'lg'}
                        percentage={0}
                        total={completed}
                        label={t('label.summary.finishReservation')}
                        labelInfoTooltip={t('label.summaryTooltip.finishReservation')}
                        transparentBadge
                        isReverse
                        isNewSize
                        isDashboard
                    />
                </Box>
                <Box css={{ '@md': { marginBottom: '$spacing-05' }, height: '$full' }}>
                    <SalesInformation
                        size={isMobile ? 'sm' : 'lg'}
                        percentage={0}
                        total={notPresent}
                        label={t('label.summary.customerAbsent')}
                        labelInfoTooltip={t('label.summaryTooltip.customerAbsent')}
                        transparentBadge
                        isReverse
                        isNewSize
                        isDashboard
                    />
                </Box>
                <Box css={{ '@md': { marginBottom: '$spacing-05' }, height: '$full' }}>
                    <SalesInformation
                        size={isMobile ? 'sm' : 'lg'}
                        percentage={0}
                        total={canceled}
                        label={t('label.summary.canceledReservation')}
                        labelInfoTooltip={t('label.summaryTooltip.canceledReservation')}
                        transparentBadge
                        isReverse
                        isNewSize
                        isDashboard
                    />
                </Box>
            </Grid>
        </Flex>
    );
};

SummarySection.propTypes = {
    dataSummary: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
    isMobile: PropTypes.bool,
    t: PropTypes.func,
};

SummarySection.defaultProps = {
    isMobile: false,
    t: string => string,
};

export const TableSection = ({
    isLoading,
    dataTable,
    handleFetch,
    searchQuery,
    handleUpdateStatus,
    onChangeRow,
    fetchFormAppointment,
    t,
    status,
    hasSatuSehatIntegration,
}) => (
    <Table
        columns={[
            {
                Header: t('label.headerTable.customer'),
                accessor: 'customer_name',
                isToggled: false,
                Cell: ({
                    row: {
                        original: {
                            customer: { name },
                        },
                    },
                }) => <div>{name}</div>,
            },
            ...(!hasSatuSehatIntegration
                ? [
                    {
                        Header: t('label.headerTable.recipient'),
                        accessor: 'service_recipient_name',
                        isMobileHeader: true,
                        Cell: ({
                            row: {
                                original: {
                                    services_recipient: { name },
                                },
                            },
                        }) => <div>{name}</div>,
                    },
                ]
                : [
                    {
                        Header: 'ID Satu Sehat',
                        accessor: 'satusehat_id',
                        Cell: NullableColumn,
                        unsortable: true,
                    },
                ]),
            {
                Header: t('label.headerTable.orderType'),
                accessor: 'transaction_type_name',
            },
            {
                Header: t('label.headerTable.reservationNo'),
                accessor: 'reservation_no',
                Cell: ({
                    row: {
                        original: { no },
                    },
                }) => <div>{no}</div>,
            },
            {
                Header: t('label.headerTable.status'),
                accessor: 'status',
                unsortable: true,
                Cell: ({
                    row: {
                        original: { status_code_reservation: statusCode },
                    },
                }) => {
                    let label = '',
                        type = '';

                    switch (statusCode) {
                        case RESERVATION_STATUS.NEW:
                            label = RESERVATION_STATUS_LABEL(t).NEW;
                            type = 'default';
                            break;
                        case RESERVATION_STATUS.NOT_CHECK_IN:
                            label = RESERVATION_STATUS_LABEL(t).NOT_CHECK_IN;
                            type = 'pending';
                            break;
                        case RESERVATION_STATUS.ON_PROCESS:
                            label = RESERVATION_STATUS_LABEL(t).ON_PROCESS;
                            type = 'process';
                            break;
                        case RESERVATION_STATUS.ON_GOING:
                            label = RESERVATION_STATUS_LABEL(t).ON_GOING;
                            type = 'process';
                            break;
                        case RESERVATION_STATUS.CANCELED:
                            label = RESERVATION_STATUS_LABEL(t).CANCELED;
                            type = 'error';
                            break;
                        case RESERVATION_STATUS.COMPLETED:
                            label = RESERVATION_STATUS_LABEL(t).COMPLETED;
                            type = 'success';
                            break;
                        case RESERVATION_STATUS.NOT_PRESENT:
                            label = RESERVATION_STATUS_LABEL(t).NOT_PRESENT;
                            type = 'error';
                            break;
                        default:
                            break;
                    }
                    return (
                        <TagStatus type={type} size="lg">
                            {label}
                        </TagStatus>
                    );
                },
            },
            ...(status !== RESERVATION_STATUS.CANCELED
                ? [
                    {
                        Header: t('label.headerTable.formStatus'),
                        accessor: 'formulirStatus',
                        unsortable: true,
                        Cell: ({
                            row: {
                                original: {
                                    formulirStatus: { count_formulir, count_formulir_done },
                                },
                            },
                        }) => (
                            <div>
                                {count_formulir_done} dari {count_formulir} formulir diisi
                            </div>
                        ),
                    },
                    {
                        Header: t('label.headerTable.updatedAt'),
                        accessor: 'updated_at',
                        headerCss: {
                            verticalAlign: 'top',
                        },
                        Cell: ({ value }) => <div>{value ? moment(value).format('DD-MM-YYYY, HH:mm:ss') : '-'}</div>,
                        unsortable: true,
                    },
                ]
                : []),
            {
                id: 'menu',
                Header: () => null,
                Cell: props => {
                    const {
                        row: {
                            original: {
                                id,
                                status_code_reservation: statusCode,
                                form: { status_code: formStatus },
                            },
                        },
                    } = props;
                    const allowedStatus = [
                        RESERVATION_STATUS.NEW,
                        RESERVATION_STATUS.NOT_CHECK_IN,
                        RESERVATION_STATUS.ON_PROCESS,
                        RESERVATION_STATUS.ON_GOING,
                        RESERVATION_STATUS.COMPLETED,
                    ];
                    const isCanFillForm =
                        allowedStatus.includes(statusCode) && formStatus === RESERVATION_FORM_STATUS.NOT_YET_FILLED;
                    const isDraftForm =
                        allowedStatus.includes(statusCode) && formStatus === RESERVATION_FORM_STATUS.DRAFT;
                    const isProceed = [
                        RESERVATION_STATUS.NEW,
                        RESERVATION_STATUS.NOT_CHECK_IN,
                        RESERVATION_STATUS.ON_PROCESS,
                    ].includes(statusCode);
                    let processLabel = t('label.headerTable.process');

                    if (![RESERVATION_STATUS.ON_PROCESS].includes(status)) {
                        if (status === RESERVATION_STATUS.NOT_PRESENT) {
                            processLabel = t('label.options.confirmationOrProcess');
                        } else if (statusCode === RESERVATION_STATUS.NEW) {
                            processLabel = t('label.options.confirmation');
                        } else if (statusCode === RESERVATION_STATUS.NOT_CHECK_IN) {
                            processLabel = t('label.options.checkIn');
                        }
                    }

                    return RowMenuColumn(
                        [
                            ...(isProceed
                                ? [
                                    {
                                        title: processLabel,
                                        onClick: () => handleUpdateStatus({ id, statusCode }),
                                    },
                                ]
                                : []),
                            ...(isCanFillForm
                                ? [
                                    {
                                        title: t('label.options.fillForm'),
                                        onClick: props => {
                                            const {
                                                id,
                                                no,
                                                services_recipient,
                                                customer,
                                                services,
                                                created_at,
                                                outlet_id,
                                                form,
                                            } = props.row.original;

                                            const payloadSubmitForm = {
                                                reservation_reservasi_id: id,
                                                reservation_no: no,
                                                reservation_penyedia_jasa_id: services[0].employee.id,
                                                reservation_created_at: created_at,
                                                outlet_id,
                                                customer_data_customer_id: customer.id,
                                            };

                                            if (id)
                                                fetchFormAppointment(
                                                    id,
                                                    services_recipient.id,
                                                    customer.id,
                                                    form.ownership_id,
                                                    payloadSubmitForm,
                                                    props.row.original,
                                                );
                                        },
                                    },
                                ]
                                : []),
                            ...(isDraftForm
                                ? [
                                    {
                                        title: t('label.options.contFillForm'),
                                        onClick: props => {
                                            const {
                                                id,
                                                no,
                                                services_recipient,
                                                customer,
                                                services,
                                                created_at,
                                                outlet_id,
                                                form,
                                            } = props.row.original;

                                            const payloadSubmitForm = {
                                                reservation_reservasi_id: id,
                                                reservation_no: no,
                                                reservation_penyedia_jasa_id: services[0].employee.id,
                                                reservation_created_at: created_at,
                                                outlet_id,
                                                customer_data_customer_id: customer.id,
                                            };

                                            if (id)
                                                fetchFormAppointment(
                                                    id,
                                                    services_recipient.id,
                                                    customer.id,
                                                    form.ownership_id,
                                                    payloadSubmitForm,
                                                    props.row.original,
                                                );
                                        },
                                    },
                                ]
                                : []),
                            {
                                title: t('label.options.viewCustDetail'),
                                onClick: props => {
                                    const { id, customer } = props.row.original;
                                    if (id) onChangeRow(customer.id, 'detail');
                                },
                            },
                        ],
                        undefined,
                        { cssContent: { maxWidth: '250px' } },
                    ).Cell(props);
                },
                unsortable: true,
            },
        ]}
        data={dataTable.data}
        fetchData={handleFetch}
        totalData={dataTable.meta.total}
        pageIndex={dataTable.meta.current_page - 1}
        isLoading={isLoading}
        searchQuery={searchQuery}
        additionalExceptionIds={['is_proceed']}
    />
);

TableSection.propTypes = {
    isMobile: PropTypes.bool,
    dataTable: PropTypes.arrayOf(PropTypes.object).isRequired,
    t: PropTypes.func,
    status: PropTypes.string.isRequired,
};

TableSection.defaultProps = {
    isMobile: false,
    t: string => string,
};

export const getField = (dt, indexFormDataMaster, indexField, onChangeValue, isDisabled, t) => {
    let selectOption = [];

    switch (Number(dt.form_field_type)) {
        case 1:
        case 2:
            selectOption = dt.form_field_choices.map(item => ({
                value: item,
                name: item,
            }));

            const valueSelected = { value: dt.form_field_value, name: dt.form_field_value };
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.form_field_is_required ? 'required' : 'label'}
                        >
                            {dt.form_field_name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <InputSelect
                            placeholder={`${t('placeholder.select', { ns: 'translation' })} ${dt.form_field_name}`}
                            option={selectOption}
                            value={valueSelected}
                            onChange={e => onChangeValue(indexFormDataMaster, indexField, e.value)}
                            disabled={isDisabled}
                        />
                    </Box>
                </Box>
            );
        case 3:
            const choices = dt.form_field_choices.map(item => ({
                id: item,
                name: item,
            }));
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.form_field_is_required ? 'required' : 'label'}
                        >
                            {dt.form_field_name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <InputSelectTag
                            placeholder={`${t('placeholder.select', { ns: 'translation' })} ${dt.form_field_name}`}
                            option={choices}
                            onChange={val => onChangeValue(indexFormDataMaster, indexField, val)}
                            defaultValue={dt.form_field_value}
                            showCounter
                            disabled={isDisabled}
                        />
                    </Box>
                </Box>
            );
        case 4:
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.form_field_is_required ? 'required' : 'label'}
                        >
                            {dt.form_field_name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <InputText
                            value={dt.form_field_value}
                            placeholder={`${t('placeholder.input', { ns: 'translation' })} ${dt.form_field_name}`}
                            onChange={e => onChangeValue(indexFormDataMaster, indexField, e.target.value)}
                            disabled={isDisabled}
                        />
                    </Box>
                </Box>
            );
        case 5:
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.form_field_is_required ? 'required' : 'label'}
                        >
                            {dt.form_field_name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <InputTextArea
                            placeholder={`${t('placeholder.input', { ns: 'translation' })} ${dt.form_field_name}`}
                            size="lg"
                            onChange={e => onChangeValue(indexFormDataMaster, indexField, e.target.value)}
                            defaultValue={dt.form_field_value}
                            disabled={isDisabled}
                            css={{ minHeight: '75px !important' }}
                        />
                    </Box>
                </Box>
            );
        case 6:
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.form_field_is_required ? 'required' : 'label'}
                        >
                            {dt.form_field_name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <InputDatePicker
                            placeholder={`${t('placeholder.select', { ns: 'translation' })} ${dt.form_field_name}`}
                            type="single"
                            value={dt.form_field_value ? moment(dt.form_field_value).toDate() : null}
                            onChange={val => onChangeValue(indexFormDataMaster, indexField, moment(val).toISOString())}
                            disabled={isDisabled}
                        />
                    </Box>
                </Box>
            );
        case 7:
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.form_field_is_required ? 'required' : 'label'}
                        >
                            {dt.form_field_name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <InputNumber
                            placeholder={`${t('placeholder.input', { ns: 'translation' })} ${dt.form_field_name}`}
                            value={dt.form_field_value}
                            size="lg"
                            onChange={e => onChangeValue(indexFormDataMaster, indexField, e.target.value)}
                            disabled={isDisabled}
                        />
                    </Box>
                </Box>
            );
        case 8:
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.form_field_is_required ? 'required' : 'label'}
                        >
                            {dt.form_field_name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <InputCounter
                            css={{
                                width: 200,
                            }}
                            min={0}
                            value={dt.form_field_value}
                            onChange={value => onChangeValue(indexFormDataMaster, indexField, value)}
                            disabled={isDisabled}
                        />
                    </Box>
                </Box>
            );
        case 9:
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.form_field_is_required ? 'required' : 'label'}
                        >
                            {dt.form_field_name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <UploadComponent
                            value={dt.form_field_value}
                            setValue={val => onChangeValue(indexFormDataMaster, indexField, val)}
                            {...{ uploadImageAddress, tokenUpload, PreviewImageDialog }}
                        />
                    </Box>
                </Box>
            );
        case 10:
        case 11:
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.form_field_is_required ? 'required' : 'label'}
                        >
                            {dt.form_field_name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <InputNumber
                            placeholder={`${t('placeholder.input', { ns: 'translation' })} ${dt.form_field_name}`}
                            value={dt.form_field_value}
                            size="lg"
                            onChange={e => onChangeValue(indexFormDataMaster, indexField, e.target.value)}
                            disabled={isDisabled}
                        />
                    </Box>
                </Box>
            );
        case 12:
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.form_field_is_required ? 'required' : 'label'}
                        >
                            {dt.form_field_name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <UploadComponent
                            value={dt.form_field_value}
                            setValue={val => onChangeValue(indexFormDataMaster, indexField, val)}
                            {...{ uploadImageAddress, tokenUpload, PreviewImageDialog }}
                        />
                    </Box>
                </Box>
            );
        case 13:
            return (
                <Box css={responsiveForm} key={dt.id}>
                    <Box css={responsiveLabel}>
                        <FormLabel
                            htmlFor="label"
                            css={responsiveFieldLabel}
                            variant={dt.form_field_is_required ? 'required' : 'label'}
                        >
                            {dt.form_field_name}
                        </FormLabel>
                    </Box>
                    <Box css={responsiveField}>
                        <InputTimePicker
                            placeholder={`${t('placeholder.select', { ns: 'translation' })} ${dt.form_field_name}`}
                            css={{
                                width: '100%',
                                '@md': {
                                    width: '200px',
                                },
                            }}
                            value={dt.form_field_value}
                            onChange={e => {
                                onChangeValue(indexFormDataMaster, indexField, e);
                            }}
                            disabled={isDisabled}
                        />
                    </Box>
                </Box>
            );
        default:
            break;
    }
};

export const FormField = ({
    dt,
    indexFormDataMaster,
    indexMasterDataForm,
    indexField,
    onChangeValue,
    t,
    isSatuSehat,
    isCompletedForm,
}) => {
    const {
        dataMasterOptions,
        fetchSnomedTerm,
        fetchMedicalMaster,
        fetchLocationList,
        fetchTreatmentList,
        setDataMasterOptions,
        isLoading,
        meta,
    } = useMasterData();
    const [answer, setAnswer] = useState(dt.answer);
    let selectOption = [];

    const fetchSnomedData = useCallback(fetchSnomedTerm, [dataMasterOptions]);
    const { addToast } = useContext(ToastContext);

    useDidMountEffect(() => {
        onChangeValue(indexFormDataMaster, indexMasterDataForm, indexField, answer);
    }, [answer]);

    if (isSatuSehat) {
        switch (dt.name.toLowerCase()) {
            case 'nama penyakit':
            case 'nama keluhan': {
                const [searchQuery, setSearchQuery] = useState('');

                const dataType = dt.name.toLowerCase() === 'nama penyakit' ? 'history-condition' : 'primary-condition';

                useEffect(() => {
                    if (dt.answer) {
                        const answers = JSON.parse(dt.answer) || [];
                        setDataMasterOptions(currentDataMaster => ({
                            ...currentDataMaster,
                            [dataType]: uniqBy(
                                answers.map(ans => ({ ...ans, isAnswer: true })),
                                'id',
                            ),
                        }));
                    }
                }, []);

                return (
                    <Box css={responsiveForm} key={dt.id}>
                        <Box css={responsiveLabel}>
                            <FormLabel
                                htmlFor="label"
                                css={responsiveFieldLabel}
                                variant={dt.is_required ? 'required' : 'label'}
                            >
                                {dt.name}
                            </FormLabel>
                        </Box>
                        <Box css={responsiveField}>
                            <InputSelectTag
                                showSelectAll={dataMasterOptions[dataType].length > 0}
                                showTagSelectAll={false}
                                placeholder={t('placeholder.select', { ns: 'translation' })}
                                option={dataMasterOptions[dataType]}
                                fetchData={e => { }}
                                onSearch={query => {
                                    setSearchQuery(query);
                                    if (query.length < 3) return;
                                    fetchSnomedData({ query, index: 0, type: dataType });
                                }}
                                disabled={isCompletedForm}
                                textEmptyItem={
                                    !searchQuery
                                        ? 'Ketik di field untuk mencari pilihan sesuai kebutuhan.'
                                        : 'Tidak ada hasil yang ditemukan.'
                                }
                                isLoading={isLoading}
                                onChange={val => {
                                    if (val && val.length > 0) {
                                        setAnswer(() => {
                                            const newAnswers = val.map(x => ({
                                                ...dataMasterOptions[dataType].find(data => data.id === x),
                                                isAnswer: true,
                                            }));
                                            const answers = uniqBy(newAnswers, 'id');
                                            setDataMasterOptions(currentDataMaster => ({
                                                ...currentDataMaster,
                                                [dataType]: uniqBy([...answers, ...currentDataMaster[dataType]], 'id'),
                                            }));
                                            return JSON.stringify(answers);
                                        });
                                    } else {
                                        setAnswer('');
                                    }
                                }}
                                sortBy={null}
                                value={answer ? JSON.parse(answer).map(x => x.id) : []}
                            />
                        </Box>
                    </Box>
                );
            }
            case 'nama alergi': {
                useEffect(() => {
                    fetchMedicalMaster({
                        index: 0,
                        type: 'allergy',
                        query: '',
                    });
                }, []);

                return (
                    <Box css={responsiveForm} key={dt.id}>
                        <Box css={responsiveLabel}>
                            <FormLabel
                                htmlFor="label"
                                css={responsiveFieldLabel}
                                variant={dt.is_required ? 'required' : 'label'}
                            >
                                {dt.name}
                            </FormLabel>
                        </Box>
                        <Box css={responsiveField}>
                            <InputSelectTag
                                showSelectAll={dataMasterOptions.allergy.length > 0}
                                showTagSelectAll={false}
                                placeholder={t('placeholder.select', { ns: 'translation' })}
                                option={dataMasterOptions.allergy}
                                disabled={isCompletedForm}
                                onChange={val => {
                                    if (val && val.length > 0) {
                                        setAnswer(() => {
                                            const newAnswers = val.map(x => ({
                                                ...dataMasterOptions.allergy.find(allergy => allergy.id === x),
                                                isAnswer: true,
                                            }));
                                            const answers = uniqBy(newAnswers, 'id');
                                            return JSON.stringify(answers);
                                        });
                                    } else {
                                        setAnswer('');
                                    }
                                }}
                                sortBy={null}
                                value={answer ? JSON.parse(answer).map(x => x.id) : []}
                            />
                        </Box>
                    </Box>
                );
            }
            case 'tipe kedatangan':
            case 'nama diagnosa': {
                const dataType = dt.name.toLowerCase() === 'nama diagnosa' ? 'diagnosis' : 'encounter';

                const fetchData = useCallback(fetchMedicalMaster, [
                    meta[dataType].pageIndex,
                    meta[dataType].hasMoreItems,
                    dataMasterOptions[dataType],
                ]);

                useEffect(() => {
                    fetchData({
                        index: 0,
                        type: dataType,
                        query: '',
                    });
                }, []);

                return (
                    <Box css={responsiveForm} key={dt.id}>
                        <Box css={responsiveLabel}>
                            <FormLabel
                                htmlFor="label"
                                css={responsiveFieldLabel}
                                variant={dt.is_required ? 'required' : 'label'}
                            >
                                {dt.name}
                            </FormLabel>
                        </Box>
                        <Box css={responsiveField}>
                            <InputSelect
                                placeholder={t('placeholder.select', { ns: 'translation' })}
                                option={dataMasterOptions[dataType]}
                                embedSearch
                                fetchData={e => {
                                    if (meta[dataType].hasMoreItems) {
                                        fetchData({
                                            ...e,
                                            index: meta[dataType].pageIndex,
                                            type: dataType,
                                        });
                                    }
                                }}
                                onSearch={query => {
                                    fetchData({
                                        index: 0,
                                        type: dataType,
                                        query,
                                    });
                                }}
                                disabled={isCompletedForm}
                                isLoading={isLoading}
                                value={answer ? JSON.parse(answer) : null}
                                onChange={e => {
                                    if (e) {
                                        const answerObj = dataMasterOptions[dataType].find(x => x.value === e.value);
                                        setAnswer(JSON.stringify(answerObj));
                                    } else {
                                        setAnswer('');
                                    }
                                }}
                            />
                        </Box>
                    </Box>
                );
            }
            case 'lokasi kedatangan': {
                const fetchData = useCallback(fetchLocationList, [
                    meta.location.pageIndex,
                    meta.location.hasMoreItems,
                    dataMasterOptions.location,
                ]);

                useEffect(() => {
                    fetchData({
                        index: 0,
                        query: '',
                    });
                }, []);

                return (
                    <Box css={responsiveForm} key={dt.id}>
                        <Box css={responsiveLabel}>
                            <FormLabel
                                htmlFor="label"
                                css={responsiveFieldLabel}
                                variant={dt.is_required ? 'required' : 'label'}
                            >
                                {dt.name}
                            </FormLabel>
                        </Box>
                        <Box css={responsiveField}>
                            <InputSelect
                                placeholder={t('placeholder.select', { ns: 'translation' })}
                                option={dataMasterOptions.location}
                                embedSearch
                                fetchData={e => {
                                    if (meta.location.hasMoreItems) {
                                        fetchData({
                                            ...e,
                                            index: meta.location.pageIndex,
                                        });
                                    }
                                }}
                                disabled={isCompletedForm}
                                onSearch={query => {
                                    fetchData({
                                        index: 0,
                                        query,
                                    });
                                }}
                                isLoading={isLoading}
                                value={answer ? JSON.parse(answer) : null}
                                onChange={e => {
                                    if (e) {
                                        const answerObj = dataMasterOptions.location.find(x => x.value === e.value);
                                        setAnswer(JSON.stringify(answerObj));
                                    } else {
                                        setAnswer('');
                                    }
                                }}
                            />
                        </Box>
                    </Box>
                );
            }
            case 'nama prosedur': {
                useEffect(() => {
                    if (dt.answer) {
                        const answers = JSON.parse(dt.answer) || [];
                        setDataMasterOptions(currentDataMaster => ({
                            ...currentDataMaster,
                            medicalTreatment: uniqBy(
                                answers.map(ans => ({ ...ans, isAnswer: true })),
                                'id',
                            ),
                        }));
                    }
                    fetchTreatmentList({
                        index: 0,
                        query: '',
                    });
                }, []);

                return (
                    <Box css={responsiveForm} key={dt.id}>
                        <Box css={responsiveLabel}>
                            <FormLabel
                                htmlFor="label"
                                css={responsiveFieldLabel}
                                variant={dt.is_required ? 'required' : 'label'}
                            >
                                {dt.name}
                            </FormLabel>
                        </Box>
                        <Box css={responsiveField}>
                            <InputSelectTag
                                showSelectAll={dataMasterOptions.medicalTreatment.length > 0}
                                showTagSelectAll={false}
                                placeholder={t('placeholder.select', { ns: 'translation' })}
                                option={dataMasterOptions.medicalTreatment}
                                fetchData={e => { }}
                                onSearch={query =>
                                    fetchTreatmentList({
                                        index: 0,
                                        query,
                                    })
                                }
                                disabled={isCompletedForm}
                                isLoading={isLoading}
                                onChange={val => {
                                    if (val && val.length > 0) {
                                        setAnswer(() => {
                                            const newAnswers = val.map(x => ({
                                                ...dataMasterOptions.medicalTreatment.find(data => data.id === x),
                                                isAnswer: true,
                                            }));
                                            const answers = uniqBy(newAnswers, 'id');
                                            setDataMasterOptions(currentDataMaster => ({
                                                ...currentDataMaster,
                                                medicalTreatment: uniqBy(
                                                    [...answers, ...currentDataMaster.medicalTreatment],
                                                    'id',
                                                ),
                                            }));
                                            return JSON.stringify(answers);
                                        });
                                    } else {
                                        setAnswer('');
                                    }
                                }}
                                sortBy={null}
                                value={answer ? JSON.parse(answer).map(x => x.id) : []}
                            />
                        </Box>
                    </Box>
                );
            }
            default:
                return <React.Fragment />;
        }
    } else {
        switch (dt.type) {
            case DATA_FIELD_LIST.INPUT_SELECT_MULTIPLE: {
                const choices = dt.choices.map(item => ({
                    id: item,
                    name: item,
                }));
                let answerValue = (answer || '').replace(/\[|\]/g, '')
                try {
                    answerValue = answer ? JSON.parse(answer) : [];
                } catch (e) {
                    answerValue = answerValue.split(',');
                }
                return (
                    <Box css={responsiveForm} key={dt.id}>
                        <Box css={responsiveLabel}>
                            <FormLabel
                                htmlFor="label"
                                css={responsiveFieldLabel}
                                variant={dt.is_required ? 'required' : 'label'}
                            >
                                {dt.name}
                            </FormLabel>
                        </Box>
                        <Box css={responsiveField}>
                            <InputSelectTag
                                placeholder={t('placeholder.select', { ns: 'translation' })}
                                option={choices}
                                onChange={val => {
                                    if (val && val.length > 0) {
                                        setAnswer(JSON.stringify(val));
                                    } else {
                                        setAnswer('');
                                    }
                                }}
                                disabled={isCompletedForm}
                                value={answerValue}
                                sortBy={null}
                            />
                        </Box>
                    </Box>
                );
            }
            case DATA_FIELD_LIST.INPUT_SELECT_SINGLE: {
                selectOption = dt.choices.map(item => ({
                    value: item,
                    name: item,
                }));

                const valueSelected = { value: answer, name: answer };

                return (
                    <Box css={responsiveForm} key={dt.id}>
                        <Box css={responsiveLabel}>
                            <FormLabel
                                htmlFor="label"
                                css={responsiveFieldLabel}
                                variant={dt.is_required ? 'required' : 'label'}
                            >
                                {dt.name}
                            </FormLabel>
                        </Box>
                        <Box css={responsiveField}>
                            <InputSelect
                                placeholder={t('placeholder.select', { ns: 'translation' })}
                                option={selectOption}
                                embedSearch
                                value={answer ? valueSelected : null}
                                onChange={e => {
                                    if (e) {
                                        setAnswer(e.value);
                                    } else {
                                        setAnswer('');
                                    }
                                }}
                                disabled={isCompletedForm}
                            />
                        </Box>
                    </Box>
                );
            }
            case DATA_FIELD_LIST.INPUT_CHECKBOX_MULTIPLE: {
                const choices = dt.choices || [];
                let answerValue = (answer || '').replace(/\[|\]/g, '')
                try {
                    answerValue = answer ? JSON.parse(answer) : [];
                } catch (e) {
                    answerValue = answerValue.split(',');
                }
                return (
                    <Box css={responsiveForm} key={dt.id}>
                        <Box css={responsiveLabel}>
                            <FormLabel
                                htmlFor="label"
                                css={responsiveFieldLabel}
                                variant={dt.is_required ? 'required' : 'label'}
                            >
                                {dt.name}
                            </FormLabel>
                        </Box>
                        <Box css={responsiveField}>
                            <CheckboxGroup
                                defaultValue={answerValue}
                                onChange={value => {
                                    if (value && value.length > 0) {
                                        setAnswer(JSON.stringify(value));
                                    } else {
                                        setAnswer('');
                                    }
                                }}
                                disabled={isCompletedForm}
                            >
                                <Flex gap={11}>
                                    <Flex direction="column" gap={2}>
                                        {choices.slice(0, Math.ceil(choices.length / 2)).map(choice => (
                                            <InputCheckbox
                                                key={choice}
                                                label={choice}
                                                value={choice}
                                                disabled={isCompletedForm}
                                            />
                                        ))}
                                    </Flex>
                                    <Flex direction="column" gap={2}>
                                        {choices.slice(Math.ceil(choices.length / 2)).map(choice => (
                                            <InputCheckbox
                                                key={choice}
                                                label={choice}
                                                value={choice}
                                                disabled={isCompletedForm}
                                            />
                                        ))}
                                    </Flex>
                                </Flex>
                            </CheckboxGroup>
                        </Box>
                    </Box>
                );
            }
            case DATA_FIELD_LIST.INPUT_TEXT:
                return (
                    <Box css={responsiveForm} key={dt.id}>
                        <Box css={responsiveLabel}>
                            <FormLabel
                                htmlFor="label"
                                css={responsiveFieldLabel}
                                variant={dt.is_required ? 'required' : 'label'}
                            >
                                {dt.name}
                            </FormLabel>
                        </Box>
                        <Box css={responsiveField}>
                            <FormGroup>
                                <FormLabel count={answer ? answer.length : 0} maxCount={40} />
                                <InputText
                                    value={answer}
                                    maxlength="40"
                                    placeholder={t('placeholder.inputHere', { ns: 'translation' })}
                                    disabled={isCompletedForm}
                                    onChange={e => {
                                        if (e && e.target) {
                                            setAnswer(e.target.value);
                                        } else {
                                            setAnswer('');
                                        }
                                    }}
                                />
                            </FormGroup>
                        </Box>
                    </Box>
                );
            case DATA_FIELD_LIST.INPUT_TEXT_AREA:
                return (
                    <Box css={responsiveForm} key={dt.id}>
                        <Box css={responsiveLabel}>
                            <FormLabel
                                htmlFor="label"
                                css={responsiveFieldLabel}
                                variant={dt.is_required ? 'required' : 'label'}
                            >
                                {dt.name}
                            </FormLabel>
                        </Box>
                        <Box css={responsiveField}>
                            <FormGroup>
                                <FormLabel count={answer ? answer.length : 0} maxCount={500} />
                                <InputTextArea
                                    placeholder={`${t('placeholder.inputHere', { ns: 'translation' })}`}
                                    size="lg"
                                    maxlength="500"
                                    onChange={e => {
                                        if (e && e.target) {
                                            setAnswer(e.target.value);
                                        } else {
                                            setAnswer('');
                                        }
                                    }}
                                    disabled={isCompletedForm}
                                    value={answer}
                                    css={{ minHeight: '75px !important' }}
                                />
                            </FormGroup>
                        </Box>
                    </Box>
                );
            case DATA_FIELD_LIST.INPUT_DATE_PICKER:
                return (
                    <Box css={responsiveForm} key={dt.id}>
                        <Box css={responsiveLabel}>
                            <FormLabel
                                htmlFor="label"
                                css={responsiveFieldLabel}
                                variant={dt.is_required ? 'required' : 'label'}
                            >
                                {dt.name}
                            </FormLabel>
                        </Box>
                        <Box css={responsiveField}>
                            <InputDatePicker
                                placeholder={`${t('placeholder.selectDate', { ns: 'translation' })}`}
                                type="single"
                                size="lg"
                                value={answer ? moment(answer, 'DD-MM-YYYY').toDate() : null}
                                disabled={isCompletedForm}
                                onChange={e => {
                                    if (e) {
                                        setAnswer(moment(e).format('DD-MM-YYYY'));
                                    } else {
                                        setAnswer('');
                                    }
                                }}
                            />
                        </Box>
                    </Box>
                );
            case DATA_FIELD_LIST.INPUT_PHONE:
                return (
                    <Box css={responsiveForm} key={dt.id}>
                        <Box css={responsiveLabel}>
                            <FormLabel
                                htmlFor="label"
                                css={responsiveFieldLabel}
                                variant={dt.is_required ? 'required' : 'label'}
                            >
                                {dt.name}
                            </FormLabel>
                        </Box>
                        <Box css={responsiveField}>
                            <InputNumber
                                placeholder={`${t('placeholder.inputHere', { ns: 'translation' })}`}
                                value={answer}
                                thousandSeparator={false}
                                size="lg"
                                disabled={isCompletedForm}
                                onChange={e => {
                                    if (e && e.target) {
                                        setAnswer(e.target.value);
                                    } else {
                                        setAnswer('');
                                    }
                                }}
                                allowLeadingZeros
                            />
                        </Box>
                    </Box>
                );
            case DATA_FIELD_LIST.INPUT_NUMBER_COUNTER:
                return (
                    <Box css={responsiveForm} key={dt.id}>
                        <Box css={responsiveLabel}>
                            <FormLabel
                                htmlFor="label"
                                css={responsiveFieldLabel}
                                variant={dt.is_required ? 'required' : 'label'}
                            >
                                {dt.name}
                            </FormLabel>
                        </Box>
                        <Box css={responsiveField}>
                            <InputCounter
                                min={0}
                                value={Number(answer || 0)}
                                onChange={value => {
                                    setAnswer(String(value));
                                }}
                                disableManualInput
                                disabledDecrement={Number(answer || 0) === 0}
                                disabled={isCompletedForm}
                                css={{
                                    width: 200,
                                }}
                            />
                        </Box>
                    </Box>
                );
            case DATA_FIELD_LIST.INPUT_FILE:
                return (
                    <Box css={responsiveForm} key={dt.id}>
                        <Box css={responsiveLabel}>
                            <FormLabel
                                htmlFor="label"
                                css={responsiveFieldLabel}
                                variant={dt.is_required ? 'required' : 'label'}
                            >
                                {dt.name}
                            </FormLabel>
                        </Box>
                        <Box css={responsiveField}>
                            <UploadComponent
                                value={answer}
                                setValue={val => {
                                    setAnswer(val);
                                }}
                                disabled={isCompletedForm}
                                {...{ uploadImageAddress, tokenUpload, PreviewImageDialog }}
                            />
                        </Box>
                    </Box>
                );
            case DATA_FIELD_LIST.INPUT_NUMBER_AUTO_GENERATE_NUMBER_BASE:
            case DATA_FIELD_LIST.INPUT_NUMBER_AUTO_GENERATE_TIME_BASE:
                return (
                    <Box css={responsiveForm} key={dt.id}>
                        <Box css={responsiveLabel}>
                            <FormLabel
                                htmlFor="label"
                                css={responsiveFieldLabel}
                                variant={dt.is_required ? 'required' : 'label'}
                            >
                                {dt.name}
                            </FormLabel>
                        </Box>
                        <Box css={responsiveField}>
                            <FormGroup>
                                <InputNumber value={answer} size="lg" readOnly isReadOnly thousandSeparator={false} />
                                <FormHelper>{t('form.label.autoFieldDesc')}</FormHelper>
                            </FormGroup>
                        </Box>
                    </Box>
                );
            case DATA_FIELD_LIST.INPUT_SIGNATURE:
                return (
                    <Box css={responsiveForm} key={dt.id}>
                        <Box css={responsiveLabel}>
                            <FormLabel
                                htmlFor="label"
                                css={responsiveFieldLabel}
                                variant={dt.is_required ? 'required' : 'label'}
                            >
                                {dt.name}
                            </FormLabel>
                        </Box>
                        <Box css={responsiveField}>
                            <InputSignature
                                defaultValue={dt.answer}
                                onChange={async val => {
                                    if (val) {
                                        try {
                                            const file = dataURItoBlob(val);
                                            const formdata = new FormData();
                                            const mimeType = file.type.split('/');
                                            formdata.append('userfile', file, `image.${mimeType[1]}`);
                    
                                            const res = await uploadImage(formdata);
                                            if (!res.item_image_path) throw Error(res.msg);
                                            setAnswer(res.item_image_path);
                                        } catch (error) {
                                            addToast({
                                                title: t('error.uploadFail', { ns: 'translation' }),
                                                description: catchError(error),
                                                variant: 'failed',
                                            });
                                        }
                                    } else {
                                        setAnswer(val);
                                    }
                                }}
                                disabled={isCompletedForm}
                            />
                        </Box>
                    </Box>
                );
            case DATA_FIELD_LIST.INPUT_TIME_PICKER: {
                return (
                    <Box css={responsiveForm} key={dt.id}>
                        <Box css={responsiveLabel}>
                            <FormLabel
                                htmlFor="label"
                                css={responsiveFieldLabel}
                                variant={dt.is_required ? 'required' : 'label'}
                            >
                                {dt.name}
                            </FormLabel>
                        </Box>
                        <Box css={responsiveField}>
                            <InputTimePicker
                                placeholder={`${t('placeholder.selectTime', { ns: 'translation' })}`}
                                value={answer ? moment(answer, 'HH:mm') : null}
                                onChange={e => {
                                    if (e) {
                                        setAnswer(moment(e).format('HH:mm'));
                                    } else {
                                        setAnswer('');
                                    }
                                }}
                                css={{
                                    width: '100%',
                                }}
                                disabled={isCompletedForm}
                                onInputOpenChange={open => {
                                    if (open && !answer) {
                                        setAnswer(moment(new Date()).format('HH:mm'));
                                    }
                                }}
                            />
                        </Box>
                    </Box>
                );
            }
            default:
                return <React.Fragment />;
        }
    }
};
