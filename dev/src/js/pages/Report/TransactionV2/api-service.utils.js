import React from 'react';
import * as reportApi from '../../../data/reports';
import { TEMPLATE_REPORT_TYPE } from '../../../v2-enum';
import ToastTitle from '../../../v2-components/ToastTitle';
import { catchError, currency } from '../../../utils/helper';
import { formatDate } from './utils';
import { TRANSACTION_TIME_TYPE } from './enums';

export const defaultStateFetchReportData = {
    pageSize: 10,
    pageIndex: 0,
    sortDirection: '',
    sortAccessor: '',
    filterKeyword: '',
};

const reformatCurrency = val => (val >= 0 ? currency({ value: val, decimal: true }) : `(${currency({ value: val * -1, decimal: true })})`);

const reformatReportData = data => data.map(x => ({
    ...x,
    payment_method_name: x.payment_method_name === 'Cash' ? 'Tunai' : x.payment_method_name,
    product_price: reformatCurrency(x.product_price),
    product_extra_name: x.product_extra_name.join(', '),
    product_discount_amount: reformatCurrency(x.product_discount_amount),
    transaction_discount_amount: reformatCurrency(x.transaction_discount_amount),
    refund_payment_method_name: x.refund_payment_method_name === 'Cash' ? 'Tunai' : x.refund_payment_method_name,
    refund_payment_method: x.refund_payment_method_name === 'Cash' ? 'Tunai' : x.refund_payment_method_name,
    refund_date: formatDate({ value: x.refund_date, formatInput: 'YYYY-MM-DD HH:mm', formatOutput: 'DD MMMM YYYY, HH:mm' }, '-'),
    transaction_date: formatDate({ value: x.transaction_date, formatInput: 'YYYY-MM-DD HH:mm', formatOutput: 'DD MMMM YYYY, HH:mm' }, '-'),
    payment_date: formatDate({ value: x.payment_date, formatInput: 'YYYY-MM-DD HH:mm', formatOutput: 'DD MMMM YYYY, HH:mm' }, '-'),
    total_gross_sales: reformatCurrency(x.total_gross_sales),
    total_sales: reformatCurrency(x.total_sales),
    total_payment: reformatCurrency(x.total_payment),
    unpaid_bill_amount: reformatCurrency(x.unpaid_bill_amount),
    tax_amount: reformatCurrency(x.tax_amount),
    service_charge_amount: reformatCurrency(x.service_charge_amount),
    shipment_cost: reformatCurrency(x.shipment_cost),
    other_cost: reformatCurrency(x.other_cost),
    mdr_amount: reformatCurrency(x.mdr_amount),
    refund_amount: reformatCurrency(x.refund_amount),
    compliment_amount: reformatCurrency(x.compliment_amount),
    product_tax_amount: reformatCurrency(x.product_tax_amount),
    point_amount: reformatCurrency(x.point_amount),
    coupon_amount: reformatCurrency(x.coupon_amount),
    service_charge_mdr_amount: reformatCurrency(x.service_charge_mdr_amount),
    rounding: reformatCurrency(x.rounding),
    product_modal_price: reformatCurrency(x.product_modal_price),
    product_sub_total: reformatCurrency(x.product_sub_total),
    total_discount_amount: reformatCurrency(x.total_discount_amount),
    product_commission_amount: reformatCurrency(x.product_commission_amount),
    transaction_commission_amount: reformatCurrency(x.transaction_commission_amount),
    total_commission_amount: reformatCurrency(x.total_commission_amount),
    transaction_id: x.id,
    id: x.transaction_detail_id ? x.transaction_detail_id : x.transaction_id,
})).map((x) => {
    let retval = {};
    const keys = Object.keys(x);

    keys.map((key) => {
        retval = { ...retval, [key]: (String(x[key]).length > 0) ? x[key] : '-' };

        return key;
    });

    return retval;
});

export const handleFetchReportData = async ({
    appliedReportFilterData, tableState, addToast, setState, filterBranch, selectedCustomTemplate,
}) => {
    setState({
        tableLoading: true,
    });

    try {
        const filterData = appliedReportFilterData;
        let state = tableState;

        if (filterData) {
            let paymentStatus = null;
            let orderTypes = null;
            let paymentMethodIds = null;
            let cashierIds = null;
            let multiPriceTypes = null;

            if (filterData.paymentStatus.length > 0) paymentStatus = filterData.paymentStatus.join(',');
            if (filterData.orderType.length > 0) orderTypes = filterData.orderType.map(id => +id).join(',');
            if (filterData.paymentMethod.length > 0) paymentMethodIds = filterData.paymentMethod.map(id => +id).join(',');
            if (filterData.cashiers.length > 0) cashierIds = filterData.cashiers.map(id => +id).join(',');
            if (filterData.priceType.length > 0) multiPriceTypes = filterData.priceType.join(',');

            state = {
                ...state,
                displayed: filterData.display,
                categoryId: filterData.categoryId,
                filterKeyword: filterData.keyword,
                filterOutlet: filterBranch,
                paymentStatus,
                orderTypes,
                paymentMethodIds,
                cashierIds,
                multiPriceTypes,
            };
        }

        const payload = {
            start_date: formatDate({ value: appliedReportFilterData.startDate, formatOutput: 'YYYY-MM-DD HH:mm' }),
            end_date: formatDate({ value: appliedReportFilterData.endDate, formatOutput: 'YYYY-MM-DD HH:mm' }),
            limit: state.pageSize,
            page: state.pageIndex + 1,
            is_payment: +(filterData.transactionTimeType === TRANSACTION_TIME_TYPE.PAYMENT_TIME),
            template_id: selectedCustomTemplate,
            ...(state.sortAccessor && {
                sort: `${state.sortDirection !== 'ASC' ? '-' : ''}${state.sortAccessor}`,
            }),
            ...(state.filterOutlet && {
                outlet_id: state.filterOutlet,
            }),
            ...(state.filterKeyword && {
                search: state.filterKeyword,
            }),
            ...(state.paymentStatus && {
                payment_status: state.paymentStatus,
            }),
            ...(state.orderTypes && {
                order_types: state.orderTypes,
            }),
            ...(state.paymentMethodIds && {
                payment_method_ids: state.paymentMethodIds,
            }),
            ...(state.cashierIds && {
                cashier_ids: state.cashierIds,
            }),
            ...(state.multiPriceTypes && {
                multi_price_types: state.multiPriceTypes,
            }),
            min_total_sales: filterData.salesMin ? +filterData.salesMin : 0,
            ...filterData.salesMax ? { max_total_sales: +filterData.salesMax } : {},
            min_gross_sales: filterData.grossSalesMin ? +filterData.grossSalesMin : 0,
            ...filterData.grossSalesMax ? { max_gross_sales: +filterData.grossSalesMax } : {},
        };

        const res = await reportApi.getDetailSales(payload);
        const { data, meta: { current_page: currentPage } } = res;
        const newData = reformatReportData(data);

        const metaRes = await reportApi.getDetailSalesMeta(payload);

        setState({
            tableLoading: false,
            reportData: {
                summary: metaRes ? {
                    ...metaRes,
                    total_transaction: metaRes.transaction_count,
                    total_receivables: metaRes.total_unpaid,
                } : {},
                data: newData,
                totalData: metaRes ? metaRes.total_data : 0,
                pageIndex: currentPage > 0 ? currentPage - 1 : 0,
                pageSize: tableState.pageSize,
                tableState,
            },
        });
    } catch (err) {
        setState({
            tableLoading: false,
        }, () => {
            addToast({
                title: <ToastTitle type="failed" />,
                description: catchError(err),
                variant: 'failed',
            });
        });
    }
};

export const handleFetchCustomTemplateMaster = async ({
    addToast, setState,
}) => {
    try {
        let reformatCustomTemplateMaster = {};
        let customTemplateMaster = [];

        const res = await reportApi.getCustomTemplateMaster({
            report_type: TEMPLATE_REPORT_TYPE.SALES_DETAILS,
        });

        res.data.forEach((x) => {
            reformatCustomTemplateMaster = {
                ...reformatCustomTemplateMaster,
                [x.key]: {
                    ...x,
                    id: x.key,
                    text: x.label,
                },
            };
            customTemplateMaster = [...customTemplateMaster, x];
        });

        setState({
            customTemplateMaster,
            reformatCustomTemplateMaster,
        });
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

export const handleFetchCustomTemplate = async ({
    addToast, setState, onFetchReportData,
}) => {
    try {
        const customTemplate = [];
        let selectedCustomTemplate = 0;

        const res = await reportApi.getCustomTemplate({
            report_type: TEMPLATE_REPORT_TYPE.SALES_DETAILS,
        });

        res.data.forEach((x) => {
            customTemplate.push({
                ...x, value: x.id, label: x.name, isDefault: x.is_default,
            });

            if (x.is_default) selectedCustomTemplate = x.id;
        });

        setState({ customTemplate, merchantTemplateCount: res.merchant_template_count, selectedCustomTemplate }, () => {
            onFetchReportData();
        });
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

// TODO: remove code ini, sepertinya sudah tidak dipake
export const handleFetchCustomTemplateDetail = async ({
    addToast, setState, templateId,
}, onSuccessCalbackFn = () => { }) => {
    try {
        const res = await reportApi.getCustomTemplateDetail(templateId, {
            report_type: TEMPLATE_REPORT_TYPE.SALES_DETAILS,
        });

        setState({ customTemplateDetail: res.data }, () => {
            onSuccessCalbackFn();
        });
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

export const handleSaveTemplate = async ({
    addToast, setState, templateId, payload, t,
}, onSuccessCalbackFn = () => { }) => {
    try {
        if (!templateId) {
            await reportApi.createCustomTemplate(payload);
        } else {
            await reportApi.updateCustomTemplate(templateId, payload);
        }

        setState({ customTemplateDetail: null }, () => {
            onSuccessCalbackFn();
            addToast({
                title: <ToastTitle type="success" />,
                description: !templateId ? t('toast.templateSaveSuccess', 'Template berhasil ditambahkan') : t('toast.templateUpdateSuccess', 'Template berhasil diubah'),
                variant: 'success',
            });
        });
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

export const handleDeleteTemplate = async ({
    addToast, setState, templateId, t,
}, onSuccessCalbackFn = () => { }) => {
    try {
        await reportApi.deleteCustomTemplate(templateId);

        setState({ customTemplateDetail: null }, () => {
            onSuccessCalbackFn();
            addToast({
                title: <ToastTitle type="success" />,
                description: t('toast.templateDeleteSuccess', 'Template berhasil dihapus'),
                variant: 'success',
            });
        });
    } catch (err) {
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};

export const handleExportReport = async ({
    filterBranch, selectedCustomTemplate, appliedReportFilterData, addToast, additionalPayload,
}, onSuccessCalbackFn = () => { }) => {
    try {
        const filterData = appliedReportFilterData;

        const {
            paymentStatus, orderType, salesMin, salesMax, grossSalesMin, grossSalesMax,
            paymentMethod, cashiers, priceType,
        } = filterData;

        const filter = {
            payment_status: paymentStatus,
            order_types: orderType.map(x => +x),
            min_total_sales: salesMin ? +salesMin : 0,
            ...salesMax ? { max_total_sales: +salesMax } : {},
            min_gross_sales: grossSalesMin ? +grossSalesMin : 0,
            ...grossSalesMax ? { max_gross_sales: +grossSalesMax } : {},
            payment_method_ids: paymentMethod ? (paymentMethod || []).map(item => parseInt(item, 10)) : [],
            cashier_ids: cashiers ? (cashiers || []).map(item => parseInt(item, 10)) : [],
            multi_price_types: priceType,
        };

        const payload = {
            template_id: selectedCustomTemplate,
            is_payment: +(filterData.transactionTimeType === TRANSACTION_TIME_TYPE.PAYMENT_TIME),
            start_date: formatDate({ value: filterData.startDate, formatOutput: 'YYYY-MM-DD HH:mm' }),
            end_date: formatDate({ value: filterData.endDate, formatOutput: 'YYYY-MM-DD HH:mm' }),
            search: filterData.keyword,
            filter,
            ...filterBranch ? { outlet_id: filterBranch } : {},
            ...additionalPayload,
        };

        await reportApi.downloadListLaporanDetilPenjualanDatamart(payload);

        onSuccessCalbackFn();
    } catch (err) {
        if (err.cause && err.cause.status && err.cause.status.code) {
            if (Number(err.cause.status.code) === 42200001) {
                onSuccessCalbackFn();
                return;
            }
        }
        addToast({
            title: <ToastTitle type="failed" />,
            description: catchError(err),
            variant: 'failed',
        });
    }
};
