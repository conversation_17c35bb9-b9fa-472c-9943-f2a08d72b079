// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`pages/Report/Sales/TransactionV2 renders component without crashing 1`] = `
<div
  class="c-PJLV c-PJLV-ihShOre-css content-wrapper"
>
  <div
    class="notifications-wrapper"
  />
  <div
    class="c-dhzjXW c-hlVbBo c-dhzjXW-iTKOFX-direction-column c-dhzjXW-ibzaees-css"
    id="retina-container"
  >
    <div
      class="c-dhzjXW c-dhzjXW-iTKOFX-direction-column c-dhzjXW-irEjuD-align-stretch c-dhzjXW-awKDG-justify-start c-dhzjXW-kVNAnR-wrap-noWrap c-dhzjXW-Ejbvo-gap-5"
    >
      <div
        class="c-cdrdfM c-cdrdfM-iiuslmR-css"
      >
        <div
          class="c-dhzjXW c-dhzjXW-ejCoEP-direction-row c-dhzjXW-irEjuD-align-stretch c-dhzjXW-awKDG-justify-start c-dhzjXW-kVNAnR-wrap-noWrap c-dhzjXW-iJdlGu-css"
        >
          <div
            class="c-dhzjXW c-dhzjXW-iTKOFX-direction-column c-dhzjXW-JrrAq-align-start c-dhzjXW-awKDG-justify-start c-dhzjXW-kVNAnR-wrap-noWrap c-dhzjXW-kQvozX-gap-2 c-dhzjXW-iOLFJF-css"
          >
            <div
              class="c-dhzjXW c-dhzjXW-ejCoEP-direction-row c-dhzjXW-jroWjL-align-center c-dhzjXW-awKDG-justify-start c-dhzjXW-kVNAnR-wrap-noWrap c-dhzjXW-kdBMZO-gap-3"
            >
              <svg
                color="#404545"
                fill="none"
                height="24"
                size="24"
                viewBox="0 0 24 24"
                width="24"
              >
                <rect
                  height="13"
                  rx="1"
                  stroke="#404545"
                  stroke-width="2"
                  width="14"
                  x="5"
                  y="6"
                />
                <path
                  d="M7 4C6.44772 4 6 4.44772 6 5C6 5.55228 6.44772 6 7 6V4ZM9 6C9.55228 6 10 5.55228 10 5C10 4.44772 9.55228 4 9 4V6ZM7 6H9V4H7V6Z"
                  fill="#404545"
                />
                <path
                  d="M15 4C14.4477 4 14 4.44772 14 5C14 5.55228 14.4477 6 15 6V4ZM17 6C17.5523 6 18 5.55228 18 5C18 4.44772 17.5523 4 17 4V6ZM15 6H17V4H15V6Z"
                  fill="#404545"
                />
                <path
                  d="M5 9C4.44772 9 4 9.44772 4 10C4 10.5523 4.44772 11 5 11V9ZM19 11C19.5523 11 20 10.5523 20 10C20 9.44772 19.5523 9 19 9V11ZM5 11H19V9H5V11Z"
                  fill="#404545"
                />
              </svg>
              <div
                class="c-PJLV"
              >
                <p
                  class="c-gPjxah c-gPjxah-MVhUR-variant-label c-gPjxah-cGqoHZ-color-secondary c-gPjxah-cmVlgk-align-left"
                >
                   - 
                </p>
              </div>
            </div>
          </div>
          <div
            class="c-dhzjXW c-dhzjXW-ejCoEP-direction-row c-dhzjXW-irEjuD-align-stretch c-dhzjXW-bZmKkd-justify-end c-dhzjXW-kVNAnR-wrap-noWrap c-dhzjXW-Ejbvo-gap-5 c-dhzjXW-ibLqFhw-css"
          >
            <div
              class="c-PJLV c-PJLV-ifgPVft-css"
            >
              <div
                class="c-dhzjXW c-dhzjXW-ejCoEP-direction-row c-dhzjXW-irEjuD-align-stretch c-dhzjXW-awKDG-justify-start c-dhzjXW-kVNAnR-wrap-noWrap c-dhzjXW-ijIhRYs-css"
              >
                <div
                  style="width:100%"
                >
                  <div
                    class="c-PJLV c-PJLV-icFOVhH-css"
                  >
                    <div
                      class="c-cfmUmn c-cfmUmn-fuukQF-size-sm c-cfmUmn-bYyCQg-isElement-true c-cfmUmn-ebktHq-isLetter-true"
                    >
                      <div
                        class="c-gPjxah c-GloIF c-bEvjGT c-eALiIO c-gPjxah-dAcyhR-variant-placeholder c-gPjxah-gWaLqH-color-primary c-gPjxah-cmVlgk-align-left c-GloIF-fuukQF-size-sm"
                      >
                        <svg
                          color="#404545"
                          fill="none"
                          height="24"
                          size="24"
                          viewBox="0 0 24 24"
                          width="24"
                        >
                          <rect
                            height="13"
                            rx="1"
                            stroke="#404545"
                            stroke-width="2"
                            width="14"
                            x="5"
                            y="6"
                          />
                          <path
                            d="M7 4C6.44772 4 6 4.44772 6 5C6 5.55228 6.44772 6 7 6V4ZM9 6C9.55228 6 10 5.55228 10 5C10 4.44772 9.55228 4 9 4V6ZM7 6H9V4H7V6Z"
                            fill="#404545"
                          />
                          <path
                            d="M15 4C14.4477 4 14 4.44772 14 5C14 5.55228 14.4477 6 15 6V4ZM17 6C17.5523 6 18 5.55228 18 5C18 4.44772 17.5523 4 17 4V6ZM15 6H17V4H15V6Z"
                            fill="#404545"
                          />
                          <path
                            d="M5 9C4.44772 9 4 9.44772 4 10C4 10.5523 4.44772 11 5 11V9ZM19 11C19.5523 11 20 10.5523 20 10C20 9.44772 19.5523 9 19 9V11ZM5 11H19V9H5V11Z"
                            fill="#404545"
                          />
                        </svg>
                      </div>
                      <input
                        autocomplete="false"
                        class="c-bOqFYj c-bOqFYj-fuukQF-size-sm c-bOqFYj-hOCEVo-isPicker-true"
                        placeholder="Pilih Tanggal"
                        value="Invalid date - Invalid date"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="c-dhzjXW c-dhzjXW-ejCoEP-direction-row c-dhzjXW-jroWjL-align-center c-dhzjXW-awKDG-justify-start c-dhzjXW-XefLA-wrap-wrap c-dhzjXW-kdBMZO-gap-3"
            >
              <button
                class="c-cbAPXR c-cbAPXR-yxjld-buttonType-ghost c-cbAPXR-iFPhHj-size-sm c-cbAPXR-fdRLKs-withIcon-true c-cbAPXR-ibPNjhd-css"
                type="button"
              >
                <div
                  class="c-kJFYXl"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="24"
                    size="24"
                    viewBox="0 0 24 24"
                    width="24"
                  >
                    <path
                      d="M10 5H4v14h6V5ZM4 21c-.55 0-1.02-.196-1.412-.587A1.93 1.93 0 0 1 2 19V5c0-.55.196-1.02.588-1.412A1.93 1.93 0 0 1 4 3h14c.55 0 1.021.196 1.413.588.392.392.588.863.587 1.412v1a.968.968 0 0 1-.288.713A.964.964 0 0 1 19 7a.972.972 0 0 1-.712-.288A.965.965 0 0 1 18 6V5h-6v14h6v-1c0-.283.096-.52.288-.712A.973.973 0 0 1 19 17c.283 0 .52.095.713.288A.96.96 0 0 1 20 18v1c0 .55-.196 1.021-.587 1.413A1.92 1.92 0 0 1 18 21H4Zm15-6a.965.965 0 0 1-.712-.288A.973.973 0 0 1 18 14v-1h-1a.965.965 0 0 1-.712-.288A.973.973 0 0 1 16 12c0-.283.095-.52.288-.712A.97.97 0 0 1 17 11h1v-1c0-.283.096-.52.288-.712A.972.972 0 0 1 19 9c.283 0 .52.095.713.288A.96.96 0 0 1 20 10v1h1c.283 0 .521.096.713.288.192.192.288.43.287.712 0 .283-.097.52-.288.713A.957.957 0 0 1 21 13h-1v1a.968.968 0 0 1-.288.713A.964.964 0 0 1 19 15Z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                Atur Tabel
              </button>
              <button
                class="c-cbAPXR c-cbAPXR-yxjld-buttonType-ghost c-cbAPXR-iFPhHj-size-sm c-cbAPXR-fdRLKs-withIcon-true c-cbAPXR-ifcBvjU-css"
              >
                <div
                  class="c-kJFYXl"
                >
                  <svg
                    color="currentColor"
                    fill="none"
                    height="24"
                    size="24"
                    viewBox="0 0 24 24"
                    width="24"
                  >
                    <path
                      d="M13.2425 18.1894L13.4851 19.1595L13.2425 18.1894ZM14.1525 12.2565L15 12.7873L14.1525 12.2565ZM18.6492 5.07654L19.4967 5.60732L18.6492 5.07654ZM9.8475 12.2565L9 12.7873L9.8475 12.2565ZM5.35067 5.07654L4.50317 5.60733L5.35067 5.07654ZM4.50317 5.60733L9 12.7873L10.695 11.7257L6.19817 4.54575L4.50317 5.60733ZM9 12.7873V17.7192H11V12.7873H9ZM5.39305 6H18.6068V4H5.39305V6ZM17.8017 4.54576L13.305 11.7257L15 12.7873L19.4967 5.60732L17.8017 4.54576ZM13 12.7873V17.2192H15V12.7873H13ZM11.4851 19.6595L13.4851 19.1595L13 17.2192L11 17.7192L11.4851 19.6595ZM13 17.2192H13L13.4851 19.1595C14.3754 18.9369 15 18.137 15 17.2192H13ZM13.305 11.7257C13.1057 12.0439 13 12.4118 13 12.7873H15L13.305 11.7257ZM18.6068 6C17.8602 6 17.4054 5.17845 17.8017 4.54576L19.4967 5.60732C19.9346 4.90803 19.432 4 18.6068 4V6ZM9 17.7192C9 19.0204 10.2228 19.9751 11.4851 19.6595L11 17.7192H9ZM9 12.7873H11C11 12.4118 10.8943 12.0439 10.695 11.7257L9 12.7873ZM6.19817 4.54575C6.59444 5.17846 6.13961 6 5.39305 6V4C4.5679 4 4.06519 4.90802 4.50317 5.60733L6.19817 4.54575Z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                Filter
              </button>
            </div>
          </div>
        </div>
        <div
          class="c-bbSzHI c-bbSzHI-igJPubW-css"
          data-orientation="horizontal"
          role="separator"
        />
        <div
          class="c-PJLV c-PJLV-inruJ-css"
        >
          <div
            class="c-dhzjXW c-dhzjXW-ejCoEP-direction-row c-dhzjXW-irEjuD-align-stretch c-dhzjXW-awKDG-justify-start c-dhzjXW-kVNAnR-wrap-noWrap c-dhzjXW-kdBMZO-gap-3 c-dhzjXW-ieFWNPI-css"
          >
            <div
              style="width:$full;@md:[object Object]"
            >
              <div
                class="c-cfmUmn c-cfmUmn-fuukQF-size-sm c-cfmUmn-bYyCQg-isElement-true c-cfmUmn-ebktHq-isLetter-true"
              >
                <div
                  class="c-gPjxah c-GloIF c-bEvjGT c-eALiIO c-gPjxah-dAcyhR-variant-placeholder c-gPjxah-gWaLqH-color-primary c-gPjxah-cmVlgk-align-left c-GloIF-fuukQF-size-sm"
                >
                  <svg
                    color="#404545"
                    fill="none"
                    height="24"
                    size="24"
                    viewBox="0 0 24 24"
                    width="24"
                  >
                    <circle
                      cx="11.3091"
                      cy="11.3091"
                      r="6.30911"
                      stroke="#404545"
                      stroke-width="2"
                    />
                    <path
                      d="M16.5249 15.1049C16.1345 14.7142 15.5014 14.7139 15.1106 15.1042C14.7199 15.4945 14.7196 16.1277 15.1099 16.5184L16.5249 15.1049ZM19.2939 20.7067C19.6842 21.0974 20.3173 21.0977 20.7081 20.7074C21.0988 20.3171 21.0991 19.6839 20.7088 19.2932L19.2939 20.7067ZM15.1099 16.5184L19.2939 20.7067L20.7088 19.2932L16.5249 15.1049L15.1099 16.5184Z"
                      fill="#404545"
                    />
                  </svg>
                </div>
                <input
                  class="c-bOqFYj c-bOqFYj-fuukQF-size-sm c-bOqFYj-iiIClND-css"
                  placeholder="placeholder.search"
                />
                <div
                  class="c-gPjxah c-GloIF c-ffvmrj c-guIpKl c-gPjxah-dAcyhR-variant-placeholder c-gPjxah-gWaLqH-color-primary c-gPjxah-cmVlgk-align-left c-GloIF-fuukQF-size-sm"
                >
                  <div />
                </div>
              </div>
            </div>
            <div
              class="c-PJLV c-PJLV-idBtnOl-css"
            >
              <div
                class="c-cfmUmn c-cfmUmn-fuukQF-size-sm c-cfmUmn-bYyCQg-isElement-true c-cfmUmn-ebktHq-isLetter-true"
              >
                <div
                  class="c-gPjxah c-GloIF c-bEvjGT c-eALiIO c-gPjxah-dAcyhR-variant-placeholder c-gPjxah-gWaLqH-color-primary c-gPjxah-cmVlgk-align-left c-GloIF-fuukQF-size-sm"
                >
                  <svg
                    color="#404545"
                    fill="none"
                    height="24"
                    size="24"
                    viewBox="0 0 24 24"
                    width="24"
                  >
                    <rect
                      height="13"
                      rx="1"
                      stroke="#404545"
                      stroke-width="2"
                      width="14"
                      x="5"
                      y="6"
                    />
                    <path
                      d="M7 4C6.44772 4 6 4.44772 6 5C6 5.55228 6.44772 6 7 6V4ZM9 6C9.55228 6 10 5.55228 10 5C10 4.44772 9.55228 4 9 4V6ZM7 6H9V4H7V6Z"
                      fill="#404545"
                    />
                    <path
                      d="M15 4C14.4477 4 14 4.44772 14 5C14 5.55228 14.4477 6 15 6V4ZM17 6C17.5523 6 18 5.55228 18 5C18 4.44772 17.5523 4 17 4V6ZM15 6H17V4H15V6Z"
                      fill="#404545"
                    />
                    <path
                      d="M5 9C4.44772 9 4 9.44772 4 10C4 10.5523 4.44772 11 5 11V9ZM19 11C19.5523 11 20 10.5523 20 10C20 9.44772 19.5523 9 19 9V11ZM5 11H19V9H5V11Z"
                      fill="#404545"
                    />
                  </svg>
                </div>
                <input
                  autocomplete="false"
                  class="c-bOqFYj c-bOqFYj-fuukQF-size-sm c-bOqFYj-hOCEVo-isPicker-true"
                  placeholder="Pilih Tanggal"
                  value="Invalid date - Invalid date"
                />
              </div>
            </div>
            <div
              class="c-PJLV"
            >
              <div
                class="c-gqtLJE"
              >
                <div
                  aria-expanded="false"
                  aria-haspopup="listbox"
                  aria-labelledby="downshift-0-label"
                  class="c-dhzjXW c-fzzJio c-fzzJio-fuukQF-size-sm"
                  role="combobox"
                >
                  <button
                    aria-haspopup="true"
                    aria-label="open menu"
                    class="c-jNnNrL c-jNnNrL-gzgkFU-size-sm"
                    data-toggle="true"
                    role="button"
                    type="button"
                  >
                    <p
                      class="c-gPjxah c-gPjxah-gWaLqH-color-primary c-gPjxah-cmVlgk-align-left c-gPjxah-iiLHpdH-css"
                    >
                      Waktu Order
                    </p>
                  </button>
                  <div
                    aria-haspopup="true"
                    aria-label="open menu"
                    class="c-kZBgog"
                    data-toggle="true"
                    role="button"
                    type="button"
                  >
                    <svg
                      color="currentColor"
                      fill="none"
                      height="24"
                      size="24"
                      viewBox="0 0 24 24"
                      width="24"
                    >
                      <path
                        d="M12.6327 14.7332L16.7361 10.5553C17.2998 9.98133 16.9006 9 16.1034 9L7.8966 9C7.09941 9 6.70018 9.98133 7.26388 10.5553L11.3673 14.7332C11.7167 15.0889 12.2833 15.0889 12.6327 14.7332Z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="c-bbSzHI c-bbSzHI-icgaMgR-css"
          data-orientation="horizontal"
          role="separator"
        />
        <div
          class="c-PJLV c-PJLV-idsYuEl-css"
        >
          <div
            class="c-PJLV c-PJLV-ikJfFVi-css"
          />
        </div>
        <div
          class="c-cfmUmn c-cfmUmn-fuukQF-size-sm c-cfmUmn-bYyCQg-isElement-true c-cfmUmn-ebktHq-isLetter-true c-cfmUmn-ikLFwMj-css"
        >
          <div
            class="c-gPjxah c-GloIF c-bEvjGT c-eALiIO c-gPjxah-dAcyhR-variant-placeholder c-gPjxah-gWaLqH-color-primary c-gPjxah-cmVlgk-align-left c-GloIF-fuukQF-size-sm"
          >
            <svg
              color="#404545"
              fill="none"
              height="24"
              size="24"
              viewBox="0 0 24 24"
              width="24"
            >
              <circle
                cx="11.3091"
                cy="11.3091"
                r="6.30911"
                stroke="#404545"
                stroke-width="2"
              />
              <path
                d="M16.5249 15.1049C16.1345 14.7142 15.5014 14.7139 15.1106 15.1042C14.7199 15.4945 14.7196 16.1277 15.1099 16.5184L16.5249 15.1049ZM19.2939 20.7067C19.6842 21.0974 20.3173 21.0977 20.7081 20.7074C21.0988 20.3171 21.0991 19.6839 20.7088 19.2932L19.2939 20.7067ZM15.1099 16.5184L19.2939 20.7067L20.7088 19.2932L16.5249 15.1049L15.1099 16.5184Z"
                fill="#404545"
              />
            </svg>
          </div>
          <input
            class="c-bOqFYj c-bOqFYj-fuukQF-size-sm c-bOqFYj-iiIClND-css"
            placeholder="Cari ..."
          />
          <div
            class="c-gPjxah c-GloIF c-ffvmrj c-guIpKl c-gPjxah-dAcyhR-variant-placeholder c-gPjxah-gWaLqH-color-primary c-gPjxah-cmVlgk-align-left c-GloIF-fuukQF-size-sm"
          >
            <div />
          </div>
        </div>
        <div
          class="c-PJLV c-PJLV-iklACFA-css"
        >
          <div
            class="c-PJLV c-PJLV-icmpvrW-css"
          >
            <div
              class="c-bDYFFV"
              id="table-container"
            >
              <table
                class="c-gGthKd"
                role="table"
              >
                <thead
                  class="c-fGrKZi"
                />
                <tbody
                  class="c-PJLV"
                  role="rowgroup"
                />
              </table>
              <nav
                class="react-contextmenu c-dXLjAr"
                role="menu"
                style="position:fixed;opacity:0;pointer-events:none"
                tabindex="-1"
              />
            </div>
          </div>
          <div
            class="c-PJLV c-PJLV-ifGHEql-css"
          >
            <div
              class="c-dhzjXW c-dhzjXW-iTKOFX-direction-column c-dhzjXW-jroWjL-align-center c-dhzjXW-bICGYT-justify-center c-dhzjXW-kVNAnR-wrap-noWrap c-dhzjXW-kdBMZO-gap-3 c-dhzjXW-ikwkYoP-css"
            >
              <img
                class="c-gRSzfU c-gRSzfU-ijsAKqR-css"
                src="data:image/webp;base64,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"
              />
              <p
                class="c-gPjxah c-PJLV c-gPjxah-gWaLqH-color-primary c-gPjxah-gjdJOs-align-center c-PJLV-jScHXe-paragraph-shortContentBold c-gPjxah-icbLlMz-css"
              >
                Data tidak tersedia
              </p>
              <p
                class="c-gPjxah c-PJLV c-gPjxah-cGqoHZ-color-secondary c-gPjxah-gjdJOs-align-center c-PJLV-hJwPH-paragraph-shortContentRegular"
              >
                Belum ada data yang dapat ditampilkan di halaman ini
              </p>
            </div>
          </div>
        </div>
      </div>
      <div
        class="modal-popup-wrap "
      >
        <div
          class="modal-popup"
          role="presentation"
          style="width:500px;max-width:95%;max-height:95%"
        >
          <div
            class="modal-popup-body"
          >
            <h3
              class="modal-title"
              style="color:#04C99E"
            >
              translation:label.exportReport
            </h3>
            <p>
              Upgrade layanan anda sekarang menjadi 
              <b>
                Starter
              </b>
              , 
              <b>
                Advance
              </b>
               atau 
              <b>
                Prime
              </b>
               untuk mendapatkan akses ekspor laporan.
            </p>
          </div>
          <div
            class="modal-popup-footer"
          >
            <div
              class="row"
            >
              <div
                class="col-xs-6 col-sm-6"
              >
                <button
                  class="btn"
                  style="height:100%;max-width:95%;white-space:normal"
                  type="button"
                >
                  Batal
                </button>
              </div>
              <div
                class="col-xs-6 col-sm-6 text-right"
              >
                <button
                  class="btn  btn-primary"
                  style="height:100%;max-width:95%;white-space:normal"
                  type="button"
                >
                  Beli Sekarang
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="modal-popup-wrap "
      >
        <div
          class="modal-popup"
          role="presentation"
          style="width:500px;max-width:95%;max-height:95%"
        >
          <div
            class="modal-popup-body"
          >
            <h3
              class="modal-title"
              style="color:#04C99E"
            >
              translation:label.exportReport
            </h3>
            <p>
              Upgrade layanan anda sekarang menjadi 
              <b>
                Starter
              </b>
              , 
              <b>
                Advance
              </b>
               atau 
              <b>
                Prime
              </b>
               untuk mendapatkan akses ekspor laporan.
            </p>
          </div>
          <div
            class="modal-popup-footer"
          >
            <div
              class="row"
            >
              <div
                class="col-xs-6 col-sm-6"
              >
                <button
                  class="btn"
                  style="height:100%;max-width:95%;white-space:normal"
                  type="button"
                >
                  Batal
                </button>
              </div>
              <div
                class="col-xs-6 col-sm-6 text-right"
              >
                <button
                  class="btn  btn-primary"
                  style="height:100%;max-width:95%;white-space:normal"
                  type="button"
                >
                  Beli Sekarang
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
