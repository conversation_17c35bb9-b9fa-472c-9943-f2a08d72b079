/* eslint-disable react/prop-types */
import React, { useContext, useState } from 'react';
import {
    Flex,
    ToastContext,
} from '@majoo-ui/react';
import PropTypes from 'prop-types';
import moment from 'moment';
import PageContext from './PageContext';
import { matchMedia<PERSON>hecker } from '../../../v2-utils';
import { MATCH_MEDIA_TYPE, SUBSCRIPTION_TYPE } from '../../../v2-enum';
import { useTranslationHook } from './lang.utils';
import * as apiServiceUtility from './api-service.utils';
import { useRefCollections } from './page-context-provider.utils';
import ManageTemplate from './components/PopoverTemplateDisplay/ManageTemplate';
import AlertDialogDeleteConfirm from './components/PopoverTemplateDisplay/AlertDialogDeleteConfirm';
import AlertDialogSaveConfirm from './components/PopoverTemplateDisplay/AlertDialogSaveConfirm';
import DialogOnlyDesktopInfo from './components/PopoverTemplateDisplay/DialogOnlyDesktopInfo';
import ReportFilterDialog from './components/ReportFilterDialog';
import MobileFilterDialog from './components/MobileFilterDialog';
import ExportReportConfirmationDialog from './components/ExportReportConfirmationDialog';
import {
    formatDate, getSavedReportFilter, removeSavedReportFilter, setSavedReportFilter,
    isAllowedSubscription,
} from './utils';
import { TRANSACTION_TIME_TYPE } from './enums';
import BlockedDownloadLaporanPopup from '../../../components/modalpopup/BlockedDownloadLaporanPopup';
import { getAccountType } from '../../../utils/savedLocalStorage';
import InfoExportDialog from './components/InfoExportDialog';
import ToastTitle from '../../../v2-components/ToastTitle';
import ExportBanner from '../../../components/retina/export/ExportBanner';
import UnauthorizedBlockerV2 from '../../LayoutV2/Dialog/UnauthorizedBlockerV2';

class PageContextProvider extends React.Component {
    defaultReportData = {
        summary: null,
        data: [],
        totalData: 0,
        pageIndex: 0,
        pageSize: 10,
        tableState: apiServiceUtility.defaultStateFetchReportData,
    }

    defaultReportFilterData = {
        startDate: formatDate(),
        endDate: formatDate(),
        paymentStatus: [],
        orderType: [],
        salesMin: '',
        salesMax: '',
        grossSalesMin: '',
        grossSalesMax: '',
        paymentMethod: [],
        priceType: [],
        cashiers: [],
        keyword: '',
        transactionTimeType: TRANSACTION_TIME_TYPE.ORDER_TIME,
    }

    state = {
        customTemplateMaster: [],
        reformatCustomTemplateMaster: {},
        customTemplate: [],
        customTemplateDetail: null,
        selectedCustomTemplate: 0,
        reportData: this.defaultReportData,
        appliedReportFilterData: this.defaultReportFilterData,
        reportFilterData: this.defaultReportFilterData,
        tableLoading: false,
        merchantTemplateCount: 0,
        showExportBanner: false,
    }

    setState = this.setState.bind(this);

    componentDidMount = () => {
        this.initialSetupReportFilterData(() => {
            this.initialFetching();
        });
    }

    componentDidUpdate(prevProps) {
        const { filterBranch } = this.props;

        if (prevProps.filterBranch !== filterBranch) {
            this.handleFetchReportData();
        }
    }

    doShowingToast = (val) => {
        const { addToast } = this.props;
        addToast(val);
    }

    initialFetching = async () => {
        const { hideProgress } = this.props;

        await Promise.all([
            this.handleFetchCustomTemplateMaster(),
            this.handleFetchCustomTemplate(),
        ]);

        hideProgress();
    }

    initialSetupReportFilterData = (callbackFn = () => { }) => {
        const { calendar } = this.props;

        const newReportFilterData = {
            ...getSavedReportFilter() ? getSavedReportFilter() : this.defaultReportFilterData,
            startDate: formatDate({ value: calendar.start, startOfDay: true }),
            endDate: formatDate({ value: calendar.end, endOfDay: true }),
        };
        this.setState({
            appliedReportFilterData: newReportFilterData,
            reportFilterData: newReportFilterData,
        }, () => {
            callbackFn();
        });
    }

    handleResetReportFilterData = () => {
        const { appliedReportFilterData } = this.state;
        const newData = {
            ...this.defaultReportFilterData,
            startDate: appliedReportFilterData.startDate,
            endDate: appliedReportFilterData.endDate,
        };

        this.setState({
            appliedReportFilterData: newData,
            reportFilterData: newData,
        }, () => {
            removeSavedReportFilter();
            this.handleFetchReportData();
        });
    }

    handleApplyReportFilterData = () => {
        const { refCollections, translationData } = this.props;
        const { reportFilterData, reportData } = this.state;
        const { LANG_DATA } = translationData;

        const startDate = moment(reportFilterData.startDate, 'DD-MM-YYYY HH:mm');
        const endDate = moment(reportFilterData.endDate, 'DD-MM-YYYY HH:mm');

        if (!endDate.isSameOrAfter(startDate)) {
            this.doShowingToast({
                title: <ToastTitle type="failed" />,
                description: LANG_DATA.TEMPLATE_COLUMN_ERROR_TIME,
                variant: 'failed',
            });
            return;
        }

        if (String(reportFilterData.salesMax).length > 0 && +reportFilterData.salesMin > +reportFilterData.salesMax) {
            this.doShowingToast({
                title: <ToastTitle type="failed" />,
                description: LANG_DATA.TEMPLATE_COLUMN_ERROR_TRANSACTION,
                variant: 'failed',
            });
            return;
        }

        if (String(reportFilterData.grossSalesMax).length > 0 && +reportFilterData.grossSalesMin > +reportFilterData.grossSalesMax) {
            this.doShowingToast({
                title: <ToastTitle type="failed" />,
                description: LANG_DATA.TEMPLATE_COLUMN_ERROR_GROSS_TRANSACTION,
                variant: 'failed',
            });
            return;
        }

        this.setState({
            appliedReportFilterData: reportFilterData,
        }, () => {
            setSavedReportFilter({ ...reportFilterData, keyword: '' });
            this.handleFetchReportData({ ...apiServiceUtility.defaultStateFetchReportData, pageSize: reportData.pageSize });
            refCollections.reportFilterDialogRef.current.handleHideDialog();
        });
    }

    handleChangeReportFilterData = (key, val, isImmediatelyApplied = false) => {
        const { translationData, assignCalendar } = this.props;
        const { reportFilterData } = this.state;
        const { LANG_DATA } = translationData;

        const callbackAfterSetState = () => {
            if (isImmediatelyApplied) this.handleApplyReportFilterData();
        };

        if (key === 'reportFilterDateParam') {
            const startDate = moment(val[0]);
            const endDate = moment(val[1]);
            const diffMonth = endDate.diff(startDate, 'months');

            if (diffMonth >= 3 && (endDate.format('DD') > startDate.format('DD'))) {
                this.doShowingToast({
                    title: LANG_DATA.ERROR_MAX_DATE,
                    description: LANG_DATA.ERROR_MAX_DATE_RANGE,
                    variant: 'warning',
                });

                return;
            }

            this.setState({
                reportFilterData: {
                    ...reportFilterData,
                    startDate: formatDate({ value: val[0], formatInput: false }),
                    endDate: formatDate({ value: val[1], formatInput: false }),
                },
            }, () => {
                assignCalendar(moment(val[0]).format('DD/MM/YYYY HH:mm'), moment(val[1]).format('DD/MM/YYYY HH:mm'), null, null);
                callbackAfterSetState();
            });
        } else {
            this.setState({
                reportFilterData: {
                    ...reportFilterData,
                    [key]: val,
                },
            }, () => {
                callbackAfterSetState();
            });
        }
    }

    handleFetchReportData = async (tableState = apiServiceUtility.defaultStateFetchReportData) => {
        const { filterBranch } = this.props;
        const { appliedReportFilterData, selectedCustomTemplate } = this.state;
        await apiServiceUtility.handleFetchReportData({
            appliedReportFilterData, tableState, addToast: this.doShowingToast, setState: this.setState, filterBranch, selectedCustomTemplate,
        });
    }

    handleFetchCustomTemplateMaster = async () => {
        await apiServiceUtility.handleFetchCustomTemplateMaster({
            addToast: this.doShowingToast, setState: this.setState,
        });
    }

    handleFetchCustomTemplate = async () => {
        await apiServiceUtility.handleFetchCustomTemplate({
            addToast: this.doShowingToast, setState: this.setState, onFetchReportData: this.handleFetchReportData,
        });
    }

    handleFetchCustomTemplateDetail = async (templateId) => {
        const { refCollections, showProgress, hideProgress } = this.props;

        showProgress();

        await apiServiceUtility.handleFetchCustomTemplateDetail({
            addToast: this.doShowingToast, setState: this.setState, templateId,
        }, () => {
            refCollections.manageTemplateRef.current.handleShowDialog();
        });

        hideProgress();
    }

    handleOpenFormManageTemplate = (templateId = '') => {
        const { customTemplate } = this.state;
        const { refCollections, setIsBlockerSupport } = this.props;

        if (!isAllowedSubscription()) {
            setIsBlockerSupport(true);
            return;
        }

        if (String(templateId).length === 0) {
            if ((customTemplate.length - 1) >= 10) {
                this.doShowingToast({
                    title: 'Perhatian!',
                    description: 'Maks. template 10. Hapus template lama untuk membuat template baru.',
                    variant: 'info',
                });
            } else {
                this.setState({ customTemplateDetail: null }, () => {
                    refCollections.manageTemplateRef.current.handleShowDialog();
                });
            }
        } else {
            const found = customTemplate.find(x => String(x.id) === String(templateId));
            this.setState({ customTemplateDetail: found }, () => {
                refCollections.manageTemplateRef.current.handleShowDialog();
            });
        }
    }

    handleOpenSaveConfirmation = (payload) => {
        const { refCollections } = this.props;

        refCollections.alertDialogSaveConfirmRef.current.handleShowDialog(payload);
    }

    handleOpenDeleteConfirmation = () => {
        const { refCollections } = this.props;

        refCollections.alertDialogDeleteConfirmRef.current.handleShowDialog();
    }

    handleSaveTemplate = async (templateId = '', payload = {}) => {
        const {
            refCollections, showProgress, hideProgress, translationData: { t },
        } = this.props;

        showProgress();

        await apiServiceUtility.handleSaveTemplate({
            addToast: this.doShowingToast, setState: this.setState, templateId, payload, t,
        }, () => {
            refCollections.manageTemplateRef.current.handleHideDialog();
            this.handleFetchCustomTemplate();
        });

        hideProgress();
    }

    handleDeleteTemplate = async (templateId) => {
        const {
            refCollections, showProgress, hideProgress, translationData: { t },
        } = this.props;

        showProgress();

        await apiServiceUtility.handleDeleteTemplate({
            addToast: this.doShowingToast, setState: this.setState, templateId, t,
        }, () => {
            refCollections.manageTemplateRef.current.handleHideDialog();
            this.handleFetchCustomTemplate();
        });

        hideProgress();
    }

    handleExportReport = async (type = 'xlsx', delimiter = ';') => {
        const {
            filterBranch, supportNeeded, refCollections, showProgress, hideProgress,
        } = this.props;
        const { appliedReportFilterData, selectedCustomTemplate, reportData: { tableState } } = this.state;
        const additionalPayload = {
            report_type: type,
            report_version: 2,
            ...(tableState && tableState.sortAccessor && {
                sort: `${tableState.sortDirection !== 'ASC' ? '-' : ''}${tableState.sortAccessor}`,
            }),
            ...(type === 'csv' && delimiter && { delimiter }),
        };

        if ([...supportNeeded, SUBSCRIPTION_TYPE.PRIMEPLUS].indexOf(getAccountType()) === -1) {
            refCollections.blockedDownloadPopupRef.current.showPopup();
            return;
        }

        showProgress();

        await apiServiceUtility.handleExportReport({
            filterBranch, selectedCustomTemplate, appliedReportFilterData, addToast: this.doShowingToast, additionalPayload,
        }, () => {
            this.handleShowInfoExportDialog();
            this.setState({
                showExportBanner: true,
            });
        });

        hideProgress();
    }

    handleSelectCustomTemplate = (val) => {
        this.setState({
            selectedCustomTemplate: val,
        }, () => {
            this.handleFetchReportData();
        });
    }

    handleShowDialogOnlyDesktopInfo = () => {
        const { refCollections } = this.props;

        refCollections.dialogOnlyDesktopInfoRef.current.handleShowDialog();
    }

    handleShowReportFilterDialog = () => {
        const { refCollections } = this.props;

        refCollections.reportFilterDialogRef.current.handleShowDialog();
    }

    handleShowMobileFilterDialog = () => {
        const { refCollections } = this.props;

        refCollections.mobileFilterDialogRef.current.handleShowDialog();
    }

    handleShowExportReportConfirmationDialog = () => {
        const { refCollections } = this.props;

        refCollections.exportReportConfirmationDialogRef.current.handleShowDialog();
    }

    handleShowInfoExportDialog = () => {
        const { refCollections } = this.props;

        refCollections.infoExportDialogRef.current.handleShowDialog();
    }

    handleIsBlockerSupport = () => {
        const { isBlockerSupport, setIsBlockerSupport } = this.props;
        setIsBlockerSupport(!isBlockerSupport);
    }

    createContextValue = () => ({
        ...this.props,
        ...this.state,
        onOpenFormManageTemplate: this.handleOpenFormManageTemplate,
        onSaveTemplate: this.handleSaveTemplate,
        onDeleteTemplate: this.handleDeleteTemplate,
        onOpenDeleteConfirmation: this.handleOpenDeleteConfirmation,
        onOpenSaveConfirmation: this.handleOpenSaveConfirmation,
        onSelectCustomTemplate: this.handleSelectCustomTemplate,
        onShowDialogOnlyDesktopInfo: this.handleShowDialogOnlyDesktopInfo,
        onChangeReportFilterData: this.handleChangeReportFilterData,
        onApplyReportFilterData: this.handleApplyReportFilterData,
        onShowReportFilterDialog: this.handleShowReportFilterDialog,
        onShowMobileFilterDialog: this.handleShowMobileFilterDialog,
        onShowExportReportConfirmationDialog: this.handleShowExportReportConfirmationDialog,
        onExportReport: this.handleExportReport,
        onShowInfoExportDialog: this.handleShowInfoExportDialog,
        onFetchReportData: this.handleFetchReportData,
        onResetReportFilterData: this.handleResetReportFilterData,
        onChangeIsBlockerSupport: this.handleIsBlockerSupport,
    });

    render() {
        const { children } = this.props;
        const { showExportBanner } = this.state;

        const contextValue = this.createContextValue();

        return (
            <PageContext.Provider value={contextValue}>
                <Flex direction="column" gap={5}>
                    <ExportBanner
                        showExportBanner={showExportBanner}
                        onDismiss={() => {
                            this.setState({
                                showExportBanner: false,
                            });
                        }}
                    />
                    {children}
                </Flex>
            </PageContext.Provider>
        );
    }
}

const ContextProvider = ({
    children, parentProps,
}) => {
    const toastContext = useContext(ToastContext);
    const { addToast } = toastContext;
    const isInMobileView = !matchMediaChecker(MATCH_MEDIA_TYPE.MD);
    const translationData = useTranslationHook();
    const refCollections = useRefCollections();
    const [isBlockerSupport, setIsBlockerSupport] = useState(false);

    const handleRedirectSupport = () => {
        refCollections.blockedDownloadPopupRef.current.hidePopup();
        parentProps.router.push('/support/buy');
    };

    return (
        <PageContextProvider
            {...parentProps}
            {...{
                addToast,
                isInMobileView,
                translationData,
                refCollections,
                isBlockerSupport,
                setIsBlockerSupport,
            }}
        >
            {children}
            <ManageTemplate ref={refCollections.manageTemplateRef} />
            <AlertDialogDeleteConfirm ref={refCollections.alertDialogDeleteConfirmRef} />
            <AlertDialogSaveConfirm ref={refCollections.alertDialogSaveConfirmRef} />
            <DialogOnlyDesktopInfo ref={refCollections.dialogOnlyDesktopInfoRef} />
            <ReportFilterDialog ref={refCollections.reportFilterDialogRef} />
            <MobileFilterDialog ref={refCollections.mobileFilterDialogRef} />
            {/* TODO: refCollections.exportReportConfirmationDialogRef sudah tidak terpakai? */}
            <ExportReportConfirmationDialog ref={refCollections.exportReportConfirmationDialogRef} />
            <InfoExportDialog ref={refCollections.infoExportDialogRef} />
            <BlockedDownloadLaporanPopup t={translationData.t} ref={refCollections.blockedDownloadPopupRef} confirmHandle={() => handleRedirectSupport()} />
            {isBlockerSupport && (
                <UnauthorizedBlockerV2 supportName="ADVANCE, PRIME, PRIME+" onCloseEvent={() => setIsBlockerSupport(false)} router={parentProps.router} />
            )}
        </PageContextProvider>
    );
};

ContextProvider.propTypes = {
    children: PropTypes.node.isRequired,
    parentProps: PropTypes.shape({
        filterBranch: PropTypes.string,
        idCabang: PropTypes.string,
        privilege: PropTypes.shape({ 
            components: PropTypes.shape({})
        }),
    }).isRequired,
    refCollections: PropTypes.shape({}),
};

ContextProvider.defaultProps = {
    refCollections: undefined,
};

export default ContextProvider;
