import React, {
    Fragment, useContext,
} from 'react';
import {
    Box, Heading,
    InputText, InputSwitch,
    Text, Flex,
} from '@majoo-ui/react';
import ResponsivePaper from '../../../../../Inventory/StockEntryV2/StockEntryDetail/ResponsivePaper'; // TODO: akan dibuatkan global component di dev/src/js/v2-components
import FormContext from './FormContext';
import HorizontalForm from '../../../../../../v2-components/HorizontalForm';
import HorizontalLine from '../../../../../../v2-components/HorizontalLine';
import * as SectionOneComponent from './section-one.components';
import PageContext from '../../../PageContext';
import { ROLES } from '../../../settings/enum';

const SectionOne = () => {
    const {
        formData, control, onChange: onChangeFormData, Controller,
    } = useContext(FormContext);
    const { translationData, permissionId } = useContext(PageContext);
    const { LANG_DATA, t } = translationData;

    return (
        <ResponsivePaper>
            <Box css={{ '@md': { marginBottom: '$spacing-03' } }}>
                <Heading
                    as="h3"
                    heading="pageTitle"
                    css={{ marginBottom: 0, '@md': { marginBottom: '$spacing-03 !important' } }}
                >
                    {LANG_DATA.DIALOG_TEMPLATE_SALES_DETAILS_DISPLAY}
                </Heading>
            </Box>
            <HorizontalForm
                labelOptions={{
                    text: LANG_DATA.DIALOG_TEMPLATE_DISPLAY_NAME,
                    required: true,
                }}
                render={({ errorReceiver }) => (
                    <Controller
                        control={control}
                        name="displayName"
                        render={({ fieldState, field: { onChange, value } }) => {
                            const receivedError = errorReceiver(fieldState.error);

                            return (
                                <Fragment>
                                    <InputText
                                        placeholder={`${t('placeholder.example', { ns: 'translation' })}Detail penjualan dengan pajak`}
                                        defaultValue={value}
                                        onChange={(e) => { onChange(e.target.value); }}
                                        isInvalid={!!receivedError}
                                        maxLength={30}
                                    />
                                    {receivedError}
                                </Fragment>
                            );
                        }}
                    />
                )}
            />
            <Flex direction="column" gap={5}>
                <Flex direction="column" gap={3}>
                    <Text
                        variant="label"
                        color="primary"
                        css={{
                            fontSize: '14px',
                            fontWeight: '600',
                        }}
                    >
                        {LANG_DATA.DIALOG_TEMPLATE_SET_DISPLAY_TITLE}
                    </Text>
                    <Text variant="helper" color="secondary">{LANG_DATA.DIALOG_TEMPLATE_SET_DISPLAY_DESCRIPTION}</Text>
                </Flex>
                <Box
                    css={{
                        display: 'grid',
                        gridTemplateColumns: '1fr 150px 1fr',
                        gap: 20,
                        alignItems: 'center',
                    }}
                >
                    <SectionOneComponent.ColumnList />
                    <Flex direction="column" gap={5}>
                        <SectionOneComponent.AddButton />
                        <HorizontalLine />
                        <SectionOneComponent.RemoveButton />
                    </Flex>
                    <SectionOneComponent.AddedColumn />
                </Box>
            </Flex>
            <SectionOneComponent.RequiredProductNameInfo />
            <HorizontalForm
                labelOptions={{
                    text: LANG_DATA.POPOVER_TEMPLATE_DEFAULT_DISPLAY,
                }}
                render={() => (
                    <Flex gap={4} direction="row">
                        <InputSwitch
                            dataOffLabel="OFF"
                            dataOnLabel="ON"
                            onCheckedChange={val => onChangeFormData('isDefaultDisplay', val)}
                            checked={formData.isDefaultDisplay}
                            disabled={permissionId !== ROLES.ADMIN}
                        />
                        <Text color="secondary" css={{ fontSize: 12, fontWeight: 500 }}>
                            {LANG_DATA.DIALOG_TEMPLATE_DEFAULT_DISPLAY_DESCRIPTION}
                        </Text>
                    </Flex>
                )}
            />
            <HorizontalForm
                labelOptions={{
                    text: LANG_DATA.DIALOG_TEMPLATE_DISPLAY_ALL_USER_TITLE,
                }}
                render={() => (
                    <Flex gap={4} direction="row">
                        <InputSwitch
                            dataOffLabel="OFF"
                            dataOnLabel="ON"
                            onCheckedChange={val => onChangeFormData('isDisplayAllUser', val)}
                            checked={formData.isDisplayAllUser}
                            disabled={false}
                        />
                        <Text color="secondary" css={{ fontSize: 12, fontWeight: 500 }}>
                            {LANG_DATA.DIALOG_TEMPLATE_DISPLAY_ALL_USER_DESCRIPTION}
                        </Text>
                    </Flex>
                )}
            />
        </ResponsivePaper>
    );
};

export default SectionOne;
