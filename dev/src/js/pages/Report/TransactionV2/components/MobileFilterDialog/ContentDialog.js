import React, { useContext } from 'react';
import {
    <PERSON>Dialog, PageDialogTitle,
    PageDialogContent,
    PageDialogFooter, Flex, Button, Box,
} from '@majoo-ui/react';
import ResponsivePaper from '../../../../Inventory/StockEntryV2/StockEntryDetail/ResponsivePaper'; // TODO: akan dibuatkan global component di dev/src/js/v2-components
import CustomPageDialogFooter from '../../../../../v2-components/CustomPageDialogFooter';
import PageContext from '../../PageContext';
import Form from './Form';

class ContentDialog extends React.Component {
    handleCloseDialog = () => {
        const { onClose } = this.props;

        onClose();
    }

    render() {
        const { isOpen, onApplyReportFilterData } = this.props;

        return (
            <PageDialog
                open={isOpen}
                defaultOpen={false}
                onOpenChange={this.handleCloseDialog}
            >
                <PageDialogTitle>
                    <Flex direction="row" align="center" gap={4}>
                        <Box>
                            Filter Tanggal
                        </Box>
                    </Flex>
                </PageDialogTitle>
                <PageDialogContent wrapperSize="lg">
                    <ResponsivePaper>
                        <Form />
                    </ResponsivePaper>
                </PageDialogContent>
                <PageDialogFooter>
                    <CustomPageDialogFooter
                        {...{ pageDialogContentHasIndicator: false }}
                        renderGroups={[
                            () => (
                                <Box
                                    css={{
                                        display: 'grid',
                                        gridTemplateColumns: '1fr 1fr',
                                        gap: 4,
                                    }}
                                >
                                    <Button
                                        type="button"
                                        buttonType="ghost"
                                        onClick={() => { this.handleCloseDialog(); }}
                                    >
                                        Reset
                                    </Button>
                                    <Button
                                        type="button"
                                        onClick={() => {
                                            onApplyReportFilterData();
                                            this.handleCloseDialog();
                                        }}

                                    >
                                        Terapkan
                                    </Button>
                                </Box>
                            ),
                        ]}
                    />
                </PageDialogFooter>
            </PageDialog>
        );
    }
}

const WithContext = (props) => {
    const { translationData, onApplyReportFilterData } = useContext(PageContext);

    return <ContentDialog {...props} {...{ translationData, onApplyReportFilterData }} />;
};

export default WithContext;
