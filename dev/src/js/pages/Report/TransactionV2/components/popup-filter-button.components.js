import React, { useContext } from 'react';
import {
    DropdownMenu,
    ButtonDropdown,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
} from '@majoo-ui/react';
import { foundations } from '@majoo-ui/core';
import { FilterOutline } from '@majoo-ui/icons';
import PageContext from '../PageContext';

const { colors } = foundations;

export const MobileFilterButton = () => {
    const { onShowMobileFilterDialog, onShowReportFilterDialog } = useContext(PageContext);

    return (
        <DropdownMenu
            align="start"
            side="bottom"
        >
            <DropdownMenuTrigger asChild>
                <ButtonDropdown
                    buttonType="secondary"
                    css={{
                        marginLeft: 'auto',
                        marginRight: 'auto',
                    }}
                    size="sm"
                    leftIcon={<FilterOutline color="currentColor" />}
                >
                    Filter
                </ButtonDropdown>
            </DropdownMenuTrigger>
            <DropdownMenuContent
                css={{
                    minWidth: 250,
                    fontWeight: 400,
                    lineHeight: 200,
                    fontSize: '14px',
                    color: colors.primary1000,
                    paddingLeft: 15,
                }}
                sideOffset={5}
            >
                <DropdownMenuItem onClick={() => { onShowMobileFilterDialog(); }}>
                    Filter Tanggal
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => { onShowReportFilterDialog(); }}>
                    Filter Laporan
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
};

