import React, { useContext } from 'react';
import {
    PageDialog, PageDialogTitle,
    PageDialogContent,
    PageDialogFooter, Flex, Button, Box,
} from '@majoo-ui/react';
import ResponsivePaper from '../../../../Inventory/StockEntryV2/StockEntryDetail/ResponsivePaper'; // TODO: akan dibuatkan global component di dev/src/js/v2-components
import CustomPageDialogFooter from '../../../../../v2-components/CustomPageDialogFooter';
import PageContext from '../../PageContext';
import * as MobileContentDialogComponent from './mobile-content-dialog.components';
import BannerSupport from './BannerSupport';
import { handleFilteredCount } from '../../utils';

class ContentDialog extends React.Component {
    handleCloseDialog = () => {
        const { onClose } = this.props;

        onClose();
    }

    render() {
        const {
            isOpen, onApplyReportFilterData, onResetReportFilterData,
            reportFilterData,
        } = this.props;

        const filteredCount = handleFilteredCount(reportFilterData);

        return (
            <PageDialog
                open={isOpen}
                defaultOpen={false}
                onOpenChange={this.handleCloseDialog}
            >
                <PageDialogTitle>
                    <Flex direction="row" align="center" gap={4}>
                        <Box>
                            Filter Laporan
                        </Box>
                    </Flex>
                </PageDialogTitle>
                <PageDialogContent wrapperSize="lg">
                    <ResponsivePaper>
                        {/* <BannerSupport /> */}
                        <MobileContentDialogComponent.FilterTab />
                    </ResponsivePaper>
                </PageDialogContent>
                <PageDialogFooter>
                    <CustomPageDialogFooter
                        {...{ pageDialogContentHasIndicator: false }}
                        renderGroups={[
                            () => (
                                <Box
                                    css={{
                                        display: 'grid',
                                        gridTemplateColumns: '1fr 1fr',
                                        gap: 8,
                                    }}
                                >
                                    <Button
                                        type="button"
                                        buttonType="ghost"
                                        onClick={() => {
                                            onResetReportFilterData();
                                        }}
                                    >
                                        Reset
                                    </Button>
                                    <Button
                                        type="button"
                                        onClick={() => {
                                            onApplyReportFilterData();
                                        }}
                                    >
                                        {`Atur ${filteredCount ? `(${filteredCount})` : ''}`}
                                    </Button>
                                </Box>
                            ),
                        ]}
                    />
                </PageDialogFooter>
            </PageDialog>
        );
    }
}

const WithContext = (props) => {
    const {
        reportFilterData, translationData, onApplyReportFilterData, onResetReportFilterData,
    } = useContext(PageContext);

    return (
        <ContentDialog
            {...props}
            {...{
                translationData, onApplyReportFilterData, onResetReportFilterData, reportFilterData,
            }}
        />
    );
};

export default WithContext;
