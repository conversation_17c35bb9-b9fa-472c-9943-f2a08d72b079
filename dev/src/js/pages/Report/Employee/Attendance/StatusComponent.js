import React from 'react';
import PropTypes from 'prop-types';
import SwitchBox from '../../../../components/form/SwitchBox';

const datasets = [{ text: 'Disetujui', value: 'Y' }, { text: '<PERSON><PERSON><PERSON>', value: 'N' }];
class StatusAbsensiColumn extends React.Component {
    static propTypes = {
        value: PropTypes.string.isRequired,
        changeEvent: PropTypes.func.isRequired,
    }

    constructor(props) {
        super(props);
        this.state = {
            value: '',
        };
    }

    componentWillMount() {
        const { value } = this.props;
        this.setState({
            value,
        });
    }

    componentWillReceiveProps(nextProps) {
        const { value } = this.props;
        
        if (String(value) !== String(nextProps.value)) {
            this.setState({
                value: nextProps.value,
            });
        }
    }

    changeAttendanceStatus = (val) => {
        const { changeEvent } = this.props;

        this.setState({
            value: val,
        }, () => {
            // console.log(this.state);
            changeEvent(val);
        });
    }

    render() {
        const { value } = this.state;
        return (
            <div style={{ display: 'block', width: '152px' }}>
                <SwitchBox
                    dataset={datasets}
                    value={value}
                    className="text-capitalize switch-box-btn-mobile"
                    changeEvent={this.changeAttendanceStatus}
                />
            </div>
        );
    }
}

export default StatusAbsensiColumn;
