/* eslint-disable import/no-cycle */
import React, {
  memo,
  useCallback,
  useState,
  useContext,
  useEffect,
  useRef,
  useMemo,
} from 'react';
import { useTranslation, Trans } from 'react-i18next';
import update from 'immutability-helper';
import { debounce } from 'lodash';
import { connect } from 'react-redux';
import propType from 'prop-types';
import moment from 'moment';
import { useForm } from 'react-hook-form';
import {
  Flex,
  Paper,
  Heading,
  Button,
  Box,
  Paragraph,
  Separator,
  InputSearchbox,
  Table,
  ToastContext,
  InputSelect,
  AlertDialogFunction,
  PageDialog,
  PageDialogContent,
  PageDialogTitle,
  PageDialogFooter,
  Grid,
  ModalDialog,
  FormGroup,
  FormLabel,
  InputTextArea,
  DialogClose,
} from '@majoo-ui/react';
import { CalendarOutline, LocationOutline } from '@majoo-ui/icons';
import NoImage from '../../../../../../assets/images/no_image.svg';
import { filterCabang } from '../../../../../actions/branchActions';
import LogModal from '../LogModal';
import CoreHOC from '../../../../../core/CoreHOC';
import BlockedDownloadPopup from '../../../../../components/modalpopup/BlockedDownloadLaporanPopup';
import { getAccountType } from '../../../../../utils/savedLocalStorage';
import { attendanceColumnsRetina } from '../utils';
import { catchError, resetDateRangeHelper } from '../../../../../utils/helper';
import { useMediaQuery } from '../../../../../utils/useMediaQuery';
import { updateLaporanAbsensi } from '../../../../../data/employee';
import { ConditionalWrapper } from '../../../../../components/wrapper/ConditionalWrapper';
import FilterSection from './FilterSection';
import userUtil from '../../../../../utils/user.util';
import { FavoriteWrapper, TooltipGuidance } from '../../../../../components/retina';
import { accountType } from '../../../../../config/enum';
import ExportReport from '../../../../../components/retina/export/ExportReport';
import {
  SORT_NORMALIZATION, downloadLAttendanceReport, fetchAttendanceReport, formattedDate,
} from './utils';
import { usePartialState } from '../../../../../utils/usePartialState';
import useDidMountEffect from '../../../../../utils/useDidMountEffect';
import { colors, styled } from '../../../../../stitches.config';

const DetailAttendace = React.lazy(() => import('./modalDetails'));

const InfoContainer = styled(Flex, {
  marginTop: '$spacing-05',
  padding: '$spacing-05',
  flexDirection: 'column',
  gap: '$spacing-03',
  alignItems: 'start',
  border: '1px solid #EEF0F0',
  borderRadius: '8px',
});

export const AttendanceContext = React.createContext();

const defaultDate = {
  start: moment().startOf('month').toDate(),
  end: moment().endOf('month').toDate(),
};

const dateOnTopWrapper = { gap: 4, mt: 8, '@md': { gap: 13 } };

const tableHeaderCss = {
  color: '$textPrimary',
  fontSize: '$section-sub-title',
  fontWeight: 600,
  lineHeight: '16px',
  letterSpacings: '$section-sub-title',
};

const paperCss = {
  userSelect: 'none',
  mb: 20,
  padding: 'unset',
  '@sm': { boxShadow: 'unset' },
  '@md': { padding: '16px 24px' },
};

const flexDirection = { '@sm': 'column', '@md': 'row' };

const flexTitleCss = { mb: 19 };

const flexFilterCss = { mt: 20, mb: 19, '@md': { mb: 23 } };

const boxFilterCss = {
  width: '100%',
  display: 'grid',
  gridTemplateColumns: '1fr',
  gap: '$compact',
  '@md': {
    gridTemplateColumns: 'auto auto 1fr 1fr',
  },
};

const boxSearchCss = { '@md': { maxWidth: 220 } };

const conditionalWrapperGridCss = { 'grid-template-columns': '91px 1fr', gap: '$compact' };

const tableCss = {
  overflowY: 'hidden',
  padding: '0',
  '@md': {
    marginBlock: 20,
    '& tr[role="row"]': {
      '& > td:nth-child(3)':
      {
        '&:hover': {
          cursor: 'pointer',
          backgroundColor: '$bgSuccess',
          border: '1px solid $bgGreen',
        },
      },
      '& > td:nth-child(4):hover':
      {
        '&:hover': {
          cursor: 'pointer',
          backgroundColor: '$bgSuccess',
          border: '1px solid $bgGreen',
        },
      },
    },
  },
};

const additionalExceptionIds = ['status', 'jam_login', 'jam_logout', 'is_accepted'];

const defaultTableState = {
  tableData: [],
  totalData: 0,
  tableLoading: false,
  pageIndex: 0,
  keywordSearch: '',
  sortAccessor: '',
  sortDirection: '',
};

const MemoizedTable = memo(Table);

const Attendance = memo(({ filterBranch, selectedCabang, ...props }) => {
  const { t, i18n } = useTranslation(['Penjualan/Laporan/absensi', 'translation']);
  const {
    hideProgress, showProgress, router, calendar, assignCalendar, listCabang, selectOutlet,
  } = props;
  const blockedDownloadPopup = useRef(null);
  const { addToast } = useContext(ToastContext);
  const [calendarDate, setCalendarDate] = useState({
    start: moment(calendar.start, 'DD-MM-YYYY').toDate(),
    end: moment(calendar.end, 'DD-MM-YYYY').toDate(),
  });
  const [tableState, setTableState] = usePartialState({
    ...defaultTableState,
    pageSize: 10,
  });
  const [logState, setLogState] = useState({ show: false, userId: 0 });
  const [selectedItems, setSelectedItems] = useState([]);
  const [statusBulk, setStatusBulk] = useState('');
  const [openModal, setopenModal] = useState(false);
  const [picture, setPicture] = useState({ pic: '', time: undefined, open: false });
  const [openNote, setOpenNote] = useState({ open: false, type: 'accept' });
  const [openChangeNote, setOpenChangeNote] = useState(false);
  const [attendanceType, setAttendanceType] = useState({
    name: 'Semua Absensi',
    value: 'all',
  });
  const {
    setValue, register, getValues,
  } = useForm({
    defaultValues: {
      form: {
        note: '',
        jam_masuk: '',
        jam_pulang: '',
        face_login: '',
        face_logout: '',
        jam_kerja: '',
        karyawan: '',
        tgl: '',
      },
    },
  });
  const isMobile = useMediaQuery('(max-width: 1090px)');
  const lang = i18n.language;

  const getDiffWorkingTime = useCallback((loginTime, logoutTime) => {
    if (logoutTime) {
      const duration = moment.duration(moment(loginTime).diff(logoutTime));
      const [seconds, minutes, hours, days] = [duration.seconds(), duration.minutes(), duration.hours(), duration.days()].map(d => Math.abs(d));
      const addPlural = val => (i18n.language === 'en' && val > 1 ? '(s)' : '');
      const Days = days === 0 ? '' : `${days} ${t('log.day', i18n.language === 'en' ? 'Day' : 'Hari')}${addPlural(days)} `;
      const Hours = hours === 0 ? '' : `${hours} ${t('log.hour', i18n.language === 'en' ? 'Hour' : 'Jam')}${addPlural(hours)} `;
      const Minutes = minutes === 0 ? '' : `${minutes} ${t('log.minute', i18n.language === 'en' ? 'Minute' : 'Menit')}${addPlural(minutes)} `;
      const Seconds = seconds === 0 ? '' : `${seconds} ${t('log.sec', i18n.language === 'en' ? 'Second' : 'Detik')}${addPlural(seconds)} `;

      return `${Days}${Hours}${Minutes}${Seconds}`;
    }

    return '-';
  }, [i18n]);

  const handleFetchAttendanceReport = useCallback(async (tableParams = {}) => {
    const { keywordSearch, sortAccessor: stateSorttAccessor, sortDirection: statesortDirection } = tableState;

    /* pageIndex = 0 -> purpose to reset page if filter change */
    const {
      pageSize, pageIndex = 0, sortAccessor = '', sortDirection = '',
    } = tableParams;
    try {
      setTableState({
        tableLoading: true,
      });
      const response = await fetchAttendanceReport({
        pageIndex,
        pageSize: pageSize || tableState.pageSize || 10,
        sortAccessor: sortAccessor || stateSorttAccessor,
        sortDirection: sortDirection || statesortDirection,
        startDate: formattedDate(calendarDate.start),
        endDate: formattedDate(calendarDate.end),
        selectedCabang,
        keywordSearch,
      });
      const { data, meta: { total, current_page: currentPage, per_page: perPage } } = response;
      const formattedData = data.map((x) => {
        let isAccepted = '';
        if (x.is_accepted && x.is_accepted === '1') {
          isAccepted = 'Y';
        } else if (x.is_accepted && x.is_accepted === '0') {
          isAccepted = 'N';
        }
        return {
          ...x,
          check: false,
          image_url_masuk: x.face_login,
          image_url_keluar: x.face_logout,
          is_accepted: isAccepted,
          jam_kerja: getDiffWorkingTime(x.jam_login, x.jam_logout),
          note: x.note || '',
        };
      });

      setTableState({
        tableData: formattedData,
        totalData: total,
        pageIndex: (currentPage || 1) - 1,
        tableLoading: false,
        pageSize: perPage || pageSize || 10,
        ...sortAccessor && { sortAccessor },
        ...sortDirection && { sortDirection },
      });
    } catch (e) {
      const message = catchError(e);
      addToast({
        title: t('error.failedGetDataCustom', { ns: 'translation', data: t('attendance') }),
        description: message,
        variant: 'failed',
        dismissAfter: 3000,
      });
      setTableState(defaultTableState);
    } finally {
      hideProgress();
    }
  }, [tableState, calendarDate, selectedCabang]); // TODO: check if can use calendar props instead of local state


  useEffect(() => {
    if (selectedItems.length < 1) setStatusBulk('');
  }, [selectedItems]);

  useEffect(async () => {
    await handleFetchAttendanceReport();
    return () => {
      setTableState({});
    };
  }, []);

  useDidMountEffect(async () => {
    await handleFetchAttendanceReport();
  }, [calendarDate, selectedCabang, tableState.keywordSearch]);

  useEffect(() => {
    setSelectedItems([]);
  }, [filterBranch]);

  const outletOptions = useMemo(() => listCabang.map(item => ({ name: item.cabang_name, value: item.id_cabang })), [listCabang]);

  // eslint-disable-next-line no-nested-ternary
  const dateToString = useCallback((date, i18nlocale = false) => moment(date).format(i18nlocale ? 'DD MMMM YYYY' : lang === 'id' ? 'DD MMMM YYYY' : 'MMMM DD YYYY'), [lang]);

  const toogleModal = useCallback(() => {
    setopenModal(prev => !prev);
  }, [openModal]);

  const redirectSupport = useCallback(() => {
    blockedDownloadPopup.current.hidePopup();
    router.push('/support/buy');
  }, []);

  const downloadLaporan = useCallback((type = 'xlsx') => {
    const { keywordSearch, sortAccessor, sortDirection } = tableState;
    const {
      supportNeeded,
    } = props;

    if (supportNeeded.indexOf(getAccountType()) === -1) {
      blockedDownloadPopup.current.showPopup();
      return;
    }

    const payload = {
      format: type === 'xlsx' ? 'excel' : 'pdf',
      outlet_id: selectedCabang,
      start_date: formattedDate(calendarDate.start),
      end_date: formattedDate(calendarDate.end),
      ...statusBulk && { is_accepted: statusBulk === 'Y' ? 1 : 0 },
      ...keywordSearch && { search: keywordSearch },
      ...(sortAccessor && sortDirection) && {
        sort: (sortDirection === 'ASC' ? '' : '-') + SORT_NORMALIZATION[sortAccessor],
      },
    };

    downloadLAttendanceReport(
      { payload, type },
      () => showProgress(),
      () => addToast({
        title: t('toast.error', { ns: 'translation' }),
        description: <Trans t={t} i18nKey="toast.failedExport" />,
        variant: 'failed',
      }),
      () => hideProgress(),
      () => addToast({
        title: t('toast.success', { ns: 'translation' }),
        description: <Trans t={t} i18nKey="toast.export" />,
        variant: 'success',
      }),
    );
  }, [selectedCabang, calendarDate, statusBulk, tableState]);

  const saveRowChangeHandler = async (id, isAccepted, isSingleSave = false, isNoteOnly = false) => {
    const updated = [];
    const { tableData } = tableState;
    const employeeData = tableData.find(row => String(row.id) === String(id));
    // const buildData = tableData.map(item => ({ id: item.id, is_accepted: item.is_accepted, note: item.note }));
    const buildData = tableData.map((item) => {
      const notePayload = getValues(`form.note_${item.id}`);
      return ({ id: item.id, is_accepted: null, note: notePayload && notePayload !== '-' ? notePayload : item.note || '-' });
    });
    let selectedData = buildData.find(x => (String(x.id) === String(id)));
    if (isSingleSave) {
      selectedData = update(selectedData, {
        note: { $set: getValues(`form.note_${id}`) },
      });
      updated.push(selectedData);
    } else {
      for (let i = 0; i < id.length; i += 1) {
        selectedData = buildData.find(x => String(x.id) === id[i]);
        updated.push(selectedData);
      }
    }
    if (updated[0]) {
      if (isAccepted === 'Y') {
        selectedData = updated.map(x => update(x, {
          is_accepted: { $set: '1' },
        }));
      } else if (isAccepted === 'N') {
        selectedData = updated.map(x => update(x, {
          is_accepted: { $set: '0' },
        }));
      }
    }

    const payload = {
      tgl_mulai: formattedDate(calendarDate.start),
      tgl_akhir: formattedDate(calendarDate.end),
      data: JSON.stringify(selectedData),
      ...selectedCabang !== '' && { id_outlet: selectedCabang },
    };

    try {
      showProgress();
      if (updated[0]) {
        const res = await updateLaporanAbsensi(payload);
        if (res.status) {
          if (isNoteOnly) {
            addToast({
              title: t('toast.success', { ns: 'translation' }),
              dismissAfter: 3000,
              variant: 'success',
              description: (
                <Trans t={t} i18nKey="toast.editSuccess" values={{ karyawan: employeeData.karyawan }}>
                  Catatan absensi karyawan
                  <strong>{` ${employeeData.karyawan} `}</strong>
                  berhasil diubah
                </Trans>),
            });
          } else {
            addToast({
              title: t('toast.success', { ns: 'translation' }),
              dismissAfter: 3000,
              variant: 'success',
              description: updated.length > 1
                ? (
                  <Trans t={t} i18nKey="toast.successUpdate" values={{ for: lang === 'id' ? 'untuk' : 'of', updated: updated.length, mode: String(selectedData[0].is_accepted) === '1' ? t('mainpage.status.approved').toLowerCase() : t('mainpage.status.declined').toLowerCase() }}>
                    Absensi untuk
                    <strong>{` ${updated.length} karyawan `}</strong>
                    berhasil
                    {` ${String(selectedData[0].is_accepted) === '1' ? 'disetujui' : 'ditolak'}`}
                  </Trans>
                )
                : (
                  <Trans t={t} i18nKey="toast.successUpdateOne" values={{ name: employeeData.karyawan, mode: String(selectedData[0].is_accepted) === '1' ? t('mainpage.status.approved').toLowerCase() : t('mainpage.status.declined').toLowerCase() }}>
                    Absensi karyawan
                    <strong>{` ${employeeData.karyawan} `}</strong>
                    berhasil
                    {` ${String(selectedData[0].is_accepted) === '1' ? 'disetujui' : 'ditolak'}`}
                  </Trans>
                ),
            });
          }
        } else {
          addToast({
            dismissAfter: 3000,
            variant: 'failed',
            description: (
              <Trans t={t} i18nKey="toast.errorUpdate" values={{ updated: updated[1] ? `${lang === 'id' ? 'untuk' : 'for'} ${updated.length} ` : '', mode: isAccepted === 'Y' ? t('details.approve') : t('details.reject') }} />
            ),
            title: t('toast.error', { ns: 'translation' }),
          });
        }
        if (openModal) toogleModal();
        handleFetchAttendanceReport({ pageIndex: tableState.pageIndex });
      } else {
        addToast({
          dismissAfter: 3000,
          variant: 'failed',
          description: t('toast.noChanges'),
          title: t('toast.error', { ns: 'translation' }),
        });
      }
    } catch (e) {
      addToast({
        dismissAfter: 3000,
        title: t('toast.error', { ns: 'translation' }),
        description: (
          <Trans t={t} i18nKey="toast.errorUpdate" values={{ updated: updated[1] ? `${lang === 'id' ? 'untuk' : 'for'} ${updated.length} ` : '', mode: isAccepted === 'Y' ? t('details.approve') : t('details.reject') }} />
        ),
        variant: 'failed',
      });
    } finally {
      hideProgress();
    }
  };

  const onDisetujuiAction = () => {
    const dialog = new AlertDialogFunction({
      isMobile,
      title: t('confirmation.approved.title'),
      css: { '@md': { width: 422 } },
      description: (
        <Trans
          t={t}
          i18nKey="confirmation.approved.desc"
          values={{ name: getValues('form.karyawan') }}
        />
      ),
      labelCancel: t('label.cancel', { ns: 'translation' }),
      labelConfirm: t('label.continue', { ns: 'translation' }),
      onConfirm: () => saveRowChangeHandler(getValues('form.id'), 'Y', true),
    });
    dialog.show();
  };

  const onDitolakAction = () => {
    const dialog = new AlertDialogFunction({
      isMobile,
      title: t('confirmation.reject.title'),
      css: { '@md': { width: 422 } },
      description: (
        <Trans t={t} i18nKey="confirmation.reject.desc" values={{ name: getValues('form.karyawan') }} />
      ),
      dialogType: 'negative',
      labelCancel: t('label.cancel', { ns: 'translation' }),
      labelConfirm: t('label.continue', { ns: 'translation' }),
      onConfirm: () => saveRowChangeHandler(getValues('form.id'), 'N', true),
    });
    dialog.show();
  };

  const bulkUpdateStatus = () => {
    const isAccepted = statusBulk === 'Y';
    const totalSelected = selectedItems.length;
    let desc = (
      <Trans
        t={t}
        i18nKey="confirmation.bulk.desc"
        values={{ total: totalSelected, mode: isAccepted ? t('details.approve').toLowerCase() : t('details.reject').toLowerCase() }}
      />
    );
    if (totalSelected === 1) {
      const { karyawan } = tableState.tableData.find(x => x.id === selectedItems[0]);
      desc = (
        <Trans
          t={t}
          i18nKey={isAccepted ? 'confirmation.approved.desc' : 'confirmation.reject.desc'}
          values={{ name: karyawan }}
        />
      );
    }
    const dialog = new AlertDialogFunction({
      isMobile,
      title: `${isAccepted ? t('confirmation.approved.title') : t('confirmation.reject.title')}`,
      css: { '@md': { width: 422 } },
      description: desc,
      labelConfirm: t('label.continue', { ns: 'translation' }),
      labelCancel: t('label.cancel', { ns: 'translation' }),
      dialogType: isAccepted ? 'primary' : 'negative',
      onConfirm: () => saveRowChangeHandler(selectedItems, statusBulk),
    });
    dialog.show();
  };

  const onChangeCalendar = useCallback((date) => {
    const { newStartDate, newEndDate, isForceReset } = resetDateRangeHelper(moment(date[0]).format('DD-MM-YYYY'), moment(date[1]).format('DD-MM-YYYY'), 90);
    if (isForceReset) {
      addToast({
        title: t('toast.errorMaxDate', 'Terjadi Kesalahan!', { ns: 'translation' }),
        description: t('toast.errorMaxDateRange', 'Maksimum rentang waktu yang dapat dipilih: 3 bulan', { ns: 'translation' }),
        variant: 'pending',
      });
      setCalendarDate({ start: moment(newStartDate, 'DD-MM-YYYY').toDate(), end: moment(newEndDate, 'DD-MM-YYYY').toDate() });
      assignCalendar(moment(newStartDate, 'DD-MM-YYYY').toDate(), moment(newEndDate, 'DD-MM-YYYY').toDate(), null, null);
    } else {
      setCalendarDate({ start: date[0], end: date[1] });
      assignCalendar(date[0], date[1], null, null);
    }
  }, []);

  const onReset = useCallback(() => {
    setCalendarDate({ start: defaultDate.start, end: defaultDate.end });
    assignCalendar(moment(defaultDate.start).format('DD-MM-YYYY'), moment(defaultDate.end).format('DD-MM-YYYY'), null, null);
  }, []);

  const onSearch = debounce(async (keyword) => {
    setTableState({
      keywordSearch: keyword,
    });
  }, 500);

  const renderBtnExport = () => (
    <ExportReport
      onExport={downloadLaporan}
      calendar={calendarDate}
      title={t('mainpage.title', 'Absensi')}
      type="multi"
    />
  );

  const onHoverPic = (pic) => {
    const _date = moment(pic.value, 'YYYY-MM-DD HH:mm:ss');
    const timeString = pic.value ? _date.format('HH:mm') : '-';
    const dateString = pic.value ? _date.format('DD MMM YYYY') : '-';
    setPicture({
      ...pic,
      open: true,
      timeString,
      dateString,
    });
  };

  const onAction = ({ type, payload }) => {
    if (type === 'log') return setLogState({ show: true, userId: payload.id });
    const notePayload = getValues(`form.note_${payload.id}`);
    setValue('form', {
      ...(getValues('form') || {}),
      ...payload,
      [`note_${payload.id}`]: notePayload && notePayload !== '-' ? notePayload : payload.note || '-',
    });
    setTimeout(() => {
      saveRowChangeHandler(getValues('form.id'), type === 'accept' ? 'Y' : 'N', true);
    }, 200);
    return 1;
  };

  const handleAttendanceNote = () => {
    setOpenNote(item => ({ ...item, open: false }));
    saveRowChangeHandler(getValues('form.id'), openNote.type === 'accept' ? 'Y' : 'N', true);
  };

  const handleChangeNote = () => {
    setOpenChangeNote(false);
    saveRowChangeHandler(getValues('form.id'), getValues('form.is_accepted'), true, true);
  };

  const handleChangeOutlet = useCallback((opt) => {
    const { value } = opt;
    selectOutlet(value);
  }, []);

  const isAdvancePrimeUser = [accountType.ADVANCE, accountType.ENTERPRISE, accountType.PRIMEPLUS, accountType.TRIAL, accountType.PRIME].includes(userUtil.getLocalConfigByKey('accountType'));
  const isAccept = openNote.type === 'accept';

  const selectedRows = useMemo(() => selectedItems.reduce((prev, curr) => {
    // eslint-disable-next-line no-param-reassign
    prev[curr] = true;

    return prev;
  }, {}), [selectedItems]);

  const tableColumnsConfig = useMemo(() => attendanceColumnsRetina(selectedCabang, calendarDate, setValue, onHoverPic, onAction, { isAllowed: isAdvancePrimeUser }, t, lang, tableState.tableData,
    (val) => {
      if (Object.values(val)[0]) {
        return setSelectedItems(prev => ([...prev, ...Object.keys(val)]));
      }
      return setSelectedItems(prev => prev.filter(f => !Object.keys(val).includes(f)));
    },
    (val) => {
      if (val) setSelectedItems(prev => ([...prev, ...tableState.tableData.map(x => x.id)]));
      else setSelectedItems(prev => prev.filter(f => !tableState.tableData.map(x => x.id).includes(f)));
    }), [tableState, selectedCabang, calendarDate, setValue]);

  const tableHiddenColumnProps = useMemo(() => (!isMobile ? ['tgl'] : []), [isMobile, tableColumnsConfig]);

  return (
    <Paper
      css={paperCss}
    >
      <div id="a-dialog" />
      <Flex
        direction={flexDirection}
        justify="between"
        css={flexTitleCss}
      >
        <Box>
          <FavoriteWrapper>
            <Heading heading="pageTitle">{t('mainpage.title', 'Absensi')}</Heading>
            <TooltipGuidance />
          </FavoriteWrapper>
          <Flex align="center" css={dateOnTopWrapper}>
            <CalendarOutline />
            <Paragraph>
              {`${dateToString(calendarDate.start, true)}
              - ${dateToString(calendarDate.end, true)}`}
            </Paragraph>
          </Flex>
        </Box>
        {!isMobile && renderBtnExport()}
      </Flex>
      <Separator responsive />
      <Flex
        direction={flexDirection}
        align="center"
        css={flexFilterCss}
      >
        <Box
          css={boxFilterCss}
        >
          <Box css={boxSearchCss}>
            <InputSearchbox onChange={onSearch} />
          </Box>
          <ConditionalWrapper
            condition={isMobile}
            wrapper={children => (
              <Grid css={conditionalWrapperGridCss}>
                {children}
              </Grid>
            )}
          >
            <FilterSection
              lang={lang}
              onReset={onReset}
              calendar={calendarDate}
              isMobile={isMobile}
              onChangeCalendar={onChangeCalendar}
              onRangeChange={onChangeCalendar}
              outletOptions={outletOptions}
              selectedOutlet={selectedCabang}
              selectedItems={selectedItems}
              attendanceType={attendanceType}
              setAttendanceType={setAttendanceType}
              handleChangeOutlet={handleChangeOutlet}
            />
            {isMobile && renderBtnExport()}
          </ConditionalWrapper>
        </Box>
        {selectedItems.length > 0 && (
          <Flex
            direction={flexDirection}
            justify="end"
            css={{
              gap: 16,
              backgroundColor: '$white',
              '@sm': {
                width: '$full',
                position: 'fixed',
                bottom: 0,
                padding: '16px 24px',
                zIndex: '$modal',
              },
              '@md': {
                width: '50%',
                position: 'relative',
                bottom: 'unset',
                padding: 'unset',
                zIndex: 'unset',
              },
            }}
          >
            <InputSelect
              css={{ '@md': { width: 185 } }}
              placement={isMobile ? 'top' : 'bottom'}
              size="sm"
              option={[
                { name: t('details.approve', 'Disetujui'), value: 'Y' },
                { name: t('details.reject', 'Ditolak'), value: 'N' },
              ]}
              placeholder={`${t('label.edit', { ns: 'translation' }, 'Ubah')} status`}
              onChange={e => setStatusBulk(e.value)}
            />
            <Button
              onClick={bulkUpdateStatus}
              css={{ '@md': { minWidth: 'unset' } }}
              size="sm"
              disabled={statusBulk === ''}
            >
              {`${t('label.edit', { ns: 'translation' })} Status (${selectedItems.length} item)`}
            </Button>
          </Flex>
        )}
      </Flex>
      <Separator css={{ '@sm': { mb: 20 }, '@md': { mb: 'unset' } }} />
      <MemoizedTable
        id="attendance_table"
        headerCss={tableHeaderCss}
        isLoading={tableState.tableLoading}
        css={tableCss}
        columns={tableColumnsConfig}
        data={tableState.tableData}
        totalData={tableState.tableData.length > 0 ? tableState.totalData : 0}
        pageIndex={tableState.pageIndex}
        rowLimit={tableState.pageSize}
        selectedIds={selectedRows}
        fetchData={handleFetchAttendanceReport}
        searchQuery={tableState.keywordSearch}
        additionalExceptionIds={additionalExceptionIds}
        hiddenColumns={tableHiddenColumnProps}
        virtualized
      />
      <BlockedDownloadPopup ref={blockedDownloadPopup} confirmHandle={() => redirectSupport()} />
      {picture.open ? (
        <ModalDialog
          hideCloseButton
          size="auto"
          open={picture.open}
          onOpenChange={val => (!val ? setPicture({ time: undefined, pic: '', open: false }) : null)}
        >
          <ModalDialog.Title>
            {picture.time === 'in' ? t('mainpage.column.clockInPhoto', 'Foto Absensi Jam Masuk') : t('mainpage.column.clockOutPhoto', 'Foto Absensi Jam Keluar')}
          </ModalDialog.Title>
          <ModalDialog.Content>
            <Flex direction="column">
              <Box
                as="img"
                src={picture.pic || NoImage}
                css={{
                  width: 'auto',
                  '@md': { maxHeight: '378px', minWidth: '383px' },
                  objectFit: 'contain',
                }}
                alt="Foto absensi"
              />
              <InfoContainer>
                <Flex gap={3} css={{ maxWidth: '367px' }}>
                  <Box css={{ flex: 1 }}>
                    <CalendarOutline color={colors.iconGreen} />
                  </Box>
                  <Paragraph color="primary">
                    <b>
                      {picture.time === 'in' ? t('mainpage.column.timeClockIn', 'Waktu Absen Masuk') : t('mainpage.column.timeClockout', 'Waktu Absen Keluar')}
                      {':'}
                    </b>
                    {' '}
                    {picture.timeString !== '-' || picture.dateString !== '-' ? (
                      <React.Fragment>
                        {picture.timeString}
                        ,
                        {' '}
                        {picture.dateString}
                      </React.Fragment>
                    ) : '-'}
                  </Paragraph>
                </Flex>
                <Separator css={{ margin: '$spacing-03 0' }} />
                <Flex gap={3} css={{ maxWidth: '367px' }}>
                  <Box css={{ flex: 1 }}>
                    <LocationOutline color={colors.iconGreen} />
                  </Box>
                  <Paragraph color="primary">
                    <b>
                      {picture.time === 'in' ? t('mainpage.column.locClockIn', 'Lokasi Absen Masuk') : t('mainpage.column.locClockout', 'Lokasi Absen Keluar')}
                      {':'}
                    </b>
                    {' '}
                    {picture.time === 'in' ? `${picture.location_login}` : `${picture.location_logout}`}
                  </Paragraph>
                </Flex>
              </InfoContainer>
            </Flex>
          </ModalDialog.Content>
          <ModalDialog.Footer>
            <DialogClose asChild>
              <Button buttonType={isMobile ? 'ghost' : 'secondary'} css={{ width: isMobile ? '100%' : 'unset' }}>{t('translation:label.close', 'Tutup')}</Button>
            </DialogClose>
          </ModalDialog.Footer>
        </ModalDialog>
      ) : null}
      {openNote.open ? (
        <ModalDialog open={openNote.open} onOpenChange={setOpenNote}>
          <ModalDialog.Title>
            {openNote.type === 'accept'
              ? t('confirmation.approved.title', 'Setujui Absensi Karyawan')
              : t('confirmation.reject.title', 'Tolak Absensi Karyawan')}
          </ModalDialog.Title>
          <ModalDialog.Content>
            <Box css={{
              display: 'grid', gap: '$compact', color: '$textPrimary', mb: '$compact',
            }}
            >
              <Box>
                <Trans t={t} i18nKey="confirmation.approvedNote" values={{ karyawan: getValues('form.karyawan'), mode: isAccept ? t('details.approved').toLowerCase() : t('details.rejected').toLowerCase() }}>
                  Absensi karyawan
                  <strong>{` ${getValues('form.karyawan')} `}</strong>
                  {`akan ${isAccept ? 'disetujui' : 'ditolak'}. Tambahkan catatan absensi bila diperlukan`}
                </Trans>
              </Box>
              <Separator css={{ my: 4 }} />
              <Box>
                <FormGroup>
                  <FormLabel htmlFor="note">{t('mainpage.column.note', 'Catatan')}</FormLabel>
                  <InputTextArea {...register('form.note')} css={{ minHeight: 62 }} placeholder={t('details.placeholder.ok', 'Contoh: Oke sesuai')} />
                </FormGroup>
              </Box>
            </Box>
          </ModalDialog.Content>
          <ModalDialog.Footer>
            <Flex gap={5}>
              <Button buttonType={isAccept ? 'ghost' : 'ghost-secondary'} onClick={() => { setOpenNote(item => ({ ...item, open: false })); }}>
                {t('translation:label.cancel', 'Batal')}
              </Button>
              <Button buttonType={isAccept ? 'primary' : 'negative'} onClick={handleAttendanceNote}>
                {isAccept ? t('approveAbsence', 'Setujui Absensi') : t('rejectAbsence', 'Tolak Absensi')}
              </Button>
            </Flex>
          </ModalDialog.Footer>
        </ModalDialog>
      ) : null}
      {openChangeNote ? (
        <ModalDialog open={openChangeNote} onOpenChange={setOpenChangeNote}>
          <ModalDialog.Title>
            {t('changeNote', 'Ubah Catatan')}
          </ModalDialog.Title>
          <ModalDialog.Content>
            <Box css={{
              display: 'grid', gap: '$compact', color: '$textPrimary', mb: '$compact',
            }}
            >
              <Box>
                <FormGroup>
                  <FormLabel htmlFor="note">{t('mainpage.column.note', 'Catatan')}</FormLabel>
                  <InputTextArea {...register('form.note')} css={{ minHeight: 62 }} placeholder={t('details.placeholder.ok', 'Contoh: Oke sesuai')} />
                </FormGroup>
              </Box>
            </Box>
          </ModalDialog.Content>
          <ModalDialog.Footer>
            <Flex css={{ width: '100%', '@md': { width: 'auto' }, '& button': { flex: 1, '@md': { flex: 'initial' } } }} gap={5}>
              <Button onClick={() => { setOpenChangeNote(false); }} buttonType="ghost">
                {t('translation:label.cancel', 'Batal')}
              </Button>
              <Button onClick={handleChangeNote}>
                {t('translation:save', 'Simpan')}
              </Button>
            </Flex>
          </ModalDialog.Footer>
        </ModalDialog>
      ) : null}
      {/*
          Presence Log Activity
       */}
      {logState.show ? (
        <LogModal
          handleClose={() => setLogState({ show: false, userId: 0 })}
          state={logState}
          start={moment(calendarDate.start).format('YYYY-MM-DD')}
          end={moment(calendarDate.end).format('YYYY-MM-DD')}
          t={t}
        />
      ) : null}
      {openModal ? (
        <PageDialog
          isMobile={isMobile}
          modal
          open={openModal}
          size="full"
          onOpenChange={toogleModal}
        >
          <PageDialogTitle>{t('details.title', 'Detail Absensi Karyawan')}</PageDialogTitle>
          <PageDialogContent wrapperSize="md">
            <React.Suspense fallback={null}>
              <DetailAttendace />
            </React.Suspense>
          </PageDialogContent>
          <PageDialogFooter css={{ padding: '12px 16px', '@md': { padding: '16px 24px' } }}>
            <Flex
              css={{
                mx: 'auto', gap: 16, width: '$full', '@md': { width: 982 },
              }}
              justify={{ '@sm': 'between', '@md': 'end' }}
            >
              <Button onClick={onDitolakAction} buttonType="negative-secondary" css={{ '@sm': { width: '50%' }, '@md': { width: 'unset' } }}>
                {t('details.reject', 'Ditolak')}
              </Button>
              <Button onClick={onDisetujuiAction} css={{ '@sm': { width: '50%' }, '@md': { width: 'unset' } }}>
                {t('details.approve', 'Disetujui')}
              </Button>
            </Flex>
          </PageDialogFooter>
        </PageDialog>
      ) : null}
    </Paper>
  );
});

Attendance.propTypes = {
  hideProgress: propType.func,
  showProgress: propType.func,
  selectedCabang: propType.string,
  router: propType.shape(),
  listCabang: propType.arrayOf(propType.shape()),
  logo: propType.string,
  namaUsaha: propType.string,
  namaUser: propType.string,
  // eslint-disable-next-line react/forbid-prop-types
  supportNeeded: propType.array,
  calendar: propType.shape({
    start: propType.string,
    end: propType.string,
  }),
  assignCalendar: propType.func,
  selectOutlet: propType.func,
  filterBranch: propType.string,
};
Attendance.defaultProps = {
  hideProgress: () => { },
  showProgress: () => { },
  selectOutlet: () => { },
  selectedCabang: '',
  router: {},
  listCabang: [{}],
  logo: '',
  namaUsaha: '',
  namaUser: '',
  supportNeeded: [],
  calendar: {
    start: moment().startOf('month').format('DD-MM-YYYY'),
    end: moment().endOf('month').format('DD-MM-YYYY'),
  },
  assignCalendar: () => { },
  filterBranch: '',
};

const mapStateToProps = state => ({
  logo: state.user.profile.user_usaha_logo_path,
  listCabang: state.branch.list,
  namaUser: state.user.profile.user_name,
  namaUsaha: state.user.profile.user_usaha_name,
  selectedCabang: state.branch.filter,
  supportNeeded: state.layout.supportNeeded,
});

const mapDispatchToProps = dispatch => ({
  selectOutlet: (idCabang) => {
    dispatch(filterCabang(idCabang));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(CoreHOC(Attendance));
