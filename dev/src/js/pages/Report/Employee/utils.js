import { priceType } from '../../../components/form/transaksi/settings/enum';
import { getTotalBiayaTambahanMdr, getKembalian, getTotalbayar } from '../../../components/form/transaksi/settings/methods';

const fieldValidation = value => (value ? parseFloat(value) : 0);

export const generatePaymentList = (data) => {
    let dataInvoice = null;
    let diskonInfoValue = 0;
    let subTotalProduk = 0;
    let ongkir = 0;

    subTotalProduk = fieldValidation(data.info.total_product_value);
    diskonInfoValue = fieldValidation(data.info.discount_amount || data.info.diskon_nominal);
    ongkir = fieldValidation(data.info.shipment_cost);

    const pembulatan = fieldValidation(data.info.round);
    const serviceInfoValue = fieldValidation(data.info.service_charge_amount);
    const taxes = Array.isArray(data.info.tax) ? data.info.tax : [];
    const totalTax = Array.isArray(data.info.tax) ? data.info.tax.reduce((totalTaxAmount, { amount }) => totalTaxAmount + amount, 0) : data.info.tax_nominal;
    const pajakInfoValue = fieldValidation(totalTax);
    const totalPromo = data.info.promo ? data.info.promo.reduce((totalPromos, promo) => totalPromos + parseFloat(promo.amount), 0) : 0;

    const finalPajakInfoValue = data.info.tax_type !== priceType.WITHTAX ? pajakInfoValue : 0;

    let biayaLain = [];
    let totalBiayaLain = 0;
    const isInvoice = data.info.sales_type === 0;
    if (isInvoice) {
        if (String(data.info.tax_type) === TAX_TYPE.PRICE_INCLUDES_TAX) {
            const deepCloneData = JSON.parse(JSON.stringify(data));
            const productsRefund = deepCloneData.produk.map(val => ({ ...val, qty_refund: val.qty }));
            dataInvoice = dataRefundInvoice({ ...deepCloneData, productsRefund, dataSettingRefund: { ...defaultSettingRefund }, refund: [] });
        }
        if (data.info.additional_cost !== null) {
            biayaLain = data.info.additional_cost;
            totalBiayaLain = biayaLain.reduce((sum, { nominal }) => sum + nominal, 0);
        }
    }

    const totalBiayaTambahanMdr = getTotalBiayaTambahanMdr(data.payment);
    const totalBayar = getTotalbayar(data.payment);
    const kembalian = getKembalian(data.payment);

    const grandTotalValue = (subTotalProduk + serviceInfoValue + finalPajakInfoValue + ongkir + pembulatan + totalBiayaLain + totalBiayaTambahanMdr - (totalPromo + diskonInfoValue));

    const kurangBayar = parseFloat(grandTotalValue) - parseFloat(totalBayar - kembalian);

    const paymentList = {
        subTotal: subTotalProduk,
        pembulatan,
        ongkir,
        serviceCharge: serviceInfoValue,
        pajak: pajakInfoValue,
        finalPajak: finalPajakInfoValue,
        diskonInfo: diskonInfoValue,
        totalPromo,
        biayaLain,
        totalBiayaLain,
        totalBiayaTambahanMdr,
        totalBayar,
        kembalian,
        grandTotal: grandTotalValue,
        kurangBayar,
        taxes,
        ...dataInvoice,
    };

    return paymentList;
};

export const getTotalPriceProduct = (data) => {
    const totalAddon = (data.item_addon_detail || []).reduce((acc, add) => {
        const quantityAddonPerProduct = add.quantity / data.quantity;

        acc += (data.qty_refund || 0) * quantityAddonPerProduct * add.price;

        return acc;
    }, 0);

    return (data.qty_refund || 0) * data.harga + totalAddon;
}