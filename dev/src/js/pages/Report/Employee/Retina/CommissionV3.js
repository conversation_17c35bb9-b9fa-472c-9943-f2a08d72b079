/* eslint-disable react/no-array-index-key */
/* eslint-disable consistent-return */
import React, {
    useEffect, useState, useMemo, useContext,
} from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import debounce from 'lodash/debounce';
import {
    Flex, Paper, Box, Heading, Button, Separator, Table, ToastContext, InputSearchbox, InputDatePicker, PageDialog, FormGroup, FormLabel, DialogClose, InputDateRange, InputSelect, Card, Tooltip,
} from '@majoo-ui/react';
import {
    CalendarOutline, FilterOutline, CircleInfoOutline,
} from '@majoo-ui/icons';
import moment from 'moment';

import { useTranslation, Trans } from 'react-i18next';
import { foundations } from '@majoo-ui/core';
import CoreHOC from '../../../../core/CoreHOC';
import { catchError, currency, resetDateRangeHelper } from '../../../../utils/helper';
import { tableColumn, FILTER_PRODUCT, hiddenCol, remappingLegendData } from '../settings/utils';
import { setText } from '../../../../actions/textActions';
import * as API from '../../../../data/reports';
import * as outletActions from '../../../../data/outlets/actions';
import { toMoment } from '../../../Inventory/StockProduksiV2/utils/helper';
import useResetDateRange from '../../../../hooks/useResetDateRange';
import { BannerText, FavoriteWrapper, TooltipGuidance } from '../../../../components/retina';
import { LineChartv2, fillDataDaily, handleXaxis } from '../../../../components/chart';
import ChartSummaryTooltip from './ChartSummaryTooltip';
import { downloadTypePdf } from '../../../../components/retina/export/utils';
import ExportReport from '../../../../components/retina/export/ExportReport';

const { colors } = foundations;

const chartColor = Object.keys(colors)
    .filter(key => key.includes('chart'))
    .reduce((prev, current) => [...prev, colors[current]], []);

const CommissionV3 = (props) => {
    const {
        showProgress,
        hideProgress,
        calendar,
        filterBranch,
        assignCalendar,
        addNotification,
    } = props;

    const [filterQuery, setFilterQuery] = useState('');
    const [filterProduct, setFilterProduct] = useState('all');
    const [filteredTable, setFilteredTable] = useState([]);
    const [filterSort, setFilterSort] = useState({ order: '', sort: '' });
    const [dateRange, setDateRange] = useState({
        start_date: moment(calendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
        end_date: moment(calendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
    });
    const [limit, setLimit] = useState('10');
    const [metaTable, setMetaTable] = useState({});
    const [dataCardSummary, setCardSummary] = useState([]);
    const [loading, setLoading] = useState(false);
    const [tempFilterProduct, setTempFilterProduct] = useState('all');
    const [openFilter, setOpenFilter] = useState(false);
    const [tempCal, setTempCal] = useState([toMoment(calendar.start).toDate(), toMoment(calendar.end).toDate()]);
    const [dateRangeKey, resetDateKey] = useResetDateRange();
    const [graphData, setGraphData] = useState([]);
    const [chart, setChart] = useState({
        data: [],
        legendData: [],
        comparators: [],
    });
    const [selectedDataComparator, setSelectedDataComparator] = useState([]);

    const { t, ready, i18n } = useTranslation(['Penjualan/Laporan/Karyawan/komisi', 'translation']);
    const currentLang = i18n.language;
    const { addToast } = useContext(ToastContext);

    const translate = value => t(`main.${value}`);

    const handleSearchChangeValue = (value) => {
        setFilterQuery(value);
    };

    const handleFilterProduct = ({ value }) => {
        setFilterProduct(() => value);
    };

    const handleSearchResults = useMemo(() => debounce(handleSearchChangeValue, 500), []);

    useEffect(() => () => {
        handleSearchResults.cancel();
    });

    const fetchData = async (paramPayload) => {
        setLoading(true);
        let payload = paramPayload || {};
        if (filterBranch) {
            payload = {
                ...payload,
                outlet_id: filterBranch,
            };
        }

        if (filterQuery) {
            payload = {
                ...payload,
                search: filterQuery,
            };
        }

        try {
            let commissionList = [];
            const response = await API.getLaporanCommission(payload);
            const responseCardSummary = await API.getLaporanCommissionCardSummary({
                start_date: payload.start_date,
                end_date: payload.end_date,
                ...(paramPayload.type && { type: paramPayload.type }),
                ...(payload.outlet_id && { outlet_id: payload.outlet_id }),
            });

            if (response.data !== null) {
                commissionList = response.data.map(commission => Object.assign(
                    {
                        id: commission.employee.id,
                        employee_name: commission.employee.name,
                        outlet_name: commission.outlet_name,
                        transaction: commission.sales,
                        product: commission.products,
                        commission: parseInt(commission.total, 10),
                    },
                    commission,
                ));
            } else {
                commissionList = [];
            }

            const cardSummary = [
                {
                    title: t('mainSummary.summaryCardSection.total', 'Total Komisi'),
                    value: currency({ value: responseCardSummary.data.total || 0, decimal: 2 }),
                    cardColor: 'orange',
                    tooltip: t('mainSummary.tooltipSummaryCardSection.total', 'Jumlah total komisi diakumulasi dari semua komisi produk dan komisi penjualan'),
                },
                {
                    title: t('mainSummary.summaryCardSection.product', 'Komisi Produk'),
                    value: currency({ value: responseCardSummary.data.product || 0, decimal: 2 }),
                    cardColor: 'blue',
                    tooltip: t('mainSummary.tooltipSummaryCardSection.product', 'Komisi didapat dari total komisi jenis produk'),
                },
                {
                    title: t('mainSummary.summaryCardSection.transaction', 'Komisi Penjualan'),
                    value: currency({ value: responseCardSummary.data.sales || 0, decimal: 2 }),
                    cardColor: 'purple',
                    tooltip: t('mainSummary.tooltipSummaryCardSection.transaction', 'Komisi didapat dari total komisi penjualan'),
                },
            ];
            setSelectedDataComparator([...commissionList.filter((_, idx) => idx < 5).map(x => x.id)])
            setFilteredTable(commissionList);
            setMetaTable(response.meta);
            switch (filterProduct) {
                case 'product':
                    setCardSummary([cardSummary[0], cardSummary[1]]);
                    break;
                case 'sales':
                    setCardSummary([cardSummary[0], cardSummary[2]]);
                    break;
                default:
                    setCardSummary(cardSummary);
                    break;
            }

            return response;
        } catch (err) {
            addToast({
                title: t('toast.error', 'Gagal!', { ns: 'translation' }),
                description: catchError(err),
                variant: 'failed',
                position: 'top-right',
            });
        } finally {
            setLoading(false);
        }
    };

    const fetchGraph = async (payload) => {
        try {
            setLoading(true);
            const resGraph = await API.getLaporanCommissionGraph(payload);
            let resGraphData = [];

            if (resGraph.data) { resGraphData = resGraph.data; }

            const legendData = remappingLegendData(resGraphData, chartColor, filterProduct);

            const getDataPerEmp = resGraphData.reduce((prev, current) => {
                const match = prev.find(p => p.date === current.date);
                if (match) {
                    Object.assign(match, { ...{ [current.employee.id]: current[filterProduct === 'all' ? 'total' : filterProduct] } });
                } else {
                    prev.push({
                        date: current.date,
                        [current.employee.id]: current[filterProduct === 'all' ? 'total' : filterProduct],
                    });
                }
                return prev;
            }, []);

            setChart(x => ({
                ...x,
                data: fillDataDaily(payload.start_date, payload.end_date, 'date', getDataPerEmp, legendData),
                legendData,
                comparators: legendData,
            }));
            setGraphData(resGraphData);
        } catch (err) {
            addToast({
                title: t('toast.error', 'Gagal!', { ns: 'translation' }),
                description: catchError(err),
                variant: 'failed',
                position: 'top-right',
            });
        } finally {
            setLoading(false);
        }
    };

    const fetchCommissionList = async () => {
        const payload = {
            start_date: moment(calendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            end_date: moment(calendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            limit,
            page: 1,
            ...(filterProduct !== 'all' && { type: filterProduct }),
        };

        fetchData(payload);
    };

    const fetchCommissionGraph = async () => {
        if (selectedDataComparator.length > 0) {
            const payload = {
                start_date: moment(calendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                end_date: moment(calendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                ids: selectedDataComparator.join(','),
                ...filterBranch && { outlet_id: filterBranch },
            };
    
            fetchGraph(payload);
        } else {
            setChart({
                data: [],
                legendData: [],
                comparators: [],
            });
        }
    };

    const handlePagination = (params) => {
        let payload = {
            start_date: moment(calendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            end_date: moment(calendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            limit: params.pageSize,
            page: params.pageIndex + 1,
        };

        if (params.sortAccessor && params.sortDirection) {
            payload = {
                ...payload,
                sort: params.sortDirection.toLowerCase() || '',
                order: params.sortAccessor.toLowerCase() || '',
            };
        }

        setLimit(params.pageSize);
        setFilterSort({ order: params.sortAccessor ? params.sortAccessor.toLowerCase() : '', sort: params.sortDirection ? params.sortDirection.toLowerCase() : '' });
        fetchData(payload);
    };

    const handleChangeDateRange = (value) => {
        const dateFormat = 'DD/MM/YYYY';
        const { newStartDate, newEndDate, isForceReset } = resetDateRangeHelper(value[0], value[1], 90);
        if (isForceReset) {
            addNotification({
                title: t('toast.errorMaxDate', 'Terjadi Kesalahan!', { ns: 'translation' }),
                description: t('toast.errorMaxDateRange', 'Maksimum rentang waktu yang dapat dipilih: 3 bulan', { ns: 'translation' }),
                level: 'pending',
            });
            const startDate = moment(newStartDate, dateFormat).format('YYYY-MM-DD');
            const endDate = moment(newEndDate, dateFormat).format('YYYY-MM-DD');

            setDateRange({ start_date: startDate, end_date: endDate });
            assignCalendar(newStartDate, newEndDate, null, null);
            setTempCal([moment(newStartDate, dateFormat).toDate(), moment(newEndDate, dateFormat).toDate()]);
        } else {
            const startDate = moment(value[0], dateFormat).format('YYYY-MM-DD');
            const endDate = moment(value[1], dateFormat).format('YYYY-MM-DD');

            setDateRange({ start_date: startDate, end_date: endDate });
            assignCalendar(value[0], value[1]);
            setTempCal(value);
        }
    };

    const downloadReport = async (type = 'xlsx') => {
        try {
            showProgress();

            const { start, end } = calendar;
            const { order, sort } = filterSort;

            let filterValue = filterProduct;

            if (filterProduct === 'all') {
                filterValue = '';
            } else if (filterProduct === 'sales') {
                filterValue = 'transaction';
            }

            const finalPayload = {
                report_type: type,
                start_date: moment(start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                end_date: moment(end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                limit: metaTable.per_page,
                page: metaTable.current_page,
                outlet_id: filterBranch,
                ...filterValue && { filter: filterValue },
                ...filterQuery && { search: filterQuery },...(order && sort) && {
                    sort: (sort === 'ASC' ? '' : '-') + order,
                },
            };
            
            const res = await API.exportLaporanCommissionV2(finalPayload);

            if (type === 'xslx') window.location = res.data;
            else downloadTypePdf(res.data);

            addToast({
                title: t('toast.success', 'Berhasil!', { ns: 'translation' }),
                description: (
                    <Trans t={t} i18nKey="main.toast.export.success" />
                ),
                variant: 'success',
            });
        } catch (e) {
            addToast({
                title: t('toast.error', 'Gagal!', { ns: 'translation' }),
                description: (
                    <Trans t={t} i18nKey="main.toast.export.error" />
                ),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    const onRowClick = (type, data) => {
        const { employee_name: employeeName } = data;
        const { router, location, setMajooText, idCabang } = props;
        setMajooText(employeeName);
        if (type === 'detail') {
            router.push({
                pathname: `${location.pathname}/per-transaksi/${employeeName}/${data.employee.id}/${idCabang}`,
                state: { empName: employeeName },
            });
        } else if (type === 'ringkasan') {
            router.push({
                pathname: `${location.pathname}/per-jenis-komisi/${employeeName}/${data.employee.id}/${idCabang}`,
                state: { empName: employeeName },
            });
        }
    };

    const responsivePaper = {
        '@sm': {
            padding: 0,
            backgroundColor: 'transparent',
            boxShadow: 'none',
        },
        '@md': {
            padding: '$spacing-05',
            backgroundColor: '$white',
            boxShadow: '0px 2px 12px #00000014',
        },
    };

    const resetFilter = () => {
        resetDateKey();
        setTempCal([moment().startOf('month').toDate(), moment().endOf('month').toDate()]);
    };

    const setFilter = () => {
        setFilterProduct(tempFilterProduct);
        handleChangeDateRange(tempCal);
    };

    const handleSearchComparator = debounce( async (search) => {
        try {
            const payloadList = {
                start_date: moment(calendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                end_date: moment(calendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                limit,
                page: 1,
                ...(filterProduct !== 'all' && { type: filterProduct }),
                ...filterBranch && { outlet_id: filterBranch },
                search,
            };
            const { data = [] } = await API.getLaporanCommission(payloadList);
            const dataComparators = chart.comparators.map(x => x.value);
            const excludeDataComparatorsExist = data.filter(x =>  !dataComparators.includes(x.employee.id)).map(x => ({ name: x.employee.name, value: x.employee.id }));

            if (data.length > 0 && excludeDataComparatorsExist.length > 0) {
                setChart(prev => ({
                    ...prev,
                    comparators: [
                        ...prev.comparators,
                        ...excludeDataComparatorsExist,
                    ],
                }));
            }
        } catch (error) {
            addToast({
                title: t('toast.error', 'Gagal!', { ns: 'translation' }),
                description: catchError(error),
                variant: 'failed',
                position: 'top-right',
            });
        }
    }, 600)

    const handleComparator = async (e) => {
        switch (e.type) {
            case 'DELETE':
                setSelectedDataComparator([...selectedDataComparator.filter(id => id !== e.payload.value)])
                break;
            case 'ADD':
                setSelectedDataComparator([...selectedDataComparator, ...[e.payload.value]]);
                break;
            case 'SEARCH':
                if (e.payload !== '') handleSearchComparator(e.payload);
                break;
            default:
                break;
        }
    };

    useEffect(() => {
        fetchCommissionList();
    }, [JSON.stringify(dateRange), filterBranch, filterQuery, filterProduct]);

    useEffect(() => {
        fetchCommissionGraph();
    }, [JSON.stringify(dateRange), filterBranch, filterQuery, filterProduct, JSON.stringify(selectedDataComparator)]);

    useEffect(() => {
        if (loading) {
            showProgress();
        } else {
            hideProgress();
        }
    }, [loading]);

    return !ready ? null : (
        <Flex
            direction="column"
            css={{ gap: '$comfortable', '@md': { marginBottom: '28px' } }}
        >
            <Paper css={responsivePaper}>
                <Box css={{ '@sm': { height: 'auto' }, '@md': { height: '100%' } }}>
                    <Flex justify="between" css={{ '@md': { padding: '$spacing-03 0 $spacing-05 0' } }}>
                        <Box>
                            <FavoriteWrapper css={{ marginBottom: '$spacing-02', '@md': { marginBottom: '12px' } }}>
                                <Heading
                                    as="h1"
                                    heading="pageTitle"
                                >
                                    {t('main.headerSection.title', 'Komisi')}
                                </Heading>
                                <TooltipGuidance />
                            </FavoriteWrapper>
                            <Flex align="center" css={{ gap: '$spacing-03', marginTop: '$spacing-02' }}>
                                <CalendarOutline size="24px" />
                                <p style={{ fontSize: 12, margin: 0 }}>
                                    {`${moment(
                                        calendar.start,
                                        'DD/MM/YYYY',
                                    )
                                        .locale(currentLang)
                                        .format(
                                            'DD MMMM YYYY',
                                        )} - ${moment(
                                            calendar.end,
                                            'DD/MM/YYYY',
                                        )
                                            .locale(currentLang)
                                            .format('DD MMMM YYYY')}`}
                                </p>
                            </Flex>
                        </Box>
                        <ExportReport
                            onExport={downloadReport}
                            calendar={calendar}
                            title={t('main.headerSection.titleDownload', 'Komisi')}
                            type="multi"
                            css={{
                                '@sm': {
                                    display: 'none',
                                },
                                '@md': {
                                    display: 'inline-flex',
                                },
                            }}
                        />
                    </Flex>
                </Box>
                <BannerText css={{ mt: 0 }} />
                <Separator
                    css={{ '@sm': { display: 'none' }, '@md': { display: 'block' } }}
                />
                <Box css={{ margin: '12px 0' }}>
                    <Flex align="center" css={{ '@sm': { flexDirection: 'column', gap: '$compact' }, '@md': { flexDirection: 'row' } }}>
                        <InputSearchbox
                            placeholder={t('placeholder.search', 'Cari...', { ns: 'translation' })}
                            onChange={handleSearchResults}
                            css={{
                                padding: '$spacing-03',
                                width: '258px',
                                '@sm': {
                                    width: '100%',
                                },
                                '@md': {
                                    flex: '1 0 258px',
                                },
                            }}
                        />
                        <InputDatePicker
                            css={{
                                display: 'none',
                                padding: '$spacing-03',
                                width: '258px',
                                '@sm': {
                                    width: '100%',
                                    padding: '$spacing-03 0',
                                },
                                '@md': {
                                    display: 'block',
                                    flex: '1 0 258px',
                                },
                            }}
                            type="advance"
                            onChange={handleChangeDateRange}
                            date={tempCal}
                        />
                        <InputSelect
                            onChange={e => handleFilterProduct(e)}
                            option={FILTER_PRODUCT}
                            value={FILTER_PRODUCT.find(x => x.value === filterProduct)}
                            size="sm"
                            css={{
                                display: 'none',
                                padding: '$spacing-03',
                                width: '258px',
                                '@sm': {
                                    width: '100%',
                                    padding: '$spacing-03 0',
                                },
                                '@md': {
                                    display: 'block',
                                    flex: '1 0 258px',
                                },
                            }}
                        />
                        <Flex css={{ width: '100%', gap: '$compact' }}>
                            <Button
                                size="sm"
                                buttonType="secondary"
                                leftIcon={<FilterOutline color="currentColor" />}
                                onClick={() => setOpenFilter(true)}
                                css={{
                                    minWidth: 'unset',
                                    display: 'flex',
                                    alignItems: 'center',
                                    '& > div': { height: '24px' },
                                    '@md': { display: 'none' },
                                }}
                            >
                                {t('mainDetail.dialogInOutKasBank.modal.filter', 'Filter')}
                            </Button>
                            <ExportReport
                                onExport={downloadReport}
                                calendar={calendar}
                                title={t('main.headerSection.titleDownload', 'Komisi')}
                                type="multi"
                                css={{
                                    '@sm': {
                                        display: 'inline-flex',
                                        width: '100%',
                                    },
                                    '@md': {
                                        display: 'none',
                                    },
                                }}
                            />
                        </Flex>
                    </Flex>
                </Box>
                <Separator css={{ my: 20, '@md': { my: 0 } }} />
                <Box css={{ margin: '$spacing-05 0', '@md': { padding: '0px $spacing-03' } }}>
                    <LineChartv2
                        data={chart.data}
                        legendData={chart.legendData}
                        comparatorList={chart.comparators}
                        onComparatorChange={handleComparator}
                        period="day"
                        chartTypeLabel=""
                        xAxisKey="date"
                        customXAxisFormat={d => handleXaxis(d, 'day')}
                        title="Grafik Komisi"
                        isEmptyData={chart.data.length <= 0}
                        customTooltip={filterProduct === 'all' ? val => <ChartSummaryTooltip {...val} {...{ graphData }} /> : undefined}
                        showSeparator={false}
                    />
                </Box>
                <Separator css={{ my: 20, '@md': { my: 0 } }} />
                <Flex
                    css={{
                        margin: '18px 0',
                        gap: '16px',
                        '@sm': { flexDirection: 'column' },
                        '@md': { flexDirection: 'row' },
                    }}
                >
                    {
                        dataCardSummary.length > 0 && dataCardSummary.map((data, idx) => (
                            <Card color={data.cardColor} shadow textColor="" width="100%" padding="8px 24px" key={idx}>
                                <Flex gap={2} align="center">
                                    <h4
                                        style={{
                                            fontSize: 14,
                                            color: '#717A7A',
                                            fontWeight: 600,
                                            margin: '8px 0',
                                        }}
                                    >
                                        {data.title}
                                    </h4>
                                    <Tooltip
                                        css={{ maxWidth: '163px' }}
                                        label={data.tooltip}
                                        withClick={false}
                                        side="top"
                                        align="center"
                                    >
                                        <CircleInfoOutline size={14} />
                                    </Tooltip>
                                </Flex>
                                <p style={{ fontSize: 18, color: '#272A2A', fontWeight: 600 }}>{data.value}</p>
                            </Card>
                        ))
                    }
                </Flex>
                <Box>
                    <Table
                        id='commission_report_table'
                        columns={tableColumn({ onRowClick, translate })}
                        hiddenColumns={hiddenCol(filterProduct)}
                        data={filteredTable}
                        totalData={metaTable && metaTable.total}
                        fetchData={handlePagination}
                        isLoading={loading}
                        searchQuery={filterQuery}
                        css={{ padding: 0 }}
                    />
                </Box>
            </Paper>
            <PageDialog open={openFilter} onOpenChange={setOpenFilter}>
                <PageDialog.Title>
                    {t('mainDetail.dialogInOutKasBank.modal.filterData.title', 'Filter Data')}
                </PageDialog.Title>
                <PageDialog.Content
                    css={{
                        display: 'flex', flexDirection: 'column', gap: '$compact', backgroundColor: '$white', padding: '$spacing-05',
                    }}
                >
                    <FormGroup>
                        <FormLabel css={{ color: '$textPrimary' }}>{t('mainDetail.dialogInOutKasBank.modal.filterData.dateRange', 'Rentang Tanggal')}</FormLabel>
                        <InputDatePicker
                            size="lg"
                            type="range"
                            onChange={(d) => { setTempCal(d); resetDateKey(); }}
                            date={tempCal}
                        />
                    </FormGroup>
                    <FormGroup>
                        <FormLabel css={{ color: '$textPrimary' }}>Rentang Tanggal</FormLabel>
                        <InputDateRange
                            key={dateRangeKey}
                            onChange={setTempCal}
                            size="lg"
                            optionCss={{ maxHeight: 150 }}
                        />
                    </FormGroup>
                    <FormGroup>
                        <FormLabel css={{ color: '$textPrimary' }}>Filter Product</FormLabel>
                        <InputSelect
                            onChange={e => setTempFilterProduct(e.value)}
                            option={FILTER_PRODUCT}
                            value={FILTER_PRODUCT.find(x => x.value === filterProduct)}
                            optionCss={{ maxHeight: 150 }}
                        />
                        <Box css={{ height: 150 }} />
                    </FormGroup>
                </PageDialog.Content>
                <PageDialog.Footer css={{ gap: '$compact' }}>
                    <Button size="md" buttonType="ghost" css={{ flex: '1' }} onClick={() => resetFilter()}>
                        Reset
                    </Button>
                    <DialogClose asChild>
                        <Button size="md" css={{ flex: 1 }} onClick={() => setFilter()}>
                            {t('mainDetail.dialogInOutKasBank.modal.filterData.apply', 'Terapkan')}
                        </Button>
                    </DialogClose>
                </PageDialog.Footer>
            </PageDialog>
        </Flex>
    );
};

CommissionV3.propTypes = {
    filterBranch: PropTypes.string,
    showProgress: PropTypes.func,
    hideProgress: PropTypes.func,
    assignCalendar: PropTypes.func,
    setMajooText: PropTypes.func,
    calendar: PropTypes.shape({
        start: PropTypes.string,
        end: PropTypes.string,
    }).isRequired,
    router: PropTypes.shape({
        push: PropTypes.func,
    }),
    location: PropTypes.shape({
        pathname: PropTypes.string,
    }),
    notificationSystem: PropTypes.shape({
        addNotification: PropTypes.func,
    }),
    addNotification: PropTypes.func,
    idCabang: PropTypes.string,
};

CommissionV3.defaultProps = {
    filterBranch: '',
    showProgress: () => { },
    hideProgress: () => { },
    assignCalendar: () => { },
    router: null,
    location: null,
    notificationSystem: null,
    setMajooText: () => { },
    addNotification: () => { },
    idCabang: '',
};

const mapDispatchToProps = dispatch => ({
    setMajooText: val => dispatch(setText(val)),
    actions: {
        outlet: bindActionCreators(outletActions, dispatch),
    },
});

const mapStateToProps = state => ({
    logo: state.user.profile.user_usaha_logo_path,
    listCabang: state.branch.list,
    namaUser: state.user.profile.user_name,
    namaUsaha: state.user.profile.user_usaha_name,
    selectedCabang: state.branch.filter,
    supportNeeded: state.layout.supportNeeded,
    card: state.report.employeeCommissionCard,
});

export default CoreHOC(connect(mapStateToProps, mapDispatchToProps)(CommissionV3));
