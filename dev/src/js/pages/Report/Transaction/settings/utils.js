import {
    enumDiskon, taxBaseType, taxType,
} from '../../../../components/form/transaksi/settings/enum';
import {
    PROMO_CATEGORY, PROMO_TYPE, SERVICE_CHARGE_TYPE, TAX_BASE_TYPE, TAX_TYPE,
} from './enum';
import { currency } from '../../../../utils/helper';
import { getAccountType } from '../../../../utils/savedLocalStorage';
import { accountType } from '../../../../config/enum';

const fieldValidation = value => (value ? parseFloat(value) : 0);

const calculateTax = (taxType, SCType, state) => {
    const {
        serviceChargeNominal,
        taxValue,
        totalDiscountProduct,
        nominalDisc,
        dataSettingRefund,
    } = state;
    let {
        totalPriceBeforeDiscount,
        totalPriceAfterDiscount,
    } = state;
    let taxAmount = 0;
    const serviceChargeTaxed = String(SCType) === SERVICE_CHARGE_TYPE.SERVICE_CHARGE_TAXED;
    const serviceChargeNoTaxed = String(SCType) === SERVICE_CHARGE_TYPE.SERVICE_CHARGE_NO_TAXED || !Number(SCType);

    const taxPercent = (parseFloat(taxValue) / 100);

    totalPriceBeforeDiscount *= parseFloat(dataSettingRefund.refund_percentage) / 100;
    totalPriceAfterDiscount *= parseFloat(dataSettingRefund.refund_percentage) / 100;

    switch (String(taxType)) {
        case TAX_TYPE.NO_TAX:
            taxAmount = 0;
            break;
        case TAX_TYPE.TAX_BEFORE_DISCOUNT:
            if (serviceChargeNoTaxed) {
                taxAmount = totalPriceBeforeDiscount * taxPercent;
            } else if (serviceChargeTaxed) {
                taxAmount = (totalPriceBeforeDiscount + serviceChargeNominal) * taxPercent;
            }
            break;
        case TAX_TYPE.TAX_AFTER_DISCOUNT:
            taxAmount = totalPriceAfterDiscount - nominalDisc;
            if (serviceChargeNoTaxed) {
                taxAmount *= taxPercent;
            } else if (serviceChargeTaxed) {
                taxAmount = (taxAmount + serviceChargeNominal) * taxPercent;
            }
            break;
        case TAX_TYPE.PRICE_INCLUDES_TAX: {
            const totalPrice = totalPriceBeforeDiscount / (1 + taxPercent);
            const totalDiscount = totalDiscountProduct + nominalDisc;
            if (serviceChargeNoTaxed) {
                taxAmount = taxPercent * (totalPrice - totalDiscount);
            } else if (serviceChargeTaxed) {
                taxAmount = taxPercent * (totalPrice - totalDiscount + serviceChargeNominal);
            }
            break;
        }
        default:
        // do nothing
    }

    return taxAmount;
};

const calculateGrandTotal = (state) => {
    const {
        priceIncludesTax,
        isTaxPerProduct,
        nominalDisc,
        taxAmount,
        totalCosts,
        serviceChargeNominal,
        subtotal,
        totalDiscountProduct,
        administrationFee,
    } = state;
    let grandTotal = 0;
    let totalTax = taxAmount;

    if (isTaxPerProduct && priceIncludesTax) totalTax = 0;

    grandTotal = parseFloat(subtotal, 10) + totalTax + parseFloat(totalCosts, 10) + serviceChargeNominal - administrationFee;

    if (!priceIncludesTax) {
        grandTotal = parseFloat(grandTotal, 10) - parseFloat(nominalDisc, 10);
    } else {
        grandTotal = parseFloat(grandTotal, 10) - parseFloat(nominalDisc, 10) - parseFloat(totalDiscountProduct, 10);
    }
    return grandTotal;
};

const calculateTaxPerProduct = (state) => {
    const {
        taxType,
        product,
        nominalDisc,
        totalPriceBeforeDiscount,
        taxProductPercentage,
    } = state;
    const price = parseFloat(product.harga);
    const qty = parseFloat(product.qty);
    const tax = taxProductPercentage / 100;

    let retval = 0;

    if (String(taxType) === TAX_TYPE.TAX_BEFORE_DISCOUNT) {
        retval = tax * (price * qty);
    } else if (String(taxType) === TAX_TYPE.TAX_AFTER_DISCOUNT) {
        retval = tax * (product.total - ((nominalDisc / totalPriceBeforeDiscount * price) * qty));
    } else {
        retval = ((product.total - (nominalDisc / totalPriceBeforeDiscount * price) * qty) / (1 + tax)) * tax;
    }

    return retval;
};

export const round = num => currency({ value: num > 0 ? num : Number(num.toFixed(2)), decimal: true, convertMinus: true });

const dataRefundInvoice = (data) => {
    const {
        dataSettingRefund,
        refund = [],
        productsRefund,
        info: {
            tax_type: taxType,
            tax,
            discount_amount: discountAmount,
        },
    } = data;
    const isReturnedShippingFee = refund.some(val => val.returned_shipping_fee);
    const isPercentDisc = data.info.discount > 0;
    let subtotalWithoutDisc = 0,
        totalDiscountProduct = 0,
        totalDiscountProductRefund = 0,
        totalPriceBeforeDiscount = 0,
        totalPriceAfterDiscount = 0,
        tempTotalPriceBeforeDiscount = 0,
        tempTotalPriceAfterDiscount = 0,
        subtotal = 0,
        taxValue = 0,
        shipmentCost = fieldValidation(data.info.shipment_cost),
        totalCosts = 0,
        nominalDisc = 0,
        totalTaxPerProduct = 0,
        serviceChargeNominal,
        taxAmount = 0,
        grandTotal = 0,
        totalQuantityRefund = 0,
        administrationFee = fieldValidation(data.info.administration_fee);

    if (String(data.info.tax_base_type) === taxBaseType.TAX_PER_TRANSACTION) {
        if (Array.isArray(tax)) taxValue = tax[0].percentage;
        else taxValue = data.info.tax || 0;
    }

    const taxPercent = (parseFloat(taxValue) / 100);

    const priceIncludesTax = String(taxType) === TAX_TYPE.PRICE_INCLUDES_TAX;
    const isTaxPerProduct = String(data.info.tax_base_type) === TAX_BASE_TYPE.TAX_PER_PRODUCT;

    data.produk.forEach((product, index) => {
        totalQuantityRefund += fieldValidation(product.qty_refund);

        let productSubtotal = (parseFloat(product.price, 10) * parseFloat(product.qty, 10));
        tempTotalPriceBeforeDiscount = parseFloat(tempTotalPriceBeforeDiscount, 10) + productSubtotal;
        productSubtotal = (parseFloat(product.harga) * parseFloat(productsRefund[index].qty_refund || 0));

        totalDiscountProduct = parseFloat(totalDiscountProduct, 10) + parseFloat(product.diskonValue);
        totalPriceBeforeDiscount = parseFloat(totalPriceBeforeDiscount, 10) + productSubtotal;
        tempTotalPriceAfterDiscount = parseFloat(tempTotalPriceAfterDiscount, 10) + parseFloat(product.total, 10);

        totalQuantityRefund += parseInt(productsRefund[index].qty_refund || 0, 10);
        if (parseInt(productsRefund[index].qty_refund || 0, 10)) {
            const discountProduct = product.diskonValue ? parseFloat(product.diskonValue / product.qty) : 0;
            totalDiscountProductRefund += parseFloat(productsRefund[index].qty_refund) * discountProduct;
            totalPriceAfterDiscount += productSubtotal - (parseFloat(productsRefund[index].qty_refund) * discountProduct);
        }
    });

    if (priceIncludesTax) {
        subtotal = totalPriceBeforeDiscount;
        subtotal /= (1 + taxPercent);
    } else {
        subtotal = totalPriceAfterDiscount;
    }

    if (isReturnedShippingFee || !Number(dataSettingRefund.is_shipment_fee_back)) shipmentCost = 0;
    subtotalWithoutDisc = totalPriceBeforeDiscount;
    totalCosts = parseFloat(totalCosts, 10) + parseFloat(shipmentCost, 10);

    let tempOtherCosts = (data.info.additional_cost || []).reduce((sum, { nominal }) => sum + nominal, 0);
    if (isReturnedShippingFee || !Number(dataSettingRefund.is_shipment_fee_back)) tempOtherCosts = 0;
    totalCosts += tempOtherCosts;

    let totalPromo = data.info.promo ? data.info.promo.reduce((totalPromos, promo) => totalPromos + parseFloat(promo.amount), 0) : 0;
    if (totalPromo) totalPromo = ((parseFloat(totalPriceBeforeDiscount) / parseFloat(tempTotalPriceAfterDiscount)) * totalPromo) * (parseFloat(dataSettingRefund.refund_percentage) / 100);

    subtotal *= parseFloat(dataSettingRefund.refund_percentage) / 100;
    subtotalWithoutDisc *= parseFloat(dataSettingRefund.refund_percentage) / 100;
    totalDiscountProduct *= parseFloat(dataSettingRefund.refund_percentage) / 100;

    if (!totalQuantityRefund) nominalDisc = 0;
    else if (isPercentDisc) {
        if (priceIncludesTax) {
            nominalDisc = (parseFloat(totalPriceAfterDiscount, 10) * parseFloat(data.info.discount, 10)) / 100;
            nominalDisc *= parseFloat(dataSettingRefund.refund_percentage) / 100;
        } else {
            nominalDisc = (parseFloat(subtotal, 10) * parseFloat(data.info.discount, 10)) / 100;
        }
    } else {
        nominalDisc = ((parseFloat(totalPriceBeforeDiscount) / parseFloat(tempTotalPriceBeforeDiscount)) * parseFloat(discountAmount)) * (parseFloat(dataSettingRefund.refund_percentage) / 100);
    }

    nominalDisc += totalPromo;

    if (isTaxPerProduct) {
        totalTaxPerProduct = productsRefund.reduce((acc, product) => {
            const temp = tax.find(x => x.amount === product.product_tax_total);

            acc += calculateTaxPerProduct({
                taxType, product, nominalDisc, totalPriceBeforeDiscount, taxProductPercentage: temp ? temp.percentage : 0,
            });

            return acc;
        }, 0);
    }

    if (!isTaxPerProduct && priceIncludesTax) {
        totalDiscountProduct /= (1 + taxPercent);
        nominalDisc /= (1 + taxPercent);
        totalDiscountProductRefund /= (1 + taxPercent);
    }
    serviceChargeNominal = priceIncludesTax ? subtotal - totalDiscountProductRefund : totalPriceAfterDiscount * parseFloat(dataSettingRefund.refund_percentage) / 100;
    if (nominalDisc > 0) serviceChargeNominal -= nominalDisc;
    serviceChargeNominal *= (parseFloat(data.info.service_charge) / 100);

    const taxState = {
        totalPriceBeforeDiscount,
        totalPriceAfterDiscount,
        serviceChargeNominal,
        taxValue,
        totalDiscountProduct: totalDiscountProductRefund,
        nominalDisc,
        dataSettingRefund,
    };

    if (isTaxPerProduct) {
        taxAmount = totalTaxPerProduct;
    } else {
        taxAmount = calculateTax(taxType, data.info.service_charge_type, taxState);
    }

    const grandTotalState = {
        priceIncludesTax,
        isTaxPerProduct,
        totalDiscountProduct: totalDiscountProductRefund,
        nominalDisc,
        taxAmount,
        subtotal,
        totalCosts,
        serviceChargeNominal,
        administrationFee,
    };
    grandTotal = calculateGrandTotal(grandTotalState);

    return {
        subtotal: priceIncludesTax ? subtotal : subtotalWithoutDisc,
        subTotal: priceIncludesTax ? subtotal : subtotalWithoutDisc, // handle mapping on retina code
        diskonInfo: nominalDisc,
        finalPajak: taxAmount,
        serviceCharge: serviceChargeNominal,
        ongkir: shipmentCost,
        totalPromo,
        grandTotal,
        pembulatan: data.info.round,
        pajak: taxAmount,
        taxes: tax,
        taxPercentage: (+taxPercent * 100).toFixed(0),
    };
};

export const calculateRefundTransaction = (data) => {
    const {
        dataSettingRefund,
        refund = [],
        productsRefund,
        info: {
            tax_type: type,
            tax,
            discount_amount: discountAmount,
            round: rounding,
            subTotal: subtotalTransaction,
        },
        dataSettingStruk,
    } = data;

    const isReturnedShippingFee = refund.some(val => val.returned_shipping_fee);
    const isPercentDisc = parseFloat(data.info.discount) > 0;
    let totalDiscountProduct = 0,
        totalDiscountProductRefund = 0,
        totalPriceBeforeDiscount = 0,
        totalPriceAfterDiscount = 0,
        totalPriceBeforeDiscountRefund = 0,
        totalPriceAfterDiscountRefund = 0,
        totalQuantityRefund = 0,
        serviceChargeNominal = 0,
        shipmentCost = fieldValidation(data.info.shipment_cost),
        totalCosts = 0,
        compliment = 0,
        taxValue = 0,
        taxAmount = 0,
        subtotal  = 0,
        nominalDisc = 0,
        grandTotal = 0,
        totalTaxPerProduct = 0,
        otherCost = 0;
    const administrationFee = fieldValidation(data.info.administration_fee);
    const refundMultiplier = parseFloat(dataSettingRefund.refund_percentage) / 100;
    // pajak per Product
    const isTaxPerProduct = String(data.info.tax_base_type) === TAX_BASE_TYPE.TAX_PER_PRODUCT;
    // pajak per transaksi
    if (!isTaxPerProduct) {
        if (Array.isArray(tax)) taxValue = tax[0].percentage;
        else taxValue = data.info.tax || 0;
    }
    // dikenai pajak
    const priceIncludesTax = String(type) === TAX_TYPE.PRICE_INCLUDES_TAX;

    data.produk.filter(product => !product.bonusProduk).forEach((product, index) => {
        totalQuantityRefund += fieldValidation(productsRefund[index].qty_refund);
        const totalAddon = product.addon.reduce((acc2, addon) => {
            acc2 += fieldValidation(addon.qty) * fieldValidation(addon.price);
            return acc2;
        }, 0);

        const totalAddonRefund = product.addon.reduce((acc2, addon) => {
            const qtyAddOnPerProduct = addon.qty / product.qty;
            acc2 += qtyAddOnPerProduct * fieldValidation(productsRefund[index].qty_refund) * fieldValidation(addon.price);
            return acc2;
        }, 0);

        const productSubtotal = (parseFloat(product.price, 10) * parseFloat(product.qty, 10)) + totalAddon;
        const productSubtotalRefund = (parseFloat(product.harga) * parseFloat(productsRefund[index].qty_refund || 0)) + totalAddonRefund;
        // transaksi
        totalDiscountProduct = parseFloat(totalDiscountProduct, 10) + parseFloat(product.diskonValue);
        totalPriceBeforeDiscount = parseFloat(totalPriceBeforeDiscount, 10) + productSubtotal;
        totalPriceAfterDiscount = parseFloat(totalPriceBeforeDiscount, 10) + parseFloat(product.total, 10);
        // refund
        totalPriceBeforeDiscountRefund = parseFloat(totalPriceBeforeDiscountRefund, 10) + productSubtotalRefund;
        totalQuantityRefund += parseInt(productsRefund[index].qty_refund || 0, 10);

        if (parseInt(productsRefund[index].qty_refund || 0, 10)) {
            const discountProduct = product.diskonValue ? parseFloat(product.diskonValue / product.qty) : 0;
            totalDiscountProductRefund += parseFloat(productsRefund[index].qty_refund) * discountProduct;
            totalPriceAfterDiscountRefund += productSubtotalRefund - (parseFloat(productsRefund[index].qty_refund) * discountProduct);
        }
    });
    const subtotalRefundPerSubtotal = parseFloat(totalPriceBeforeDiscountRefund) / parseFloat(totalPriceBeforeDiscount);
    // persen pajak
    const taxPercent = (parseFloat(taxValue) / 100);

    // exclude ongkir
    if (isReturnedShippingFee || !Number(dataSettingRefund.is_shipment_fee_back)) shipmentCost = 0;
    totalCosts = parseFloat(totalCosts, 10) + parseFloat(shipmentCost, 10);
    // biaya lainnya
    const additionalCosts = (data.info.additional_cost || []).map(({ name, nominal }) => ({
        name,
        cost: (subtotalRefundPerSubtotal * parseFloat(nominal)) * refundMultiplier,
    }));
    const totalOtherCost = (data.info.additional_cost || []).reduce((sum, { nominal }) => sum + nominal, 0);
    otherCost = (subtotalRefundPerSubtotal * parseFloat(totalOtherCost)) * refundMultiplier;

    totalCosts += otherCost;

    subtotal = totalPriceAfterDiscountRefund * refundMultiplier;
    totalDiscountProduct *= refundMultiplier;
    totalDiscountProductRefund *= refundMultiplier;

    const promoExcludeCompliment = data.info.promo ? data.info.promo.filter(promo => String(promo.category) !== String(PROMO_CATEGORY.COMPLIMENT)) : [];
    const promoCompliment = data.info.promo ? data.info.promo.filter(promo => String(promo.category) === String(PROMO_CATEGORY.COMPLIMENT)) : [];

    // total non komplimen
    const totalPromo = promoExcludeCompliment.reduce((totalPromos, promo) => {
        let temp = 0;
        if (promo.promo_type === PROMO_TYPE.NOMINAL) {
            temp = (subtotalRefundPerSubtotal * promo.amount) * refundMultiplier;
        } else {
            const percentValue = promo.amount / data.info.total_product_value * 100;
            temp = (parseFloat(subtotal, 10) * parseFloat(percentValue, 10)) / 100;
        }
        return totalPromos + temp;
    }, 0);
    // total diskon kompliment
    compliment = promoCompliment.reduce((totalPromos, promo) => {
        let temp = 0;
        if (promo.promo_type === PROMO_TYPE.NOMINAL) {
            temp = (subtotalRefundPerSubtotal * promo.amount) * refundMultiplier;
        } else {
            const percentValue = promo.amount / data.info.total_product_value * 100;
            temp = (parseFloat(subtotal, 10) * parseFloat(percentValue, 10)) / 100;
        }
        return totalPromos + temp;
    }, 0);

    if (!totalQuantityRefund) nominalDisc = 0;
    else if (isPercentDisc) {
        nominalDisc = (parseFloat(subtotal, 10) * parseFloat(data.info.discount, 10)) / 100;
    } else {
        nominalDisc = (subtotalRefundPerSubtotal * parseFloat(discountAmount)) * refundMultiplier;
    }

    nominalDisc += totalPromo;

    serviceChargeNominal = subtotal;
    if (nominalDisc > 0) serviceChargeNominal -= nominalDisc;
    if (priceIncludesTax && String(data.info.service_charge_type) === SERVICE_CHARGE_TYPE.SERVICE_CHARGE_NO_TAXED) serviceChargeNominal /= (1 + taxPercent);
    serviceChargeNominal *= (parseFloat(data.info.service_charge) / 100);

    let taxState = {
        totalPriceBeforeDiscount: totalPriceBeforeDiscountRefund,
        totalPriceAfterDiscount: totalPriceAfterDiscountRefund,
        serviceChargeNominal,
        taxValue,
        totalDiscountProduct: totalDiscountProductRefund,
        nominalDisc,
        dataSettingRefund,
    };

    // tax
    if (isTaxPerProduct) {
        totalTaxPerProduct = productsRefund.reduce((acc, product) => {
            const temp = tax.find(x => x.amount === product.product_tax_total);
            acc += calculateTaxPerProduct({
                taxType, product, nominalDisc, totalPriceBeforeDiscount, taxProductPercentage: temp ? temp.percentage : 0,
            });

            return acc;
        }, 0);
        taxAmount = totalTaxPerProduct;
    } else {
        if (priceIncludesTax) {
            taxState = {
                ...taxState,
                totalDiscountProduct: totalDiscountProductRefund / (1 + taxPercent),
                serviceChargeNominal: (subtotal / (1 + taxPercent) - nominalDisc / (1 + taxPercent)) * (parseFloat(data.info.service_charge) / 100),
                nominalDisc: nominalDisc / (1 + taxPercent),
            }
        }
        taxAmount = calculateTax(type, data.info.service_charge_type, taxState);
    }

    const grandTotalState = {
        priceIncludesTax,
        isTaxPerProduct,
        totalDiscountProduct: !priceIncludesTax ? totalDiscountProduct : 0,
        nominalDisc,
        taxAmount: !priceIncludesTax ? taxAmount : 0,
        subtotal,
        totalCosts,
        serviceChargeNominal,
        administrationFee,
    };

    grandTotal = calculateGrandTotal(grandTotalState);

    let pembulatan = rounding;

    if (dataSettingStruk.is_pembulatan && rounding) {
        pembulatan = (subtotal / subtotalTransaction) * rounding * refundMultiplier;
        grandTotal += pembulatan;
    }

    if (compliment >= grandTotal) compliment = grandTotal;

    grandTotal -= compliment;

    return {
        subtotal,
        subTotal: subtotal,
        diskonInfo: nominalDisc + compliment,
        finalPajak: taxAmount,
        serviceCharge: serviceChargeNominal,
        ongkir: shipmentCost,
        totalPromo,
        grandTotal,
        pembulatan,
        pajak: taxAmount,
        taxes: tax,
        taxPercentage: (+taxPercent * 100).toFixed(0),
        compliment,
        otherCost,
        additionalCosts,
    };

    
};

export const isValidUserSN = () => [accountType.PRIME, accountType.ENTERPRISE, accountType.PRIMEPLUS].includes(getAccountType());

const checkIsMultiVariant = (id, listProduct) => {
    const filteredProduct = listProduct.filter(x => x.id_product === id);

    return filteredProduct.length > 1;
};

export const getProductTobeRefund = (products = [], refundList = []) => {
    const deepCloneProducts = JSON.parse(JSON.stringify(products));

    const temp = deepCloneProducts.map((product, index) => {
        const productRefunded = refundList.reduce((acc2, val2) => acc2 + (val2.products[index].qty || 0), 0);
        const serialNumberRefunded = refundList.reduce((acc, val) => {
            const packages = val.products[index].package_contents || [];
            const packageSn = packages.reduce((prev, current) => [...prev, ...current.serial_numbers], []);
            return [...acc, ...val.products[index].serial_numbers, ...packageSn];
        }, []);
        const isValidUser = isValidUserSN();

        return {
            ...product,
            qty: product.qty - productRefunded,
            qty_refund: product.is_product_package ? 0 : product.qty - productRefunded,
            selected_serial_numbers: isValidUser ? product.bonus_type === enumDiskon.PRODUCT ? product.serial_numbers : [] : product.serial_numbers,
            draft_serial_numbers: [],
            serial_numbers_refunded: serialNumberRefunded,
            flag_serial_numbers: 0,
            isError: false,
            package_contents: (product.package_contents || []).map(x => ({
                ...x,
                draft_serial_numbers: [],
                selected_serial_numbers: [],
                selected: false,
                ...checkIsMultiVariant(x.id_product, product.package_contents) ? {
                    qty_refund: 0,
                    isMultiVariant: true,
                } : {
                    qty_refund: x.qty * (product.qty - productRefunded) / product.qty,
                    isMultiVariant: false,
                },
            })),
        };
    });

    return temp;
};


export const getTotalPriceProduct = (data) => {
    const totalAddon = (data.item_addon_detail || []).reduce((acc, add) => {
        const quantityAddonPerProduct = add.quantity / data.quantity;

        acc += (data.qty_refund || 0) * quantityAddonPerProduct * add.price;

        return acc;
    }, 0);

    return (data.qty_refund || 0) * data.harga + totalAddon;
};

export const isDisabledEditSN = (produk, isMobile, isRefund) => {
    if (produk.package_contents && produk.package_contents.length > 0) {
        const serialNumbers = produk.package_contents.reduce((prev, current) => [...prev, ...(current.serial_numbers || [])], []);
        return serialNumbers.length === 0;
    }

    if (isMobile) return produk.serial_numbers.length === 0 || produk.jenisDiskon === enumDiskon.PRODUCT;
    return (isRefund && produk.serial_numbers.length === 0) || produk.jenisDiskon === enumDiskon.PRODUCT || produk.is_need_update_sn !== 1;
};
