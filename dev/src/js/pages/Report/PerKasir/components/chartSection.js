import React from 'react';
import PropTypes from 'prop-types';
import { Box, Flex, InputSelect } from '@majoo-ui/react';
import { LineChartv2 } from '../../../../components/chart';
import { formatCurrency } from '../../../../utils/helper';
import { filterTypeChart } from '../settings/utils';

const inputStyle = {
    '@sm': { flex: 1 },
    '@md': {
        flex: 'none',
        width: 150
    }
};

const ChartSection = ({
    period,
    data,
    legendData,
    comparatorList,
    customXAxisFormat,
    onComparatorChange,
    chartTypeLabel,
    isEmptyData,
    periodOptions,
    handleChangePeriod,
    typeChart,
    handleChangeChart,
    t,
}) => (
    <Box css={{ '@md': { padding: '0px $spacing-03' } }}>
        <LineChartv2
            data={data}
            legendData={legendData}
            chartTypeLabel={chartTypeLabel}
            period={period}
            xAxisKey="date"
            title={t('chart.title', 'Grafik per Kasir')}
            comparatorList={comparatorList}
            customXAxisFormat={customXAxisFormat}
            customYAxisFormat={val =>
                formatCurrency(val, { notation: 'compact', compactDisplay: 'short' })
                    .replace(chartTypeLabel !== 'Penjualan' || val === 0 ? 'Rp' : '', '')
                    .trim()
            }
            onComparatorChange={onComparatorChange}
            isEmptyData={isEmptyData}
            showSeparator={false}
        >
            <Flex gap={5} align="center">
                <InputSelect
                    size="sm"
                    option={periodOptions}
                    placeholder="Pilih Periode"
                    value={periodOptions.find(f => f.value === period)}
                    onChange={option => handleChangePeriod(option.value)}
                    css={inputStyle}
                />
                <InputSelect
                    size="sm"
                    option={filterTypeChart(t)}
                    defaultValue={filterTypeChart(t).find(f => f.value === filterTypeChart(t)[0].value)}
                    value={filterTypeChart(t).find(f => f.value === typeChart)}
                    onChange={option => handleChangeChart(option.value)}
                    css={inputStyle}
                />
            </Flex>
        </LineChartv2>
    </Box>
);

export default ChartSection;

ChartSection.propTypes = {
    period: PropTypes.string.isRequired,
    data: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
    legendData: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
    comparatorList: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
    customXAxisFormat: PropTypes.func.isRequired,
    onComparatorChange: PropTypes.func.isRequired,
    chartTypeLabel: PropTypes.string.isRequired,
    isEmptyData: PropTypes.bool.isRequired,
    t: PropTypes.func.isRequired,
    periodOptions: PropTypes.arrayOf(PropTypes.shape({})),
    handleChangePeriod: PropTypes.func.isRequired,
    handleChangeChart: PropTypes.func.isRequired,
    typeChart: PropTypes.string,
};

ChartSection.defaultProps = {
    periodOptions: [{}],
    typeChart: 'transaction_cnt',
};
