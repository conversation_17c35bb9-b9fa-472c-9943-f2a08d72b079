import React, { useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import {
    Paper,
    Separator,
    Table,
    AlertDialog,
    InputSearchbox,
    TableRow,
    TableCell,
    Flex,
    Paragraph,
    TableHead,
    Text,
} from '@majoo-ui/react';
import { debounce } from 'lodash';
import { useTranslation } from 'react-i18next';
import moment from 'moment';
import BlockedDownloadLaporanPopup from '../../../components/modalpopup/BlockedDownloadLaporanPopup';
import CoreHOC from '../../../core/CoreHOC';
import { DataType, useProductSalesData } from './hooks/useProductSalesData';
import { tableColumns } from './utils/table.utils';
import { TitleSection } from './components/TitleSection';
import { FilterSection } from './components/FilterSection';
import { SummarySection } from './components/SummarySection';
import { handleXax<PERSON>, LineChart } from '../../../components/chart';
import { currency, formatCurrency } from '../../../utils/helper';
import { useMediaQuery } from '../../../utils/useMediaQuery';
import FilterMobileModal from './components/FilterMobileModal';
import { BannerText } from '../../../components/retina';

function ProdukPerKasir(props) {
    const { router, addNotification } = props;
    const { t } = useTranslation(['Penjualan/Laporan/produkPerKasir', 'translation']);
    const blockedDownloadPopupRef = useRef(null);
    const {
        filter,
        setFilter,
        isLoading,
        cashierSummaries,
        salesChartData,
        handleChartComparatorChange,
        exportLaporan,
        cashierData,
        fetchCashierProductSales,
        productSalesData,
        confirmDialogState,
        lastUpdate,
    } = useProductSalesData(props, blockedDownloadPopupRef, t);
    const [openFilterModal, setOpenFilterModal] = useState(false);
    const isMobile = useMediaQuery('(max-width: 1090px)');

    const redirectSupport = () => {
        if (blockedDownloadPopupRef && blockedDownloadPopupRef.current) {
            blockedDownloadPopupRef.current.hidePopup();
        }
        router.push('/support/buy');
    };

    return (
        <React.Fragment>
            <Paper responsive css={{ padding: 0, '@lg': { padding: '$compact' } }}>
                <TitleSection
                    title={t('title', 'Laporan Kupon')}
                    startDate={filter.startDate}
                    endDate={filter.endDate}
                    downloadLaporan={exportLaporan}
                    translator={t}
                    isPrimary
                />
                {!isMobile && <Separator />}
                <BannerText />
                <FilterSection
                    filter={filter}
                    setFilter={setFilter}
                    downloadLaporan={exportLaporan}
                    onOpenFilterClick={() => setOpenFilterModal(true)}
                    isMobile={isMobile}
                    translator={t}
                    addNotification={addNotification}
                />
                <Separator css={{ my: '20px', '@lg': { mb: '20px' } }} />
                <LineChart
                    key={`${salesChartData.chartData.length}-perkasir-${filter.type}`}
                    title={t('chartTitle', 'Grafik Kupon')}
                    xAxisKey="date"
                    data={salesChartData.chartData}
                    legendData={salesChartData.legendData}
                    period={filter.group}
                    comparatorList={salesChartData.comparatorList}
                    onComparatorChange={handleChartComparatorChange}
                    customXAxisFormat={date => handleXaxis(date, filter.group)}
                    customYAxisFormat={
                        filter.type === DataType.sales
                            ? val =>
                                  formatCurrency(val, { notation: 'compact', compactDisplay: 'short' })
                                      .replace(val === 0 ? 'Rp' : '', '')
                                      .trim()
                            : null
                    }
                    isEmptyData={cashierData.length === 0}
                />
                <Separator
                    css={{
                        my: '36px',
                        '@lg': { m: '$spacing-06 0 $spacing-08' },
                    }}
                />
                <TitleSection
                    title={t('title', 'Laporan Kupon')}
                    startDate={filter.startDate}
                    endDate={filter.endDate}
                    translator={t}
                    hideTooltipGuidance
                />
                {!isMobile && <Separator />}
                <InputSearchbox
                    onChange={debounce(val => {
                        setFilter(current => ({
                            ...current,
                            searchKeyword: val,
                        }));
                    }, 700)}
                    css={{
                        my: '$spacing-05',
                        '@lg': {
                            width: '260px',
                            margin: '$spacing-06 0',
                        },
                    }}
                />
                {!isMobile && <Separator />}
                <SummarySection salesSummaries={cashierSummaries} translator={t} />
                {cashierData &&
                cashierData.length > 0 &&
                Object.keys(productSalesData).length === cashierData.length ? (
                    cashierData.map((cashier, index) => {
                        const salesData = productSalesData[cashier.id] || {
                            data: [],
                            meta: { current_page: 1 },
                            hasMoreItems: true,
                        };
                        return (
                            <Table
                                id="cashier_product_sales"
                                key={cashier.id}
                                data={salesData.data}
                                columns={tableColumns(t)}
                                totalData={salesData.data.length}
                                isLoading={isLoading}
                                pageIndex={salesData.meta.current_page}
                                isInfiniteScroll
                                virtualized={false}
                                fetchData={params => {
                                    fetchCashierProductSales(params, cashier);
                                }}
                                hasMoreItems={salesData.hasMoreItems}
                                hideHeader={index > 0}
                                additionalHeader={
                                    <TableHead
                                        css={{
                                            top: index > 0 ? -1 : 62,
                                        }}
                                    >
                                        <TableRow
                                            css={{
                                                backgroundColor: '$bgGray',
                                            }}
                                        >
                                            <TableCell
                                                colSpan={2}
                                                css={{
                                                    padding: '20px 8px',
                                                    verticalAlign: 'center',
                                                }}
                                            >
                                                <Flex align="center" gap={2}>
                                                    <Paragraph paragraph="shortContentBold">{cashier.name}</Paragraph>
                                                </Flex>
                                            </TableCell>
                                            <TableCell
                                                colSpan={2}
                                                css={{
                                                    padding: '20px 8px',
                                                    verticalAlign: 'center',
                                                }}
                                            >
                                                <Flex align="center" gap={2}>
                                                    <Paragraph paragraph="shortContentBold">
                                                        {t('headerTable.soldQtyTotal')}: {cashier.quantity}
                                                    </Paragraph>
                                                </Flex>
                                            </TableCell>
                                            <TableCell
                                                colSpan={2}
                                                css={{
                                                    padding: '20px 8px',
                                                    verticalAlign: 'center',
                                                }}
                                            >
                                                <Flex align="center" gap={2}>
                                                    <Paragraph paragraph="shortContentBold">
                                                        {t('headerTable.totalTransaction')}:{' '}
                                                        {currency({ value: cashier.sales, decimal: true })}
                                                    </Paragraph>
                                                </Flex>
                                            </TableCell>
                                        </TableRow>
                                    </TableHead>
                                }
                                css={{
                                    padding: 0,
                                    height: '240px',
                                    '@lg': {
                                        padding: '0 $spacing-05',
                                        maxHeight: '480px',
                                        height: 'unset',
                                    },
                                }}
                            />
                        );
                    })
                ) : (
                    <Table
                        id="cashier_product_sales"
                        data={[]}
                        columns={tableColumns(t)}
                        totalData={cashierData ? cashierData.length : 0}
                        hideDataInfo
                        isLoading={isLoading}
                        hidePagination
                    />
                )}

                {lastUpdate ? (
                    <Flex justify="end">
                        <Text as="i">
                            {t('label.lastUpdated', {
                                ns: 'translation',
                                date: moment(lastUpdate, 'YYYY-MM-DD HH:mm:ss').fromNow(),
                            })}
                        </Text>
                    </Flex>
                ) : null}
            </Paper>
            {openFilterModal && (
                <FilterMobileModal
                    open={openFilterModal}
                    onOpenChange={open => setOpenFilterModal(open)}
                    filter={filter}
                    setFilter={setFilter}
                    translator={t}
                    addNotification={addNotification}
                />
            )}
            <BlockedDownloadLaporanPopup ref={blockedDownloadPopupRef} confirmHandle={() => redirectSupport()} />
            {confirmDialogState.open && (
                <AlertDialog
                    onConfirm={confirmDialogState.onConfirm}
                    open={confirmDialogState.open}
                    title={confirmDialogState.title}
                    description={confirmDialogState.description}
                    singleButton={confirmDialogState.isSingleButton}
                    labelConfirm={confirmDialogState.confirmLabel}
                    isMobile={isMobile}
                    onCancel={confirmDialogState.onCancel}
                />
            )}
        </React.Fragment>
    );
}

ProdukPerKasir.propTypes = {
    router: PropTypes.shape({
        push: PropTypes.func,
    }),
    location: PropTypes.shape({
        pathname: PropTypes.string.isRequired,
    }).isRequired,
    addNotification: PropTypes.func.isRequired,
};

ProdukPerKasir.defaultProps = {
    router: {
        push: () => {},
    },
};

const mapStateToProps = state => ({
    logo: state.user.profile.user_usaha_logo_path,
    listCabang: state.branch.list,
    namaUser: state.user.profile.user_name,
    namaUsaha: state.user.profile.user_usaha_name,
    selectedCabang: state.branch.filter,
    supportNeeded: state.layout.supportNeeded,
});

export default connect(mapStateToProps)(CoreHOC(ProdukPerKasir));
