import React, { useRef, useState } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { InputSearchbox, Paper, Separator, Table, Flex, Button, Paragraph } from '@majoo-ui/react';
import { FilterOutline } from '@majoo-ui/icons';
import { debounce } from 'lodash';
import { Trans, useTranslation } from 'react-i18next';
import PopupConfirmDownload from '~/components/modalpopup/PopupConfirmDownload';
import moment from 'moment';
import CoreHOC from '../../../../core/CoreHOC';
import { formatCurrency, resetDateRangeHelper } from '../../../../utils/helper';

import { FilterSection } from './components/FilterSection';
import { FilterSectionGraphic } from './components/FilterSectionGraphic';
import { TitleSection } from './components/TitleSection';
import { handleXaxis, LineChartv2 } from '../../../../components/chart';
import { useFetchSubVarianSales } from './hooks/useSubVarianFetch';
import BlockedDownloadLaporanPopup from '../../../../components/modalpopup/BlockedDownloadLaporanPopup';
import { SummarySection } from './components/SummarySection';
import { ChartType } from './utils/enum';
import { useMediaQuery } from '../../../../utils/useMediaQuery';
import FilterMobile from './components/FilterMobile';
import { BannerText } from '../../../../components/retina';
import ExportReport, { toMoment } from '../../../../components/retina/export/ExportReport';
import { tableColumns } from './utils/config';
import ExportModal from './components/ExportModal';

function SubVarianSales(props) {
    const { router, calendar, addNotification } = props;
    const isMobile = useMediaQuery('(max-width: 798px)');
    const [modalFilter, setModalFilter] = useState(false);
    const [exportModalOpen, setExportModalOpen] = useState(false);
    const [exportConfirmModalState, setExportConfirmModalState] = useState({
        open: false,
        type: null,
        isDetail: false,
    });

    const blockedDownloadPopupRef = useRef(null);
    const {
        fetchSubVarian,
        handleChartComparatorChange,
        metaData,
        filter,
        chartFilter,
        setFilter,
        setChartFilter,
        isLoading,
        subVarianData,
        subVarianSummaries,
        exportLaporan,
        subVarianChartData,
        chartType,
        setChartType,
        pageLimit,
    } = useFetchSubVarianSales(props, blockedDownloadPopupRef);

    const redirectSupport = () => {
        if (blockedDownloadPopupRef && blockedDownloadPopupRef.current) {
            blockedDownloadPopupRef.current.hidePopup();
        }
        router.push('/support/buy');
    };

    const { t } = useTranslation(['Report/Sales/subVariant', 'translation']);

    const handleDateChange = date => {
        const { newStartDate, newEndDate, isForceReset } = resetDateRangeHelper(date[0], date[1], 366, true, 12);
        if (isForceReset) {
            addNotification({
                title: t('toast.errorMaxDate', 'Terjadi Kesalahan!', { ns: 'translation' }),
                description: t('toast.errorMaxDateRangeYear', 'Maksimum rentang waktu yang dapat dipilih: 1 tahun', {
                    ns: 'translation',
                }),
                level: 'pending',
            });
            const startDate = moment(newStartDate, 'DD/MM/YYYY').toDate();
            const endDate = moment(newEndDate, 'DD/MM/YYYY').toDate();
            setFilter(current => ({
                ...current,
                startDate,
                endDate,
            }));
        } else {
            setFilter(current => ({
                ...current,
                startDate: moment(date[0]).format('DD/MM/YYYY'),
                endDate: moment(date[1]).format('DD/MM/YYYY'),
            }));
        }
    };

    return (
        <React.Fragment>
            <Paper responsive css={{ mb: 20, padding: 'unset', '@md': { padding: 20 } }}>
                <TitleSection
                    title={t('Report/Sales/subVariant:subExtraSales', { defaultValue: 'Penjualan Sub Ekstra' })}
                    startDate={filter.startDate}
                    endDate={filter.endDate}
                    onEksporLaporanClick={() => setExportModalOpen(true)}
                    t={t}
                    isPrimary
                />
                {!isMobile ? (
                    <React.Fragment>
                        <BannerText />
                        <Separator />
                        <FilterSection
                            filter={filter}
                            chartFilter={chartFilter}
                            setFilter={setFilter}
                            setChartFilter={setChartFilter}
                            t={t}
                            onDateChange={handleDateChange}
                        />
                    </React.Fragment>
                ) : (
                    <React.Fragment>
                        <BannerText />
                        <InputSearchbox
                            placeholder={t('translation:placeholder.search', { defaultValue: 'Cari ...' })}
                            onChange={debounce(query => {
                                setFilter(current => ({
                                    ...current,
                                    search: query,
                                }));
                            }, 700)}
                            css={{
                                width: '100%',
                                mb: '$compact',
                            }}
                        />
                        <Flex gap={3}>
                            <Button
                                size="sm"
                                buttonType="secondary"
                                leftIcon={<FilterOutline color="currentColor" />}
                                css={{ flex: 1 }}
                                onClick={() => setModalFilter(true)}
                            >
                                Filter
                            </Button>
                            <ExportReport
                                onExport={(type) => exportLaporan(type, t)}
                                calendar={{ start: filter.startDate, end: filter.endDate }}
                                title={t('modal.export.reportTitle', 'Laporan Penjualan Sub Ekstra')}
                                css={{ fontWeight: 600, flex: 3 }}
                                type="multi"
                            />
                        </Flex>
                    </React.Fragment>
                )}
                <Separator css={{ m: '$compact 0' }} />
                <LineChartv2
                    title={t('Report/Sales/subVariant:subExtrasSalesChart', {
                        defaultValue: 'Grafik Penjualan Sub Ekstra',
                    })}
                    xAxisKey="date"
                    data={subVarianChartData.chartData}
                    legendData={subVarianChartData.legendData}
                    period={chartFilter.group}
                    comparatorList={subVarianChartData.comparatorList}
                    onComparatorChange={handleChartComparatorChange}
                    customXAxisFormat={date => handleXaxis(date, chartFilter.group)}
                    customYAxisFormat={val =>
                        chartType === ChartType.produk
                            ? val
                            : formatCurrency(val, { notation: 'compact', compactDisplay: 'short' })
                                  .replace(val === 0 ? 'Rp' : '', '')
                                  .trim()
                    }
                    isEmptyData={subVarianChartData.rawData.length <= 0}
                >
                    <FilterSectionGraphic
                        filter={filter}
                        chartFilter={chartFilter}
                        setChartFilter={setChartFilter}
                        chartType={chartType}
                        setChartType={setChartType}
                        t={t}
                    />
                </LineChartv2>
                <SummarySection subVarianSummaries={subVarianSummaries} t={t} />
                <Separator />
                <Table
                    id="report_product_subextrasales_dtm"
                    columns={tableColumns(t, true)}
                    data={subVarianData}
                    pageIndex={metaData.pageIndex}
                    totalData={metaData.totalData}
                    fetchData={fetchSubVarian}
                    isLoading={isLoading}
                    searchQuery={filter.search}
                    hiddenColumns={['ingredients_quantity']}
                    css={{
                        padding: 0,
                    }}
                />
            </Paper>
            {modalFilter && (
                <FilterMobile
                    open={modalFilter}
                    onClose={() => setModalFilter(false)}
                    filter={filter}
                    setFilter={setFilter}
                    chartType={chartType}
                    setChartType={setChartType}
                    chartFilter={chartFilter}
                    setChartFilter={setChartFilter}
                />
            )}
            {exportModalOpen && (
                <ExportModal
                    isOpen={exportModalOpen}
                    onOpenChange={setExportModalOpen}
                    onConfirm={(isDetail, type) => setExportConfirmModalState({ open: true, type, isDetail })}
                    t={t}
                />
            )}
            {exportConfirmModalState.open && (
                <PopupConfirmDownload
                    title={t('modal.export.reportTitle', 'Laporan Penjualan Sub Ekstra')}
                    isOpen={exportConfirmModalState.open}
                    actionDownloadReport={() =>
                        exportLaporan(exportConfirmModalState.type, t, exportConfirmModalState.isDetail)
                    }
                    isMobile={isMobile}
                    onOpen={() => setExportConfirmModalState({ open: false, type: null, isDetail: false })}
                >
                    <Paragraph paragraph="longContentRegular" color="primary">
                        <Trans i18nKey="export.description">
                            {{
                                reportName: t('modal.export.reportTitle', 'Laporan Penjualan Sub Ekstra'),
                                startDate: `${moment(toMoment(calendar.start).toDate()).format('DD MMMM YYYY')}`,
                                endDate: `${moment(toMoment(calendar.end).toDate()).format('DD MMMM YYYY')}`,
                                type: exportConfirmModalState.type === 'pdf' ? 'PDF' : 'Excel',
                            }}
                        </Trans>
                    </Paragraph>
                </PopupConfirmDownload>
            )}
            <BlockedDownloadLaporanPopup ref={blockedDownloadPopupRef} confirmHandle={() => redirectSupport()} />
        </React.Fragment>
    );
}

SubVarianSales.propTypes = {
    router: PropTypes.shape({
        push: PropTypes.func,
    }),
    location: PropTypes.shape({
        pathname: PropTypes.string.isRequired,
    }).isRequired,
    calendar: PropTypes.shape({
        start: PropTypes.string,
        end: PropTypes.string,
    }).isRequired,
};

SubVarianSales.defaultProps = {
    router: {
        push: () => {},
    },
};

const mapStateToProps = state => ({
    logo: state.user.profile.user_usaha_logo_path,
    listCabang: state.branch.list,
    namaUser: state.user.profile.user_name,
    namaUsaha: state.user.profile.user_usaha_name,
    supportNeeded: state.layout.supportNeeded,
});

export default connect(mapStateToProps)(CoreHOC(SubVarianSales));
