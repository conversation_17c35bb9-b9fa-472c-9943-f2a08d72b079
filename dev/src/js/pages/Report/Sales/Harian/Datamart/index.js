import {
  Box,
  Flex,
  Paper,
  Separator,
  Table,
} from '@majoo-ui/react';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, {
 useCallback, useEffect, useState, useMemo,
} from 'react';
import { connect } from 'react-redux';
import { debounce } from 'lodash';
import { Trans, useTranslation } from 'react-i18next';
import {
  fillDataDaily,
  fillDataHourly,
  fillDataMonthly,
  fillDataWeekly,
  LineChartv2,
} from '../../../../../components/chart';
import {
 periodList, chartLegendData, tableColumns, defaultNameChartLegend, reformatTableData,
 handleCustomXaxis,
} from './utils';
import CoreHOC from '../../../../../core/CoreHOC';
import {
  getDailySalesReportDatamart,
  getDailySalesSummaryDatamart,
  getDailySalesChartDatamart,
  getDailySalesExportDatamart,
  getDailySalesExportDatamartV2,
} from '../../../../../data/reports';
import { getTransactionType } from '../../../../../data/transactionType';
// import { useMediaQuery } from '../../../../../utils/useMediaQuery';
import { catchError, checkisValueExistOrEmpty, resetDateRangeHelper } from '../../../../../utils/helper';
import { FilterSection, TableSummary, TitleSection } from '../components';
// import { getNameOrderTypeLegacy, orderTypeOption, orderTypeOptionTranslate } from '../../config/references';
import { BannerText } from '../../../../../components/retina';
import { downloadTypePdf } from '../../../../../components/retina/export/utils';

const HarianDatamart = (props) => {
  const { t, i18n, ready } = useTranslation([
    'Penjualan/harian',
    'translation',
    'Penjualan/orderType',
  ]);
  const {
    calendar,
    assignCalendar,
    filterBranch,
    showProgress,
    hideProgress,
    addNotification,
    getContentTranslation,
    privilege,
  } = props;
  const currentLang = i18n.language;
  const [isTableLoading, setIsTableLoading] = useState(false);
  const [calendarDate, setCalendarDate] = useState([
    moment(calendar.start, 'DD-MM-YYYY').toDate(),
    moment(calendar.end, 'DD-MM-YYYY').toDate(),
  ]);
  const [options, setOptions] = useState({
    periodOptions: periodList(t),
    orderType: [],
  });
  const [filter, setFilter] = useState({
    period: 'day',
    orderType: '',
  });
  const [chart, setChart] = useState({
    initChart: [],
    chartData: [],
    chartLegend: chartLegendData,
  });
  const [table, setTable] = useState({
    tableSummary: {},
    tableData: [],
    totalData: 0,
    page: 0,
    search: '',
    sort: '',
    order: '',
  });
  const [pageLimit, setPageLimit] = useState(10);

  const fetchDataChart = useCallback(async () => {
    if (!ready) return;
    const payload = {
      start_date: moment(calendarDate[0]).format('YYYY-MM-DD'),
      end_date: moment(calendarDate[1]).format('YYYY-MM-DD'),
      group: filter.period,
      ...(filterBranch && {
        outlet_id: filterBranch,
      }),
      ...(table.search && {
        search: table.search,
      }),
      ...(filter.orderType && {
        order_type: filter.orderType,
      }),
    };

    showProgress();
    try {
      const response = await getDailySalesChartDatamart(payload);
      setChart(current => ({ ...current, initChart: response.data || [], chartData: [] }));

      const startDate = calendarDate[0];
      const endDate = calendarDate[1];

      if (response.data) {
        if (filter.period === 'hour') {
          setChart(current => ({
            ...current,
            chartData: fillDataHourly(startDate, endDate, 'date', response.data, chart.chartLegend),
          }));
        } else if (filter.period === 'day') {
          setChart(current => ({
            ...current,
            chartData: fillDataDaily(startDate, endDate, 'date', response.data, chart.chartLegend),
          }));
        } else if (filter.period === 'week') {
          setChart(current => ({
            ...current,
            chartData: fillDataWeekly(
              startDate,
              endDate,
              'date',
              response.data,
              chart.chartLegend,
            ),
          }));
        } else if (filter.period === 'month') {
          setChart(current => ({
            ...current,
            chartData: fillDataMonthly(
              startDate,
              endDate,
              'date',
              response.data,
              chart.chartLegend,
            ),
          }));
        } else if (response.data.length > 0) {
          setChart(current => ({
            ...current,
            chartData: response.data,
          }));
        } else {
          setChart(current => ({
            ...current,
            chartData: [
              chart.chartLegend.reduce(
                (prev, cur) => ({
                  ...prev,
                  date: moment(startDate).format('YYYY'),
                  [cur.dataKey]: 0,
                }),
                {},
              ),
            ],
          }));
        }
      }
    } catch (error) {
      addNotification({
        title: t('toast.error', { ns: 'translation' }),
        message: catchError(error),
        level: 'error',
      });
    } finally {
      hideProgress();
    }
  }, [
    calendarDate,
    filterBranch,
    filter.period,
    filter.orderType,
    table.search,
    currentLang,
    ready,
  ]);

  const fetchDataSummary = useCallback(async () => {
    const payload = {
      start_date: moment(calendarDate[0]).format('YYYY-MM-DD'),
      end_date: moment(calendarDate[1]).format('YYYY-MM-DD'),
      group: filter.period,
      ...(table.search && { search: table.search }),
      ...(filter.orderType && { order_type: filter.orderType }),
      ...(!!filterBranch && !Number.isNaN(filterBranch) && { outlet_id: filterBranch }),
    };

    showProgress();
    try {
      const response = await getDailySalesSummaryDatamart(payload);
      setTable(current => ({
        ...current,
        tableSummary: response.data,
      }));
    } catch (error) {
      addNotification({
        title: t('toast.error', { ns: 'translation' }),
        message: catchError(error),
        level: 'error',
      });
    } finally {
      hideProgress();
    }
  }, [
    calendarDate,
    filterBranch,
    filter.period,
    filter.orderType,
    table.search,
    currentLang,
    ready,
  ]);

  const fetchData = useCallback(
    async (state) => {
      const {
        pageSize, pageIndex = 0, keyword, sortAccessor, sortDirection,
      } = state;

      const payload = {
        start_date: moment(calendarDate[0]).format('YYYY-MM-DD'),
        end_date: moment(calendarDate[1]).format('YYYY-MM-DD'),
        group: filter.period,
        page: pageIndex + 1 || 1,
        limit: state.pageLimit || pageSize || 10,
        ...(sortAccessor
          && sortDirection && {
            order: sortDirection.toLowerCase() === 'asc' ? sortAccessor : `-${sortAccessor}`,
          }),
        ...(keyword && { search: keyword }),
        ...(filter.orderType && { order_type: filter.orderType }),
        ...(!!filterBranch && !Number.isNaN(filterBranch) && { outlet_id: filterBranch }),
      };

      showProgress();
      setIsTableLoading(true);
      try {
        const response = await getDailySalesReportDatamart(payload);

        if (response.data) {
          setTable(current => ({
            ...current,
            tableData: reformatTableData((response.data || []), filter.period),
            totalData: response.meta.total || 0,
            sort: sortDirection,
            order: sortAccessor,
          }));
        } else {
          setTable(current => ({
            ...current,
            tableData: [],
            totalData: 0,
            sort: sortDirection,
            order: sortAccessor,
          }));
        }
      } catch (error) {
        addNotification({
          title: t('toast.error', { ns: 'translation' }),
          message: catchError(error),
          level: 'error',
        });
      } finally {
        hideProgress();
        setIsTableLoading(false);
        if (pageSize) setPageLimit(pageSize);
      }
    },
    [calendarDate, filterBranch, filter.period, filter.orderType, currentLang, ready],
  );

  const fetchDownloadReport = async (type = 'xlsx') => {
    const payload = {
      report_type: type,
      start_date: moment(calendarDate[0]).format('YYYY-MM-DD'),
      end_date: moment(calendarDate[1]).format('YYYY-MM-DD'),
      group: filter.period,
      ...(table.search && { search: table.search }),
      ...(filter.orderType && { order_type: filter.orderType }),
      ...(!!filterBranch && !Number.isNaN(filterBranch) && { outlet_id: filterBranch }),
      ...(table.order && table.sort && {
        sort: table.sort.toLowerCase() === 'asc' ? table.order : `-${table.order}`,
      }),
    };

    showProgress();
    try {
      const response = await getDailySalesExportDatamartV2(payload);

      if (type === 'xlsx') window.location = response.data;
      else downloadTypePdf(response.data);

      if (response.data) {
        addNotification({
          title: t('toast.success', { ns: 'translation' }),
          message: (
            <Box css={{ width: '280px' }}>
              <Trans t={t} i18nKey="toast.exportSuccess">
                Laporan
                {' '}
                <b>Penjualan per Periode</b>
                {' '}
                berhasil diekspor
              </Trans>
            </Box>
          ),
          level: 'success',
        });
      }
    } catch (error) {
      addNotification({
        title: t('toast.error', { ns: 'translation' }),
        message: (
          <Box css={{ width: '280px' }}>
            <Trans t={t} i18nKey="toast.exportFailed">
              Laporan
              {' '}
              <b>Penjualan per Periode</b>
              {' '}
              gagal diekspor
            </Trans>
          </Box>
        ),
        level: 'error',
      });
    } finally {
      hideProgress();
    }
  };

  const handleTableSearch = debounce(async (keyword) => {
    setTable(current => ({ ...current, search: keyword }));
    await fetchData({ keyword, pageLimit });
  }, 500);

  const getDefaultName = name => defaultNameChartLegend[name] || name;

  const chartLegend = useMemo(
    () => chart.chartLegend.filter(item => checkisValueExistOrEmpty(privilege.components, item.id)).map(item => ({
        ...item,
        name: t(`legend.${item.name}`, { ns: 'translation', defaultValue: getDefaultName(item.name) }),
      })),
    [currentLang, privilege],
  );

  useEffect(() => {
    if (ready) fetchData({ pageLimit });
  }, [calendarDate, filterBranch, filter.period, filter.orderType, currentLang, ready]);

  useEffect(() => {
    if (ready) fetchDataSummary();
    fetchDataChart();
  }, [
    calendarDate,
    filterBranch,
    filter.period,
    filter.orderType,
    table.search,
    currentLang,
    ready,
  ]);

  useEffect(() => {
    if (ready) {
      if (moment(calendarDate[0]).diff(calendarDate[1], 'days') === 0) {
        setOptions(current => ({
          ...current,
          periodOptions: periodList(t).map(item => (item.value === 'hour' ? { ...item, isDisabled: false } : item)),
        }));
      } else {
        setOptions(current => ({
          ...current,
          periodOptions: periodList(t).map(item => (item.value === 'hour' ? { ...item, isDisabled: true } : item)),
        }));
        if (filter.period === 'hour') {
          setFilter(current => ({ ...current, period: 'day' }));
        }
      }
    }
  }, [calendarDate, filter.period, currentLang, ready]);

  const filteredColumns = useMemo(() => {
    if (ready) return tableColumns(filter.period, t).filter(item => checkisValueExistOrEmpty(privilege.components, item.accessor));
    return [];
  }, [filter.period, t, ready, privilege]);

  const fetchOrderTypeOption = async () => {
    showProgress();
    try {
      const res = await getTransactionType();
      const { data = [] } = res;
      setOptions(current => ({
        ...current,
        orderType: [
          ...[{ value: '', name: t('translation:select.allOrderType', 'Semua Jenis Order') }],
          ...data.map(x => ({ value: String(x.id), name: getContentTranslation(x.key) })),
        ],
      }));
    } catch (error) {
      addNotification({
        title: t('toast.error', { ns: 'translation' }),
        message: catchError(error),
        level: 'error',
      });
    } finally {
      hideProgress();
    }
  }

  useEffect(() => {
    fetchOrderTypeOption();
  }, []);

  const handleChangeDateRange = (value) => {
    const { newStartDate, newEndDate, isForceReset } = resetDateRangeHelper(value[0], value[1], 366, true, 12);
    if (isForceReset) {
        addNotification({
            title: t('toast.errorMaxDate', 'Terjadi Kesalahan!', { ns: 'translation' }),
            description: t('toast.errorMaxDateRangeYear', 'Maksimum rentang waktu yang dapat dipilih: 1 tahun', { ns: 'translation' }),
            level: 'pending',
        });
        setCalendarDate([moment(newStartDate, 'DD-MM-YYYY').toDate(), moment(newEndDate, 'DD-MM-YYYY').toDate()]);
        assignCalendar(newStartDate, newEndDate, null, null);
    } else {
      setCalendarDate(value);
      assignCalendar(moment(value[0]).format('DD-MM-YYYY'), moment(value[1]).format('DD-MM-YYYY'), null, null);
    }
  };

  return (
    <Flex
      css={{
        flexDirection: 'column',
        gap: '$comfortable',
        '@md': { marginBottom: '28px', paddingTop: 0 },
      }}
    >
      <Paper responsive css={{ padding: 0, '@md': { padding: '$compact' } }}>
        <Flex css={{ flexDirection: 'column', rowGap: '$compact' }}>
          <TitleSection
            title={t('titleSection.title', 'Penjualan per Periode')}
            date={calendarDate}
            onDownloadLaporan={fetchDownloadReport}
            hideComparison
            isPrimary
          />
          <Separator responsive />
          <BannerText css={{ my: 0 }} />
          <FilterSection
            setPeriod={val => setFilter(current => ({ ...current, period: val.value }))}
            setCalendarDate={handleChangeDateRange}
            date={calendarDate}
            period={filter.period}
            periodOptions={options.periodOptions}
            onDownloadLaporan={fetchDownloadReport}
            filterOrderType={filter.orderType}
            setFilterOrderType={val => setFilter(current => ({ ...current, orderType: val.value }))}
            t={t}
            lang={currentLang}
            handleTableSearch={handleTableSearch}
            orderTypeOptions={options.orderType}
          />
          <Separator css={{ my: 4, '@md': { m: 0 } }} />
          <Box css={{ '@md': { padding: '0px $spacing-03' } }}>
            <LineChartv2
              key={`${chart.chartData.length}-harian-${filter.period}-${currentLang}`}
              data={chart.chartData}
              legendData={chartLegend}
              xAxisKey="date"
              title={t('chartSection.title', 'Grafik Penjualan per Periode')}
              customXAxisFormat={d => handleCustomXaxis({ date: d, periodType: filter.period })}
              period={filter.period}
              isEmptyData={chart.initChart.length <= 0}
            />
          </Box>
        </Flex>
        <Flex css={{ flexDirection: 'column', rowGap: '$compact' }}>
          <TableSummary
            key={`summary-${ready}-${currentLang}`}
            t={t}
            data={table.tableSummary}
            privilege={privilege}
            isDatamart
          />
          <Table
            data={table.tableData}
            columns={filteredColumns}
            totalData={table.totalData}
            isLoading={isTableLoading}
            searchQuery={table.search}
            fetchData={fetchData}
            pageIndex={table.page}
            css={{ padding: 0 }}
            isMobileLayout={false}
          />
        </Flex>
      </Paper>
    </Flex>
  );
};

HarianDatamart.propTypes = {
  filterBranch: PropTypes.string,
  calendar: PropTypes.shape({
    start: PropTypes.string,
    end: PropTypes.string,
    rangeLimit: PropTypes.number,
    onchange: PropTypes.func,
  }).isRequired,
  showProgress: PropTypes.func.isRequired,
  hideProgress: PropTypes.func.isRequired,
  addNotification: PropTypes.func.isRequired,
  assignCalendar: PropTypes.func,
  getContentTranslation: PropTypes.func.isRequired,
  privilege: PropTypes.shape({ 
    components: PropTypes.shape({})
  }).isRequired,
};

HarianDatamart.defaultProps = {
  filterBranch: '',
  assignCalendar: () => { },
};

const mapStateToProps = state => ({
  supportNeeded: state.layout.supportNeeded,
  privilege: state.layouts.detailPrivilege,
});

export default CoreHOC(connect(mapStateToProps)(HarianDatamart));
