import {
    Box,
    DecimalColumn,
    Flex,
    Paper,
    Separator,
    Table,
} from '@majoo-ui/react';
import { debounce } from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, {
    useCallback, useEffect, useReducer, useState,
} from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import {
    fillDataDaily,
    fillDataHourly,
    fillDataMonthly,
    fillDataWeekly,
    handleXaxis,
    LineChartv2,
} from '../../../../components/chart';
import { PriceColumn } from '../../../../components/retina/table';
import CoreHOC from '../../../../core/CoreHOC';
import { getOutlet } from '../../../../data/outlets';
import { getTransactionType } from '../../../../data/transactionType';
import {
    getOutletSalesChartReportDatamart,
    getOutletSalesReportDatamart,
    downloadOutletSalesReportDatamartV2,
} from '../../../../data/reports';
import {
    FilterSection,
    FilterSectionGraphic,
    SummarySection,
    TitleSection,
} from './components';
import { mapDispatch, reducer } from './settings/reducer';
import { BannerText, PercentageColumn } from '../../../../components/retina';
import { formatCurrency, toTitleCase, resetDateRangeHelper, catchError, checkisValueExistOrEmpty } from '../../../../utils/helper';
import { colors } from '../../../../stitches.config';
import { downloadTypePdf } from '../../../../components/retina/export/utils';
import { useMediaQuery } from '../../../../utils/useMediaQuery';

const chartColor = Object.keys(colors)
    .filter(key => key.includes('chart'))
    .reduce((prev, current) => [...prev, colors[current]], []);

const initialState = {
    isLoading: true,
    isError: false,
    errorMessage: undefined,
    rawChartData: [],
    lineChartData: [],
    lineChartColors: chartColor,
    lineChartLegend: [],
    tableData: [],
    // TODO: Kurang kolom tipe persen.
    headerData: (t, _isMobile) => [
        // { ...RowExpanderColumn, sticky: 'left', stickyPosition: 0 },
        {
            id: 'outlet_name',
            Header: 'Outlet',
            accessor: 'outlet.name',
            isMobileHeader: true,
            sticky: 'left',
            // stickyPosition: 30,
            shadow: true,
            colMinWidth: _isMobile ? 100 : 200,
        },
        {
            Header: t('header.sales', 'Penjualan (RP)'),
            accessor: 'sales',
            Cell: instance => PriceColumn({ ...instance, decimal: true, convertMinus: true }),
            colMinWidth: 150,
        },
        {
            Header: t('header.grossProfit', 'Laba Kotor (RP)'),
            accessor: 'gross_profit',
            Cell: instance => PriceColumn({ ...instance, decimal: true, convertMinus: true }),
            colMinWidth: 150,
        },
        {
            Header: t('header.productCount', 'Jumlah Produk'),
            accessor: 'product_quantity',
            Cell: DecimalColumn,
            colMinWidth: 150,
        },
        {
            Header: t('header.transactionCount', 'Jumlah Transaksi'),
            accessor: 'transaction_quantity',
            Cell: DecimalColumn,
            colMinWidth: 150,
        },
        {
            Header: t('header.salesPercentage', 'Penjualan %'),
            accessor: 'sales_percentage',
            Cell: PercentageColumn,
            // isSubRow: true,
            colMinWidth: 150,
            unsortable: true,
        },
        {
            Header: t('header.grossProfitPercentage', 'Laba Kotor %'),
            accessor: 'gross_profit_percentage',
            Cell: PercentageColumn,
            // isSubRow: true,
            colMinWidth: 150,
            unsortable: true,
        },
        {
            Header: t('header.productPercentage', 'Produk %'),
            accessor: 'product_quantity_percentage',
            Cell: PercentageColumn,
            // isSubRow: true,
            colMinWidth: 150,
            unsortable: true,
        },
        {
            Header: t('header.transactionPercentage', 'Transaksi (%)'),
            accessor: 'transaction_quantity_percentage',
            Cell: PercentageColumn,
            // isSubRow: true,
            colMinWidth: 150,
            unsortable: true,
        },
        {
            Header: t('header.orderTransaction', 'Penjualan/Transaksi (Rp)'),
            accessor: 'sales_per_transaction',
            Cell: PriceColumn,
            // isSubRow: true,
            colMinWidth: 150,
            unsortable: true,
        },
        {
            Header: t('header.productTransaction', 'Produk/Transaksi'),
            accessor: 'product_per_transaction',
            Cell: DecimalColumn,
            // isSubRow: true,
            colMinWidth: 150,
            unsortable: true,
        },
    ],
    tableSummary: {
        data: 0,
        gross_profit: 0,
        product_quantity: 0,
        sales: 0,
        transaction_quantity: 0,
    },
    calendarDate: [],
    period: 'day',
    type: 'omzet',
    typeOptions: t => [
        { name: t('legend.sales', { ns: 'translation', defaultValue: 'Penjualan' }), value: 'sales', typeParams: 'omzet' },
        { name: t('legend.profit', { ns: 'translation', defaultValue: 'Laba' }), value: 'gross_profit', typeParams: 'laba' },
        { name: t('legend.transaction', { ns: 'translation', defaultValue: 'Transaksi' }), value: 'transaction_quantity', typeParams: 'transaksi' },
        { name: t('legend.product', { ns: 'translation', defaultValue: 'Produk' }), value: 'product_quantity', typeParams: 'produk' },
    ],
    outletList: [],
    outletIds: [],
    filteredOutlets: [],
    order: '',
    sort: '',
    filterKeyword: '',
    page: 1,
    limit: 10,
    totalData: undefined,
    comparatorList: [],
    filterOrderType: {
        isDisabled: undefined,
        name: 'Semua Jenis Order',
        render: undefined,
        value: '',
    },
};

const PenjualanOutlet = (props) => {
    const {
        calendar,
        filterBranch,
        assignCalendar,
        showProgress,
        hideProgress,
        addNotification,
        getContentTranslation,
        privilege,
    } = props;
    const isMobile = useMediaQuery('(max-width: 767px)');
    const { t } = useTranslation(['Penjualan/outlet', 'translation', 'Penjualan/orderType']);

    const [state, dispatch] = useReducer(reducer, initialState);
    const [periodOptions, setPeriodOptions] = useState([
        { name: t('period.hour', { ns: 'translation', defaultValue: 'Jam' }), value: 'hour', isDisabled: true },
        { name: t('period.day', { ns: 'translation', defaultValue: 'Hari' }), value: 'day' },
        { name: t('period.week', { ns: 'translation', defaultValue: 'Minggu' }), value: 'week' },
        { name: t('period.month', { ns: 'translation', defaultValue: 'Bulan' }), value: 'month' },
        { name: t('period.year', { ns: 'translation', defaultValue: 'Tahun' }), value: 'year' },
    ]);
    const [orderTypeOption, setOrderTypeOption] = useState([{ value: '', name: 'Semua Jenis Order' }]);

    const {
        limit,
        page,
        calendarDate,
        isLoading,
        tableData,
        rawChartData,
        lineChartData,
        lineChartLegend,
        lineChartColors,
        tableSummary,
        period,
        isError,
        errorMessage,
        order,
        sort,
        filterKeyword,
        totalData,
        headerData,
        type,
        typeOptions,
        outletList,
        outletIds,
        comparatorList,
        filterOrderType,
    } = state;

    const {
        setLimit,
        setPage,
        fetchInit,
        fetchSuccess,
        fetchFailure,
        setTableData,
        setRawChartData,
        setLineChartData,
        setTotalData,
        setSort,
        setOrder,
        setPeriod,
        setCalendarDate,
        setTableSummary,
        setFilterKeyword,
        setType,
        setOutletList,
        setOutletIds,
        setLineChartLegend,
        setComparatorList,
        setFilterOrderType,
    } = mapDispatch(dispatch);

    const [tableLoading, setTableLoading] = useState(false);

    const fetchOutlet = useCallback(async () => {
        const payloadOutlet = {
            is_cms: 1,
            is_active: 1,
        };
        try {
            const outletResult = await getOutlet(payloadOutlet);
            setOutletList(outletResult.data);
            // const comparators = outletResult.data.map(obj => ({ name: obj.cabang_name, value: obj.id_cabang }));
            // setComparatorList(comparators);
        } catch (e) {
            fetchFailure(e);
        }
    }, []);

    const fetchLineChart = async () => {
        const payloadLineChart = {
            start_date: moment(calendarDate[0]).format('YYYY-MM-DD'),
            end_date: moment(calendarDate[1]).format('YYYY-MM-DD'),
            group: period,
            type,
            outlets: outletIds.join(','),
            ...filterOrderType.value && {
                order_type_id: parseInt(filterOrderType.value, 10),
            },
        };

        const lineLegend = outletList
            .filter(outlet => (filterBranch ? outlet.id_cabang === String(filterBranch) : outletIds.includes(Number(outlet.id_cabang))))
            .map((outlet, index) => ({
                dataKey: outlet.cabang_name,
                name: outlet.cabang_name,
                color: lineChartColors[index % lineChartColors.length],
                isComparator: true,
                value: Number(outlet.id_cabang),
                isCurrency: type === typeOptions(t)[0].typeParams || type === typeOptions(t)[1].typeParams,
            }));
        
        showProgress();
        try {
            const dailyReports = await getOutletSalesChartReportDatamart(payloadLineChart);
            await setRawChartData(dailyReports.data.outlet_sales);
            const getDataPerOutlet = await dailyReports.data.outlet_sales.reduce((prev, current) => {
                const match = prev.find(p => p.date === current.date);
                if (match) {
                    Object.assign(match, {
                        ...{
                            [current.outlet.name]:
                                current[typeOptions(t).find(x => x.typeParams === type).value],
                        },
                    });
                } else {
                    prev.push({
                        date: current.date,
                        monday_date: current.date,
                        [current.outlet.name]:
                            current[typeOptions(t).find(x => x.typeParams === type).value],
                    });
                }
                return prev;
            }, []);
            const startDate = calendarDate[0].setHours(0, 0, 0, 0);
            const endDate = calendarDate[1].setHours(0, 0, 0, 0);
            if (period === 'hour') {
                await setLineChartData(
                    fillDataHourly(
                        startDate,
                        endDate,
                        'date',
                        getDataPerOutlet,
                        lineLegend,
                    ),
                );
            } else if (period === 'day') {
                await setLineChartData(
                    fillDataDaily(
                        startDate,
                        endDate,
                        'date',
                        getDataPerOutlet,
                        lineLegend,
                    ),
                );
            } else if (period === 'week') {
                await setLineChartData(
                    fillDataWeekly(
                        startDate,
                        endDate,
                        'monday_date',
                        getDataPerOutlet,
                        lineLegend,
                    ),
                );
            } else if (period === 'month') {
                await setLineChartData(
                    fillDataMonthly(
                        startDate,
                        endDate,
                        'date',
                        getDataPerOutlet,
                        lineLegend,
                    ),
                );
            } else if (getDataPerOutlet.length > 0) {
                await setLineChartData(getDataPerOutlet);
            } else {
                await setLineChartData([lineLegend.reduce((prev, cur) => ({
                    ...prev,
                    date: moment(startDate).format('YYYY'),
                    [cur.dataKey]: 0,
                }), {})]);
            }
            await setLineChartLegend(lineLegend);
            fetchSuccess();
        } catch (e) {
            fetchFailure(e);
        } finally {
            hideProgress();
        }
    };

    const fetchTable = async () => {
        setTableLoading(true);
        const payloadTableSummary = {
            start_date: moment(calendarDate[0]).format('YYYY-MM-DD'),
            end_date: moment(calendarDate[1]).format('YYYY-MM-DD'),
            limit,
            page,
            group: period,
            ...(order && {
                order,
            }),
            ...(sort && {
                sort,
            }),
            ...(filterBranch && {
                outlet_id: filterBranch,
            }),
            ...(filterKeyword && {
                search: filterKeyword,
            }),
            ...filterOrderType.value && {
                order_type_id: parseInt(filterOrderType.value, 10),
            },
        };
        // setTableData([]);
        try {
            const { data: { summary, outlets }, meta } = await getOutletSalesReportDatamart(payloadTableSummary);
            setTableSummary(summary);
            setTableData(outlets);
            setTotalData(meta.total);

            const removeDuplicateOutlet = outlets.reduce((prev, current) => {
                prev.push(current.outlet.id);
                return prev;
            }, []);
            setOutletIds(
                removeDuplicateOutlet.slice(0, 5),
            );
            setComparatorList(
                outletList.filter(outlet => removeDuplicateOutlet.includes(+outlet.id_cabang)).map(d => ({ name: d.cabang_name, value: d.id_cabang })),
            );
            fetchSuccess();
        } catch (e) {
            fetchFailure(e);
        } finally {
            setTableLoading(false);
        }
    };

    const fetchTableFilter = useCallback(
        (val) => {
            if (!val) {
                fetchTable();
            } else {
                setLimit(val.pageSize);
                // + 1 karena params page dimulai dari 1 sedangkan pageIndex mulai dari 0
                setPage(val.pageIndex + 1);
                setSort(val.sortDirection);
                setOrder(val.sortAccessor);
            }
        },
        [JSON.stringify(calendarDate), filterBranch, period],
    );

    const handleComparator = useCallback(
        (value) => {
            switch (value.type) {
                case 'ADD':
                    return setOutletIds([...outletIds, Number(value.payload.value)]);
                case 'DELETE':
                    return setOutletIds(outletIds.filter(o => o !== value.payload.value));
                default:
                    return value;
            }
        },
        [outletIds],
    );

    const fetchOrderTypeOption = async () => {
        try {
            const res = await getTransactionType();
            const { data = [] } = res;
            setOrderTypeOption(
                [
                    ...[{ value: '', name: t('translation:select.allOrderType', 'Semua Jenis Order') }],
                    ...data.map(x => ({ ...x, value: String(x.id), name: getContentTranslation(x.key) })),
                ]
            );
        } catch (error) {
            addNotification({
                    title: t('toast.error', { ns: 'translation' }),
                    message: catchError(error),
                    level: 'error',
            });
        }
    }

    useEffect(() => {
        if (orderTypeOption.length > 0) {
            setOrderTypeOption(orderTypeOption.map(x => ({ ...x, name: x.key ? getContentTranslation(x.key) : x.name })));
        }
    }, [JSON.stringify(getContentTranslation)]);

    // Init
    useEffect(() => {
        fetchOrderTypeOption();
        fetchInit();
        setCalendarDate([
            moment(calendar.start, 'DD/MM/YYYY').toDate(),
            moment(calendar.end, 'DD/MM/YYYY').toDate(),
        ]);
        fetchOutlet();
    }, []);

    // Calendar handler
    useEffect(() => {
        if (calendarDate.length > 0) {
            fetchInit();
            assignCalendar(
                moment(calendarDate[0]).format('DD/MM/YYYY'),
                moment(calendarDate[1]).format('DD/MM/YYYY'),
            );
            fetchTableFilter();
            if (moment(calendarDate[0]).diff(calendarDate[1], 'days') === 0) {
                setPeriodOptions(
                    periodOptions.map(p => (p.value === 'hour' ? { ...p, isDisabled: false } : p)),
                );
            } else {
                setPeriodOptions(
                    periodOptions.map(p => (p.value === 'hour' ? { ...p, isDisabled: true } : p)),
                );
            }
        }
    }, [JSON.stringify(calendarDate)]);

    // LineChart Handler
    useEffect(() => {
        if (outletIds.length > 0 && outletList.length > 0) {
            fetchLineChart();
        } else {
            setLineChartData([]);
            setLineChartLegend([]);
            setComparatorList([]);
        }
    }, [JSON.stringify(outletIds), JSON.stringify(outletList), period, type]);

    // Table handler
    useEffect(() => {
        if (calendarDate.length > 0) {
            fetchTable();
        }
    }, [limit, page, sort, order, period, filterKeyword, filterBranch, filterOrderType]);

    // Loading handling
    useEffect(() => {
        if (isLoading) {
            showProgress();
        } else {
            hideProgress();
        }
    }, [isLoading]);

    // Error handling
    useEffect(() => {
        if (!isError) return;
        let errMsg = t('toast.failedToGetData', { ns: 'translation' });
        if (typeof errorMessage.message === 'string') {
            errMsg = errorMessage.message;
        }
        hideProgress();
        addNotification({
            title: t('toast.error', { ns: 'translation' }),
            message: errMsg,
            level: 'error',
        });
    }, [isError]);

    const downloadLaporan = async (reportType = 'xlsx') => {
        try {
            showProgress();
            const payloadDownload = {
                report_type: reportType,
                start_date: moment(calendarDate[0]).format('YYYY-MM-DD'),
                end_date: moment(calendarDate[1]).format('YYYY-MM-DD'),
                limit: totalData,
                group: period,
                ...(filterBranch && {
                    outlet_id: filterBranch,
                }),
                ...(filterKeyword && {
                    search: filterKeyword,
                }),
                ...filterOrderType.value && {
                    order_type_id: parseInt(filterOrderType.value, 10),
                },
                ...(sort && order && {
                    sort: (sort === 'ASC' ? '' : '-') + order,
                }),
            };

            const { data } = await downloadOutletSalesReportDatamartV2(payloadDownload);

            if (type === 'xlsx') window.location = data;
            else downloadTypePdf(data);


            addNotification({
                title: t('toast.success', { ns: 'translation' }),
                message: (
                    <Trans t={t} i18nKey="toast.exportSuccess">
                        Laporan
                        <b>Penjualan Outlet</b>
                        berhasil diekspor
                    </Trans>
                ),
                level: 'success',
            });
        } catch (e) {
            addNotification({
                title: t('toast.error', { ns: 'translation' }),
                message: (
                    <Trans t={t} i18nKey="toast.exportFailed" />
                ),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    const responsivePaper = {
        '@sm': {
            padding: 0,
            backgroundColor: 'transparent',
            boxShadow: 'none',
        },
        '@md': {
            padding: '$spacing-05',
            backgroundColor: '$white',
            boxShadow: '0px 2px 12px #00000014',
        },
    };

    const handleChangeDateRange = (value) => {
        const { newStartDate, newEndDate, isForceReset } = resetDateRangeHelper(value[0], value[1], 366, true, 12);
        if (isForceReset) {
            addNotification({
                title: t('toast.errorMaxDate', 'Terjadi Kesalahan!', { ns: 'translation' }),
                description: t('toast.errorMaxDateRangeYear', 'Maksimum rentang waktu yang dapat dipilih: 1 tahun', { ns: 'translation' }),
                level: 'pending',
            });
            assignCalendar(newStartDate, newEndDate);
            setCalendarDate([moment(newStartDate, 'DD-MM-YYYY').toDate(), moment(newEndDate, 'DD-MM-YYYY').toDate()]);
        } else {
            assignCalendar(moment(value[0]).format('DD-MM-YYYY'), moment(value[1]).format('DD-MM-YYYY'), null, null);
            setCalendarDate(value);
        }
    };
    
    return (
        !isLoading && (
            <React.Fragment>
                <Flex
                    css={{
                        overflowX: 'hidden',
                        flexDirection: 'column',
                        gap: '$comfortable',
                        paddingTop: 12,
                        '@md': { marginBottom: '28px', padding: 0 },
                    }}
                >
                    <Paper css={responsivePaper}>
                        <Flex css={{ flexDirection: 'column', rowGap: '$compact' }}>
                            <TitleSection
                                t={t}
                                title={t('titleSection.title', 'Penjualan Outlet')}
                                date={calendarDate}
                                onDownloadLaporan={downloadLaporan}
                                hideComparison
                            />
                            <BannerText css={{ my: 0 }} />
                            <Separator
                                css={{
                                    '@sm': { display: 'none' },
                                    '@md': { display: 'block' },
                                }}
                            />
                            <FilterSection
                                t={t}
                                setCalendarDate={handleChangeDateRange}
                                date={calendarDate}
                                onDownloadLaporan={downloadLaporan}
                                filterOrderType={filterOrderType}
                                orderTypeOption={orderTypeOption}
                                setFilterOrderType={setFilterOrderType}
                                filterKeyword={filterKeyword}
                                handleFilterKeyword={debounce(val => setFilterKeyword(val), 600)}
                            />
                            <Separator css={{ my: 4, '@md': { my: 0 } }} />
                            <SummarySection t={t} summaryData={tableSummary} privilege={privilege} />
                            <Separator css={{ mt: '-8px', '@md': { mt: 0 } }} />
                            <Box css={{ '@md': { padding: '0px $spacing-03' } }}>
                                {isLoading ? (
                                    <Box css={{ height: 300 }} />
                                ) : (
                                    <LineChartv2
                                        data={lineChartData}
                                        legendData={lineChartLegend}
                                        period={period}
                                        chartTypeLabel={toTitleCase(typeOptions(t).find(x => x.typeParams === type).name)}
                                        xAxisKey={period === 'week' ? 'monday_date' : 'date'}
                                        title={t('chartSection.title', 'Grafik Penjualan Outlet')}
                                        onComparatorChange={x => handleComparator(x)}
                                        comparatorList={comparatorList}
                                        customXAxisFormat={d => handleXaxis(d, period)}
                                        customYAxisFormat={val => formatCurrency(val, { notation: 'compact', compactDisplay: 'short' }).replace(type === 'transaksi' || type === 'produk' || val === 0 ? 'Rp' : '', '').trim()}
                                        isEmptyData={rawChartData.length <= 0}
                                    >
                                        <FilterSectionGraphic
                                            period={period}
                                            periodOptions={periodOptions}
                                            setPeriod={value => setPeriod(value.value)}
                                            type={type}
                                            typeOptions={typeOptions(t)}
                                            setType={value => setType(typeOptions(t).find(f => f.value === value.value).typeParams)}
                                        />
                                    </LineChartv2>
                                )}
                            </Box>
                        </Flex>
                        <Flex
                            css={{
                                flexDirection: 'column',
                                rowGap: '$compact',
                                '@sm': { marginTop: '$spacing-04' },
                                '@md': { margin: 0 },
                            }}
                        >
                            <Table
                                id="report_sales_outletsales_dtm"
                                data={tableData}
                                columns={headerData(t, isMobile).filter(item => checkisValueExistOrEmpty(privilege.components, item.accessor))}
                                totalData={tableData.length > 0 ? totalData : 0}
                                searchQuery={filterKeyword}
                                fetchData={fetchTableFilter}
                                isLoading={tableLoading}
                                css={{ padding: 0 }}
                            />
                        </Flex>
                    </Paper>
                </Flex>
            </React.Fragment>
        )
    );
};

PenjualanOutlet.propTypes = {
    assignCalendar: PropTypes.func.isRequired,
    filterBranch: PropTypes.string,
    calendar: PropTypes.shape({
        start: PropTypes.string,
        end: PropTypes.string,
        rangeLimit: PropTypes.number,
        onchange: PropTypes.func,
    }).isRequired,
    showProgress: PropTypes.func.isRequired,
    hideProgress: PropTypes.func.isRequired,
    addNotification: PropTypes.func.isRequired,
    getContentTranslation: PropTypes.func.isRequired,
    privilege: PropTypes.shape({ 
        components: PropTypes.shape({})
    }).isRequired,
};

PenjualanOutlet.defaultProps = {
    filterBranch: '',
};

const mapStateToProps = state => ({
    namaUser: state.user.profile.user_name,
    namaUsaha: state.user.profile.user_usaha_name,
    selectedCabang: state.branch.filter,
    logo: state.user.profile.user_usaha_logo_path,
    listCabang: state.branch.list,
    supportNeeded: state.layout.supportNeeded,
    privilege: state.layouts.detailPrivilege,
});

export default CoreHOC(connect(mapStateToProps)(PenjualanOutlet));
