import {
  Box,
  DecimalColumn,
  Flex,
  InputSearchbox,
  Paper,
  RowExpanderColumn,
  Separator,
  Table,
  InputSelect,
} from '@majoo-ui/react';
import { debounce } from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, {
  useCallback, useEffect, useReducer, useState,
} from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import {
  fillDataDaily,
  fillDataHourly,
  fillDataMonthly,
  fillDataWeekly,
  handleXaxis,
  LineChart,
} from '../../../../components/chart';
import { PriceColumn } from '../../../../components/retina/table';
import CoreHOC from '../../../../core/CoreHOC';
import { getOutlet } from '../../../../data/outlets';
import {
  getOutletSalesChartReport,
  getOutletSalesReport,
} from '../../../../data/reports';
import { printExcel } from '../../../../utils/printout';
import { FilterSection, SummarySection, TitleSection } from './components';
import { mapDispatch, reducer } from './settings/reducer';
import { BannerText, PercentageColumn } from '../../../../components/retina';
import { useMediaQuery } from '../../../../utils/useMediaQuery';
import { formatCurrency, resetDateRangeHelper, toTitleCase } from '../../../../utils/helper';
import { orderTypeOptionTranslate } from '../config/references';
import { colors } from '../../../../stitches.config';
import { checkLogo } from '../../../../components/retina/export/utils';

const chartColor = Object.keys(colors)
  .filter(key => key.includes('chart'))
  .reduce((prev, current) => [...prev, colors[current]], []);

const initialState = translate => ({
  isLoading: true,
  isError: false,
  errorMessage: undefined,
  rawChartData: [],
  lineChartData: [],
  lineChartColors: chartColor,
  lineChartLegend: [],
  tableData: [],
  // TODO: Kurang kolom tipe persen.
  headerData: t => [
    { ...RowExpanderColumn, sticky: 'left', stickyPosition: 0 },
    {
      Header: 'Outlet',
      accessor: 'outlet',
      isMobileHeader: true,
      sticky: 'left',
      stickyPosition: 30,
      shadow: true,
    },
    {
      Header: t('header.sales', 'Penjualan (RP)'),
      accessor: 'omzet',
      Cell: instance => PriceColumn({ ...instance, decimal: false, convertMinus: true }),
    },
    {
      Header: t('header.grossProfit', 'Laba Kotor (RP)'),
      accessor: 'profit',
      Cell: instance => PriceColumn({ ...instance, decimal: false, convertMinus: true }),
    },
    {
      Header: t('header.productCount', 'Jumlah Produk'),
      accessor: 'product_cnt',
      Cell: DecimalColumn,
    },
    {
      Header: t('header.transactionCount', 'Jumlah Transaksi'),
      accessor: 'transaction_cnt',
      Cell: DecimalColumn,
    },
    {
      Header: t('header.salesPercentage'),
      accessor: 'omzet_percentage',
      Cell: PercentageColumn,
      isSubRow: true,
    },
    {
      Header: t('header.grossProfitPercentage'),
      accessor: 'profit_percentage',
      Cell: PercentageColumn,
      isSubRow: true,
    },
    {
      Header: t('header.productPercentage'),
      accessor: 'product_cnt_percentage',
      Cell: PercentageColumn,
      isSubRow: true,
    },
    {
      Header: t('header.transactionPercentage'),
      accessor: 'transaction_cnt_percentage',
      Cell: PercentageColumn,
      isSubRow: true,
    },
    {
      Header: t('header.orderTransaction'),
      accessor: 'order_trx',
      Cell: PriceColumn,
      isSubRow: true,
    },
    {
      Header: t('header.productTransaction'),
      accessor: 'product_trx',
      Cell: DecimalColumn,
      isSubRow: true,
    },
  ],
  tableSummary: [],
  calendarDate: [],
  period: 'hari',
  type: 'produk',
  typeOptions: t => [
    { name: t('legend.sales', { ns: 'translation', defaultValue: 'Penjualan' }), value: 'omzet', typeParams: 'omzet' },
    { name: t('legend.profit', { ns: 'translation', defaultValue: 'Laba' }), value: 'profit', typeParams: 'laba' },
    { name: t('legend.transaction', { ns: 'translation', defaultValue: 'Transaksi' }), value: 'transaction_cnt', typeParams: 'transaksi' },
    { name: t('legend.product', { ns: 'translation', defaultValue: 'Produk' }), value: 'product_cnt', typeParams: 'produk' },
  ],
  outletList: [],
  outletIds: [],
  filteredOutlets: [],
  order: '',
  sort: '',
  filterKeyword: '',
  page: 1,
  limit: 10,
  totalData: undefined,
  comparatorList: [],
  filterOrderType: {
    isDisabled: undefined,
    name: 'Semua Jenis Order',
    render: undefined,
    value: '',
  },
});

const PenjualanOutlet = (props) => {
  const {
    calendar,
    filterBranch,
    assignCalendar,
    showProgress,
    hideProgress,
    addNotification,
    listCabang,
    namaUsaha,
    logo,
  } = props;
  const { t, i18n, ready } = useTranslation(['Penjualan/outlet', 'translation', 'Penjualan/orderType']);
  const currentLang = i18n.language;

  const [state, dispatch] = useReducer(reducer, initialState(t));
  const {
    limit,
    page,
    calendarDate,
    isLoading,
    tableData,
    rawChartData,
    lineChartData,
    lineChartLegend,
    lineChartColors,
    tableSummary,
    period,
    isError,
    errorMessage,
    order,
    sort,
    filterKeyword,
    totalData,
    headerData,
    type,
    typeOptions,
    outletList,
    outletIds,
    comparatorList,
    filterOrderType,
  } = state;

  const {
    setLimit,
    setPage,
    fetchInit,
    fetchSuccess,
    fetchFailure,
    setTableData,
    setRawChartData,
    setLineChartData,
    setTotalData,
    setSort,
    setOrder,
    setPeriod,
    setCalendarDate,
    setTableSummary,
    setFilterKeyword,
    setType,
    setOutletList,
    setOutletIds,
    setLineChartLegend,
    setComparatorList,
    setFilterOrderType,
  } = mapDispatch(dispatch);

  const [periodOptions, setPeriodOptions] = useState([
    {
      tkey: 'hour', name: t('period.hour', { ns: 'translation', defaultValue: 'Jam' }), value: 'jam', isDisabled: true,
    },
    { tkey: 'day', name: t('period.day', { ns: 'translation', defaultValue: 'Hari' }), value: 'hari' },
    { tkey: 'week', name: t('period.week', { ns: 'translation', defaultValue: 'Minggu' }), value: 'minggu' },
    { tkey: 'month', name: t('period.month', { ns: 'translation', defaultValue: 'Bulan' }), value: 'bulan' },
    { tkey: 'year', name: t('period.year', { ns: 'translation', defaultValue: 'Tahun' }), value: 'tahun' },
  ]);

  useEffect(() => {
    setPeriodOptions((options) => {
      const res = options.map(l => ({ ...l, name: t(`period.${l.tkey}`, { ns: 'translation' }) }));
      return res;
    });
    setFilterOrderType({ ...filterOrderType, name: filterOrderType.name });
  }, [currentLang]);

  const [tableLoading, setTableLoading] = useState(false);

  const fetchOutlet = useCallback(async () => {
    const payloadOutlet = {
      is_cms: 1,
      is_active: 1,
    };
    try {
      const outletResult = await getOutlet(payloadOutlet);
      setOutletList(outletResult.data);
    } catch (e) {
      fetchFailure(e);
    }
  }, []);

  const fetchLineChart = useCallback(async () => {
    const payloadLineChart = {
      start_date: moment(calendarDate[0]).format('YYYY-MM-DD'),
      end_date: moment(calendarDate[1]).format('YYYY-MM-DD'),
      group: period,
      type,
      outlets: filterBranch ? +filterBranch : outletIds.join(',\n'),
      ...filterOrderType.value && {
        order_type: filterOrderType.value,
      },
    };

    const lineLegend = outletList.filter(outlet => (filterBranch ? outlet.id_cabang === String(filterBranch) : outletIds.includes(Number(outlet.id_cabang)))).map((outlet, index) => ({
      dataKey: outlet.cabang_name,
      name: outlet.cabang_name,
      color: lineChartColors[index % lineChartColors.length],
      isComparator: true,
      value: Number(outlet.id_cabang),
      isCurrency: type === typeOptions(t)[0].typeParams || type === typeOptions(t)[1].typeParams,
    }));

    try {
      const dailyReports = await getOutletSalesChartReport(payloadLineChart);
      setRawChartData(dailyReports.data);
      const getDataPerOutlet = dailyReports.data.reduce((prev, current) => {
        const match = prev.find(p => p.date === current.date);
        if (match) {
          Object.assign(match, {
            ...{
              [current.outlet]:
                current[typeOptions(t).find(o => o.typeParams === type).value],
            },
          });
        } else {
          prev.push({
            date: current.date,
            monday_date: current.monday_date,
            [current.outlet]:
              current[typeOptions(t).find(o => o.typeParams === type).value],
          });
        }
        return prev;
      }, []);

      const startDate = calendarDate[0].setHours(0, 0, 0, 0);
      const endDate = calendarDate[1].setHours(0, 0, 0, 0);
      if (period === 'jam') {
        setLineChartData(
          fillDataHourly(
            startDate,
            endDate,
            'date',
            getDataPerOutlet,
            lineLegend,
          ),
        );
      } else if (period === 'hari') {
        setLineChartData(
          fillDataDaily(
            startDate,
            endDate,
            'date',
            getDataPerOutlet,
            lineLegend,
          ),
        );
      } else if (period === 'minggu') {
        setLineChartData(
          fillDataWeekly(
            startDate,
            endDate,
            'monday_date',
            getDataPerOutlet,
            lineLegend,
          ),
        );
      } else if (period === 'bulan') {
        setLineChartData(
          fillDataMonthly(
            startDate,
            endDate,
            'date',
            getDataPerOutlet,
            lineLegend,
          ),
        );
      } else if (getDataPerOutlet.length > 0) {
        setLineChartData(getDataPerOutlet);
      } else {
        setLineChartData([lineLegend.reduce((prev, cur) => ({
          ...prev,
          date: moment(startDate).format('YYYY'),
          [cur.dataKey]: 0,
        }), {})]);
      }
      setLineChartLegend(lineLegend);
      fetchSuccess();
    } catch (e) {
      fetchFailure(e);
    } finally {
      hideProgress();
    }
  }, [calendarDate, period, outletIds, outletList, type, filterBranch, filterOrderType]);

  const fetchTable = useCallback(async () => {
    setTableLoading(true);
    const payloadTableSummary = {
      start_date: moment(calendarDate[0]).format('YYYY-MM-DD'),
      end_date: moment(calendarDate[1]).format('YYYY-MM-DD'),
      limit,
      page,
      group: period,
      ...(order && {
        order,
      }),
      ...(sort && {
        sort,
      }),
      ...(filterBranch && {
        id_outlet: filterBranch,
      }),
      ...(filterKeyword && {
        search: filterKeyword,
      }),
      ...filterOrderType.value && {
        order_type: filterOrderType.value,
      },
    };
    // setTableData([]);
    try {
      const { summary, data, meta } = await getOutletSalesReport(payloadTableSummary);
      setTableSummary(summary);
      setTableData(data);
      setTotalData(meta.total);

      setOutletIds(
        data.reduce((prev, current) => {
          prev.push(current.M_Cabang_id_cabang);
          return prev;
        }, []).slice(0, 5),
      );
      fetchSuccess();
    } catch (e) {
      fetchFailure(e);
    } finally {
      setTableLoading(false);
    }
  }, [
    limit,
    page,
    sort,
    order,
    filterKeyword,
    calendarDate,
    period,
    filterBranch,
    filterOrderType,
  ]);

  const fetchTableFilter = useCallback(
    (val) => {
      if (!val) {
        fetchTable();
      } else {
        setLimit(val.pageSize);
        // + 1 karena params page dimulai dari 1 sedangkan pageIndex mulai dari 0
        setPage(val.pageIndex + 1);
        setSort(val.sortDirection);
        setOrder(val.sortAccessor);
      }
    },
    [calendarDate, filterBranch, period],
  );

  const handleComparator = useCallback(
    (value) => {
      switch (value.type) {
        case 'ADD':
          return setOutletIds([...outletIds, Number(value.payload.value)]);
        case 'DELETE':
          return setOutletIds(outletIds.filter(o => o !== value.payload.value));
        default:
          return value;
      }
    },
    [outletIds],
  );

  const handleFilterKeyword = useCallback(
    debounce(value => setFilterKeyword(value), 1000),
    [],
  );

  // Init
  useEffect(() => {
    fetchInit();
    setCalendarDate([
      moment(calendar.start, 'DD/MM/YYYY').toDate(),
      moment(calendar.end, 'DD/MM/YYYY').toDate(),
    ]);
    fetchOutlet();
  }, []);

  // Calendar handler
  useEffect(() => {
    if (calendarDate.length > 0) {
      fetchInit();
      assignCalendar(
        moment(calendarDate[0]).format('DD/MM/YYYY'),
        moment(calendarDate[1]).format('DD/MM/YYYY'),
      );
      fetchTableFilter();
      if (moment(calendarDate[0]).diff(calendarDate[1], 'days') === 0) {
        setPeriodOptions(
          periodOptions.map(p => (p.value === 'jam' ? { ...p, isDisabled: false } : p)),
        );
      } else {
        setPeriodOptions(
          periodOptions.map(p => (p.value === 'jam' ? { ...p, isDisabled: true } : p)),
        );
      }
    }
  }, [calendarDate]);

  // LineChart Handler
  useEffect(() => {
    if (
      outletList.length > 0
      && outletIds.length > 0
      && calendarDate.length > 0
    ) {
      showProgress();
      setComparatorList(
        outletList.filter(outlet => !filterBranch || outlet.id_cabang === String(filterBranch)).map(d => ({ name: d.cabang_name, value: d.id_cabang })),
      );
      fetchLineChart();
    }
  }, [outletIds, outletList, period, type, calendarDate, filterBranch, filterOrderType]);

  // Table handler
  useEffect(() => {
    if (calendarDate.length > 0) {
      fetchTable();
    }
  }, [limit, page, sort, order, period, filterKeyword, filterBranch, filterOrderType]);

  // Loading handling
  useEffect(() => {
    if (isLoading) {
      showProgress();
    } else {
      hideProgress();
    }
  }, [isLoading]);

  // Error handling
  useEffect(() => {
    if (!isError) return;
    let errMsg = t('toast.failedToGetData', { ns: 'translation' });
    if (typeof errorMessage.message === 'string') {
      errMsg = errorMessage.message;
    }
    hideProgress();
    addNotification({
      title: t('toast.error', { ns: 'translation' }),
      message: errMsg,
      level: 'error',
    });
  }, [isError]);

  const downloadLaporan = async (reportType = 'xlsx') => {
    try {
      showProgress();
      const payloadDownload = {
        start_date: moment(calendarDate[0]).format('YYYY-MM-DD'),
        end_date: moment(calendarDate[1]).format('YYYY-MM-DD'),
        limit: totalData,
        group: period,
        ...(filterBranch && {
          id_outlet: filterBranch,
        }),
        ...(filterKeyword && {
          search: filterKeyword,
        }),
        ...filterOrderType.value && {
          order_type: filterOrderType.value,
        },
      };

      const { data } = await getOutletSalesReport(payloadDownload);

      const downloadData = data.map((item) => {
        const result = {
          outlet_name: item.outlet,
          transaction_nominal: item.omzet,
          transaction_nominal_percentage: item.omzet_percentage,
          gross_profit_nominal: item.profit,
          gross_profit_percentage: item.profit_percentage,
          transaction_count: item.transaction_cnt,
          transaction_count_percentage: item.transaction_cnt_percentage,
          product_count: item.product_cnt,
          product_count_percentage: item.product_cnt_percentage,
        };

        return { ...result };
      });

      let i = 0;
      let totalOmzet = 0;
      let totalLabaKotor = 0;
      let totalTransaksi = 0;
      let totalProduk = 0;

      const cabang = listCabang.length > 0
        ? listCabang.find(item => item.id_cabang === filterBranch)
          .cabang_name
        : 'Semua Cabang';

      for (i = 0; i < downloadData.length; i += 1) {
        totalOmzet = Number(totalOmzet) + Number(downloadData[i].transaction_nominal);
        totalTransaksi = parseInt(totalTransaksi, 10) + parseInt(downloadData[i].transaction_count, 10);
        totalProduk = parseInt(totalProduk, 10) + parseInt(downloadData[i].product_count, 10);
        totalLabaKotor = Number(totalLabaKotor) + Number(downloadData[i].gross_profit_nominal);
        downloadData[i].sales_transaction_nominal = Number.isNaN(downloadData[i].transaction_nominal / downloadData[i].transaction_count)
          ? 0 : Math.floor(downloadData[i].transaction_nominal / downloadData[i].transaction_count);
        downloadData[i].product_transaction_count = Number.isNaN(downloadData[i].product_count / downloadData[i].transaction_count)
          ? 0 : Math.floor(downloadData[i].product_count / downloadData[i].transaction_count);
      }
      const orderTypeName = orderTypeOptionTranslate(t).find(f => f.value === filterOrderType.value);

      const  payload = {
        template: 'penjualan_cabang_v3.xlsx',
        output_name: 'penjualan_cabang',
        alias: 'x',
        export_type: reportType,
        header_default: {
          date_now: moment().format('DD/MM/YYYY'),
          search: filterKeyword || '',
          start: calendar.start,
          end: calendar.start,
          outlet: cabang,
          merchant: namaUsaha,
          logo: checkLogo(logo),
        },
        header_custom: {
          order_type: orderTypeName ? orderTypeName.name : '',
          transaction_nominal: totalOmzet,
          transaction_count: totalTransaksi,
          gross_profit_nominal: totalLabaKotor,
          product_count: totalProduk,
        },
        data: downloadData,
      };
      const version = '0_0_2';
      await printExcel(payload, addNotification, {
        title: t('toast.success', { ns: 'translation' }),
        description: (
          <Trans t={t} i18nKey="toast.exportSuccess">
            Laporan
            <b>Penjualan Harian</b>
            berhasil diekspor
          </Trans>
        ),
        errorTitle: t('toast.error', { ns: 'translation' }),
        errorDescription: <Trans t={t} i18nKey="toast.exportFailed" />,
      }, version);
    } catch (e) {
      addNotification({
        title: t('toast.error', { ns: 'translation' }),
        message: (
          <Trans t={t} i18nKey="toast.exportFailed" />
        ),
        level: 'error',
      });
    } finally {
      hideProgress();
    }
  };

  const responsivePaper = {
    '@sm': {
      padding: 0,
      backgroundColor: 'transparent',
      boxShadow: 'none',
    },
    '@md': {
      padding: '$spacing-05',
      backgroundColor: '$white',
      boxShadow: '0px 2px 12px #00000014',
    },
  };

  const handleChangeDateRange = (value) => {
    const { newStartDate, newEndDate, isForceReset } = resetDateRangeHelper(value[0], value[1], 90);
    if (isForceReset) {
      addNotification({
        title: t('toast.errorMaxDate', 'Terjadi Kesalahan!', { ns: 'translation' }),
        description: t('toast.errorMaxDateRange', 'Maksimum rentang waktu yang dapat dipilih: 3 bulan', { ns: 'translation' }),
        level: 'pending',
      });
      assignCalendar(newStartDate, newEndDate);
      setCalendarDate([moment(newStartDate, 'DD-MM-YYYY').toDate(), moment(newEndDate, 'DD-MM-YYYY').toDate()]);
    } else {
      assignCalendar(moment(value[0]).format('DD-MM-YYYY'), moment(value[1]).format('DD-MM-YYYY'), null, null);
      setCalendarDate(value);
    }
  };

  return (
    !isLoading && (
      <React.Fragment>
        <Paper css={responsivePaper}>
          <Flex css={{ flexDirection: 'column', rowGap: '$compact' }}>
            <TitleSection
              title={t('titleSection.title', 'Penjualan Outlet')}
              date={calendarDate}
              onDownloadLaporan={downloadLaporan}
              hideComparison
              exportLabel={t('label.exportReport', { ns: 'translation', defaultValue: 'Ekspor Laporan' })}
              isPrimary
            />
            <Separator
              css={{
                '@sm': { display: 'none' },
                '@md': { display: 'block' },
              }}
            />
            <BannerText css={{ my: 0 }} />
            <FilterSection
              setCalendarDate={handleChangeDateRange}
              date={calendarDate}
              period={period}
              periodOptions={periodOptions}
              setPeriod={value => setPeriod(value.value)}
              type={type}
              typeOptions={typeOptions(t)}
              setType={value => setType(typeOptions(t).find(f => f.value === value.value).typeParams)}
              onDownloadLaporan={downloadLaporan}
              exportLabel={t('label.exportReport', { ns: 'translation', defaultValue: 'Ekspor Laporan' })}
              filterOrderType={orderTypeOptionTranslate(t).find(f => f.value === filterOrderType.value)}
              orderTypeOption={orderTypeOptionTranslate(t)}
              setFilterOrderType={setFilterOrderType}
              t={t}
              lang={currentLang}
            />
            <Separator css={{ my: 4, '@md': { my: 0 } }} />
            <SummarySection summaryData={tableSummary} t={t} />
            <Separator css={{ mt: '-8px', '@md': { mt: 0 } }} />
            <Box css={{ '@md': { padding: '0px $spacing-03' } }}>
              {isLoading ? (
                <Box css={{ height: 300 }} />
              ) : (
                <LineChart
                  data={lineChartData}
                  legendData={lineChartLegend}
                  period={period}
                  chartTypeLabel={toTitleCase(typeOptions(t).find(o => o.typeParams === type).name)}
                  xAxisKey={period === 'minggu' ? 'monday_date' : 'date'}
                  title={t('chartSection.title', 'Grafik Penjualan Outlet')}
                  onComparatorChange={x => handleComparator(x)}
                  comparatorList={comparatorList}
                  customXAxisFormat={d => handleXaxis(d, period)}
                  customYAxisFormat={val => formatCurrency(val, { notation: 'compact', compactDisplay: 'short' }).replace(type === 'transaksi' || type === 'produk' || val === 0 ? 'Rp' : '', '').trim()}
                  isEmptyData={rawChartData.length <= 0}
                />
              )}
            </Box>
          </Flex>
          <Separator
            css={{
              my: 32,
              '@md': { display: 'block', margin: '36px -16px' },
            }}
          />
          <Flex
            css={{
              flexDirection: 'column',
              rowGap: '$compact',
              '@sm': { marginTop: '$spacing-04' },
              '@md': { margin: 0 },
            }}
          >
            <TitleSection
              title={t('titleSection.title', 'Penjualan Outlet')}
              date={calendarDate}
              hideDownloadButton
              hideComparison
              hideTooltipGuidance
            />
            <Separator
              css={{
                my: 4,
                '@md': { m: 0 },
              }}
            />
            <Box
              css={{
                display: 'block',
                '@sm': {
                  padding: 0,
                  margin: 0,
                },
                '@md': {
                  width: 550,
                  padding: '0px $spacing-03',
                },
              }}
            >
              <Flex css={{ flexDirection: 'column', '@md': { flexDirection: 'row' } }} gap={5}>
                <InputSearchbox
                  placeholder={t('placeholder.search', { ns: 'translation', defaultValue: 'Cari ...' })}
                  onChange={(e) => {
                    handleFilterKeyword(e);
                  }}
                />
                <InputSelect
                  size="sm"
                  placeholder="Pilih Jenis Order"
                  value={orderTypeOptionTranslate(t).find(f => f.value === filterOrderType.value)}
                  onChange={e => setFilterOrderType(e)}
                  option={orderTypeOptionTranslate(t)}
                  css={{ display: 'none', '@md': { display: 'block' } }}
                />
              </Flex>
            </Box>
            <Separator
              css={{
                '@sm': { display: 'none' },
                '@md': { display: 'block' },
              }}
            />
            <Table
              id="report_sales_outletsales"
              data={tableData}
              columns={headerData(t)}
              totalData={totalData}
              searchQuery={filterKeyword}
              fetchData={fetchTableFilter}
              isLoading={tableLoading}
              css={{ padding: 0 }}
            />
          </Flex>
        </Paper>
      </React.Fragment>
    )
  );
};

PenjualanOutlet.propTypes = {
  assignCalendar: PropTypes.func.isRequired,
  filterBranch: PropTypes.string,
  calendar: PropTypes.shape({
    start: PropTypes.string,
    end: PropTypes.string,
    rangeLimit: PropTypes.number,
    onchange: PropTypes.func,
  }).isRequired,
  showProgress: PropTypes.func.isRequired,
  hideProgress: PropTypes.func.isRequired,
  addNotification: PropTypes.func.isRequired,
  listCabang: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  logo: PropTypes.string,
  namaUsaha: PropTypes.string,
  namaUser: PropTypes.string,
};

PenjualanOutlet.defaultProps = {
  filterBranch: '',
  namaUsaha: '',
  namaUser: '',
  logo: '',
};

const mapStateToProps = state => ({
  namaUser: state.user.profile.user_name,
  namaUsaha: state.user.profile.user_usaha_name,
  selectedCabang: state.branch.filter,
  logo: state.user.profile.user_usaha_logo_path,
  listCabang: state.branch.list,
  supportNeeded: state.layout.supportNeeded,
});

export default CoreHOC(connect(mapStateToProps)(PenjualanOutlet));
