import React from 'react';
import PropTypes from 'prop-types';

import {
    Flex,
    InputSelect,
} from '@majoo-ui/react';

const inputStyle = {
    '@sm': { flex: 1 },
    '@md': {
        flex: 'none',
        width: 150
    }
};

const FilterChart = ({
    periodOptions,
    period,
    setPeriod,
    groupByOptions,
    groupBy,
    setGroupBy,
}) => (
    <Flex direction='row' gap={5} css={{ '@md': { padding: '0px $spacing-03' } }}>
        <InputSelect
            size="sm"
            option={periodOptions}
            placeholder="Pilih Periode"
            value={periodOptions.find(f => f.value === period)}
            onChange={setPeriod} // TODO: need to recheck this
            css={inputStyle}
        />
        <InputSelect
            size="sm"
            option={groupByOptions}
            placeholder="Pilih Jumlah Berdasarkan"
            value={groupByOptions.find(f => f.value === groupBy)}
            onChange={setGroupBy}
            css={inputStyle}
        />
    </Flex>
);

FilterChart.propTypes = {
    periodOptions: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
    period: PropTypes.string.isRequired,
    setPeriod: PropTypes.func.isRequired,
    groupByOptions: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
    groupBy: PropTypes.string.isRequired,
    setGroupBy: PropTypes.func.isRequired,
}

export default FilterChart;
