import React, { useCallback } from 'react';
import { Flex, Paper, Separator, Table, ToastContext } from '@majoo-ui/react';
import { connect } from 'react-redux';
import { debounce } from 'lodash';
import { numSeparator, formatCurrency, toTitleCase } from '../../../../utils/helper';
import CoreHOC from '../../../../core/CoreHOC';
import TitleSection from './components/TitleSection';
import FilterSection from './components/FilterSection';
import FilterChart from './components/FilterChart';
import {
    LineChartv2,
} from '../../../../components/chart';
import { jenisOrder } from './settings/utils';
import { columnTable } from './settings/table';
import { BannerText, CardWrapper } from '../../../../components/retina';
import { useListViewModel } from './model/useListViewModel';
import { responsivePaper } from './style/style';

function OrderTypeDatamart(props) {
    const { addToast } = React.useContext(ToastContext);

    const {
        date,
        changeDate,
        period,
        setPeriod,
        resData,
        chartData,
        dataType,
        setDataType,
        periodOptions,
        handleComparator,
        handleXaxis,
        handleDetail,
        headerMetaTranslation,
        t,
        search,
        setSearch,
        fetchData,
        handleDownloadLaporan,
        dataComparator,
        getContentTranslation,
    } = useListViewModel({ ...props, addToast });

    const onKeywordSearch = useCallback(
        debounce(keyword => setSearch(keyword), 700),
        [],
    );

    return (
        <Flex
            css={{
                flexDirection: 'column',
                gap: '$comfortable',
                '@md': { marginBottom: '28px' },
            }}
        >
            <Paper css={responsivePaper}>
                <Flex css={{ flexDirection: 'column', rowGap: '$compact' }}>
                    <TitleSection
                        title={t('header.title', 'Laporan Jenis Order')}
                        date={date}
                        onDownloadLaporan={handleDownloadLaporan}
                        hideComparison
                        isPrimary
                    />
                    <Separator
                        css={{
                            '@sm': { display: 'none' },
                            '@md': { display: 'block' },
                        }}
                    />
                    <BannerText css={{ my: 0 }} />
                    <FilterSection
                        onKeywordSearch={onKeywordSearch}
                        date={date}
                        changeDate={changeDate}
                        onDownloadLaporan={handleDownloadLaporan}
                        t={t}
                    />
                    <Separator />
                    <LineChartv2
                        data={chartData.chartData ? chartData.chartData : []}
                        legendData={chartData.lineChartLegend ? chartData.lineChartLegend : []}
                        chartTypeLabel={toTitleCase(jenisOrder(t).find(val => val.value === dataType).name)}
                        period={period}
                        xAxisKey="date"
                        placeholder={t('placeholder.search', 'Cari...', { ns: 'translation' })}
                        title={t('chart.title', 'Grafik Jenis Order')}
                        onComparatorChange={x => handleComparator(x)}
                        comparatorList={dataComparator}
                        customXAxisFormat={d => handleXaxis(d)}
                        customYAxisFormat={val =>
                            formatCurrency(val, { notation: 'compact', compactDisplay: 'short' })
                                .replace(dataType === 'count' || val === 0 ? 'Rp' : '', '')
                                .trim()
                        }
                        isEmptyData={!chartData.chartData || chartData.chartData.length === 0}
                    >
                        <FilterChart
                            periodOptions={periodOptions}
                            period={period}
                            setPeriod={value => setPeriod(value.value)}
                            groupByOptions={jenisOrder(t)}
                            groupBy={dataType}
                            setGroupBy={value => setDataType(value.value)}
                        />
                    </LineChartv2>
                </Flex>

                <Flex
                    css={{
                        flexDirection: 'column',
                        rowGap: '$compact',
                        '@sm': { marginTop: '$spacing-04' },
                        '@md': { margin: 0 },
                    }}
                >
                    <CardWrapper
                        data={[
                            {
                                color: 'green',
                                label: `Total ${t('legend.transaction', 'Transaksi', { ns: 'translation' })}`,
                                description: numSeparator(resData.summary.count),
                            },
                            {
                                color: 'blue',
                                label: `Total ${t('legend.sales', 'Penjualan', { ns: 'translation' })}`,
                                description: formatCurrency(resData.summary.price),
                            },
                        ]}
                    />
                    <Table
                        id="report_sales_ordertype_dtm"
                        data={resData.data}
                        columns={columnTable(handleDetail, headerMetaTranslation, getContentTranslation)}
                        totalData={resData.meta.total}
                        pageIndex={resData.meta.current_page}
                        searchQuery={search}
                        css={{ padding: 0 }}
                        onRowClick={({ original: { transaction_type } }) =>
                            handleDetail(transaction_type.id, transaction_type.name)
                        }
                        customEmptyDataProps={{ title: t('table.emptyDataMessage', 'Tidak ada data') }}
                        fetchData={fetchData}
                    />
                </Flex>
            </Paper>
        </Flex>
    );
}

const mapStateToProps = state => ({
    logo: state.user.profile.user_usaha_logo_path,
    listCabang: state.branch.list,
    namaUser: state.user.profile.user_name,
    namaUsaha: state.user.profile.user_usaha_name,
    selectedCabang: state.branch.filter,
    supportNeeded: state.layout.supportNeeded,
});

export default CoreHOC(connect(mapStateToProps)(OrderTypeDatamart));
