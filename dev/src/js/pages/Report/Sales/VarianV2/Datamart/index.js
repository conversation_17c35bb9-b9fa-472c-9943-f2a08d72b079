import React, { useRef, useState } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import {
    InputSearchbox,
    Paper,
    Separator,
    Table,
    Button,
    Flex,
} from '@majoo-ui/react';
import {
    FilterOutline,
} from '@majoo-ui/icons';
import { debounce } from 'lodash';
import { useTranslation } from 'react-i18next';
import CoreHOC from '../../../../../core/CoreHOC';
import {
    formatCurrency,
} from '../../../../../utils/helper';

import { FilterSection } from './components/FilterSection';
import { FilterSectionGraphic } from './components/FilterSectionGraphic';
import { TitleSection } from './components/TitleSection';
import {
    LineChartv2,
    handleXaxis,
} from '../../../../../components/chart';
import { useFetchVarianSales } from './hooks/useVarianFetch';
import BlockedDownloadLaporanPopup from '../../../../../components/modalpopup/BlockedDownloadLaporanPopup';
import { useMediaQuery } from '../../../../../utils/useMediaQuery';
import { SummarySection } from './components/SummarySection';
import { ChartType } from './utils/enum';
import { tableColumns } from './utils/table.utils';
import FilterMobile from './components/FilterMobile';
import { BannerText } from '../../../../../components/retina';
import ExportReport from '../../../../../components/retina/export/ExportReport';

function VarianSales(props) {
    const {
        router,
    } = props;
    const isMobile = useMediaQuery('(max-width: 798px)');

    const { t } = useTranslation(['Report/Sales/variant', 'translation']);

    const [modalFilter, setModalFilter] = useState(false);

    const blockedDownloadPopupRef = useRef(null);
    const {
        fetchVarian, handleChartComparatorChange,
        metaData, filter, chartFilter, setFilter, setChartFilter,
        isLoading, varianData, varianSummaries, exportLaporan,
        varianChartData, chartType, setChartType,
        handleDetail,
    } = useFetchVarianSales(props, blockedDownloadPopupRef);

    const redirectSupport = () => {
        if (blockedDownloadPopupRef && blockedDownloadPopupRef.current) {
            blockedDownloadPopupRef.current.hidePopup();
        }
        router.push('/support/buy');
    };

    return (
        <React.Fragment>
            <Paper responsive css={{ mb: 20, padding: 'unset', '@md': { padding: 20 } }}>
                <TitleSection
                    title={t('Report/Sales/variant:extraSales')}
                    startDate={filter.startDate}
                    endDate={filter.endDate}
                    onEksporLaporanClick={(type) => exportLaporan(type, t)}
                    isPrimary
                />
                {!isMobile ? (
                    <React.Fragment>
                        <Separator />
                        <BannerText />
                        <FilterSection
                            filter={filter}
                            chartFilter={chartFilter}
                            setFilter={setFilter}
                            setChartFilter={setChartFilter}
                            t={t}
                        />
                    </React.Fragment>
                ) : (
                    <React.Fragment>
                        <BannerText css={{ mt: 0 }} />
                        <InputSearchbox
                            placeholder={t('translation:placeholder.search')}
                            onChange={debounce((query) => {
                                setFilter(current => ({
                                    ...current,
                                    search: query,
                                }));
                            }, 700)}
                            css={{ width: '100%', mb: 20 }}
                        />
                        <Flex gap={3}>
                            <Button
                                size="sm"
                                buttonType="secondary"
                                leftIcon={<FilterOutline color="currentColor" />}
                                css={{ flex: 1 }}
                                onClick={() => setModalFilter(true)}
                            >
                                Filter
                            </Button>
                            <ExportReport
                                onExport={(type) => exportLaporan(type, t)}
                                calendar={{ start: filter.startDate, end: filter.endDate }}
                                title={t('Report/Sales/variant:modal.export.reportTitle', 'Laporan Penjualan Ekstra')}
                                css={{ fontWeight: 600, flex: 3 }}
                                type="multi"
                            />
                        </Flex>
                    </React.Fragment>
                )}
                <Separator css={{ '@sm': { margin: '$spacing-05 0' }, '@md': { marginBottom: '$spacing-05' } }} />
                <LineChartv2
                    title={t('Report/Sales/variant:extraSalesChart')}
                    xAxisKey="date"
                    data={varianChartData.chartData}
                    legendData={varianChartData.legendData}
                    period={chartFilter.group}
                    comparatorList={varianChartData.comparatorList}
                    onComparatorChange={handleChartComparatorChange}
                    customXAxisFormat={date => handleXaxis(date, chartFilter.group)}
                    customYAxisFormat={val => (chartType === ChartType.produk ? val : formatCurrency(val, { notation: 'compact', compactDisplay: 'short' }).replace(val === 0 ? 'Rp' : '', '').trim())}
                    isEmptyData={varianChartData.rawData.length <= 0}
                >
                    <FilterSectionGraphic
                        filter={filter}
                        chartFilter={chartFilter}
                        setFilter={setFilter}
                        setChartFilter={setChartFilter}
                        chartType={chartType}
                        setChartType={setChartType}
                    />
                </LineChartv2>
                <SummarySection varianSummaries={varianSummaries} t={t} />
                <Separator css={{ '@sm': { display: 'none' }, '@md': { display: 'block' } }} />
                <Table
                    id="report_product_extrasales_dtm"
                    columns={tableColumns(t, handleDetail)}
                    data={varianData}
                    pageIndex={metaData.pageIndex}
                    totalData={metaData.totalData}
                    searchQuery={filter.search}
                    fetchData={fetchVarian}
                    isLoading={isLoading}
                    css={{
                        padding: 0,
                    }}
                    onRowClick={({ original }) => handleDetail(original)}
                />
            </Paper>

            <FilterMobile
                open={modalFilter}
                onClose={() => setModalFilter(false)}
                filter={filter}
                setFilter={setFilter}
                chartType={chartType}
                setChartType={setChartType}
                chartFilter={chartFilter}
                setChartFilter={setChartFilter}
                t={t}
            />

            <BlockedDownloadLaporanPopup ref={blockedDownloadPopupRef} confirmHandle={() => redirectSupport()} />
        </React.Fragment>
    );
}

VarianSales.propTypes = {
    router: PropTypes.shape({
        push: PropTypes.func,
    }),
    location: PropTypes.shape({
        pathname: PropTypes.string.isRequired,
    }).isRequired,
};

VarianSales.defaultProps = {
    router: {
        push: () => { },
    },
};

const mapStateToProps = state => ({
    logo: state.user.profile.user_usaha_logo_path,
    listCabang: state.branch.list,
    namaUser: state.user.profile.user_name,
    namaUsaha: state.user.profile.user_usaha_name,
    selectedCabang: state.branch.filter,
    supportNeeded: state.layout.supportNeeded,
});

export default connect(mapStateToProps)(CoreHOC(VarianSales));
