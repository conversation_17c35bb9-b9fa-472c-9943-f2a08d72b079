import React from 'react';
import PropTypes from 'prop-types';
import { currency } from '../../../../../utils/helper';
import { CardWrapper } from '../../../../../components/retina';

const SummarySection = ({ summaryData, t }) => {
  const data = [
    {
        id: 'summary-total-offline',
      color: 'green',
      label: t('summary.offline', 'Total Penerimaan Offline'),
      description: currency({ value: summaryData.total_offline, decimal: true }),
    },
    {
      id: 'summary-total-online',
      color: 'blue',
      label: t('summary.online', 'Total Penerimaan Online'),
      description: currency({ value: summaryData.total_online, decimal: true }),
    },
    {
      id: 'summary-total-marketplace',
      color: 'purple',
      label: t('summary.ecommerce', 'Total Penerimaan e-Commerce'),
      description: currency({ value: summaryData.total_marketplace, decimal: true }),
    },
  ];

  return (<CardWrapper data={data} />);
};

SummarySection.propTypes = {
  summaryData: PropTypes.objectOf(PropTypes.number).isRequired,
  t: PropTypes.func.isRequired,
};

export default SummarySection;