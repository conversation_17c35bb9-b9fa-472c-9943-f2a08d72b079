const orderTypeOptionLegacy = [
    ['', 'Semua <PERSON> Order'],
    ['23', '<PERSON><PERSON><PERSON><PERSON>'],
    ['2', 'Bungkus'],
    ['4', 'Delivery'],
    ['3', 'Drive Thru'],
    ['10', 'Free Table'],
    ['16', 'Grabfood'],
    ['7', '<PERSON><PERSON>'],
    ['99', '<PERSON>nny<PERSON>'],
    ['6', 'Ojek Online'],
    ['19', 'Shopee'],
    ['1', 'Table'],
    ['15', 'Tokopedia'],
    ['24', 'Web Order'],
];

export const orderTypeOption = [
    {
        name: 'Semua Jenis Order',
        value: '',
    },
    {
        name: 'Table',
        value: '1',
    },
    {
        name: '<PERSON>ung<PERSON>',
        value: '2',
    },
    {
        name: 'Drive Thru',
        value: '3',
    },
    {
        name: 'Delivery',
        value: '4',
    },
    {
        name: 'Ojek Online',
        value: '6',
    },
    {
        name: '<PERSON><PERSON>',
        value: '7',
    },
    // hide filter for old data
    // {
    //     name: 'Go-Food',
    //     value: '8',
    // },
    {
        name: 'Grab Food',
        value: '16',
    },
    {
        name: 'Free Table',
        value: '10',
    },
    {
        name: 'Reservasi',
        value: '11',
    },
    {
        name: 'Parcel',
        value: '12',
    },
    {
        name: 'Self Order',
        value: '13',
    },
    {
        name: 'Consumer',
        value: '14',
    },
    {
        name: 'Tokopedia',
        value: '15',
    },
    {
        name: 'Grabfood Marketplace',
        value: '16',
    },
    {
        name: 'Grabfood Take Away',
        value: '17',
    },
    {
        name: 'Grabfood Delivered by Grab',
        value: '18',
    },
    {
        name: 'Shopee',
        value: '19',
    },
    {
        name: 'Web Order Take Away',
        value: '20',
    },
    {
        name: 'Web Order Serve to Table',
        value: '21',
    },
    {
        name: 'Web Order Delivery',
        value: '22',
    },
    {
        name: 'Bukalapak',
        value: '23',
    },
    {
        name: 'Web Order',
        value: '24',
    },
    {
        name: 'Grabmart',
        value: '25',
    },
    {
        name: 'Web Order Dinein',
        value: '26',
    },
    {
        name: 'Gofood',
        value: '27',
    },
    {
        name: 'Gofood Takeaway',
        value: '28',
    },
    {
        name: 'Gofood Delivered',
        value: '29',
    },
    {
        name: 'Web Order Dine in di Depan',
        value: '30',
    },
    {
        name: 'Web Order Kurir Outlet (Internal)',
        value: '31',
    },
    {
        name: 'Consumer Apps Take Away',
        value: '32',
    },
    {
        name: 'Consumer Apps Take Delivery',
        value: '33',
    },
    {
        name: 'Web Store',
        value: '34',
    },
    {
        name: 'Consumer Apps Kurir Outlet (Internal)',
        value: '35',
    },
    {
        name: 'Reservasi Jasa',
        value: '36',
    },
    {
        name: 'Quick Service',
        value: '39',
    },
    {
        name: 'Supplies',
        value: '60',
    },
    {
        name: 'Lainnya',
        value: '99',
    },
];

export const orderTypeOptionTranslate = t => orderTypeOption.map(o => ({ ...o, name: t(o.name, { ns: 'Penjualan/orderType', defaultValue: o.name }) }));

export const getNameOrderTypeLegacy = (id = '') => {
    if (!id) return 'Semua';

    const data = orderTypeOptionLegacy.find(val => val[0] === id);

    return data[1];
};

export const orderProductType = t => [
    {
        name: t('filter.productTypeOpt.1', 'Semua Jenis Produk'),
        value: '',
    },
    {
        name: t('filter.productTypeOpt.2', 'Produk Satuan'),
        value: '0',
    },
    {
        name: t('filter.productTypeOpt.3', 'Produk Paket'),
        value: '3',
    },
]
