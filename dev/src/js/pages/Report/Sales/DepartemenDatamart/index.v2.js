import React, { useState, useContext } from 'react';
import { connect } from 'react-redux';
import moment from 'moment';
import {
    Flex,
    Paper,
    Separator,
    Table,
    ToastContext,
    PageDialog,
    Box,
    FormGroup,
    FormLabel,
    InputDatePicker,
    InputDateRange,
    RANGE_PICKER,
    Button,
    DialogClose,
} from '@majoo-ui/react';
import { Trans, useTranslation } from 'react-i18next';
import { TitleSection } from '../Branch/components';
import { currency, numSeparator, resetDateRangeHelper } from '../../../../utils/helper';
import CoreHOC from '../../../../core/CoreHOC';
import * as reportApi from '../../../../data/reports';
import { tableMeta } from './settings/table';
import { useMediaQuery } from '../../../../utils/useMediaQuery';
import { BannerText, CardWrapper } from '../../../../components/retina';
import { downloadTypePdf } from '../../../../components/retina/export/utils';
import { FilterSection } from './components/FilterSection';

function ReportPenjualanDepartment({ calendar, assignCalendar, hideProgress, filterBranch, ...props }) {
    const { t } = useTranslation(['Report/Sales/department', 'translation']);
    const { addToast } = useContext(ToastContext);
    const isMobile = useMediaQuery('(max-width: 767px)');
    const [date, setDate] = useState([
        moment(calendar.start, 'DD/MM/YYYY').toDate(),
        moment(calendar.end, 'DD/MM/YYYY').toDate(),
    ]);

    const [data, setData] = useState([]);
    const [dataTemp, setDataTemp] = useState([]);
    const [summary, setSummary] = useState({});
    const [summaryTemp, setSummaryTemp] = useState({});
    const [search, setSearch] = useState('');
    const [pageIndex, setPageIndex] = useState(0);
    const [totalData, setTotalData] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const [pageLimit, setPageLimit] = React.useState(10);
    const [sortData, setSortData] = useState({ sort: '', order: '' });
    const [openFilter, setOpenFilter] = useState(false);
    const [tempCal, setTempCal] = useState(date);
    const [dateRangeKey, setDateRangeKey] = useState(0);

    const changeDate = val => {
        const { newStartDate, newEndDate, isForceReset } = resetDateRangeHelper(val[0], val[1], 366, true, 12);
        if (isForceReset) {
            addToast({
                title: t('toast.errorMaxDate', 'Terjadi Kesalahan!', { ns: 'translation' }),
                description: t('toast.errorMaxDateRangeYear', 'Maksimum rentang waktu yang dapat dipilih: 1 tahun', {
                    ns: 'translation',
                }),
                variant: 'pending',
            });
            const startDate = moment(newStartDate, 'DD/MM/YYYY').toDate();
            const endDate = moment(newEndDate, 'DD/MM/YYYY').toDate();
            setDate([startDate, endDate]);
            assignCalendar(startDate, endDate);
        } else {
            setDate(val);
            assignCalendar(val[0], val[1]);
        }
    };

    const fetchData = async val => {
        const { pageSize, pageIndex = 0, sortAccessor, sortDirection } = val;
        try {
            setIsLoading(true);
            const payload = {
                start_date: moment(date[0]).format('YYYY-MM-DD'),
                end_date: moment(date[1]).format('YYYY-MM-DD'),
                limit: val.pageLimit || pageSize || 10,
                page: pageIndex + 1 || 1,
                ...(sortDirection && {
                    sort: sortDirection,
                }),
                ...(sortAccessor && {
                    order: sortAccessor,
                }),
                ...(filterBranch &&
                    filterBranch.length > 0 && {
                        id_outlet: filterBranch,
                    }),
                ...(search &&
                    search.length > 0 && {
                        search,
                    }),
            };

            const res = await reportApi.getDepartmentSalesDatamart(payload);

            const { meta, summary } = res;

            const totalProduct = res.data.reduce(
                (acc, curr) => parseFloat(acc, 10) + parseFloat(curr.product_cnt, 10),
                0,
            );
            const totalOmzet = res.data.reduce((acc, curr) => parseFloat(acc, 10) + parseFloat(curr.omzet, 10), 0);

            const reportData = res.data.map(x => ({
                ...x,
                jumlah_produk: x.product_cnt,
                produk_percentage:
                    totalProduct > 0 ? parseFloat(((x.product_cnt / totalProduct) * 100).toFixed(2), 10) : 0,
                omzet_percentage: totalOmzet > 0 ? parseFloat(((x.omzet / totalOmzet) * 100).toFixed(2), 10) : 0,
            }));

            setData(reportData);
            setDataTemp(reportData);
            setSummary({
                total_produk: summary.total,
                total_omzet: summary.price,
            });
            setSummaryTemp({
                total_produk: summary.total,
                total_omzet: summary.price,
            });
            setTotalData(meta.total);
            setPageIndex(Number(meta.current_page) - 1);
            setSortData({ sort: val.sortDirection, order: val.sortAccessor });
            // setSummary(res.summary); TODO: summary ga dipakai?
        } catch (error) {
            // addToast({
            //   title: 'Gagal!',
            //   description: 'Laporan Penjualan Kategori gagal di dapatkan',
            //   variant: 'failed',
            //   position: 'top-right',
            // });
        } finally {
            setIsLoading(false);
            hideProgress();
            if (pageSize) setPageLimit(pageSize);
        }
    };

    const fetchDataTable = React.useCallback(fetchData, [pageIndex, search, filterBranch, date]);

    React.useEffect(() => {
        fetchDataTable({ pageLimit });
    }, [date, setDate, search, filterBranch]);

    const downloadLaporan = async (type = 'xlsx') => {
        const { listCabang, selectedCabang, namaUsaha, logo, showProgress } = props;

        const outlet =
            listCabang.length > 0
                ? listCabang.find(item => item.id_cabang === selectedCabang).cabang_name
                : 'Semua Outlet';

        const payload = {
            start_date: moment(date[0]).format('YYYY-MM-DD'),
            end_date: moment(date[1]).format('YYYY-MM-DD'),
            report_type: type,
            ...(selectedCabang && { outlet_id: Number(selectedCabang) }),
            ...(search && { search }),
            ...(sortData.sort && {
                sort: sortData.sort.toLowerCase() === 'desc' ? `-${sortData.order}` : sortData.order,
            }),
        };

        try {
            showProgress();

            const res = await reportApi.generateDepartmentReportV2(payload);

            if (type === 'xlsx') window.location = res.data;
            else downloadTypePdf(res.data);

            addToast({
                title: t('translation:toast.success'),
                description: (
                    <Trans t={t} i18nKey="Report/Sales/department:modal.toast.success" components={{ bold: <b /> }} />
                ),
                variant: 'success',
                position: 'top-right',
            });
        } catch (e) {
            addToast({
                title: t('translation:toast.error'),
                description: (
                    <Trans t={t} i18nKey="Report/Sales/department:modal.toast.failed" components={{ bold: <b /> }} />
                ),
                variant: 'failed',
                position: 'top-right',
            });
        } finally {
            hideProgress();
        }
    };

    const resetFilter = () => {
        setDateRangeKey(current => current + 1);
        setTempCal([moment(calendar.start, 'DD/MM/YYYY').toDate(), moment(calendar.end, 'DD/MM/YYYY').toDate()]);
    };

    const applyFilter = () => {
        changeDate(tempCal);
        setOpenFilter(false);
    };

    return (
        <Flex
            css={{
                flexDirection: 'column',
                gap: '$comfortable',
                '@md': { marginBottom: '28px' },
            }}
        >
            <Paper responsive>
                <Flex css={{ flexDirection: 'column', rowGap: '$compact' }}>
                    <TitleSection
                        title={t('Report/Sales/department:departmentSales')}
                        reportTitle={t('modal.export.reportTitle', 'Laporan Penjualan Departemen')}
                        date={date}
                        onDownloadLaporan={downloadLaporan}
                        hideComparison
                        t={t}
                        isPrimary
                    />
                    <Separator />
                    <BannerText css={{ my: 0 }} />
                    <FilterSection
                        isMobile={isMobile}
                        t={t}
                        setSearch={setSearch}
                        date={date}
                        changeDate={changeDate}
                        setTempCal={setTempCal}
                        setOpenFilter={setOpenFilter}
                        onDownloadLaporan={downloadLaporan}
                        reportTitle={t('modal.export.reportTitle', 'Laporan Penjualan Departemen')}
                        title={t('Report/Sales/department:departmentSales')}
                    />
                    <Separator />
                    <CardWrapper
                        data={[
                            {
                                color: 'green',
                                label: t('Report/Sales/department:summary.totalDepartment', 'Total Departemen'),
                                description:
                                    (summaryTemp &&
                                        summaryTemp.total_produk &&
                                        numSeparator(summaryTemp.total_produk)) ||
                                    0,
                            },
                            {
                                color: 'blue',
                                label: t(
                                    'Report/Sales/department:summary.totalDepartmentSales',
                                    'Total Penjualan Departemen',
                                ),
                                description:
                                    (summaryTemp &&
                                        summaryTemp.total_omzet !== undefined &&
                                        currency({
                                            value: summaryTemp.total_omzet,
                                            decimal: summaryTemp.total_omzet !== 0,
                                            convertMinus: true,
                                        })) ||
                                    0,
                            },
                        ]}
                    />
                    <Table
                        id="report_product_deptsales_dtm"
                        data={dataTemp}
                        columns={tableMeta(t)}
                        totalData={totalData}
                        pageIndex={pageIndex}
                        isLoading={isLoading}
                        searchQuery={search}
                        fetchData={fetchDataTable}
                        css={{ padding: 0 }}
                    />
                </Flex>
            </Paper>
            {openFilter && (
                <PageDialog open={openFilter} onOpenChange={setOpenFilter}>
                    <PageDialog.Title>Filter</PageDialog.Title>
                    <PageDialog.Content>
                        <Box css={{ display: 'grid', gap: '$compact', padding: '$compact' }}>
                            <FormGroup>
                                <FormLabel css={{ color: '$textPrimary' }}>
                                    {t('translation:label.selectDate')}
                                </FormLabel>
                                <InputDatePicker
                                    size="lg"
                                    type={RANGE_PICKER}
                                    onChange={value => {
                                        setDateRangeKey(current => current + 1);
                                        setTempCal(value);
                                    }}
                                    date={tempCal}
                                />
                            </FormGroup>
                            <FormGroup>
                                <FormLabel css={{ color: '$textPrimary' }}>
                                    {t('translation:label.dateRange')}
                                </FormLabel>
                                <InputDateRange
                                    key={dateRangeKey}
                                    size="lg"
                                    onChange={value => setTempCal(value)}
                                    optionCss={{ maxHeight: 150 }}
                                />
                            </FormGroup>
                        </Box>
                    </PageDialog.Content>
                    <PageDialog.Footer css={{ gap: '$compact' }}>
                        <Button size="md" buttonType="ghost" css={{ flex: '1' }} onClick={() => resetFilter()}>
                            Reset
                        </Button>
                        <DialogClose asChild>
                            <Button size="md" css={{ flex: 1 }} onClick={() => applyFilter()}>
                                {t('translation:label.apply')}
                            </Button>
                        </DialogClose>
                    </PageDialog.Footer>
                </PageDialog>
            )}
        </Flex>
    );
}

const mapStateToProps = state => ({
    isFetching: state.report.isFetching,
    logo: state.user.profile.user_usaha_logo_path,
    listCabang: state.branch.list,
    namaUser: state.user.profile.user_name,
    namaUsaha: state.user.profile.user_usaha_name,
    selectedCabang: state.branch.filter,
    supportNeeded: state.layout.supportNeeded,
});

export default connect(mapStateToProps)(CoreHOC(ReportPenjualanDepartment));
