import React, { useState, useEffect, useCallback } from 'react';
import { foundations } from '@majoo-ui/core';
import moment from 'moment';
import { debounce } from 'lodash';

import usePrevious from '~/utils/usePrevious';
import { getKategoriProduk, getProduct } from '../../../../../../data/product';
import { getDepartemen } from '../../../../../../data/department';
import { getTransactionType } from '../../../../../../data/transactionType';

import * as reportApi from '../../../../../../data/reports';

import { useSearchState } from '../../../../../../hooks/useSearchState';
import { useGraphicPeriodState } from '../../../../../../hooks/useGraphicPeriodeState';
import { typeList, PRODUCT_TYPE, PRODUCT_DISPLAY_TYPE, SORT_ORDER, ALL_ITEM, ITEM_BUNDLE } from '../../settings/utils.datamart';

import { catchError } from '../../../../../../utils/helper';
import {
    fillDataMonthly,
    fillDataWeekly,
    fillDataDaily,
    fillDataHourly,
} from '../../../../../../components/chart';

const defaults = t => ({
    categories: [{ value: '', name: t('filter.categories', 'Semua Kategori') }],
    productTypes: [{ value: '', name: t('filter.productTypeOpt.1', 'Semua Jenis Produk') }],
    departments: [{ value: '', name: t('filter.departments', 'Semua Departemen') }],
    orderTypes: [{ value: '', name: t('filter.orderTypeOpt.default', 'Semua Jenis Order') }],
});

const formatArrDate = calendar => [
    moment(calendar.start, 'DD/MM/YYYY').toDate(),
    moment(calendar.end, 'DD/MM/YYYY').toDate(),
];

const { colors } = foundations;

const chartColor = Object.keys(colors)
    .filter(key => key.includes('chart'))
    .reduce((prev, current) => [...prev, colors[current]], []);

const initAdditionalFilters = t => ({
    category: '',
    productType: {
        name: t('filter.productTypeOpt.1', 'Semua Jenis Produk'),
        value: '',
    },
    department: '',
    orderType: {
        name: t('filter.orderTypeOpt.default', 'Semua Jenis Order'),
        value: '',
    },
})

export const useProductSalesReport = params => {
    const {
        calendar,
        filterBranch,
        showProgress,
        hideProgress,
        addNotification,
        getContentTranslation,
        addToast,
        setDialogInfoExport,
        t,
        Trans,
        ready,
    } = params;
    const prevCalendar = usePrevious(calendar);
    const [isLoading, setLoading] = useState(true);

    const [arrDateRange, setArrDateRange] = useState(formatArrDate(calendar)); // TODO: this handler to sync format data with non datamart components, if non datamart is removed, should directly use calendar props
    /**
     * filter page state
     */
    const { keyword, onKeywordChange } = useSearchState();
    const [additionalFilters, setAdditionalFilters] = useState(initAdditionalFilters(t));
    const filterDefaults = defaults(t);
    const [categories, setCategories] = useState(filterDefaults.categories);
    /** productType options state directly handled in filter component */
    const [departments, setDepartments] = useState(filterDefaults.departments);
    const [orderTypes, setOrderTypes] = useState(filterDefaults.orderTypes);

    const onCategoryChange = param =>
        setAdditionalFilters(curr => ({
            ...curr,
            category: param.value,
        }));
    const onProductTypeChange = param =>
        setAdditionalFilters(curr => ({
            ...curr,
            productType: param,
        }));
    const onDepartmentChange = param =>
        setAdditionalFilters(curr => ({
            ...curr,
            department: param.value,
        }));
    const onOrderTypeChange = param =>
        setAdditionalFilters(curr => ({
            ...curr,
            orderType: param,
        }));

    /**
     * filter chart state
     */
    const { periodOptions, period, onPeriodChange } = useGraphicPeriodState({ calendar });
    const [groupBy, setGroupBy] = useState('penjualan');
    const [groupByOptions, setGroupByOptions] = useState(typeList(t));

    const onGroupByChange = param => setGroupBy(param.value);

    /**
     * chart state
     */
    const [chart, setChart] = useState({
        comparatorList: [],
        data: [],
        legend: [],
    });
    const [triggerReloadChart, setTriggerReloadChart] = useState(0);
    const [tempChart, setTempChart] = useState({
        comparatorList: [],
        legend: [],
    });

    /**
     * table
     */
    const [table, setTable] = useState({
        pageIndex: 0,
        limit: 10,
        total: 0,
        sortAccessor: undefined,
        sortDirection: undefined,
        data: [],
        summary: [],
    });

    // has item bundles
    const [hasItemBundles, setHasItemBundles] = useState(false);
    const [productType, setProductType] = useState(ALL_ITEM);

    // TODO: set await all: categories, departments, orderTypes instead of await 1 by 1
    const fetchCategories = async () => {
        try {
            const response = await getKategoriProduk();
            const categoryOptions = response.data.reduce(
                (acc, category) => [
                    ...acc,
                    {
                        value: category.id_category_item,
                        name: category.category_item_name,
                    },
                ],
                filterDefaults.categories,
            );
            setCategories(categoryOptions);
        } catch (e) {
            addToast({ title: t('toast.error', { ns: 'translation' }), variant: 'failed', description: catchError(e) });
        }
    };

    const fetchDepartments = async () => {
        try {
            const response = await getDepartemen({
                is_report: 1,
            });
            const departmentOptions = response.data.reduce(
                (acc, department) => [
                    ...acc,
                    {
                        value: department.id,
                        name: department.name,
                    },
                ],
                filterDefaults.departments,
            );
            setDepartments(departmentOptions);
        } catch (e) {
            addToast({ title: t('toast.error', { ns: 'translation' }), variant: 'failed', description: catchError(e) });
        }
    };

    const fetchOrderTypes = async () => {
        try {
            const res = await getTransactionType();
            const { data = [] } = res;
            setOrderTypes([
                ...[{ value: '', name: t('translation:select.allOrderType', 'Semua Jenis Order') }],
                ...data.map(x => ({ ...x, value: String(x.id), name: getContentTranslation(x.key) })),
            ]);
        } catch (e) {
            addNotification({
                title: t('toast.error', { ns: 'translation' }),
                message: catchError(e),
                level: 'error',
            });
        }
    };

    const fetchDataTable = useCallback(
        async tableParams => {
            const { pageIndex = 0, pageSize = 10, sortAccessor = 'qty', sortDirection = 'DESC' } = tableParams ?? {};
            setLoading(true);
            const payloadTable = {
                start_date: moment(calendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                end_date: moment(calendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                page: pageIndex + 1,
                limit: pageSize,
                order: sortAccessor && SORT_ORDER[sortAccessor] ? SORT_ORDER[sortAccessor] : sortAccessor,
                sort: sortDirection,
                ...(additionalFilters.category && {
                    category_id: additionalFilters.category,
                }),
                ...(additionalFilters.productType.value && {
                    product_type: additionalFilters.productType.value,
                }),
                ...(additionalFilters.department && {
                    department_id: additionalFilters.department,
                }),
                ...(additionalFilters.orderType.value && {
                    order_type_id: additionalFilters.orderType.value,
                }),
                ...(filterBranch && {
                    outlet_id: filterBranch,
                }),
                ...(keyword && {
                    search: keyword,
                }),
            };
            try {
                const getItemReport = productType === ITEM_BUNDLE ? reportApi.getReportProductBundles : reportApi.getItemSalesReportDatamart;
                const tableReports = await getItemReport(payloadTable);
                const { product_sales: productSales } = tableReports.data;
                setTable(curr => ({
                    ...curr,
                    pageIndex: tableReports.meta.current_page - 1,
                    limit: tableReports.meta.per_page,
                    total: tableReports.meta.total,
                    data: productSales,
                    summary: tableReports.data.summary,
                    sortAccessor,
                    sortDirection,
                }));

                if (pageIndex === 0) {
                    let legend = [];
                    if (productSales.length > 0) {
                        let index = 0;
                        let legendLength = 0;

                        while (index < productSales.length && legendLength < 5) {
                            const itemProduct = productSales[index];

                            if (!legend.find(item => item.value === +itemProduct.product.id)) {
                                legend = [
                                    ...legend,
                                    {
                                        dataKey: itemProduct.product.id,
                                        name: itemProduct.product.name,
                                        color: chartColor[index % chartColor.length],
                                        isComparator: true,
                                        value: itemProduct.product.id,
                                        variants: itemProduct.detail_variants,
                                        isCurrency: groupBy === 'penjualan',
                                    },
                                ];
                                legendLength += 1;
                            }
                            index += 1;
                        }
                    }
                    setChart({
                        comparatorList: [],
                        data: [],
                        legend: [],
                    });
                    setTempChart({
                        comparatorList: legend,
                        legend,
                    });
                    setTriggerReloadChart(prev => prev + 1);
                }
            } catch (e) {
                addToast({ title: t('toast.somethingWrong', { ns: 'translation' }, 'Terjadi Kesalahan'), variant: 'failed', description: catchError(e) });
            } finally {
                setLoading(false);
            }
        },
        [
            table.pageIndex,
            table.limit,
            keyword,
            calendar,
            additionalFilters.category,
            additionalFilters.productType.value,
            additionalFilters.department,
            additionalFilters.orderType.value,
            filterBranch,
            productType,
        ],
    );

    const fetchLineChart = async () => {
        const payloadLineChart = {
            start_date: moment(calendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            end_date: moment(calendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            group: period,
            // type: groupBy, // TODO: need to check since this not effect the API response since alway return sales, and quantity, this should only impact to local
            ...(filterBranch && {
                outlet_id: filterBranch,
            }),
            product_ids: tempChart.legend.map(legend => legend.value).join(),
        };

        try {
            showProgress();
            const response = await reportApi.getItemSalesChartReportDatamart(payloadLineChart);
            const filterProduct = response.data.map(productResponse => ({
                ...productResponse,
                product_amount: productResponse.qty,
                product_id: productResponse.product.id,
                product_name: productResponse.product.name,
                product_price: productResponse.price,
                product_variant: productResponse.detail_variants,
            }));

            const getDataPerProduct = filterProduct.reduce((prev, current) => {
                const match = prev.find(p => p.date === current.date);
                if (match) {
                    Object.assign(match, {
                        ...{
                            [current.product_id]:
                                current[groupByOptions.find(chartType => chartType.value === groupBy).typeParams],
                        },
                    });
                } else {
                    prev.push({
                        date: current.date,
                        [current.product_id]:
                            current[groupByOptions.find(chartType => chartType.value === groupBy).typeParams],
                    });
                }
                return prev;
            }, []);

            const startDate = arrDateRange[0].setHours(0, 0, 0, 0);
            const endDate = arrDateRange[1].setHours(0, 0, 0, 0);
            if (period === 'hour') {
                setChart({
                    legend: tempChart.legend,
                    comparatorList: tempChart.comparatorList,
                    data: fillDataHourly(startDate, endDate, 'date', getDataPerProduct, tempChart.legend),
                });
            } else if (period === 'day') {
                setChart({
                    legend: tempChart.legend,
                    comparatorList: tempChart.comparatorList,
                    data: fillDataDaily(startDate, endDate, 'date', getDataPerProduct, tempChart.legend),
                });
            } else if (period === 'week') {
                setChart({
                    legend: tempChart.legend,
                    comparatorList: tempChart.comparatorList,
                    data: fillDataWeekly(startDate, endDate, 'date', getDataPerProduct, tempChart.legend),
                });
            } else if (period === 'month') {
                setChart({
                    legend: tempChart.legend,
                    comparatorList: tempChart.comparatorList,
                    data: fillDataMonthly(startDate, endDate, 'date', getDataPerProduct, tempChart.legend),
                });
            } else if (response.data.length > 0) {
                setChart({
                    legend: tempChart.legend,
                    comparatorList: tempChart.comparatorList,
                    data: getDataPerProduct,
                });
            } else {
                setChart({
                    legend: tempChart.legend,
                    comparatorList: tempChart.comparatorList,
                    data: [{ date: moment(startDate).format('YYYY') }],
                });
            }
        } catch (e) {
            addToast({ title: t('toast.somethingWrong', { ns: 'translation' }, 'Terjadi Kesalahan'), variant: 'failed', description: catchError(e) });
        } finally {
            hideProgress();
        }
    }

    const fetchProduct = useCallback(async (search = '') => {
        if (!search) {
            return;
        }
        const payload = {
            item_type: PRODUCT_TYPE.FINISHED,
            displayed: PRODUCT_DISPLAY_TYPE.SHOW_ITEM_DISPLAYED_AND_ITEM_NOT_DISPLAYED,
            search,
            product_type: productType === ALL_ITEM ? '0, 3' : productType,
        };
        try {
            const response = await getProduct(payload);

            setChart(curr => ({
                ...curr,
                comparatorList: response.data
                    .filter(item => item.is_variant === 0)
                    .map(item => ({
                        name: item.name,
                        value: +item.id,
                        variants: item.variants,
                    })),
            }));
        } catch (e) {
            addToast({ title: t('toast.somethingWrong', { ns: 'translation' }, 'Terjadi Kesalahan'), variant: 'failed', description: catchError(e) });
        }
    }, [productType]);

    const handleProductSearch = useCallback(
        debounce(value => fetchProduct(value), 1000),
        [productType],
    );

    const handleComparator = useCallback(
        value => {
            switch (value.type) {
                case 'ADD': {
                    setTempChart(curr => ({
                        ...curr,
                        legend: [
                            ...curr.legend,
                            {
                                dataKey: value.payload.value,
                                name: value.payload.name,
                                color: chartColor[curr.legend.length % chartColor.length],
                                isComparator: true,
                                value: value.payload.value,
                                variants: value.payload.variants,
                                isCurrency: groupBy === 'penjualan',
                            },
                        ],
                    }));
                    setTriggerReloadChart(prev => prev + 1);
                    break;
                }
                case 'DELETE': {
                    setTempChart(curr => ({
                        ...curr,
                        legend: curr.legend.filter(legendItem => legendItem.value !== value.payload.value).map((x, i) => ({ ...x, color: chartColor[i] })),
                    }));
                    setChart(curr => ({
                        ...curr,
                        legend: curr.legend.filter(legendItem => legendItem.value !== value.payload.value).map((x, i) => ({ ...x, color: chartColor[i] })),
                    }));
                    break;
                }
                case 'SEARCH':
                    handleProductSearch(value.payload);
                    break;
                default:
                    throw Error();
            }
        },
        [chart.legend],
        groupBy,
    );

    const downloadLaporan = async (reportType = 'xlsx') => {
        try {
            showProgress();
            const payloadDownload = {
                start_date: moment(calendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                end_date: moment(calendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                sort: !table.sortAccessor ? 'qty' : `${table.sortDirection === 'DESC' ? '-' : ''}${table.sortAccessor}`,
                ...(additionalFilters.category && {
                    category_id: +additionalFilters.category,
                }),
                ...(additionalFilters.productType.value && {
                    product_type: +additionalFilters.productType.value,
                }),
                ...(additionalFilters.department && {
                    department_id: +additionalFilters.department,
                }),
                ...(additionalFilters.orderType.value && {
                    order_type_id: +additionalFilters.orderType.value,
                }),
                ...(filterBranch && {
                    outlet_id: filterBranch,
                }),
                ...(keyword && {
                    search: keyword,
                }),
                ...(filterBranch && {
                    outlet_id: filterBranch,
                }),
                report_type: reportType,
                report_version: 2,
            };
            await reportApi.downloadItemSalesRequestReportDatamart(payloadDownload);
            setDialogInfoExport({
                isDialog: true,
                state: {
                    title: <Trans t={t} i18nKey="modal.label" defaults="Ekspor Penjualan Produk" />,
                    description: (
                        <Trans
                            t={t}
                            i18nKey="modal.descriptionRequest"
                            defaults="Mohon menunggu, sistem sedang memproses Laporan <strong>Penjualan Produk</strong>. Anda akan menerima pemberitahuan notifikasi dan email saat data siap untuk diunduh."
                            components={{ strong: <b /> }}
                        />
                    ),
                    btnCloseOnly: false,
                },
            });
        } catch (error) {
            if (error.cause && error.cause.status.code) {
                if (Number(error.cause.status.code) === 42200001) {
                    setDialogInfoExport({
                        isDialog: true,
                        state: {
                            title: <Trans t={t} i18nKey="label.exportReport" defaults="Ekspor Laporan" />,
                            description: error.cause.status.message,
                            btnCloseOnly: true,
                        },
                    });
                    return;
                }
            }
            addNotification({
                title: t('toast.error', { ns: 'translation' }),
                message: (
                    <Trans t={t} i18nKey="toast.exportFailed">
                        Laporan <b>Penjualan Produk</b> gagal diekspor
                    </Trans>
                ),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    const checkHasItemBundles = async () => {
        try {
            const { data } = await reportApi.checkMerchantHasItemBundles();
            setHasItemBundles(data && data.merchant_has_item_bundles);
        } catch (e) {
            addToast({ title: t('toast.error', { ns: 'translation' }), variant: 'failed', description: catchError(e) });
        }
    }

    useEffect(() => {
        fetchCategories();
        fetchDepartments();
        fetchOrderTypes();
        setAdditionalFilters(curr => ({
            ...curr,
            productType: { ...curr.productType, name: t('filter.productTypeOpt.1', 'Semua Jenis Produk') },
            orderType: { ...curr.orderType, name: t('filter.orderTypeOpt.default', 'Semua Jenis Order') },
        }));
        setGroupByOptions(typeList(t));
        checkHasItemBundles();
    }, [ready]);

    useEffect(() => {
        if (productType === ITEM_BUNDLE && JSON.stringify(initAdditionalFilters(t)) !== JSON.stringify(additionalFilters)) setAdditionalFilters(initAdditionalFilters(t));
        else fetchDataTable();
        if (prevCalendar !== calendar) {
            setArrDateRange(formatArrDate(calendar));
        }
    }, [
        keyword,
        calendar,
        additionalFilters.category,
        additionalFilters.productType.value,
        additionalFilters.department,
        additionalFilters.orderType.value,
        filterBranch,
        productType,
    ]);

    useEffect(() => {
        if (tempChart.legend.length > 0) {
            fetchLineChart();
        } else {
            setChart({
                comparatorList: [],
                data: [],
                legend: [],
            });
        }
    }, [
        triggerReloadChart,
        period,
        groupBy,
    ]);

    useEffect(() => {
        if (isLoading) {
            showProgress();
        } else {
            hideProgress();
        }
    }, [isLoading]);

    return {
        /* for UI / UX */
        arrDateRange,
        /* filters */
        onKeywordChange,
        additionalFilters,
        onCategoryChange,
        onProductTypeChange,
        onDepartmentChange,
        onOrderTypeChange,
        categories,
        departments,
        orderTypes,
        /* filter chart */
        periodOptions,
        period,
        onPeriodChange,
        groupBy,
        groupByOptions,
        onGroupByChange,
        handleComparator,
        /* chart */
        chart,
        /* table */
        table,
        /* actions */
        fetchDataTable,
        downloadLaporan,
        /* item bundle */
        hasItemBundles,
        productType,
        setProductType,
    };
};
