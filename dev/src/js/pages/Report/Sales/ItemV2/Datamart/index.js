import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import moment from 'moment';
import { Flex, Paper, Separator, Table, TableRow, TableCell, ToastContext, AlertDialog, Text } from '@majoo-ui/react';
import { Trans, useTranslation } from 'react-i18next';

import { TitleSection, CardSectionDatamart } from '../components';

import Filter from './components/FilterPage';
import FilterChart from './components/FilterChart';
import { handleXaxis, LineChartv2 } from '../../../../../components/chart';
import { ALL_ITEM, headerTable } from '../settings/utils.datamart';
import {
    resetDateRangeHelper,
    toTitleCase,
    formatCurrency,
    checkisValueExistOrEmpty,
} from '../../../../../utils/helper';
import { useMediaQuery } from '../../../../../utils/useMediaQuery';
import CoreHOC from '../../../../../core/CoreHOC';
import { BannerText } from '../../../../../components/retina';
import { useProductSalesReport } from './hooks/useProductSalesReport';

import styles from './styles';

const PenjualanProduk = ({
    calendar,
    filterBranch,
    assignCalendar,
    showProgress,
    hideProgress,
    addNotification,
    getContentTranslation,
    privilege,
}) => {
    const { addToast } = React.useContext(ToastContext);
    /* folder location -> dev\src\assets\locales */
    const { t, i18n, ready } = useTranslation(['Penjualan/Laporan/product', 'translation']);
    const currentLang = i18n.language;
    const isMobile = useMediaQuery('(max-width: 767px)');

    const [dialogInfoExport, setDialogInfoExport] = useState({
        isDialog: false,
        state: {
            title: '',
            description: '',
            btnCloseOnly: false,
        },
    });

    const {
        /* for UI / UX */
        arrDateRange,
        /* filters */
        onKeywordChange,
        additionalFilters,
        onCategoryChange,
        onProductTypeChange,
        onDepartmentChange,
        onOrderTypeChange,
        categories,
        departments,
        orderTypes,
        /* filter chart */
        periodOptions,
        period,
        onPeriodChange,
        groupBy,
        groupByOptions,
        onGroupByChange,
        handleComparator,
        /* chart */
        chart,
        /* table */
        table,
        /* actions */
        fetchDataTable,
        downloadLaporan,
        /* item bundle */
        hasItemBundles,
        productType,
        setProductType,
    } = useProductSalesReport({
        calendar,
        filterBranch,
        showProgress,
        hideProgress,
        addNotification,
        getContentTranslation,
        addToast,
        setDialogInfoExport,
        t,
        Trans,
        ready,
    });

    const handleChangeDateRange = value => {
        const { newStartDate, newEndDate, isForceReset } = resetDateRangeHelper(value[0], value[1], 366, true, 12);
        if (isForceReset) {
            addToast({
                title: t('toast.errorMaxDate', 'Terjadi Kesalahan!', { ns: 'translation' }),
                description: t('toast.errorMaxDateRangeYear', 'Maksimum rentang waktu yang dapat dipilih: 1 tahun', {
                    ns: 'translation',
                }),
                variant: 'pending',
            });
            assignCalendar(newStartDate, newEndDate);
        } else {
            assignCalendar(value[0], value[1]);
        }
    };

    const chartTypeLabel = toTitleCase(groupByOptions.find(chartType => chartType.value === groupBy).name);

    return (
        <Flex css={{ flexDirection: 'column', gap: '$comfortable' }}>
            <Paper
                css={{
                    marginBottom: '20px',
                    '@sm': {
                        padding: 0,
                        backgroundColor: 'transparent',
                        boxShadow: 'none',
                    },
                    '@md': {
                        padding: '$spacing-05',
                        backgroundColor: '$white',
                        boxShadow: '0px 2px 12px #00000014',
                    },
                }}
            >
                <Flex css={{ flexDirection: 'column', rowGap: '$compact' }}>
                    <TitleSection
                        title={t('header.title', 'Penjualan Produk')}
                        date={arrDateRange}
                        isMobile={isMobile}
                        onDownloadLaporan={downloadLaporan}
                        t={t}
                        isPrimary
                    />
                    <Separator css={{ '@sm': { display: 'none' }, '@md': { display: 'block' } }} />
                    <BannerText css={{ my: 0 }} />
                    <Filter
                        isMobile={isMobile}
                        t={t}
                        setFilterKeyword={onKeywordChange}
                        date={arrDateRange}
                        setCalendarDate={handleChangeDateRange}
                        categoryOptions={categories}
                        category={additionalFilters.category}
                        setCategory={onCategoryChange}
                        departmentOptions={departments}
                        department={additionalFilters.department}
                        setDepartment={onDepartmentChange}
                        orderTypeOptions={orderTypes}
                        filterOrderType={additionalFilters.orderType}
                        setFilterOrderType={onOrderTypeChange}
                        filterProductType={additionalFilters.productType}
                        setFilterProductType={onProductTypeChange}
                        lang={currentLang}
                        onDownloadLaporan={downloadLaporan}
                        hasBundle={hasItemBundles}
                        productType={productType}
                        setProductType={setProductType}
                    />

                    <Separator />

                    <LineChartv2
                        data={chart.data}
                        legendData={chart.legend}
                        period={period}
                        chartTypeLabel={chartTypeLabel}
                        xAxisKey="date"
                        title={t('chart.title', 'Grafik Penjualan Produk')}
                        comparatorList={chart.comparatorList}
                        customXAxisFormat={d => handleXaxis(d, period)}
                        customYAxisFormat={val =>
                            formatCurrency(val, { notation: 'compact', compactDisplay: 'short' })
                                .replace(chartTypeLabel !== 'Penjualan' || val === 0 ? 'Rp' : '', '')
                                .trim()
                        }
                        onComparatorChange={handleComparator}
                        placeholder={t('placeholder.search', { ns: 'translation' }, 'Cari...')}
                        isEmptyData={chart.legend.length <= 0}
                        emptyDataMessage={t('chart.emptyDataMessage', 'Produk tidak ditemukan')}
                    >
                        <FilterChart
                            periodOptions={periodOptions}
                            period={period}
                            setPeriod={onPeriodChange}
                            typeOptions={groupByOptions}
                            type={groupBy}
                            setType={onGroupByChange}
                        />
                    </LineChartv2>
                </Flex>

                <Flex css={styles.tableWrapperStyle}>
                    <CardSectionDatamart
                        summaryData={table.summary}
                        t={t}
                        privilegeComponents={privilege.components}
                        productType={productType}
                    />
                    <Table
                        id="report_product_productsales_dtm"
                        data={table.data}
                        columns={headerTable(t, productType === ALL_ITEM).filter(item =>
                            checkisValueExistOrEmpty(privilege.components, item.id),
                        )}
                        totalData={table.total}
                        fetchData={fetchDataTable}
                        pageIndex={table.pageIndex}
                        rowLimit={table.limit}
                        css={styles.tableStyle}
                        rowGroup={{
                            groupKey: 'childs',
                            showGroupingWhenSorted: true,
                            headerRender: row => {
                                const { parentOriginal } = row.original;
                                return (
                                    <TableRow
                                        {...row.getRowProps()}
                                        key={`${row.id}-group_header`}
                                        css={{
                                            backgroundColor: '$bgGray',
                                        }}
                                    >
                                        <TableCell
                                            css={{
                                                padding: '20px 8px',
                                                verticalAlign: 'center',
                                            }}
                                        >
                                            <Text variant="contentButton">{parentOriginal.product.name}</Text>
                                        </TableCell>
                                        <TableCell
                                            css={{
                                                padding: '20px 8px',
                                                verticalAlign: 'center',
                                            }}
                                        >
                                            <Text variant="contentButton">
                                                {t('headerTable.quantity', 'Penjualan')}
                                                {': '}
                                                {parentOriginal.qty}
                                            </Text>
                                        </TableCell>
                                        <TableCell
                                            colSpan={2}
                                            css={{
                                                padding: '20px 8px',
                                                verticalAlign: 'center',
                                            }}
                                        >
                                            <Text variant="contentButton">
                                                {t('headerTable.sales', 'Penjualan')}
                                                {': '}
                                                {formatCurrency(parentOriginal.price)}
                                            </Text>
                                        </TableCell>
                                        <TableCell
                                            colSpan={3}
                                            css={{
                                                padding: '20px 8px',
                                                verticalAlign: 'center',
                                            }}
                                        >
                                            <Text variant="contentButton">
                                                {t('headerTable.grossProfitGroup', 'Laba Kotor')}
                                                {': '}
                                                {formatCurrency(parentOriginal.gross_profit)}
                                            </Text>
                                        </TableCell>
                                    </TableRow>
                                );
                            },
                        }}
                    />
                    {table.summary.last_update !== null && (
                        <Text align="right" as="i">
                            {t('label.lastUpdated', {
                                ns: 'translation',
                                date: moment(table.summary.last_update, 'YYYY-MM-DD h:mm:ss').fromNow(),
                            })}
                        </Text>
                    )}
                </Flex>
                <AlertDialog
                    isMobile={isMobile}
                    onConfirm={() => {
                        setDialogInfoExport(prev => ({ ...prev, isDialog: false }));
                        addNotification({
                            title: t('toast.success', { ns: 'translation', defaultValue: 'Berhasil!' }),
                            message: (
                                <Trans t={t} i18nKey="toast.exportRequestSuccess" components={{ strong: <b /> }}>
                                    Laporan <strong>Penjualan Produk</strong> dalam proses ekspor
                                </Trans>
                            ),
                            level: 'success',
                        });
                    }}
                    onCancel={() => setDialogInfoExport(prev => ({ ...prev, isDialog: false }))}
                    open={dialogInfoExport.isDialog}
                    title={dialogInfoExport.state.title}
                    description={dialogInfoExport.state.description}
                    labelConfirm={<Trans t={t} i18nKey="translation:label.confirm" defaults="Oke, Mengerti" />}
                    labelCancel={<Trans t={t} i18nKey="translation:label.close" defaults="Tutup" />}
                    css={{
                        width: isMobile ? 'unset' : '422px',
                    }}
                    actionButtonProps={{ size: 'md' }}
                    cancelButtonProps={{ size: 'md' }}
                    singleButton={!dialogInfoExport.state.btnCloseOnly}
                    hideActionButton={dialogInfoExport.state.btnCloseOnly}
                />
            </Paper>
        </Flex>
    );
};

PenjualanProduk.propTypes = {
    assignCalendar: PropTypes.func.isRequired,
    filterBranch: PropTypes.string.isRequired,
    calendar: PropTypes.shape({
        start: PropTypes.string,
        end: PropTypes.string,
        rangeLimit: PropTypes.number,
        onchange: PropTypes.func,
    }).isRequired,
    showProgress: PropTypes.func.isRequired,
    hideProgress: PropTypes.func.isRequired,
    addNotification: PropTypes.func.isRequired,
    getContentTranslation: PropTypes.func.isRequired,
    privilege: PropTypes.shape({
        components: PropTypes.shape({}),
    }).isRequired,
};

const mapStateToProps = state => ({
    namaUsaha: state.user.profile.user_usaha_name,
    selectedCabang: state.branch.filter,
    listCabang: state.branch.list,
    supportNeeded: state.layout.supportNeeded,
    privilege: state.layouts.detailPrivilege,
});

export default CoreHOC(connect(mapStateToProps)(PenjualanProduk));
