import React from 'react';
import moment from 'moment';
import {
 formatHour,
 formatDay,
 formatYear,
 LineChartv2,
} from '../../../../../../components/chart';
import { formatCurrency, toTitleCase } from '../../../../../../utils/helper';
import { CHART_TYPE_DATAMART } from '../../../ItemV2/DataChart/enums';
import { useTranslationHook } from '../../lang.utils';
import { usePageContext } from '../context/pageContext';
import FilterChart from './FilterChart';

const createChartTypeLabel = (chartType, langData) => {
    let retval = '';

    switch (chartType) {
        case CHART_TYPE_DATAMART.TRANSACTION:
            retval = toTitleCase(langData.LEGEND_TRANSACTION);
            break;
        case CHART_TYPE_DATAMART.SALES:
            retval = toTitleCase(langData.LEGEND_SALES);
            break;
        default:
        // do nothing
    }

    return retval;
};

const customXAxisFormat = (date, period) => {
    switch (period) {
      case 'hour':
        return formatHour(date);
      case 'day':
        return formatDay(date);
      case 'week':
        return formatDay(date);
      case 'month':
        return moment(date).format('MMM YYYY');
      case 'year':
        return formatYear(date);
      default:
        throw new Error();
    }
};

const Chart = () => {
    const {
        chart, handleChangeComparator, periodOptions, period, onPeriodChange, groupByOptions, groupBy, onGroupByChange,
    } = usePageContext();

    const {
        initChart, chartData, chartLegend, comparatorList,
    } = chart;

    const { LANG_DATA } = useTranslationHook();

    const chartTypeLabel = createChartTypeLabel(groupBy, LANG_DATA);

    return (
        <LineChartv2
            key={`jenis-${chartData.length}-bayar-${period}-datamart`}
            data={chartData}
            chartTypeLabel={chartTypeLabel}
            legendData={chartLegend}
            period={period}
            xAxisKey="date"
            title={LANG_DATA.CHART_TITLE}
            comparatorList={comparatorList}
            onComparatorChange={handleChangeComparator}
            roundUpYAxis={false}
            customXAxisFormat={date => customXAxisFormat(date, period)}
            customYAxisFormat={val => formatCurrency(val, { notation: 'compact', compactDisplay: 'short' }).replace(groupBy === CHART_TYPE_DATAMART.TRANSACTION || val === 0 ? 'Rp' : '', '').trim()}
            isEmptyData={initChart.length === 0}
        >
          <FilterChart
            periodOptions={periodOptions}
            period={period}
            onPeriodChange={onPeriodChange}
            groupByOptions={groupByOptions}
            groupBy={groupBy}
            onGroupByChange={onGroupByChange}
          />
        </LineChartv2>
    );
};

export default Chart;
