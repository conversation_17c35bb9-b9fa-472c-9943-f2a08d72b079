import React, { useRef, useState } from 'react';
import PropTypes from 'prop-types';
import {
    Paper, Separator,
    Table,
    AlertDialog, Box,
} from '@majoo-ui/react';
import { useTranslation, Trans as TransComponent } from 'react-i18next';
import { connect } from 'react-redux';
import ExportBanner from '~/components/retina/export/ExportBanner';
import moment from 'moment';
import { resetDateRangeHelper } from '~/utils/helper';
import CoreHOC from '../../../../../core/CoreHOC';
import {
    useFetchPoinDatamart,
} from '../hooks/usePoinDatamartFetch';
import { TitleSection } from '../component/TitleSection';
import { FilterSection } from '../component/FilterSectionDatamart';
import { SummarySection } from '../component/SummarySection';
import { LineChartv2 } from '../../../../../components/chart';
import { getLegendDataDatamart, handleXaxis, PoinType } from './utils';
import { tableColumnsPoinDatamart } from '../utils/table.utils';
import BlockedDownloadLaporanPopup from '../../../../../components/modalpopup/BlockedDownloadLaporanPopup';
import FilterMobileModal from '../component/FilterMobileModalDatamart';
import { BannerText } from '../../../../../components/retina';
import { isMobile as mobileQuery } from '../../../../../config/config';


function ReportPoinDatamart(props) {
    const { t } = useTranslation(['Penjualan/Laporan/poin', 'translation']);
    const {
        router, location, assignCalendar, addNotification,
    } = props;
    const [openFilterModal, setOpenFilterModal] = useState(false);
    const blockedDownloadPopupRef = useRef(null);
    const {
        filter,
        setFilter,
        fetchPoinList,
        poinData,
        totalData,
        rowLimit,
        poinSummaries,
        poinChartData,
        exportReportConfirm,
        exportReportAction,
        searchQuery,
        setSearchQuery,
        periodOptions,
        period,
        onPeriodChange,
        isOpenExportConfirm,
        setIsOpenExportConfirm,
        showExportBanner,
        setShowExportBanner,
    } = useFetchPoinDatamart(props, blockedDownloadPopupRef, t);
    const isMobile = mobileQuery.matches;

    const redirectSupport = () => {
        if (blockedDownloadPopupRef && blockedDownloadPopupRef.current) {
            blockedDownloadPopupRef.current.hidePopup();
        }
        router.push('/support/buy');
    };

    const goToPoinDetail = (rowDate) => {
        router.push({
            pathname: `${location.pathname}/detail`,
            state: { ...filter, period, rowDate },
        });
    };

    const handleChangeDateRange = (value, onChange) => {
        const { newStartDate, newEndDate, isForceReset } = resetDateRangeHelper(value[0], value[1], 366, true, 12);
        if (isForceReset) {
            addNotification({
                title: t('toast.errorMaxDate', 'Terjadi Kesalahan!', { ns: 'translation' }),
                message: t('toast.errorMaxDateRangeYear', 'Maksimum rentang waktu yang dapat dipilih: 1 tahun', {
                    ns: 'translation',
                }),
                level: 'pending',
            });
            onChange([moment(newStartDate, 'DD/MM/YYYY').format('DD-MM-YYYY'), moment(newEndDate, 'DD/MM/YYYY').format('DD-MM-YYYY')]);
        } else {
            onChange([moment(value[0]).format('DD-MM-YYYY'), moment(value[1]).format('DD-MM-YYYY')]);
        }
    };

    return (
        <React.Fragment>
            <Box css={{ ...showExportBanner && { marginBottom: '$spacing-05' } }}>
                <ExportBanner
                    showExportBanner={showExportBanner}
                    onDismiss={() => setShowExportBanner(false)}
                />
            </Box>
            <Paper responsive css={{ padding: 0, '@md': { padding: '$compact' } }}>
                <TitleSection
                    title={t('title', 'Laporan Poin')}
                    startDate={filter.startDate}
                    endDate={filter.endDate}
                    downloadLaporan={exportReportConfirm}
                    translator={t}
                    isPrimary
                    type={filter.type}
                />
                {!isMobile && (<Separator css={{ my: '20px' }} />)}
                <BannerText css={{ my: 0 }} />
                <FilterSection
                    filter={filter}
                    setFilter={setFilter}
                    assignCalendar={assignCalendar}
                    periodOptions={periodOptions}
                    period={period}
                    onPeriodChange={onPeriodChange}
                    downloadLaporan={exportReportConfirm}
                    onOpenFilterClick={() => setOpenFilterModal(true)}
                    isMobile={isMobile}
                    translator={t}
                    title={t('title', 'Laporan Poin')}
                    searchQuery={searchQuery}
                    setSearchQuery={setSearchQuery}
                    handleChangeDateRange={handleChangeDateRange}
                />
                <Separator css={{ my: '20px' }} />
                <LineChartv2
                    key={`${poinChartData.length}-poin-${period}-${filter.type}`}
                    title={t('chart.title', 'Grafik Poin')}
                    xAxisKey="date"
                    data={poinChartData}
                    legendData={getLegendDataDatamart(filter.type, t)}
                    period={period}
                    customXAxisFormat={date => handleXaxis(date, period)}
                    isEmptyData={poinData.length === 0}
                />
                <SummarySection poinSummaries={poinSummaries} translator={t} />
                <Table
                    id="report_promo_point_datamart"
                    data={poinData}
                    fetchData={fetchPoinList}
                    columns={tableColumnsPoinDatamart(period, data => goToPoinDetail(data.date), filter.type === PoinType.earn, t)}
                    totalData={totalData}
                    rowLimit={rowLimit}
                    onRowClick={row => goToPoinDetail(row.original.date)}
                    css={{
                        padding: 0,
                        '@lg': {
                            padding: '0 $spacing-05',
                        },
                    }}
                    searchQuery={searchQuery}
                />
            </Paper>
            {openFilterModal && (
                <FilterMobileModal
                    open={openFilterModal}
                    onOpenChange={open => setOpenFilterModal(open)}
                    filter={filter}
                    setFilter={setFilter}
                    assignCalendar={assignCalendar}
                    periodOptions={periodOptions}
                    period={period}
                    onPeriodChange={onPeriodChange}
                    translator={t}
                    handleChangeDateRange={handleChangeDateRange}
                />
            )}
            <BlockedDownloadLaporanPopup ref={blockedDownloadPopupRef} confirmHandle={() => redirectSupport()} />
            <AlertDialog
                title={t('translation:label.exportReport')}
                isMobile={isMobile}
                hideCloseButton
                singleButton
                description={(
                    <TransComponent i18nKey="export.descriptionConfirmExport">
                        {{
                            menu: t('title', 'Laporan Poin')
                        }}
                    </TransComponent>
                )}
                open={isOpenExportConfirm}
                labelConfirm={t('translation:label.confirm')}
                onConfirm={() => {
                    setIsOpenExportConfirm(false);
                    exportReportAction();
                }}
            />
        </React.Fragment>
    );
}

ReportPoinDatamart.propTypes = {
    router: PropTypes.shape({
        push: PropTypes.func,
    }),
    location: PropTypes.shape({
        pathname: PropTypes.string.isRequired,
    }).isRequired,
    assignCalendar: PropTypes.func,
};

ReportPoinDatamart.defaultProps = {
    router: {
        push: () => { },
    },
    assignCalendar: () => {},
};

const mapStateToProps = state => ({
    namaUser: state.user.profile.user_name,
    namaUsaha: state.user.profile.user_usaha_name,
    selectedCabang: state.branch.filter,
    logo: state.user.profile.user_usaha_logo_path,
    listCabang: state.branch.list,
    supportNeeded: state.layout.supportNeeded,
});

export default connect(mapStateToProps)(CoreHOC(ReportPoinDatamart));
