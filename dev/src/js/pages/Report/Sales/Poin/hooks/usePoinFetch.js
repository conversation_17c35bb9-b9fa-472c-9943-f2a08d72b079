/* eslint-disable import/no-cycle */
import {
    useState,
} from 'react';
import moment from 'moment';
import { getLaporanPoinSalesDetail, getLaporanPoinSalesDetailCustomer } from '../../../../../data/reports';
import { fetcher } from '../../../../../utils/api.util';
import * as reportSelectors from '../../../../../data/reports/selectors';
import { getTransaksiDetailV12 } from '../../../../../data/reports/api';

export const PoinType = {
    earn: 'earn',
    redeem: 'redeem',
};

export const GroupFilter = {
    jam: 'jam',
    hari: 'hari',
    bulan: 'bulan',
    tahun: 'tahun',
};

export const useFetchPoinSalesDetail = (filterBranch) => {
    const [originalPoinDetailData, setOriginalPoinDetailData] = useState([]);
    const [poinDetailData, setPoinDetailData] = useState([]);
    const [isLoading, setIsLoading] = useState(true);

    const fetchPoinDetail = async (trxId, startDate, endDate, type) => {
        if (!trxId || !startDate || !endDate) return;
        setIsLoading(true);
        const payload = {
            trx_id: trxId,
            tgl_mulai: moment(startDate, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            tgl_akhir: moment(endDate, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            type,
        };

        if (filterBranch && filterBranch.length > 0) {
            Object.assign(payload, { id_outlet: filterBranch });
        }

        const response = await fetcher(getLaporanPoinSalesDetail, payload, undefined, undefined, true);
        if (response !== null) {
            const mappedResponseData = response.data.map(data => ({
                ...data,
                point: type === PoinType.earn ? data.point_earn : data.point_redeem,
                point_redeemed: data.point_redeem,
            }));
            setPoinDetailData(mappedResponseData);
            setOriginalPoinDetailData(mappedResponseData);
        }
        setIsLoading(false);
    };

    const filterPoinDetailData = (query, type) => {
        if (type === PoinType.earn) {
            setPoinDetailData(originalPoinDetailData.filter(data => String(data.point).toLowerCase().startsWith(query.toLowerCase())
                || String(data.point_redeemed).toLowerCase().startsWith(query.toLowerCase())
                || String(data.nota).toLowerCase().startsWith(query.toLowerCase())
                || String(data.customer).toLowerCase().startsWith(query.toLowerCase())
                || String(data.kasir).toLowerCase().startsWith(query.toLowerCase())
                || String(data.point_redeem_sales).toLowerCase().startsWith(query.toLowerCase())
                || String(data.omzet).toLowerCase().startsWith(query.toLowerCase())));
        } else {
            setPoinDetailData(originalPoinDetailData.filter(data => String(data.point_redeem).toLowerCase().startsWith(query.toLowerCase())
                || String(data.point).toLowerCase().startsWith(query.toLowerCase())
                || String(data.nota).toLowerCase().startsWith(query.toLowerCase())
                || String(data.customer).toLowerCase().startsWith(query.toLowerCase())
                || String(data.kasir).toLowerCase().startsWith(query.toLowerCase())
                || String(data.omzet).toLowerCase().startsWith(query.toLowerCase())
                || String(data.point_redeem_sales).toLowerCase().startsWith(query.toLowerCase())));
        }
    };

    return {
        fetchPoinDetail, isLoading, poinDetailData, filterPoinDetailData,
    };
};

export const useFetchTransaksiPoinDetail = (rootProps) => {
    const { addNotification, router, t } = rootProps;
    const fetchTransaksiDetail = async (data) => {
        const payload = {
            id_transaction: data.trx_id,
            outlet_id: data.id_cabang,
        };

        try {
            const response = await getTransaksiDetailV12(data.trx_id, payload);
            const detailTransaksi = reportSelectors.toTransactionDetail(response.data);
            return detailTransaksi;
        } catch (message) {
            if (!message) {
                router.push('/auth/login');
            } else {
                addNotification({
                    title: t('toast.error', 'Gagal!', { ns: 'translation' }),
                    message: t('toast.failGotData', 'Gagal mendapatkan data', { ns: 'translation' }),
                    level: 'error',
                });
            }
            return {};
        }
    };


    return {
        fetchTransaksiDetail,
    };
};
