import moment from 'moment';
import { colors } from '../../../../../stitches.config';
import { GroupFilter, PoinType } from '../hooks/usePoinFetch';

export const getLegendData = (type, t) => (type === PoinType.earn ? [
    { dataKey: 'point_earn', name: t('earnedData', 'Poin Didapatkan'), color: colors.chartYellow },
    { dataKey: 'trx_count', name: t('chart.earned.transactionLegend', 'Transaksi Didapatkan'), color: colors.chartGreen },
    {
        dataKey: 'omzet', name: t('chart.earned.salesLegend', 'Total Penjualan'), color: colors.chartPurple, isCurrency: true,
    },
] : [
    { dataKey: 'point_redeem', name: t('redeemedData', '<PERSON><PERSON>'), color: colors.chartYellow },
    { dataKey: 'trx_count', name: t('chart.redeemed.transactionLegend', '<PERSON>aks<PERSON>'), color: colors.chartGreen },
    {
        dataKey: 'point_redeem_sales', name: t('chart.redeemed.salesLegend', 'Total Nilai Ditukar'), color: colors.chartPurple, isCurrency: true,
    },
]);

const getGroupDateFormat = (group) => {
    switch (group) {
        case GroupFilter.bulan:
            return 'YYYY-MM';
        case GroupFilter.tahun:
            return 'YYYY';
        default:
            return 'YYYY-MM-DD';
    }
};

export const fillChartData = (group, dateStart, dateEnd, dateKey, chartData, legendData) => {
    if (chartData.length === 0) {
        return [];
    }
    if (group !== GroupFilter.hari) {
        return chartData.map(data => ({
            ...data,
            tanggal: moment(data.tanggal, getGroupDateFormat(group)).toDate(),
        }));
    }
    const data = [];
    const allKey = legendData.map(d => ({ [d.dataKey]: 0 }));
    while (dateEnd.diff(dateStart, 'days') >= 0) {
        const index = chartData.findIndex(
            d => moment(d[dateKey], getGroupDateFormat(group)).valueOf() === dateStart.valueOf(),
        );
        if (!chartData[index]) {
            data.push({
                ...Object.assign({}, ...allKey),
                [dateKey]: dateStart.toDate(),
            });
        } else {
            // convert string value to number
            const numberData = Object.keys(chartData[index])
                .filter(key => !key.includes(dateKey))
                .reduce(
                    (current, key) => Object.assign(current, { [key]: Number(chartData[index][key]) }),
                    {},
                );
            data.push({
                ...Object.assign({}, ...allKey),
                ...numberData,
                [dateKey]: dateStart.toDate(),
            });
        }
        dateStart.add(1, 'days');
    }
    return data;
};
