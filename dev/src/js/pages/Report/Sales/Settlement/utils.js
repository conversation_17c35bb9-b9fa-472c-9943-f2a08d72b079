export const TAB_LIST_VALUE = {
    // value ini mengikuti param filter API
    TRANSAKSI_BERHASIL: 'success',
    TRANSAKSI_GAGAL: 'failed',
    SETTLEMENT_DIPROSES: '2',
    SETTLEMENT_BERHASIL: '3',
    SETTLEMENT_TERTUNDA: '4', // Settlement Gagal
};

// TODO: implement multi language
export const TAB_LIST = (t) => [
    {
        value: TAB_LIST_VALUE.TRANSAKSI_BERHASIL,
        label:  t('Report/Settlement/ewallet:tabSection.successTransaction'),
    },
    {
        value: TAB_LIST_VALUE.TRANSAKSI_GAGAL,
        label:  t('Report/Settlement/ewallet:tabSection.failedTransaction'),
    },
    {
        value: TAB_LIST_VALUE.SETTLEMENT_DIPROSES,
        label:  t('Report/Settlement/ewallet:tabSection.processSettlement'),   
    },
    {
        value: TAB_LIST_VALUE.SETTLEMENT_TERTUNDA,
        label:  t('Report/Settlement/ewallet:tabSection.pendingSettlement'),   
    },
    {
        value: TAB_LIST_VALUE.SETTLEMENT_BERHASIL,
        label:  t('Report/Settlement/ewallet:tabSection.successSettlement'),    
    },
];

export const dataTipeQRIS = t => [
    {
        value: '',
        name: t('Report/Settlement/ewallet:tipeQRIS.allQRIS'),
    },
    {
        value: 'static',
        name: t('Report/Settlement/ewallet:tipeQRIS.qrisStatic'),
    },
    {
        value: 'dynamic',
        name: t('Report/Settlement/ewallet:tipeQRIS.qrisDynamic'),
    },
];

const dataStatusTransaksiBerhasil = t => [
    {
        value: '0',
        name: t('Report/Settlement/ewallet:status.allStatus'),
    },
    {
        value: '1',
        name: t('Report/Settlement/ewallet:status.waitingReconcile'),
    },
    {
        value: '2',
        name: t('Report/Settlement/ewallet:status.processSettlement'),
    },
    {
        value: '3',
        name: t('Report/Settlement/ewallet:status.successSettlement'),
    },
    {
        value: '4',
        name: t('Report/Settlement/ewallet:status.failedSettlement'),
    },
]

const dataStatusTransaksiGagal = t => [
    {
        value: '0',
        name: t('Report/Settlement/ewallet:status.allStatus'),
    },
    {
        value: '1',
        name: t('Report/Settlement/ewallet:status.waitingPayment'),
    },
    {
        value: '2',
        name: t('Report/Settlement/ewallet:status.failedPayment'),
    },
]

// filter hanya digunakan pada tab transaksi berhasil & gagal
export const dataListStatus = (t, tabSelected) => {
    let list = [];
    switch (tabSelected) {
    case TAB_LIST_VALUE.TRANSAKSI_BERHASIL:
        list = dataStatusTransaksiBerhasil(t);
        break;
    case TAB_LIST_VALUE.TRANSAKSI_GAGAL:
        list = dataStatusTransaksiGagal(t);
        break;
    default:
        break;
    }
    return list;
}