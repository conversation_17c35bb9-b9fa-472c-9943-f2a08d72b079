import {
  ADVANCE_PICKER,
  Button,
  Flex,
  FormLabel,
  InputDatePicker,
  InputSelect,
  InputSelectTag,
  PageDialog,
  PageDialogContent,
  PageDialogFooter,
  PageDialogTitle,
  InputDateRange,
  Box,
} from '@majoo-ui/react';
import React from 'react';

import { isMobile as mobileQuery } from '../../../../../config/config';
import useResetDateRange from '../../../../../hooks/useResetDateRange';

function FilterMobile({
  open,
  onClose,
  onSubmit,
  date,
  changeDate,
  optPaymentMethod,
  selectPaymentMethod,
  setSelectPaymentMethod,
  optStatus,
  selectStatus,
  setSelectStatus,
  t,
}) {
  const [dateTemp, setDateTemp] = React.useState([new Date(), new Date()]);
  const [paymentMethodTemp, setPaymentMethodTemp] = React.useState([]);
  const [statusTemp, setStatusTemp] = React.useState('');
  const isMobile = mobileQuery.matches;
  const [dateRangeKey, resetDateKey] = useResetDateRange();

  const resetFilter = () => {
    resetDateKey();
    setDateTemp(date);
    // const valPaymentTemp = optPaymentMethod.find((item) => item.value === selectPaymentMethod);
    setPaymentMethodTemp(selectPaymentMethod);
    const valStatus = optStatus(t).find((item) => item.value === selectStatus).value;
    setStatusTemp(valStatus);
  };

  const handleSubmit = () => {
    changeDate(dateTemp);
    setSelectPaymentMethod(paymentMethodTemp);
    setSelectStatus(statusTemp);
    onSubmit();
  };

  React.useEffect(() => {
    if (open) {
      resetFilter();
    }
  }, [open]);

  return (
    <PageDialog
      modal
      open={open}
      size="full"
      onOpenChange={() => {
        resetFilter();
        onClose();
      }}
      isMobile
    >
      <PageDialogTitle>Filter Data</PageDialogTitle>
      <PageDialogContent css={{ padding: 0 }}>
        <Flex direction="column" css={{ padding: 20 }} gap={6}>
          <Flex direction="column" gap={3}>
            <FormLabel labelCss={{ color: '$textPrimary' }}>
              {t('Report/Settlement/ewallet:selectDate', { defaultValue: "Pilih Tanggal" })}
            </FormLabel>
            <InputDatePicker
              value={dateTemp}
              type={!isMobile ? ADVANCE_PICKER : 'range'}
              size="lg"
              onChange={(date) => {
                resetDateKey();
                setDateTemp(date);
              }}
            />
          </Flex>
          <Flex direction="column" gap={3}>
            <FormLabel labelCss={{ color: '$textPrimary' }}>
              {t('Report/Settlement/ewallet:dateRange', { defaultValue: 'Rentang Tanggal' })}
            </FormLabel>
            <InputDateRange key={dateRangeKey} size="lg" onChange={(range) => { setDateTemp(range); }} optionCss={{ maxHeight: 150 }} />
          </Flex>

          <Flex direction="column" gap={3}>
            <FormLabel labelCss={{ color: '$textPrimary' }}>{t('Report/Settlement/ewallet:tableColumns.paymentMethod', { defaultValue: "Metode Pembayaran" })}</FormLabel>
            <InputSelectTag
                size="lg"
                option={optPaymentMethod}
                //   option={periodOptions ? periodOptions : []}
                placeholder={t('Report/Settlement/ewallet:selectPaymentMethod', { defaultValue: "Pilih Metode Pembayaran" })}
                showSelectAll
                selectAllLabel={t('Report/Settlement/ewallet:allPaymentMethod', { defaultValue: "Semua Metode Pembayaran" })}
                showCounter
                labelCounter={t('Report/Settlement/ewallet:selectedMethod', { defaultValue: "metode terpilih" })}
                onChange={(value) => {
                  setSelectPaymentMethod(value);
                }}
                value={paymentMethodTemp}
                optionCss={{ maxHeight: 150 }}
              />
          </Flex>

          <Flex direction="column" gap={3}>
            <FormLabel labelCss={{ color: '$textPrimary' }}>Status</FormLabel>
            <InputSelect
                size="lg"
                option={optStatus(t)}
                placeholder="Status"
                value={optStatus(t).find((f) => f.value === statusTemp)}
                onChange={(value) => {
                  setStatusTemp(
                    optStatus(t).find((f) => f.value === value.value).value
                  );
                }}
                optionCss={{ maxHeight: 150 }}
              />
          </Flex>

          <Box css={{ height: 150 }} />
        </Flex>
      </PageDialogContent>
      <PageDialogFooter>
        <Flex
          direction="row"
          gap={3}
          css={{
            width: '100%',
          }}
        >
          <Button
            size="md"
            buttonType="ghost"
            onClick={() => resetFilter()}
            css={{ flex: 1 }}
          >
            Reset
          </Button>
          <Button size="md" onClick={() => handleSubmit()} css={{ flex: 1 }}>
          {t('translation:label.apply', { defaultValue: 'Terapkan' })}
          </Button>
        </Flex>
      </PageDialogFooter>
    </PageDialog>
  );
}

export default FilterMobile;
