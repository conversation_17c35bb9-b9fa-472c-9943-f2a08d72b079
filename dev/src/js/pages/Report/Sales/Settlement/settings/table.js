/* eslint-disable react/prop-types */
import React from 'react';
import { Icon<PERSON>utton } from '@majoo-ui/react';
import { EyeOutline } from '@majoo-ui/icons';
import DateTimeColumn from '../../../../../components/table/components/DateTimeColumn';
import { PriceColumn } from '../../../../../components/retina/table';
import StatusColumn from './statusColumn';
import {
  TAB_LIST_VALUE,
  dataTipeQRIS,
} from '../utils'

const columnsTransaksiBerhasil = (t, onRowAction, tabSelected) => [
  {
    Header: t('Report/Settlement/ewallet:tableColumns.transactionNo'),
    accessor: 'transaction_id',
    sticky: 'left',
    shadow: true,
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.transactionDate'),
    accessor: 'transaction_date',
    Cell: (params) => {
      const { value } = params;
      return <DateTimeColumn value={value} />;
    },
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.qrisType'),
    accessor: 'qris_type',
    Cell: (params) => {
      const { value } = params;
      const dataQRIS = dataTipeQRIS(t).find(x => x.value === value);
      return dataQRIS ? dataQRIS.name : '-';
    },
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.reference'),
    accessor: 'reference_no',
    unsortable: true,
  },
  {
    Header: 'Total',
    accessor: 'transaction_amount',
    Cell: PriceColumn,
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.settlementStatus'),
    accessor: 'settlement_status',
    Cell: ({ value }) => StatusColumn({ value, t, tabSelected }),
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.settlementDate'),
    accessor: 'settlement_date_desc',
    unsortable: true,
  },
  // akan dikerjakan next enhancement
  // {
  //   id: 'eye',
  //   Header: () => null,
  //   Cell: ({ row: { original } }) => <IconButton onClick={() => onRowAction(original)}><EyeOutline /></IconButton>,
  //   isAction: true,
  //   unsortable: true,
  // },
];

const columnsTransaksiGagal = (t, onRowAction, tabSelected) => [
  {
    Header: t('Report/Settlement/ewallet:tableColumns.transactionNo'),
    accessor: 'transaction_id',
    sticky: 'left',
    shadow: true,
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.transactionDate'),
    accessor: 'transaction_date',
    Cell: (params) => {
      const { value } = params;
      return <DateTimeColumn value={value} />;
    },
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.qrisType'),
    accessor: 'qris_type',
    Cell: (params) => {
      const { value } = params;
      const dataQRIS = dataTipeQRIS(t).find(x => x.value === value);
      return dataQRIS ? dataQRIS.name : '-';
    },
    unsortable: true,
  },
  {
    Header: 'Total',
    accessor: 'transaction_amount',
    Cell: PriceColumn,
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.transactionStatus'),
    accessor: 'settlement_status',
    Cell: ({ value }) => StatusColumn({ value, t, tabSelected }),
    unsortable: true,
  },
  // akan dikerjakan next enhancement
  // {
  //   id: 'eye',
  //   Header: () => null,
  //   Cell: ({ row: { original } }) => <IconButton onClick={() => onRowAction(original)}><EyeOutline /></IconButton>,
  //   isAction: true,
  //   unsortable: true,
  // },
];

const columnsSettlementDiproses = (t, onRowAction) => [
  {
    Header: t('Report/Settlement/ewallet:tableColumns.settlementNo'),
    accessor: 'settlement_no',
    sticky: 'left',
    shadow: true,
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.submissionDate'),
    accessor: 'settlement_date',
    Cell: (params) => {
      const { value } = params;
      return <DateTimeColumn value={value} />;
    },
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.bankAccount'),
    accessor: 'bank_name',
    Cell: (params) => {
      const { value, row: { original: { bank_account_no: backAccountNo } } } = params;
      return `${value} - ${backAccountNo}`;
    },
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.totalSettlement'),
    accessor: 'settlement_amount',
    Cell: PriceColumn,
    unsortable: true,
  },
  {
    id: 'eye',
    Header: () => null,
    Cell: ({ row: { original } }) => <IconButton onClick={() => onRowAction(original)}><EyeOutline /></IconButton>,
    isAction: true,
    unsortable: true,
  },
];

const columnsSettlementTertunda = (t, onRowAction) => [
  {
    Header: t('Report/Settlement/ewallet:tableColumns.settlementNo'),
    accessor: 'settlement_no',
    sticky: 'left',
    shadow: true,
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.settlementDate'),
    accessor: 'settlement_date',
    Cell: (params) => {
      const { value } = params;
      return <DateTimeColumn value={value} />;
    },
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.bankAccount'),
    accessor: 'bank_name',
    Cell: (params) => {
      const { value, row: { original: { bank_account_no: backAccountNo } } } = params;
      return `${value} - ${backAccountNo}`;
    },
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.totalSettlement'),
    accessor: 'settlement_amount',
    Cell: PriceColumn,
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.note'),
    accessor: 'notes',
    unsortable: true,
  },
  {
    id: 'eye',
    Header: () => null,
    Cell: ({ row: { original } }) => <IconButton onClick={() => onRowAction(original)}><EyeOutline /></IconButton>,
    isAction: true,
    unsortable: true,
  },
];

const columnsSettlementBerhasil = (t, onRowAction) => [
  {
    Header: t('Report/Settlement/ewallet:tableColumns.settlementNo'),
    accessor: 'settlement_no',
    sticky: 'left',
    shadow: true,
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.settlementDate'),
    accessor: 'settlement_date',
    Cell: (params) => {
      const { value } = params;
      return <DateTimeColumn value={value} />;
    },
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.bankAccount'),
    accessor: 'bank_name',
    Cell: (params) => {
      const { value, row: { original: { bank_account_no: backAccountNo } } } = params;
      return `${value} - ${backAccountNo}`;
    },
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.totalSettlement'),
    accessor: 'settlement_amount',
    Cell: PriceColumn,
    unsortable: true,
  },
  {
    id: 'eye',
    Header: () => null,
    Cell: ({ row: { original } }) => <IconButton onClick={() => onRowAction(original)}><EyeOutline /></IconButton>,
    isAction: true,
    unsortable: true,
  },
];

const columnsTable = (t, tabSelected, onRowAction) => {
  let selectedColumns = [];
  switch (tabSelected) {
    case TAB_LIST_VALUE.TRANSAKSI_BERHASIL:
      selectedColumns = columnsTransaksiBerhasil(t, onRowAction, tabSelected);
      break;
    case TAB_LIST_VALUE.TRANSAKSI_GAGAL:
      selectedColumns = columnsTransaksiGagal(t, onRowAction, tabSelected);
      break;
    case TAB_LIST_VALUE.SETTLEMENT_DIPROSES:
      selectedColumns = columnsSettlementDiproses(t, onRowAction);
      break;
    case TAB_LIST_VALUE.SETTLEMENT_TERTUNDA:
      selectedColumns = columnsSettlementTertunda(t, onRowAction);
      break;
    case TAB_LIST_VALUE.SETTLEMENT_BERHASIL:
      selectedColumns = columnsSettlementBerhasil(t, onRowAction);
      break;
    default:
      break;
  }
  return selectedColumns;
}

const columnsTableDetailSettlement = (t) => [
  {
    Header: t('Report/Settlement/ewallet:tableColumns.transactionDate'),
    accessor: 'transaction_date',
    Cell: (params) => {
      const { value } = params;
      return <DateTimeColumn value={value} />;
    },
    sticky: 'left',
    shadow: true,
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.transactionNo'),
    accessor: 'transaction_id',
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.qrisType'),
    accessor: 'qris_type',
    Cell: (params) => {
      const { value } = params;
      const dataQRIS = dataTipeQRIS(t).find(x => x.value === value);
      return dataQRIS ? dataQRIS.name : '-';
    },
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.nominalTransaction'),
    accessor: 'transaction_amount',
    Cell: PriceColumn,
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.mdr'),
    accessor: 'mdr',
    Cell: PriceColumn,
    unsortable: true,
  },
  {
    Header: t('Report/Settlement/ewallet:tableColumns.nominalSettlement'),
    accessor: 'settlement_amount',
    Cell: PriceColumn,
    unsortable: true,
  },
];

export {
  columnsTable,
  columnsTableDetailSettlement,
};