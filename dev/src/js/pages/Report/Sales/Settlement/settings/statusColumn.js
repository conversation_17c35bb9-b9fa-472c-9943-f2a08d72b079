import React from 'react';
import { Badge } from '@majoo-ui/react';
import PropTypes from 'prop-types';
import { styled, colors } from '../../../../../stitches.config';
import { TAB_LIST_VALUE } from '../utils';

const StatusColumn = ({ value, t, tabSelected }) => {
  let status, colorScheme, colorIcon;

  if (tabSelected === TAB_LIST_VALUE.TRANSAKSI_BERHASIL) {
    switch (+value) {
      case 1: {
        status = t('Report/Settlement/ewallet:status.waitingReconcile');
        colorScheme = 'orange';
        colorIcon = colors.orange500;
        break;
      }
      case 2: {
        status = t('Report/Settlement/ewallet:status.processSettlement');
        colorScheme = 'secondary';
        colorIcon = colors.secondary500;
        break;
      }
      case 3: {
        status = t('Report/Settlement/ewallet:status.failedSettlement');
        colorScheme = 'red';
        colorIcon = colors.red500;
        break;
      }
      case 4: {
        status = t('Report/Settlement/ewallet:status.successSettlement');
        colorScheme = 'green';
        colorIcon = colors.green500;
        break;
      }
      default:
        break;
    }
  } else if (tabSelected === TAB_LIST_VALUE.TRANSAKSI_GAGAL) {
    switch (+value) {
      case 1: {
        status = t('Report/Settlement/ewallet:status.waitingPayment');
        colorScheme = 'orange';
        colorIcon = colors.orange500;
        break;
      }
      case 2: {
        status = t('Report/Settlement/ewallet:status.failedPayment');
        colorScheme = 'red';
        colorIcon = colors.red500;
        break;
      }
      default:
        break;
    }
  } else {
    switch (+value) {
      case 2: {
        status = t('Report/Settlement/ewallet:status.processSettlement');
        colorScheme = 'secondary';
        colorIcon = colors.secondary500;
        break;
      }
      case 3: {
        status = t('Report/Settlement/ewallet:status.successSettlement');
        colorScheme = 'green';
        colorIcon = colors.green500;
        break;
      }
      case 4: {
        status = t('Report/Settlement/ewallet:status.failedSettlement');
        colorScheme = 'red';
        colorIcon = colors.red500;
        break;
      }
      default:
        break;
    }
  }

  const Primitive = styled('div');

  return (
    <Badge
      colorScheme={colorScheme}
      badgeVariant="subtle"
      css={{ color: '$textPrimary', padding: '$spacing-02 $spacing-03', borderRadius: 24 }}
    >
      <Primitive
        css={{
          display: 'inline',
          width: 6,
          height: 6,
          backgroundColor: colorIcon,
          borderRadius: '100%',
          marginRight: '$spacing-03',
        }}
      />
      {status}
    </Badge>
  );
};

StatusColumn.propTypes = {
  value: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
    PropTypes.bool,
  ]).isRequired,
  t: PropTypes.func.isRequired,
  tabSelected: PropTypes.string.isRequired,
};

export default StatusColumn;
