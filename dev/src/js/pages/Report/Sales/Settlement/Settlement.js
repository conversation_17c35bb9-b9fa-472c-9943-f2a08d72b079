import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { connect } from 'react-redux';
import {
    ADVANCE_PICKER,
    Box,
    Button,
    Flex,
    InputDatePicker,
    InputSearchbox,
    InputSelect,
    Paper,
    Separator,
    Table,
    Tabs,
    TabsList,
    TabsTrigger,
    AlertDialog,
} from '@majoo-ui/react';
import { debounce } from 'lodash';
import { Trans, useTranslation } from 'react-i18next';
import { useMediaQuery } from '~/utils/useMediaQuery';

import { FilterOutline } from '@majoo-ui/icons';
import { requestExportReportDatamartV1 } from '~/data/accounting';
import { BUTTON_TYPE } from '~/components/retina/export/useExportReport';
import CoreHOC from '../../../../core/CoreHOC';
import DetailTransaction from './detail';
import FilterMobileDialog from './components/FilterMobileDialog';

import {
    getSettlementTransaction,
    getSettlementTransactionWidget,
    getSettlementQRIS,
    getCountQrisTransaction,
    getCountQrisSettlement,
} from '../../../../data/settlement';

import { catchError, currency } from '../../../../utils/helper';
import { TAB_LIST, TAB_LIST_VALUE, dataTipeQRIS, dataListStatus } from './utils';

import { columnsTable } from './settings/table';

import { TitleSection } from '../Branch/components';
import { BannerText, CardWrapper } from '../../../../components/retina';

function Settlement({ addNotification, showProgress, hideProgress, filterBranch }) {
    const isMobile = useMediaQuery('(max-width: 767px)');
    const { t } = useTranslation(['Report/Settlement/ewallet', 'translation', 'Keuangan/exportReport']);
    const [metaTable, setMetaTable] = useState({
        pageIndex: 0,
        pageSize: 10,
        total: 0,
    });
    const [dataTable, setDataTable] = useState([]);
    const [isFetching, setIsFetching] = useState(false);
    const [dataCount, setDataCount] = useState({
        success: 0,
        failed: 0,
        on_hold: 0,
        on_process: 0,
    });
    const [dataSummary, setDataSummary] = useState({});
    const [filterReport, setFilterReport] = useState({
        date: [moment().toDate(), moment().toDate()],
        search: '',
        selectTipeQRIS: '',
        selectStatus: '0',
    });

    const [tab, setTab] = useState(TAB_LIST_VALUE.TRANSAKSI_BERHASIL);
    const [detailModal, setDetailModal] = useState({
        isOpen: false,
        data: {},
    });
    const [filterMobileOpen, setFilterMobileOpen] = useState(false);
    const [dialogInfoExport, setDialogInfoExport] = useState({
        open: false,
        state: {
            title: '',
            description: '',
            btnCloseOnly: false,
        },
    });

    const title = t('Report/Settlement/ewallet:eWalletSettlementReport', {
        defaultValue: 'Laporan Settlement QRIS',
    })

    const isTransactionTab = tab === TAB_LIST_VALUE.TRANSAKSI_BERHASIL || tab === TAB_LIST_VALUE.TRANSAKSI_GAGAL;

    const fetchSettlementTransaction = async tableProps => {
        const payload = {
            page: metaTable.pageIndex + 1,
            limit: metaTable.pageSize,
            start_date: moment(filterReport.date[0]).format('YYYY-MM-DD'),
            end_date: moment(filterReport.date[1]).format('YYYY-MM-DD'),
            trx_type: tab,
            ...(filterReport.search && { search: filterReport.search }),
            ...(filterBranch && { outlet_id: filterBranch }),
            ...(filterReport.selectTipeQRIS && { qris_type: filterReport.selectTipeQRIS }),
            ...(filterReport.selectStatus && { status: filterReport.selectStatus }),
        };

        if (tableProps) {
            const { pageSize, pageIndex } = tableProps;

            Object.assign(payload, {
                page: pageIndex + 1,
                limit: pageSize,
            });
        }

        showProgress();
        setIsFetching(true);
        try {
            const { data, meta } = await getSettlementTransaction(payload);
            setDataTable(data);
            setMetaTable(prev => ({
                ...prev,
                ...(tableProps && tableProps),
                total: meta.total,
            }));

            const resWidget = await getSettlementTransactionWidget({
                start_date: moment(filterReport.date[0]).format('YYYY-MM-DD'),
                end_date: moment(filterReport.date[1]).format('YYYY-MM-DD'),
                ...(filterBranch && { outlet_id: filterBranch }),
            });
            setDataSummary(resWidget);
        } catch (err) {
            addNotification({
                title: t('translation:toast.error', { defaultValue: 'Gagal!' }),
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
            setIsFetching(false);
        }
    };

    const fetchSettlementQRIS = async tableProps => {
        const payload = {
            page: metaTable.pageIndex + 1,
            limit: metaTable.pageSize,
            start_date: moment(filterReport.date[0]).format('YYYY-MM-DD'),
            end_date: moment(filterReport.date[1]).format('YYYY-MM-DD'),
            status: tab,
            ...(filterBranch && { outlet_id: filterBranch }),
            ...(filterReport.search && { search: filterReport.search }),
        };
        if (tableProps) {
            const { pageSize, pageIndex } = tableProps;
            Object.assign(payload, {
                page: pageIndex + 1,
                limit: pageSize,
            });
        }
        showProgress();
        setIsFetching(true);
        try {
            const res = await getSettlementQRIS(payload);
            setDataTable(res.data);
            setMetaTable(prev => ({
                ...prev,
                ...(tableProps && tableProps),
                total: res.meta.total,
            }));
        } catch (err) {
            addNotification({
                title: t('translation:toast.error', { defaultValue: 'Gagal!' }),
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
            setIsFetching(false);
        }
    };

    const handleFetchTab = async tableProps => {
        if (isTransactionTab) {
            await fetchSettlementTransaction(tableProps);
        } else {
            await fetchSettlementQRIS(tableProps);
        }
    };

    const handleTabChanged = newTabValue => {
        setTab(newTabValue);

        // for reset filter when tab changed
        let updatedDate = [];
        if (newTabValue === TAB_LIST_VALUE.TRANSAKSI_BERHASIL || newTabValue === TAB_LIST_VALUE.TRANSAKSI_GAGAL) {
            updatedDate = [moment().toDate(), moment().toDate()];
        } else {
            updatedDate = [moment().startOf('month').toDate(), moment().endOf('month').toDate()];
        }

        setFilterReport({
            date: updatedDate,
            search: '',
            selectTipeQRIS: '',
            selectStatus: '0',
        });
    };

    const fetchCountQris = async () => {
        try {
            const {
                data: { success, failed },
            } = await getCountQrisTransaction();
            const {
                data: { on_hold: onHold, on_process: onProcess },
            } = await getCountQrisSettlement();
            setDataCount({
                success: success || 0,
                failed: failed || 0,
                on_hold: onHold || 0,
                on_process: onProcess || 0,
            });
        } catch (err) {
            addNotification({
                title: t('translation:toast.error', { defaultValue: 'Gagal!' }),
                message: catchError(err),
                level: 'error',
            });
        }
    };

    const fetchDownloadReport = async (type = 'xlsx') => {
        showProgress();
        try {
            const payload = {
                report_name: isTransactionTab ? 'qris_transaction' : 'qris_settlement',
                report_format: type,
                parameters: {
                    format: type,
                    start_date: moment(filterReport.date[0]).format('YYYY-MM-DD'),
                    end_date: moment(filterReport.date[1]).format('YYYY-MM-DD'),
                    ...(filterBranch && { outlet_id: Number(filterBranch) }),
                    ...(filterReport.search && { search: filterReport.search }),
                    ...(isTransactionTab ? {
                        trx_type: tab,
                        ...(filterReport.selectTipeQRIS && { qris_type: filterReport.selectTipeQRIS }),
                        ...(filterReport.selectStatus && { status: Number(filterReport.selectStatus) }),
                    } : {
                        status: Number(tab),
                    }),
                },
            };

            await requestExportReportDatamartV1(payload);

            setDialogInfoExport({
                open: true,
                state: {
                    title: (
                        <Trans
                            t={t}
                            i18nKey="dialog.labelExport"
                            defaults="Ekspor Laporan Settlement QRIS"
                            components={{ strong: <b /> }}
                            values={{ menu: title.replace('Laporan', '').replace('Report', '') }}
                            ns="Keuangan/exportReport"
                        />
                    ),
                    description: (
                        <Trans
                            t={t}
                            i18nKey="dialog.descriptionExport"
                            defaults="Mohon menunggu, sistem sedang memproses <strong>Laporan Settlement QRIS</strong>. Anda akan menerima pemberitahuan notifikasi dan email saat data siap untuk diunduh."
                            components={{ strong: <b /> }}
                            values={{ menu: title.replace('Laporan', '').replace('Report', '') }}
                            ns="Keuangan/exportReport"
                        />
                    ),
                    btnCloseOnly: false,
                },
            });
        } catch (error) {
            if (error.cause && error.cause.status && error.cause.status.code) {
                if (Number(error.cause.status.code) === 422002) {
                    setDialogInfoExport({
                        open: true,
                        state: {
                            title: (
                                <Trans
                                    t={t}
                                    i18nKey="label.exportReport"
                                    defaults="Ekspor Laporan"
                                    ns="Keuangan/exportReport"
                                />
                            ),
                            description: error.cause.status.message,
                            btnCloseOnly: true,
                        },
                    });
                    return;
                }
            }

            const message = catchError(error);
            addNotification({
                title: t('toast.error', { ns: 'translation' }),
                message,
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    const generateCountTitle = tabSelected => {
        let countTitle = '';
        switch (tabSelected) {
            case TAB_LIST_VALUE.TRANSAKSI_BERHASIL:
                countTitle = dataCount.success > 0 ? ` (${dataCount.success})` : '';
                break;
            case TAB_LIST_VALUE.TRANSAKSI_GAGAL:
                countTitle = dataCount.failed > 0 ? ` (${dataCount.failed})` : '';
                break;
            case TAB_LIST_VALUE.SETTLEMENT_DIPROSES:
                countTitle = dataCount.on_process ? ` (${dataCount.on_process})` : '';
                break;
            case TAB_LIST_VALUE.SETTLEMENT_TERTUNDA:
                countTitle = dataCount.on_hold ? ` (${dataCount.on_hold})` : '';
                break;
            default:
                break;
        }
        return countTitle;
    };

    useEffect(() => {
        handleFetchTab({ pageIndex: 0, pageSize: metaTable.pageSize });
        fetchCountQris();
    }, [filterReport, filterBranch]);

    const handleRowAction = rowData => {
        setDetailModal({
            isOpen: true,
            data: rowData,
        });
    };

    return (
        <React.Fragment>
            <Paper responsive css={{ mb: 20, padding: 'unset', '@md': { padding: 20 } }}>
                <Flex css={{ flexDirection: 'column', rowGap: '$compact' }}>
                    <TitleSection
                        title={title}
                        date={filterReport.date}
                        hideComparison
                        t={t}
                        isPrimary
                        exportType={BUTTON_TYPE.SINGLE}
                        onDownloadLaporan={fetchDownloadReport}
                    />
                    <BannerText css={{ my: 0 }} />
                    <Tabs
                        activationMode="automatic"
                        dir="ltr"
                        defaultValue={tab}
                        orientation="horizontal"
                        style={{ boxShadow: 'none' }}
                        onValueChange={val => handleTabChanged(val)}
                    >
                        <TabsList style={{ boxShadow: 'none' }}>
                            {TAB_LIST(t).map(item => (
                                <TabsTrigger
                                    value={item.value}
                                    key={item.value}
                                    activeTabs={item.value === tab}
                                    css={{ color: item.value === tab ? '$textGreen' : '$textPrimary' }}
                                >
                                    {item.label}
                                    {generateCountTitle(item.value)}
                                </TabsTrigger>
                            ))}
                        </TabsList>
                    </Tabs>
                    <Box
                        css={{
                            flexDirection: 'column',
                            gap: '$compact',
                            display: 'flex',
                            '@md': { padding: '0px $spacing-03', flexDirection: 'row' },
                        }}
                    >
                        <Box
                            css={{
                                width: '100%',
                                '@md': {
                                    width: '260px',
                                },
                            }}
                        >
                            <InputSearchbox
                                key={tab} // handle reset defaultValue search when tab changed
                                placeholder={t('translation:placeholder.search', { defaultValue: 'Cari ...' })}
                                onChange={debounce(keyword => {
                                    setFilterReport(prev => ({
                                        ...prev,
                                        search: keyword,
                                    }));
                                }, 600)}
                            />
                        </Box>
                        {isMobile ? (
                            <Button
                                leftIcon={<FilterOutline color="currentColor" />}
                                buttonType="secondary"
                                size="sm"
                                onClick={() => setFilterMobileOpen(true)}
                            >
                                Filter
                            </Button>
                        ) : (
                            <React.Fragment>
                                <Box>
                                    <InputDatePicker
                                        type={ADVANCE_PICKER}
                                        onChange={value => {
                                            setFilterReport(prev => ({
                                                ...prev,
                                                date: value,
                                            }));
                                        }}
                                        date={filterReport.date}
                                        css={{
                                            width: '100%',
                                            '@md': { width: 258 },
                                        }}
                                        minDate={moment('01-10-2024', 'DD-MM-YYYY').toDate()}
                                    />
                                </Box>
                                {isTransactionTab && (
                                        <Box
                                            css={{
                                                width: '100%',
                                                '& > div > div': { borderColor: '$gray300' },
                                                '@md': { width: 220 },
                                            }}
                                        >
                                            <InputSelect
                                                size="sm"
                                                option={dataTipeQRIS(t)}
                                                placeholder="Tipe QRIS"
                                                value={dataTipeQRIS(t).find(f => f.value === filterReport.selectTipeQRIS)}
                                                onChange={value => {
                                                    setFilterReport(prev => ({
                                                        ...prev,
                                                        selectTipeQRIS: dataTipeQRIS(t).find(f => f.value === value.value)
                                                            .value,
                                                    }));
                                                }}
                                            />
                                        </Box>
                                    )}
                                {isTransactionTab && (
                                        <Box
                                            css={{
                                                width: '100%',
                                                '& > div > div': { borderColor: '$gray300' },
                                                '@md': { width: 220 },
                                            }}
                                        >
                                            <InputSelect
                                                size="sm"
                                                option={dataListStatus(t, tab)}
                                                placeholder="Status"
                                                value={dataListStatus(t, tab).find(
                                                    f => f.value === filterReport.selectStatus,
                                                )}
                                                onChange={value => {
                                                    setFilterReport(prev => ({
                                                        ...prev,
                                                        selectStatus: dataListStatus(t, tab).find(
                                                            f => f.value === value.value,
                                                        ).value,
                                                    }));
                                                }}
                                            />
                                        </Box>
                                    )}
                            </React.Fragment>
                        )}
                    </Box>
                    <Separator
                        css={{
                            display: 'block',
                        }}
                    />
                    {tab === TAB_LIST_VALUE.TRANSAKSI_BERHASIL && (
                        <CardWrapper
                            data={[
                                {
                                    color: 'green',
                                    label: t('Report/Settlement/ewallet:summary.totalSales', 'Total Sales'),
                                    description: currency({
                                        value: dataSummary.transaction_amount || 0,
                                        decimal: String(dataSummary.transaction_amount) !== '0.00',
                                        convertMinus: true,
                                    }),
                                    tooltip: t(
                                        'Report/Settlement/ewallet:tooltip.totalSales',
                                        'Total transaksi dengan pembayaran berhasil',
                                    ),
                                },
                                {
                                    color: 'purple',
                                    label: t('Report/Settlement/ewallet:summary.totalMDR', 'Total MDR'),
                                    description: currency({
                                        value: dataSummary.mdr || 0,
                                        decimal: String(dataSummary.mdr) !== '0.00',
                                        convertMinus: true,
                                    }),
                                    tooltip: t(
                                        'Report/Settlement/ewallet:tooltip.totalMDR',
                                        'Total biaya sebesar 0,07% atas pembayaran berhasil',
                                    ),
                                },
                                {
                                    color: 'blue',
                                    label: t('Report/Settlement/ewallet:summary.totalSettlement', 'Total Settlement'),
                                    description: currency({
                                        value: dataSummary.settlement || 0,
                                        decimal: String(dataSummary.settlement) !== '0.00',
                                        convertMinus: true,
                                    }),
                                    tooltip: t(
                                        'Report/Settlement/ewallet:tooltip.totalSettlement',
                                        'Total nominal yang berhasil dilakukan settlement',
                                    ),
                                },
                                {
                                    color: 'orange',
                                    label: t(
                                        'Report/Settlement/ewallet:summary.pendingSettlement',
                                        'Settlement Tertunda',
                                    ),
                                    description: currency({
                                        value: dataSummary.settlement_pending || 0,
                                        decimal: String(dataSummary.settlement_pending) !== '0.00',
                                        convertMinus: true,
                                    }),
                                    tooltip: t(
                                        'Report/Settlement/ewallet:tooltip.pendingSettlement',
                                        'Total nominal settlement yang prosesnya tertunda',
                                    ),
                                },
                            ]}
                            isMobile={isMobile}
                        />
                    )}
                    <Box>
                        <Table
                            id={`report_settlement_qris::${tab}`}
                            columns={columnsTable(t, tab, handleRowAction) || []}
                            data={dataTable || []}
                            totalData={metaTable.total}
                            pageIndex={metaTable.pageIndex}
                            rowLimit={metaTable.pageSize}
                            fetchData={handleFetchTab}
                            searchQuery={filterReport.search}
                            isLoading={isFetching}
                        />
                    </Box>
                </Flex>
            </Paper>
            {detailModal.isOpen && (
                <DetailTransaction
                    isOpen={detailModal.isOpen}
                    onOpenChange={open => setDetailModal(prev => ({ ...prev, isOpen: open }))}
                    data={detailModal.data}
                    tab={tab}
                    filterReport={filterReport}
                    filterBranch={filterBranch}
                    t={t}
                    isMobile={isMobile}
                    showProgress={showProgress}
                    hideProgress={hideProgress}
                    addNotification={addNotification}
                />
            )}
            {filterMobileOpen && (
                <FilterMobileDialog
                    open={filterMobileOpen}
                    onClose={() => setFilterMobileOpen(false)}
                    onSubmit={() => {
                        handleFetchTab({ pageIndex: 0, pageSize: metaTable.pageSize });
                        setFilterMobileOpen(false);
                    }}
                    filterReport={filterReport}
                    setFilterReport={setFilterReport}
                    tab={tab}
                    t={t}
                />
            )}
            {dialogInfoExport.open && (
                <AlertDialog
                    isMobile={isMobile}
                    onConfirm={() => {
                        setDialogInfoExport(prev => ({ ...prev, open: false }));
                        addNotification({
                            title: t('toast.success', { ns: 'translation', defaultValue: 'Berhasil!' }),
                            message: (
                                <Trans
                                    t={t}
                                    i18nKey="toast.successRequestExport"
                                    components={{ strong: <b /> }}
                                    values={{ menu: title.replace('Laporan', '').replace('Report', '') }}
                                    ns="Keuangan/exportReport"
                                >
                                    Laporan
                                    {' '}
                                    <strong>Settlement QRIS</strong>
                                    {' '}
                                    dalam proses ekspor
                                </Trans>
                            ),
                            level: 'success',
                        });
                    }}
                    onCancel={() => setDialogInfoExport(prev => ({ ...prev, open: false }))}
                    open={dialogInfoExport.open}
                    title={dialogInfoExport.state.title}
                    description={dialogInfoExport.state.description}
                    labelConfirm={(
                        <Trans
                            t={t}
                            ns="translation"
                            i18nKey="label.confirm"
                            defaults="Oke, Mengerti"
                        />
                    )}
                    labelCancel={(
                        <Trans
                            t={t}
                            ns="translation"
                            i18nKey="label.close"
                            defaults="Tutup"
                        />
                    )}
                    css={{
                        width: isMobile ? 'unset' : '422px',
                    }}
                    actionButtonProps={{ size: 'md' }}
                    cancelButtonProps={{ size: 'md' }}
                    singleButton={!dialogInfoExport.state.btnCloseOnly}
                    hideActionButton={dialogInfoExport.state.btnCloseOnly}
                />
            )}
        </React.Fragment>
    );
}

Settlement.propTypes = {
    calendar: PropTypes.shape({
        start: PropTypes.string.isRequired,
        end: PropTypes.string.isRequired,
    }),
    addNotification: PropTypes.func.isRequired,
    showProgress: PropTypes.func.isRequired,
    hideProgress: PropTypes.func.isRequired,
    filterBranch: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
};

Settlement.defaultProps = {
    calendar: {
        start: '',
        end: '',
    },
};

const mapStateToProps = state => ({
    listCabang: state.branch.list,
});

export default connect(mapStateToProps)(CoreHOC(Settlement));
