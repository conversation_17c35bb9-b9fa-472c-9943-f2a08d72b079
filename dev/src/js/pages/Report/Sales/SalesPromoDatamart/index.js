import React, {
    useCallback,
    useEffect,
    useState,
    useRef,
} from 'react';
import PropTypes from 'prop-types';
import queryString from 'query-string';
import {
    Paper,
    Separator,
    Table,
    ToastContext,
    AlertDialog,
    Box,
    Flex,
} from '@majoo-ui/react';
import moment from 'moment';
import { Trans, withTranslation } from 'react-i18next';
import { useGraphicPeriodState } from '~/hooks/useGraphicPeriodeState';
import { useSearchState } from '~/hooks/useSearchState';
import CoreHOC from '../../../../core/CoreHOC';
import TitleSection from './components/TitleSection';
import FilterSection from './components/FilterSection';
import SummarySection from './components/SummarySection';
import FilterMobileModal from './components/FilterMobileModal';
import { useMediaQuery } from '../../../../utils/useMediaQuery';
import {
    COLUMNS_TABLE,
    parseSummary,
    getLegendData,
    fillChartData,
    handleChartComparator<PERSON>hange,
    parseDateForChart,
} from './utils';
import { catchError, resetDateRangeHelper } from '../../../../utils/helper';
import { LineChartv2, handleXaxis } from '../../../../components/chart';
import FilterChart from './components/FilterChart';
import {
    getPromoDatamart,
    getPromoChartDatamart,
    getPromoRequestReportDatamart,
    getSalesPromoSummaryDatamart,
} from '../../../../data/reports';
import { BannerText, SalesIncreasement } from '../../../../components/retina';

const paperResponsive = {
    '@sm': {
        padding: 0,
        backgroundColor: 'transparent',
        boxShadow: 'none',
    },
    '@md': {
        padding: '$spacing-05',
        backgroundColor: '$white',
        boxShadow: '0px 2px 12px #00000014',
    },
};

const SalesPromo = (props) => {
    const {
        calendar: propsCalendar,
        filterBranch,
        router,
        location,
        t,
        showProgress,
        hideProgress,
        assignCalendar,
    } = props;
    const { addToast } = React.useContext(ToastContext);
    const isMobile = useMediaQuery('(max-width: 1090px)');
    const isTablet = useMediaQuery('(max-width: 1024px)');
    const overviewRef = useRef();
    const [isLoading, setLoading] = useState(false);
    const [increasementNominal, setIncreasementNominal] = useState(0);
    const [dialogInfoExport, setDialogInfoExport] = useState({
        isDialog: false,
        state: {
            title: '',
            description: '',
            btnCloseOnly: false,
        },
    });
    const { periodOptions, period, onPeriodChange } = useGraphicPeriodState({ excluded: ['hour'] });
    const { keyword, onKeywordChange } = useSearchState();
    const [filterDownload, setFilterDownload] = useState({});
    const [openFilterModal, setOpenFilterModal] = useState(false);
    const [dataChart, setDataChart] = useState({
        chartData: [],
        legendData: [],
        comparatorList: [],
        original: [],
    });
    const [dataIds, setDataIds] = useState([]);
    const [dataTable, setDataTable] = useState({
        data: [],
        meta: {
            total: 0,
            current_page: 1,
            per_page: 10,
        },
    });
    const [summary, setSummary] = useState([]);
    const [pageIdx, setPageIdx] = useState(0);
    const [pageLimit, setPageLimit] = useState(10);
    const goToOverviewSection = () => overviewRef.current.scrollIntoView({ behavior: 'smooth' });
    const fetchSummaryIncreasement = async () => {
        const promoSummary = await getSalesPromoSummaryDatamart({
            start_date: moment().startOf('month').format('YYYY-MM-DD'),
            end_date: moment().endOf('month').format('YYYY-MM-DD'),
            ...filterBranch && { outlet_id: filterBranch },
        });
        setIncreasementNominal(promoSummary.data.transaction.total_value);
    };
    const fetchListPromo = async (params) => {
        if (params && params.sortAccessor && params.sortDirection) {
            setFilterDownload({
                order: params.sortAccessor,
                sort: params.sortDirection,
            });
        }
        try {
            setLoading(true);
            const payload = {
                start_date: moment(propsCalendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                end_date: moment(propsCalendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                ...filterBranch && { outlet_id: filterBranch },
                page: params.pageIndex + 1 || 1,
                limit: params.pageLimit || params.pageSize || 10,
                ...keyword && { search: keyword },
                ...params && params.sortAccessor !== undefined && { order: params.sortAccessor === 'promo.name' ? 'name' : params.sortAccessor },
                ...params && params.sortDirection !== undefined && { sort: params.sortDirection },
            };
            setPageIdx(params.pageIndex);
            setPageLimit(params.pageLimit || params.pageSize);

            const res = await getPromoDatamart(payload);
            const { data: { summary: resSummary, promos }, meta } = res;
            setSummary(parseSummary(resSummary));
            setDataTable({ data: promos, meta: { total: meta.total, current_page: meta.current_page, per_page: meta.per_page } });
            
            const rawIds = new Set(promos.map(x => x.promo.id));
            setDataIds([...rawIds].slice(0, 5));

            const removeDuplicatedPromos = promos.reduce((prev, current) => {
                if (!prev.find(p => p.promo.id === current.promo.id)) {
                    prev.push(current);
                }
                return prev;
            }, []);
            setDataChart(current => ({
                ...current,
                comparatorList: removeDuplicatedPromos.map(sales => ({
                    name: sales.promo.name,
                    value: sales.promo.id,
                })),
            }));

        } catch (e) {
            addToast({ title: 'Gagal!', description: catchError(e), variant: 'failed' });
        } finally {
            setLoading(false);
        }
    };
    const fetchChartPromo = async () => {
        try {
            setLoading(true);
            const payload = {
                start_date: moment(propsCalendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                end_date: moment(propsCalendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                group: period,
                ...(filterBranch && { outlet_id: filterBranch }),
                ...keyword && { search: keyword },
                ...dataIds.length > 0 && { ids: dataIds.map(id => String(id)).join(',') },
            };
            const res = await getPromoChartDatamart(payload);
            const { data } = res;
            if (res !== null && data.length > 0) {
                const mappedData = data.map(sales => ({
                    ...sales,
                    date: parseDateForChart(period, sales.date),
                }));
                const removeDuplicatedPromos = mappedData.reduce((prev, current) => {
                    if (!prev.find(p => p.promo.id === current.promo.id)) {
                        prev.push(current);
                    }
                    return prev;
                }, []);
                const legendData = getLegendData(removeDuplicatedPromos);
                setDataChart(prev => ({
                    ...prev,
                    chartData: fillChartData(
                        period,
                        moment(propsCalendar.start, 'DD/MM/YYYY').toDate(),
                        moment(propsCalendar.end, 'DD/MM/YYYY').toDate(),
                        mappedData,
                        legendData,
                    ),
                    legendData,
                    original: mappedData,
                }));
            } else {
                setDataChart({
                    chartData: [],
                    legendData: [],
                    comparatorList: [],
                    original: [],
                });
            }
        } catch (e) {
            addToast({ title: 'Gagal!', description: catchError(e), variant: 'failed' });
            setDataChart({
                chartData: [],
                legendData: [],
                comparatorList: [],
                original: [],
            });
        } finally {
            setLoading(false);
        }
    };
    const fetchDataTable = useCallback(fetchListPromo, [keyword, propsCalendar, filterBranch]);
    const goToPromoDetail = (data) => {
        const queryParams = queryString.stringify({
            name: data.promo.name,
            datadate: moment(data.date).format('YYYY-MM-DD'),
            startdate: moment(propsCalendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            enddate: moment(propsCalendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            periode: period,
            outletId: data.outlet_id || filterBranch,
        });
        router.push({
            pathname: `${location.pathname}/detail/${data.promo.id}`,
            search: `?${queryParams}`,
        });
    };
    const handleDownloadReport = async (type = 'xlsx') => {
        try {
            setLoading(true);
            const payload = {
                start_date: moment(propsCalendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                end_date: moment(propsCalendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                ...(filterBranch && { outlet_id: Number(filterBranch) }),
                ...(keyword && { search: keyword }),
                ...(filterDownload.order && { sort: `${filterDownload.sort === 'DESC' ? '-' : ''}${filterDownload.order}` }),
                ...(period && { time_period: period }),
                report_type: type,
                report_version: 2,
            };

            await getPromoRequestReportDatamart(payload);

            setDialogInfoExport({
                isDialog: true,
                state: {
                    title: (
                        <Trans
                            t={t}
                            i18nKey="modal.exportRequest.label"
                            defaults="Ekspor Laporan Promo"
                        />
                    ),
                    description: (
                        <Trans
                            t={t}
                            i18nKey="modal.exportRequest.description"
                            defaults="Mohon menunggu, sistem sedang memproses <strong>Detail Penjualan</strong>. Anda akan menerima pemberitahuan notifikasi dan email saat data siap untuk diunduh."
                            components={{ bold: <b /> }}
                        />
                    ),
                    btnCloseOnly: false,
                },
            });
        } catch (error) {
            if (error.cause && error.cause.status && error.cause.status.code) {
                if (Number(error.cause.status.code) === 42200001) {
                    setDialogInfoExport({
                        isDialog: true,
                        state: {
                            title: (
                                <Trans
                                    t={t}
                                    i18nKey="label.exportReport"
                                    defaults="Ekspor Laporan"
                                />
                            ),
                            description: error.cause.status.message,
                            btnCloseOnly: true,
                        },
                    });
                    return;
                }
            }
            addToast({ title: 'Gagal!', description: catchError(error), variant: 'failed' });
        } finally {
            setLoading(false);
        }
    };

    // const handlePromoSearch = debounce(async (search = '') => {
    //     if (!search) {
    //         return;
    //     }
    //     const payload = {
    //         start_date: moment(propsCalendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
    //         end_date: moment(propsCalendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
    //         search,
    //     };
    //     try {
    //         const res = await getPromoDatamart(payload);
    //         const { data: { promos } } = res;
    //         const removeDuplicatedPromos = promos.reduce((prev, current) => {
    //             if (!prev.find(p => p.promo.id === current.promo.id)) {
    //                 prev.push(current);
    //             }
    //             return prev;
    //         }, []);
    //         setDataChart(current => ({
    //             ...current,
    //             comparatorList: removeDuplicatedPromos.map(sales => ({
    //                 name: sales.promo.name,
    //                 value: sales.promo.id,
    //             })),
    //         }));
    //     } catch (e) {
    //         addToast({
    //             title: t('translation:toast.error', 'Gagal!'),
    //             description: catchError(e),
    //             variant: 'failed',
    //         });
    //     }
    // }, 700);

    const handleAutoScrollToOverview = () => {
        const params = queryString.parse(window.location.search);
        if (Object.keys(params).length > 0 && !!params.is_go_to_overview) {
            goToOverviewSection();
        }
    };

    const handleChangeDateRange = (value) => {
        const { newStartDate, newEndDate, isForceReset } = resetDateRangeHelper(value[0], value[1], 366, true, 12);
        if (isForceReset) {
            addToast({
                title: t('toast.errorMaxDate', 'Terjadi Kesalahan!', { ns: 'translation' }),
                description: t('toast.errorMaxDateRangeYear', 'Maksimum rentang waktu yang dapat dipilih: 1 tahun', {
                    ns: 'translation',
                }),
                variant: 'pending',
            });
            assignCalendar(newStartDate, newEndDate);
        } else {
            assignCalendar(moment(value[0]).format('DD-MM-YYYY'), moment(value[1]).format('DD-MM-YYYY'), null, null);
        }
    };

    const title = t('Report/Sales/promo:promoReport', { defaultValue: 'Laporan Promo' });

    useEffect(() => {
        handleAutoScrollToOverview();
    }, []);

    useEffect(() => {
        fetchSummaryIncreasement();
    }, [filterBranch]);

    useEffect(() => {
        fetchListPromo({ pageIndex: 0, pageLimit });
    }, [keyword, JSON.stringify(propsCalendar), filterBranch]);

    useEffect(() => {
        // Grafik ketrigger hanya jika ada dataIds ambil 5 data dari list table
        if (dataIds.length > 0) {
            fetchChartPromo();
        } else {
            setDataChart({
                chartData: [],
                legendData: [],
                comparatorList: [],
                original: [],
            });
        }
    }, [period, JSON.stringify(dataIds)]);

    useEffect(() => {
        if (isLoading) {
            showProgress();
        } else {
            hideProgress();
        }
    }, [isLoading]);

    return (
        <React.Fragment>
            <Paper
                responsive
                css={paperResponsive}
            >
                <Flex css={{ flexDirection: 'column', rowGap: '$compact' }}>
                    <TitleSection title={title} propsCalendar={propsCalendar} downloadLaporan={handleDownloadReport} />
                    <Separator
                        css={{
                            '@sm': { display: 'none' },
                            '@md': { display: 'block' },
                        }}
                    />
                    <BannerText />
                    <FilterSection
                        isTablet={isTablet}
                        propsCalendar={propsCalendar}
                        onKeywordChange={onKeywordChange}
                        onOpenFilterClick={() => setOpenFilterModal(true)}
                        downloadLaporan={handleDownloadReport}
                        salesIncreasementHandler={goToOverviewSection}
                        increasementNominal={increasementNominal}
                        t={t}
                        title={title}
                        handleChangeDateRange={handleChangeDateRange}
                    />
                    <Separator />

                    <Box
                        css={{
                            mb: 20,
                            sm: { display: 'block' },
                            '@lg': { display: 'none' },
                        }}
                    >
                        <SalesIncreasement
                            value={increasementNominal}
                            promoPageHandler={goToOverviewSection}
                            isMobile={isMobile}
                            isTablet={isTablet}
                            t={t}
                        />
                    </Box>
                    <LineChartv2
                        data={dataChart.chartData}
                        legendData={dataChart.legendData}
                        title="Grafik Promo"
                        xAxisKey="date"
                        period={period}
                        comparatorList={dataChart.comparatorList}
                        onComparatorChange={handleChartComparatorChange(
                            dataChart.original,
                            dataChart,
                            setDataChart,
                            period,
                            propsCalendar,
                            setDataIds,
                        )}
                        customXAxisFormat={date => handleXaxis(date, period)}
                        isEmptyData={dataChart.chartData === 0}
                    >
                        <FilterChart periodOptions={periodOptions} period={period} setPeriod={onPeriodChange} />
                    </LineChartv2>
                </Flex>

                <Flex
                    css={{
                        flexDirection: 'column',
                        rowGap: '$compact',
                        '@sm': { marginTop: '$spacing-04' },
                        '@md': { margin: 0 },
                    }}
                >
                    <SummarySection promoSummaries={summary} isMobile={isMobile} />
                    <div ref={overviewRef} css={{ display: 'none' }} />
                    <Table
                        id="report_promo_promo_dtm"
                        data={dataTable.data}
                        columns={COLUMNS_TABLE(data => goToPromoDetail(data))}
                        totalData={dataTable.meta.total}
                        isLoading={isLoading}
                        fetchData={fetchDataTable}
                        pageIndex={pageIdx}
                        rowLimit={pageLimit}
                    />
                </Flex>
            </Paper>
            {openFilterModal && (
                <FilterMobileModal
                    open={openFilterModal}
                    onOpenChange={open => setOpenFilterModal(open)}
                    propsCalendar={propsCalendar}
                    handleChangeDateRange={handleChangeDateRange}
                    t={t}
                />
            )}
            <AlertDialog
                isMobile={isMobile}
                onConfirm={() => {
                    const { addNotification } = props;
                    setDialogInfoExport(prev => ({ ...prev, isDialog: false }));
                    addNotification({
                        title: t('toast.success', { ns: 'translation', defaultValue: 'Berhasil!' }),
                        message: (
                            <Trans t={t} i18nKey="toast.successExportRequest" components={{ bold: <b /> }}>
                                Laporan <strong>Promo</strong> dalam proses ekspor
                            </Trans>
                        ),
                        level: 'success',
                    });
                }}
                onCancel={() => setDialogInfoExport(prev => ({ ...prev, isDialog: false }))}
                open={dialogInfoExport.isDialog}
                title={dialogInfoExport.state.title}
                description={dialogInfoExport.state.description}
                labelConfirm={<Trans t={t} i18nKey="label.confirm" defaults="Oke, Mengerti" />}
                labelCancel={<Trans t={t} i18nKey="label.close" defaults="Tutup" />}
                css={{
                    width: isMobile ? 'unset' : '422px',
                }}
                actionButtonProps={{ size: 'md' }}
                cancelButtonProps={{ size: 'md' }}
                singleButton={!dialogInfoExport.state.btnCloseOnly}
                hideActionButton={dialogInfoExport.state.btnCloseOnly}
            />
        </React.Fragment>
    );
};

SalesPromo.propTypes = {
    calendar: PropTypes.shape({
        start: PropTypes.string,
        end: PropTypes.string,
    }).isRequired,
    filterBranch: PropTypes.string.isRequired,
    router: PropTypes.shape({
        push: PropTypes.func.isRequired,
    }).isRequired,
    location: PropTypes.shape({
        pathname: PropTypes.string,
    }),
    addNotification: PropTypes.func.isRequired,
    t: PropTypes.func.isRequired,
    showProgress: PropTypes.func,
    hideProgress: PropTypes.func,
    i18n: PropTypes.shape({
        language: PropTypes.string.isRequired,
    }).isRequired,
    assignCalendar: PropTypes.func.isRequired,
};

SalesPromo.defaultProps = {
    showProgress: () => { },
    hideProgress: () => { },
    location: {
        pathname: '',
    }
};

export default withTranslation(['Report/Sales/promo', 'translation'])(CoreHOC(SalesPromo));
