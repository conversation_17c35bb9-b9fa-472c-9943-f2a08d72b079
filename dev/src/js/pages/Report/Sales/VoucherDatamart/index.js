import React, { useEffect, useState } from 'react';
import { debounce } from 'lodash';
import { Paper, Separator, Table, ToastContext } from '@majoo-ui/react';
import moment from 'moment';
import queryString from 'query-string';
import { Trans, useTranslation } from 'react-i18next';
import CoreHOC from '../../../../core/CoreHOC';
import TitleSection from './components/TitleSection';
import FilterSection from './components/FilterSection';
import FilterSectionGraphic from './components/FilterSectionGraphic';
import FilterMobileModal from './components/FilterMobileModal';
import {
    PERIOD_TYPE,
    COLUMNS_TABLE,
    parseSummary,
    getLegendData,
    fillChartData,
    handleChartComparatorChange,
    parseDateForChart,
} from './utils';
import { catchError, resetDateRangeHelper } from '../../../../utils/helper';
import { LineChartv2, handleXaxis } from '../../../../components/chart';
import { getVoucherDatamart, getVoucherChartDatamart, getVoucherReportDatamartV2 } from '../../../../data/reports';
import { BannerText } from '../../../../components/retina';
import { downloadTypePdf } from '../../../../components/retina/export/utils';
import { encodeName } from './utils/utils';
import { SummarySection } from './components/SummarySection';
import { isMobile as mobileQuery } from '../../../../config/config';

const SalesVoucher = props => {
    const { calendar, filterBranch, router, location, showProgress, hideProgress, assignCalendar } = props;
    const { t } = useTranslation(['Penjualan/Laporan/kupon', 'translation']);
    const { addToast } = React.useContext(ToastContext);
    const isMobile = mobileQuery.matches;
    const [isLoading, setLoading] = useState(false);
    const [filter, setFilter] = useState({
        startDate:
            location.state && location.state.rangeDateStart
                ? moment(location.state.rangeDateStart).toDate()
                : calendar.start,
        endDate:
            location.state && location.state.rangeDateEnd ? moment(location.state.rangeDateEnd).toDate() : calendar.end,
        periodType: PERIOD_TYPE(t)[0].value,
        search: '',
    });
    const [filterDownload, setFilterDownload] = useState({});
    const [openFilterModal, setOpenFilterModal] = useState(false);
    const [dataChart, setDataChart] = useState({
        chartData: [],
        legendData: [],
        comparatorList: [],
        original: [],
    });
    const [dataTable, setDataTable] = useState({
        data: [],
        meta: {
            total: 0,
            current_page: 1,
            per_page: 10,
        },
    });
    const [summary, setSummary] = useState(parseSummary(undefined, true));
    const [pageLimit, setPageLimit] = useState(10);

    const fetchListVoucher = async tableState => {
        const params = tableState;
        if (params && params.sortAccessor && params.sortDirection) {
            if (params.sortAccessor === 'coupon.name') params.sortAccessor = 'coupon_name';
            setFilterDownload({
                order: params.sortAccessor,
                sort: params.sortDirection,
            });
        }
        try {
            setLoading(true);
            const payload = {
                start_date: moment(filter.startDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
                end_date: moment(filter.endDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
                page: params.pageIndex + 1 || 1,
                limit: params.pageLimit || params.pageSize || 10,
                ...(filterBranch && { outlet_id: filterBranch }),
                ...(filter.search && { search: filter.search }),
                ...(params && params.sortAccessor !== undefined && { order: params.sortAccessor }),
                ...(params && params.sortDirection !== undefined && { sort: params.sortDirection }),
            };
            if (params.pageSize) setPageLimit(params.pageSize);
            const res = await getVoucherDatamart(payload);
            const {
                data: { summary: resSummary, coupons },
                meta,
            } = res;
            setSummary(parseSummary(resSummary));
            setDataTable({
                data: coupons,
                meta: { total: meta.total, current_page: meta.current_page, per_page: meta.per_page },
            });
        } catch (e) {
            addToast({ title: t('translation:toast.error', 'Gagal!'), description: catchError(e), variant: 'failed' });
        } finally {
            setLoading(false);
        }
    };

    const fetchChartVoucher = async () => {
        try {
            setLoading(true);
            const payload = {
                start_date: moment(filter.startDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
                end_date: moment(filter.endDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
                group: filter.periodType,
                ...(filterBranch && { outlet_id: filterBranch }),
                ...(filter.search && { search: filter.search }),
            };
            const res = await getVoucherChartDatamart(payload);
            const { data } = res;
            if (res !== null && data.length > 0) {
                const mappedData = data.map(sales => ({
                    ...sales,
                    date: parseDateForChart(filter.periodType, sales.date),
                }));
                const removeDuplicatedVouchers = mappedData.reduce((prev, current) => {
                    if (!prev.find(p => p.coupon.name === current.coupon.name)) {
                        prev.push(current);
                    }
                    return prev;
                }, []);
                const legendData = getLegendData(removeDuplicatedVouchers);
                setDataChart({
                    chartData: fillChartData(
                        filter.periodType,
                        moment(filter.startDate, 'DD-MM-YYYY').toDate(),
                        moment(filter.endDate, 'DD-MM-YYYY').toDate(),
                        mappedData,
                        legendData,
                    ),
                    legendData,
                    comparatorList: removeDuplicatedVouchers.map(sales => ({
                        name: sales.coupon.name,
                        value: sales.coupon.name,
                    })),
                    original: mappedData,
                });
            } else {
                setDataChart({
                    chartData: [],
                    legendData: [],
                    comparatorList: [],
                    original: [],
                });
            }
        } catch (e) {
            addToast({ title: t('translation:toast.error', 'Gagal!'), description: catchError(e), variant: 'failed' });
        } finally {
            setLoading(false);
        }
    };

    const goToVoucherDetail = data => {
        const queryParams = queryString.stringify({
            coupon: encodeName(data.coupon.name),
            datadate: moment(data.date).utc().format('YYYY-MM-DD'),
            startdate: moment(filter.startDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
            enddate: moment(filter.endDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
            is_testing: 1,
        });
        router.push({
            pathname: `${location.pathname}/detail/${data.coupon.id}`,
            search: queryParams,
            state: {
                data,
                rangeDateStart: filter.startDate,
                rangeDateEnd: filter.endDate,
            },
        });
    };

    const handleDownloadReport = async (type = 'xlsx') => {
        try {
            setLoading(true);
            const payload = {
                report_type: type,
                start_date: moment(filter.startDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
                end_date: moment(filter.endDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
                ...(filterBranch && { outlet_id: filterBranch }),
                ...(filter.search && { search: filter.search }),
                ...(filterDownload.sort && {
                    sort: filterDownload.sort === 'DESC' ? `-${filterDownload.order}` : filterDownload.order,
                }),
            };

            const { data } = await getVoucherReportDatamartV2(payload);
            if (type === 'xlsx') window.location = data;
            else downloadTypePdf(data);

            addToast({
                title: t('translation:toast.success', 'Berhasil!'),
                description: (
                    <Trans t={t} i18nKey="toast.exportSuccess">
                        <span>
                            <b>Laporan Kupon</b> berhasil diekspor
                        </span>
                    </Trans>
                ),
                variant: 'success',
            });
        } catch (e) {
            addToast({ title: t('translation:toast.error', 'Gagal!'), description: catchError(e), variant: 'failed' });
        } finally {
            setLoading(false);
        }
    };

    const handleVoucherSearch = debounce(async (search = '') => {
        if (!search) {
            return;
        }
        const payload = {
            start_date: moment(filter.startDate, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            end_date: moment(filter.endDate, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            search,
        };
        try {
            const res = await getVoucherDatamart(payload);
            const {
                data: { coupons },
            } = res;
            const removeDuplicatedVouchers = coupons.reduce((prev, current) => {
                if (!prev.find(p => p.coupon.name === current.coupon.name)) {
                    prev.push(current);
                }
                return prev;
            }, []);
            setDataChart(current => ({
                ...current,
                comparatorList: removeDuplicatedVouchers.map(sales => ({
                    name: sales.coupon.name,
                    value: sales.coupon.name,
                })),
            }));
        } catch (e) {
            addToast({
                title: t('translation:toast.error', 'Gagal!'),
                description: catchError(e),
                variant: 'failed',
            });
        }
    }, 700);

    const handleChangeDateRange = (value, onChange) => {
        const { newStartDate, newEndDate, isForceReset } = resetDateRangeHelper(value[0], value[1], 366, true, 12);
        if (isForceReset) {
            addToast({
                title: t('toast.errorMaxDate', 'Terjadi Kesalahan!', { ns: 'translation' }),
                description: t('toast.errorMaxDateRangeYear', 'Maksimum rentang waktu yang dapat dipilih: 1 tahun', {
                    ns: 'translation',
                }),
                variant: 'pending',
            });
            onChange([moment(newStartDate, 'DD/MM/YYYY').format('DD-MM-YYYY'), moment(newEndDate, 'DD/MM/YYYY').format('DD-MM-YYYY')]);
        } else {
            onChange([moment(value[0]).format('DD-MM-YYYY'), moment(value[1]).format('DD-MM-YYYY')]);
        }
    };

    useEffect(() => {
        if (!filter.startDate) return;
        assignCalendar(
            moment(filter.startDate, 'DD/MM/YYYY').format('DD/MM/YYYY'),
            moment(filter.endDate, 'DD/MM/YYYY').format('DD/MM/YYYY'),
        );
    }, [filter.startDate, filter.endDate]);

    useEffect(() => {
        fetchListVoucher({ pageLimit, pageIndex: 0 });
    }, [filter.startDate, filter.endDate, filter.search, filterBranch]);

    useEffect(() => {
        fetchChartVoucher();
    }, [filter.startDate, filter.endDate, filter.search, filter.periodType, filterBranch]);

    useEffect(() => {
        if (isLoading) {
            showProgress();
        } else {
            hideProgress();
        }
    }, [isLoading]);

    return (
        <React.Fragment>
            <Paper responsive css={{ padding: 0, '@md': { padding: '$compact' } }}>
                <TitleSection
                    title={t('title', 'Laporan Kupon')}
                    filter={filter}
                    downloadLaporan={handleDownloadReport}
                    isPrimary
                />
                {!isMobile && <Separator />}
                <BannerText />
                <FilterSection
                    isMobile={isMobile}
                    filter={filter}
                    setFilter={setFilter}
                    onOpenFilterClick={() => setOpenFilterModal(true)}
                    downloadLaporan={handleDownloadReport}
                    title={t('title', 'Laporan Kupon')}
                    translator={t}
                    handleChangeDateRange={handleChangeDateRange}
                    assignCalendar={assignCalendar}
                />
                <Separator css={{ my: '20px', '@lg': { mb: '20px' } }} />
                <LineChartv2
                    key={`${dataChart.chartData.length}-kupon-${filter.periodType}`}
                    data={dataChart.chartData}
                    legendData={dataChart.legendData}
                    title={t('chartTitle', 'Grafik Kupon')}
                    xAxisKey="date"
                    period={filter.periodType}
                    comparatorList={dataChart.comparatorList}
                    onComparatorChange={handleChartComparatorChange(
                        dataChart.original,
                        dataChart,
                        setDataChart,
                        filter,
                        handleVoucherSearch,
                    )}
                    customXAxisFormat={date => handleXaxis(date, filter.periodType)}
                    isEmptyData={dataChart.chartData === 0}
                >
                    <FilterSectionGraphic filter={filter} setFilter={setFilter} PERIOD_TYPE={PERIOD_TYPE(t)} />
                </LineChartv2>
                <SummarySection voucherSummaries={summary} translator={t} />
                <Table
                    id="sales_coupon_report"
                    data={dataTable.data}
                    columns={COLUMNS_TABLE(data => goToVoucherDetail(data), t)}
                    onRowClick={row => goToVoucherDetail(row.original)}
                    totalData={dataTable.data && dataTable.data.length > 0 ? dataTable.meta.total : 0}
                    isLoading={isLoading}
                    fetchData={fetchListVoucher}
                    searchQuery={filter.search}
                />
            </Paper>
            {openFilterModal && (
                <FilterMobileModal
                    open={openFilterModal}
                    onOpenChange={open => setOpenFilterModal(open)}
                    filter={filter}
                    setFilter={setFilter}
                    handleChangeDateRange={handleChangeDateRange}
                    assignCalendar={assignCalendar}
                />
            )}
        </React.Fragment>
    );
};

export default CoreHOC(SalesVoucher);
