import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { PRINT_TYPE } from '../../../setting/enum';
import { boldStyle, invoiceNumberStyle } from '../../../../InvoiceSales/component/ModalDotMatrix/style';

const QuotationNumber = ({ data, LANG_DATA }) => {
    const title = LANG_DATA.DOTMATRIX.HEADER.QUOTATION;

    return (
        <table style={invoiceNumberStyle().table}>
            <tbody>
                <tr>
                    <td style={{ ...invoiceNumberStyle().td, paddingLeft: '0px', borderLeft: '0px' }}>
                        <div style={{ ...invoiceNumberStyle().container, paddingLeft: '0px', borderLeft: '0px' }}>
                            <div style={invoiceNumberStyle().darkColor}>
                                <b>{LANG_DATA.DOTMATRIX.HEADER.QUOTATION_NO}</b>
                            </div>
                            <div>
                                {data.sales_quote_no}
                            </div>
                        </div>
                    </td>
                    <td style={invoiceNumberStyle().td}>
                        <div style={invoiceNumberStyle().container}>
                            <div style={invoiceNumberStyle().darkColor}>
                                <b>{LANG_DATA.DOTMATRIX.HEADER.DATE}</b>
                            </div>
                            <div>
                                {moment(data.date).format('DD MMMM YYYY HH:mm')}
                            </div>
                        </div>
                    </td>
                    <td style={invoiceNumberStyle().td}>
                        <div style={invoiceNumberStyle().container}>
                            <div style={invoiceNumberStyle().darkColor}>
                                <b>{LANG_DATA.DOTMATRIX.HEADER.DUE_DATE}</b>
                            </div>
                            <div>
                                {moment(data.due_date).format('DD MMMM YYYY HH:mm')}
                            </div>
                        </div>
                    </td>
                    <td
                        style={{
                            ...invoiceNumberStyle().td,
                            textAlign: 'right',
                            padding: '0px',
                            borderLeft: '0px',
                            width: '100%',
                        }}
                    >
                        <h2 style={{ position: 'relative', top: '9px' }} className="title">
                            <b style={boldStyle}>{title}</b>
                        </h2>
                    </td>
                </tr>
            </tbody>
        </table>
    );
};

QuotationNumber.propTypes = {
    data: PropTypes.shape({
        invoice_no: PropTypes.string,
        date: PropTypes.string,
        due_date: PropTypes.string,
    }).isRequired,
    LANG_DATA: PropTypes.shape({}).isRequired,
};

export default QuotationNumber;
