import { useEffect, useState } from 'react';
import { debounce } from 'lodash';
import * as customerApi from '../../../../../../data/customers';

export const useCustomerInfo = (props) => {
    const { handleChange, clearErrors, form } = props;
    const [dummyCustomerName, setDummyCustomerName] = useState(form.customerName)
    const [dataSource, setDataSource] = useState({
        option: [],
        rawData: [],
    });

    const handleFetch = async (search) => {
        try {
            const payload = {
                is_cms: 1, // required for cms
                order: 'createdate',
                sort: 'DESC',
                ...search && { search },
            };

            const { data, status, msg } = await customerApi.getCustomer(payload);

            if (!status) throw Error(msg);

            const option = data.map(val => `${val.name} (${val.code})`);

            setDataSource({
                option,
                rawData: data,
            });
        } catch (error) {
            console.error(e);
        }
    };

    const setCustomer = (values) => {
        clearErrors('customerId');
        const newAddress = {
            SHIPMENT: { ...form.address.SHIPMENT },
            CUSTOMER: { ...form.address.CUSTOMER },
            OUTLET: { ...form.address.OUTLET },
        };

        if (values) {
            handleChange(values.id_customer, 'customerId');
            handleChange(values.customer_name, 'customerName');

            const detailAddress = {
                address: values.customer_alamat,
                phone: values.customer_notlp,
                email: values.customer_email,
            };

            if (form.address.SHIPMENT.checked) {
                newAddress.CUSTOMER = { ...detailAddress };
                newAddress.SHIPMENT = { ...detailAddress, checked: true };
            } else {
                newAddress.CUSTOMER = { ...detailAddress };
            }

            handleChange(newAddress, 'address');
        }
    };

    const handleSetCustomer = debounce(value => setCustomer(value), 500);

    const handleChangeCheckbox = (value) => {
        const newAddress = {
            SHIPMENT: { ...form.address.SHIPMENT },
            CUSTOMER: { ...form.address.CUSTOMER },
            OUTLET: { ...form.address.OUTLET },
        };

        if (!value) newAddress.SHIPMENT = { address: '', phone: '', email: '' };
        else newAddress.SHIPMENT = { ...form.address.CUSTOMER };

        newAddress.SHIPMENT.checked = value;

        handleChange(newAddress, 'address');
    };

    const handleChangeShipment = (value, key) => {
        const newAddress = {
            SHIPMENT: { ...form.address.SHIPMENT },
            CUSTOMER: { ...form.address.CUSTOMER },
            OUTLET: { ...form.address.OUTLET },
        };

        newAddress.SHIPMENT[key] = value;

        handleChange(newAddress, 'address');
    };

    useEffect(() => {
        if (form.customerName) setDummyCustomerName(form.customerName);
    }, [form.customerName]);

    const handleSaveConfirm = values => setCustomer(values);

    const refetchListComboBox = () => handleFetch('');

    return {
        option: dataSource.option,
        handleSetCustomer,
        handleChangeCheckbox,
        handleSaveConfirm,
        handleChangeShipment,
        dummyCustomerName,
        setDummyCustomerName,
        refetchListComboBox,
    };
};
