const priceIncludeTaxMockData = {
    nominalDisc: 2272.7272727272725,
    serviceChargeNominal: 295.45454545454544,
    totalDiscountProduct: 909.090909090909,
};

export const createCalculateTaxCases = () => {
    const stateWithoutRefund = {
        isRefund: false,
        nominalDisc: 2500,
        serviceChargeNominal: 0,
        settingRefund: undefined,
        taxValue: 10,
        totalDiscountProduct: 1000,
        totalPriceAfterDiscount: 9000,
        totalPriceBeforeDiscount: 10000,
    };
    const stateWithRefund = {
        ...stateWithoutRefund,
        settingRefund: { refund_percentage: '100' },
        isRefund: true,
    };
    const testCases = [
        {
            taxType: '',
            serviceChargeType: '',
            state: stateWithoutRefund,
            output: { taxAmount: 0, taxBase: 0 },
        },
        {
            taxType: '0',
            serviceChargeType: '',
            state: stateWithoutRefund,
            output: { taxAmount: 0, taxBase: 0 },
        },
        {
            taxType: '1',
            serviceChargeType: '1',
            state: { ...stateWithoutRefund, serviceChargeNominal: 325 },
            output: { taxAmount: 1032.5, taxBase: 10325 },
        },
        {
            taxType: '1',
            serviceChargeType: '2',
            state: { ...stateWithoutRefund, serviceChargeNominal: 325 },
            output: { taxAmount: 1000, taxBase: 10000 },
        },
        {
            taxType: '2',
            serviceChargeType: '1',
            state: { ...stateWithoutRefund, serviceChargeNominal: 325 },
            output: { taxAmount: 682.5, taxBase: 6825 },
        },
        {
            taxType: '2',
            serviceChargeType: '2',
            state: { ...stateWithoutRefund, serviceChargeNominal: 325 },
            output: { taxAmount: 650, taxBase: 6500 },
        },
        {
            taxType: '3',
            serviceChargeType: '1',
            state: { ...stateWithoutRefund, ...priceIncludeTaxMockData },
            output: { taxAmount: 620.4545454545454, taxBase: 6204.545454545453 },
        },
        {
            taxType: '3',
            serviceChargeType: '2',
            state: { ...stateWithoutRefund, ...priceIncludeTaxMockData },
            output: { taxAmount: 590.9090909090909, taxBase: 5909.090909090908 },
        },
        {
            taxType: '3',
            serviceChargeType: '2',
            state: { ...stateWithRefund, ...priceIncludeTaxMockData },
            output: { taxAmount: 590.9090909090909, taxBase: 5909.090909090908 },
        },
    ];

    return testCases;
};

export const createCalculateGrandTotalCases = () => {
    const baseState = {
        nominalDisc: 2500,
        serviceChargeNominal: 325,
        subtotal: 9000,
        totalCosts: 0,
        totalDiscountProduct: 1000,
    };
    const testCases = [
        {
            isTaxPerProduct: false,
            priceIncludesTax: false,
            state: { ...baseState, taxAmount: 1032.5 },
            output: 7857.5,
        },
        {
            isTaxPerProduct: true,
            priceIncludesTax: false,
            state: { ...baseState, taxAmount: 1032.5 },
            output: 7857.5,
        },
        {
            isTaxPerProduct: false,
            priceIncludesTax: true,
            state: { ...baseState, ...priceIncludeTaxMockData, subtotal: 9090.9090909090909, taxAmount: 620.4545454545454 },
            output: 6825.000000000001,
        },
        {
            isTaxPerProduct: true,
            priceIncludesTax: true,
            state: { ...baseState, ...priceIncludeTaxMockData, subtotal: 9090.9090909090909, taxAmount: 620.4545454545454 },
            output: 6204.545454545455,
        },
    ];

    return testCases;
};
