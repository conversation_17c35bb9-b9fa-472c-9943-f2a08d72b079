import { useEffect, useState } from 'react';
import moment from 'moment';
import { debounce } from 'lodash';
import { PROMO_CRITERIA_TYPE, PROMO_POINT_TYPES, PROMO_TYPES } from '../../utils/enum';
import { checkVoucherValidity, setListPromoManual } from './util';
import { CRITERIA_PROMO, POINT_REDEEM_TYPE, PROMO_TYPE, VOUCHER_UNIT_TYPE } from './enum';
import { handleFetchPointSetting } from '../../InvoiceSales/setting/utils';
import * as voucherApi from '../../../../data/promo';
import { catchError } from '../../../../utils/helper';
import { useTranslationHook } from '../../InvoiceSales/lang/lang.utils';
import { calculateData } from '../../utils/utils.calculation';

export const useModalPromo = (props) => {
    const {
        modalPointDetailRef, listPromoManual, subtotal, listProduct, submitPromo,
        modalProductSelectOne, outletId, addToast, allPromos, form,
        showProgress, hideProgress, open, setOpen, calculationState,
    } = props;

    const { LANG_DATA } = useTranslationHook();
    const [listPromo, setListPromo] = useState([]);
    // const [open, setOpen] = useState(false);
    const [promoType, setPromoType] = useState(PROMO_TYPES.PROMO);
    const [totalSelectedPromos, setTotalSelectedPromos] = useState(0);
    const [listVouchers, setListVouchers] = useState([]);
    const [voucherErrorMessage, setVoucherErrorMessage] = useState('');
    const [pointRedeemData, setPointRedeemData] = useState({
        discountData: null,
        productData: null,
    });
    const [promoPointType, setPromoPointType] = useState('');
    const [pointRedeemSetting, setPointRedeemSetting] = useState(null);
    const [customerPoint, setCustomerPoint] = useState(0);
    const [promoProductSelectOne, setPromoProductSelectOne] = useState({
        list: [],
        promo: null,
        perProduct: false,
    });
    const [search, setSearch] = useState('');

    const {
        grandTotal,
    } = calculateData({
        ...calculationState,
        pointRedeems: pointRedeemData
    });

    const handleClose = () => {
        setSearch('');
        setVoucherErrorMessage('');
        setOpen(false);
    };

    const handlePromoTypesFilter = (value) => {
        if (value) setPromoType(value);
    };

    const handleRemoveVoucher = async (key) => {
        // TODO: logic cancel voucher, kemungkinan akan dihapuse, karena terdapat 2 event antara simpan invoice, dengan book / cancel / claim voucher, tidak ideal dilakukan dari client (idealnya dihandle by API)
        // try {
        //     showProgress();
        //     const payload = { id_outlet: outletId, id_voucher: key };

        //     const { status, msg } = await voucherApi.cancelVoucher(payload);
        //     if (!status) throw new Error(msg);
            const filteredListVouchers = listVouchers.filter(item => item.key !== key);

            setTotalSelectedPromos(totalSelectedPromos - 1);
            setListVouchers(filteredListVouchers);
        // }
        // catch (error) {
        //     addToast({
        //         title: LANG_DATA.TOAST_ERROR,
        //         description: catchError(error),
        //         variant: 'failed',
        //         position: 'top-right',
        //     });
        // } finally {
        //     hideProgress();
        // }
    };

    const handleSubmitVoucher = async (code) => {
        showProgress();
        const newListVouchers = listVouchers;

        try {
            const payload = { id_outlet: outletId, code };

            const { data: voucherData, msg } = await voucherApi.getVoucherBook(payload);

            if (voucherData) {
                const { canAddVoucher, errorMessage } = checkVoucherValidity(newListVouchers, voucherData, LANG_DATA);

                if (canAddVoucher) {
                    const type = voucherData.voucher_unit_option;
                    const value = voucherData.voucher_unit_option_value;
                    const voucherHasNoLimit = voucherData.remaining_voucher === undefined;
                    const voucher = {
                        key: voucherData.id_voucher_unit,
                        id: voucherData.id_voucher_unit,
                        name: voucherData.voucher_unit_name,
                        type,
                        value: type === VOUCHER_UNIT_TYPE.PERCENT ? 0 : value,
                        percentage: type === VOUCHER_UNIT_TYPE.PERCENT ? value : 0,
                        remaining: voucherHasNoLimit ? '-' : voucherData.remaining_voucher,
                        date: moment(voucherData.voucher_unit_time_finish).format('DD MMMM YYYY'),
                        promoType: PROMO_TYPES.VOUCHER,
                        joinType: voucherData.unit_is_join,
                        maxChangeValue: parseFloat(voucherData.unit_max_change_value),
                    };

                    newListVouchers.push(voucher);
                    setVoucherErrorMessage('');
                    setTotalSelectedPromos(totalSelectedPromos + 1);
                    setListVouchers(newListVouchers);
                } else {
                    setVoucherErrorMessage(errorMessage);
                }
            } else {
                setVoucherErrorMessage(LANG_DATA.PROMO_MODAL.COUPON.ERROR.NOT_AVAILABLE);
            }
        } catch (error) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(error),
                variant: 'failed',
                position: 'top-right',
            });
        } finally {
            hideProgress();
        }
    };

    const handleSubmitPoint = (type, pointData) => {
        const {
            id, name, pointUsed, product, nominal, percentage, type: valueType, multiplier,
        } = pointData;
        let error = '';

        if (pointUsed > customerPoint) {
            error = LANG_DATA.PROMO_MODAL.POINT.ERROR.POINTS_NOT_SUFFICED;
        } else if (nominal > grandTotal) {
            error = LANG_DATA.PROMO_MODAL.POINT.ERROR.EXCEED_GRAND_TOTAL;
        } else if (type === PROMO_POINT_TYPES.PRODUCT) {
            const bonusProduct = form.products.find(val => String(val.product_id) === product.id);
            const productOnCart = form.products.find(val => val.product_id === bonusProduct.product_id);

            if (multiplier > productOnCart.qty) {
                error = LANG_DATA.PROMO_MODAL.POINT.ERROR.EXCEED_PRODUCT_QTY;
            } else if (pointRedeemSetting.productSetting.maxRedeem > 0 && pointUsed > pointRedeemSetting.productSetting.maxRedeem) {
                error = LANG_DATA.PROMO_MODAL.POINT.ERROR.EXCEED_POINT_LIMIT;
            } else {
                setPointRedeemData({
                    ...pointRedeemData,
                    productData: {
                        id,
                        name,
                        product,
                        pointUsed,
                        promoType: PROMO_TYPES.POINT,
                        type,
                        total: bonusProduct.price * multiplier,
                    },
                });
            }
        } else if (type === PROMO_POINT_TYPES.DISCOUNT) {
            if (pointRedeemSetting.nominalSetting.maxRedeem > 0 && nominal > pointRedeemSetting.nominalSetting.maxRedeem) {
                error = LANG_DATA.PROMO_MODAL.POINT.ERROR.EXCEED_POINT_LIMIT;
            } else {
                setPointRedeemData({
                    ...pointRedeemData,
                    discountData: {
                        id,
                        name,
                        valueType,
                        value: nominal,
                        percentage,
                        pointUsed,
                        promoType: PROMO_TYPES.POINT,
                        type,
                        multiplier,
                    },
                });
            }
        }

        if (error) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(error),
                variant: 'failed',
                position: 'top-right',
            });
        } else {
            setTotalSelectedPromos(totalSelectedPromos + 1);
            modalPointDetailRef.current.closeModal();
        }
    };

    const handleSelectPromo = (val, type) => {
        if (type === 'remove') {
            submitPromo(val, type, val.promo_type === PROMO_CRITERIA_TYPE.PRODUCT);
            setTotalSelectedPromos(totalSelectedPromos - 1);
        }
        else if (val.promo_type === PROMO_CRITERIA_TYPE.INVOICE) {
            if (val.bonus_type === PROMO_TYPE.PRODUCT && val.bonus_type_item_one) {
                setPromoProductSelectOne({
                    list: val.bonus_type_item_list.map(x => ({ ...x, id: x.majoo_product_id, name: x.majoo_product_name })),
                    promo: val,
                    perProduct: false,
                });
            } else if (val.bonus_type === PROMO_TYPE.PRODUCT) {
                const listBonusProduct = val.bonus_type_item_list.map(x => ({ ...x, id: x.majoo_product_id, name: x.majoo_product_name }));

                submitPromo({ ...val, bonus_type_item_list: listBonusProduct }, type);
            } else {
                submitPromo(val, type);
            }
        } else if (val.promo_type === PROMO_CRITERIA_TYPE.PRODUCT) {
            const newItem = val.list_promo_item.map(x => x.id_item);
            const newPromo = { ...val, item: newItem };
            if (val.bonus_type !== PROMO_TYPE.PRODUCT) {
                submitPromo(newPromo, type, true);
            } else if (val.bonus_type === PROMO_TYPE.PRODUCT) {
                if (parseInt(val.bonus_type_item_one_free, 10)) {
                    const products = listProduct.filter((x) => {
                        if (!val.list_promo_item.find(y => y.id_item === x.product_id)) return false;
                        if (val.minimal_criteria === CRITERIA_PROMO.PAYMENT) return parseFloat(x.total) >= parseFloat(val.minimal_criteria_value);
                        if (val.minimal_criteria === CRITERIA_PROMO.QUANTITY) return parseInt(x.qty, 10) >= parseInt(val.minimal_criteria_value, 10);
                    }).map(x => x.product_id);
                    setPromoProductSelectOne({
                        list: val.list_promo_item.filter(x => products.includes(x.id_item)).map(x => ({ ...x, id: x.id_item, name: x.item_name })),
                        promo: val,
                        perProduct: true,
                    });
                } else if (parseInt(val.bonus_type_item_sold, 10)) {
                    if (parseInt(val.bonus_type_item_one, 10)) {
                        const products = listProduct.filter((x) => {
                            if (!val.list_promo_item.find(y => y.id_item === x.product_id)) return false;
                            if (val.minimal_criteria === CRITERIA_PROMO.PAYMENT) return parseFloat(x.total) >= parseFloat(val.minimal_criteria_value);
                            if (val.minimal_criteria === CRITERIA_PROMO.QUANTITY) return parseInt(x.qty, 10) >= parseInt(val.minimal_criteria_value, 10);
                        }).map(x => x.product_id);
                        setPromoProductSelectOne({
                            list: val.list_promo_item.filter(x => products.includes(x.id_item)).map(x => ({ ...x, id: x.id_item, name: x.item_name })),
                            promo: val,
                            perProduct: true,
                        });
                    } else {
                        submitPromo({ ...newPromo, list_bonus_item: val.list_promo_item.map(x => ({ ...x, id: x.id_item, name: x.item_name })) }, type, true);
                    }
                } else if (parseInt(val.bonus_type_item_one, 10)) {
                    setPromoProductSelectOne({
                        list: val.list_bonus_item.map(x => ({ ...x, id: x.id_item, name: x.item_name })),
                        promo: val,
                        perProduct: true,
                    });
                } else {
                    submitPromo(newPromo, type, true);
                }
            }
        }

        if (type !== 'remove') setTotalSelectedPromos(totalSelectedPromos + 1);
    };

    const handleSelectOneProduct = (val) => {
        const reformatPromo = {
            ...promoProductSelectOne.promo,
            ...!promoProductSelectOne.perProduct ? {
                bonus_type_item_list: val,
            } : {
                list_bonus_item: val,
                ...(parseInt(promoProductSelectOne.promo.bonus_type_item_one_free, 10)
                || (parseInt(promoProductSelectOne.promo.bonus_type_item_sold, 10)
                && parseInt(promoProductSelectOne.promo.bonus_type_item_one, 10)))
                && {
                    list_promo_item: val,
                },
            },
        };

        submitPromo(reformatPromo, 'add', promoProductSelectOne.perProduct);

        modalProductSelectOne.current.closeModal();
        setPromoProductSelectOne({ promo: null, list: [] });
    };

    const getSelectedPromos = () => {
        setSearch('');

        return {
            voucher: listVouchers,
            point: pointRedeemData,
        };
    };

    const handleDefaultPromos = () => {
        const defaultVouchers = (allPromos || []).filter(item => item.promoType === PROMO_TYPES.VOUCHER);
        const discountData = pointRedeemData.discountData ? 1 : 0;
        const productData = pointRedeemData.productData ? 1 : 0;
        const totalPromos = (allPromos ? allPromos.length : 0) + discountData + productData;

        setListVouchers(defaultVouchers);
        setTotalSelectedPromos(totalPromos);
    };

    const handleModalPointDetail = (value) => {
        setPromoPointType(value);
        modalPointDetailRef.current.openModal();
    };

    const fetchPointRedeemSetting = async () => {
        try {
            const { nominalSetting, productSetting } = await handleFetchPointSetting();

            setPointRedeemSetting({ nominalSetting, productSetting });
        } catch (error) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(error),
                variant: 'failed',
                position: 'top-right',
            });
        }
    };

    const removePointRedeemData = (type) => {
        if (type === POINT_REDEEM_TYPE.NOMINAL) {
            setPointRedeemData({ ...pointRedeemData, discountData: null });
        } else {
            setPointRedeemData({ ...pointRedeemData, productData: null });
        }

        setTotalSelectedPromos(totalSelectedPromos - 1);
    };

    useEffect(() => {
        if (form.pointRedeems) setPointRedeemData(form.pointRedeems);
    }, []);

    useEffect(() => {
        if (open && listPromoManual.length > 0) setListPromo(setListPromoManual(listPromoManual, subtotal, listProduct));
    }, [open, listPromoManual]);

    useEffect(() => {
        if (open) handleDefaultPromos();
    }, [open]);

    useEffect(() => {
        if (open) setCustomerPoint(form.customerPoint || 0);
    }, [open, form.customerPoint]);

    const handleSearch = debounce(val => setSearch(val), 750);

    const handleFilterData = () => {
        const result = { listPromo: [], listVoucher: [], listPoint: [] };

        if (promoType === PROMO_TYPES.PROMO) {
            if (!search) result.listPromo = listPromo;
            else {
                result.listPromo = listPromo.filter(val => val.name.toLowerCase().includes(search.toLowerCase()));
            }
        } else if (promoType === PROMO_TYPES.VOUCHER) {
            if (!search) result.listVoucher = listVouchers;
            else {
                result.listVoucher = listVouchers.filter(val => val.name.toLowerCase().includes(search.toLowerCase()));
            }
        }

        return result;
    };

    useEffect(() => {
        if (open && !pointRedeemSetting) fetchPointRedeemSetting();
    }, [open, pointRedeemSetting]);

    return {
        open,
        setOpen,
        handleClose,
        totalSelectedPromos,
        listPromo: handleFilterData().listPromo,
        promoType,
        handlePromoTypesFilter,
        listVouchers: handleFilterData().listVoucher,
        voucherErrorMessage,
        handleRemoveVoucher,
        handleSubmitVoucher,
        handleModalPointDetail,
        promoPointType,
        customerPoint,
        handleSubmitPoint,
        handleSelectPromo,
        promoProductSelectOne,
        handleSelectOneProduct,
        getSelectedPromos,
        handleSearch,
        pointRedeemSetting,
        pointRedeemData,
        removePointRedeemData,
    };
};
