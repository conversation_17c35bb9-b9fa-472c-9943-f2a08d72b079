import {
    DISCOUNT_TYPE, INVOICE_STATUS, PROMO_CRITERIA_TYPE,
    SERVICE_CHARGE_TYPE, TAX_BASE_TYPE, TAX_TYPE,
} from './enum';
import {
    SALES_ORDER_STATUS_NAME
} from '../SalesOrder/setting/enum';
import { CRITERIA_PROMO, POINT_NOMINAL_TYPE, VOUCHER_UNIT_TYPE } from '../component/ModalPromo/enum';

export const calculateTax = (taxType, SCType, state) => {
    const {
        serviceChargeNominal,
        taxValue,
        totalDiscountProduct,
        nominalDisc,
        settingRefund,
        isRefund,
    } = state;
    let {
        totalPriceBeforeDiscount,
        totalPriceAfterDiscount,
    } = state;
    let taxAmount = 0,
        taxBase = 0;
    const serviceChargeTaxed = String(SCType) === SERVICE_CHARGE_TYPE.SERVICE_CHARGE_TAXED;
    const serviceChargeNoTaxed = String(SCType) === SERVICE_CHARGE_TYPE.SERVICE_CHARGE_NO_TAXED || !Number(SCType);

    const taxPercent = (parseFloat(taxValue) / 100);

    if (isRefund) totalPriceBeforeDiscount *= parseFloat(settingRefund.refund_percentage) / 100;
    if (isRefund) totalPriceAfterDiscount *= parseFloat(settingRefund.refund_percentage) / 100;

    switch (String(taxType)) {
        case TAX_TYPE.NO_TAX:
            taxAmount = 0;
            break;
        case TAX_TYPE.TAX_BEFORE_DISCOUNT:
            if (serviceChargeNoTaxed) {
                taxBase = totalPriceBeforeDiscount;
            } else if (serviceChargeTaxed) {
                taxBase = totalPriceBeforeDiscount + serviceChargeNominal;
            }
            taxAmount = taxBase * taxPercent;
            break;
        case TAX_TYPE.TAX_AFTER_DISCOUNT:
            taxBase = totalPriceAfterDiscount - nominalDisc;
            if (serviceChargeTaxed) {
                taxBase += serviceChargeNominal;
            }
            taxAmount = taxBase * taxPercent;
            break;
        case TAX_TYPE.PRICE_INCLUDES_TAX: {
            const totalPrice = totalPriceBeforeDiscount / (1 + taxPercent);
            const totalDiscount = totalDiscountProduct + nominalDisc;
            if (serviceChargeNoTaxed) {
                taxBase = totalPrice - totalDiscount;
            } else if (serviceChargeTaxed) {
                taxBase = totalPrice - totalDiscount + serviceChargeNominal;
            }
            taxAmount = taxPercent * taxBase;
            break;
        }
        default:
        // do nothing
    }

    return { taxAmount, taxBase };
};

export const calculateTaxPerProduct = (taxType, product, nominalDisc, totalPriceBeforeDiscount) => {
    let retval = 0;
    const price = parseFloat(product.price, 10);
    const qty = parseFloat(product.qty, 10);
    const tax = product.tax_detail[0].percentage / 100;

    if (String(taxType) === TAX_TYPE.TAX_BEFORE_DISCOUNT) {
        retval = tax * (price * qty);
    } else if (String(taxType) === TAX_TYPE.TAX_AFTER_DISCOUNT) {
        retval = tax * (product.total - ((nominalDisc / totalPriceBeforeDiscount * price) * qty));
    } else if (String(taxType) === TAX_TYPE.PRICE_INCLUDES_TAX) {
        retval = ((product.total - (nominalDisc / totalPriceBeforeDiscount * price) * qty) / (1 + tax)) * tax;
    } else {
        retval = 0;
    }

    return retval;
};

export const calculateGrandTotal = (state) => {
    const {
        priceIncludesTax,
        isTaxPerProduct,
        nominalDisc,
        taxAmount,
        totalCosts,
        serviceChargeNominal,
        subtotal,
        totalDiscountProduct,
    } = state;
    let grandTotal = 0;
    let totalTax = taxAmount;

    if (isTaxPerProduct && priceIncludesTax) totalTax = 0;

    grandTotal = parseFloat(subtotal, 10) + totalTax + parseFloat(totalCosts, 10) + serviceChargeNominal;

    if (!priceIncludesTax) {
        grandTotal = parseFloat(grandTotal, 10) - parseFloat(nominalDisc, 10);
    } else {
        grandTotal = parseFloat(grandTotal, 10) - parseFloat(nominalDisc, 10) - parseFloat(totalDiscountProduct, 10);
    }

    return grandTotal;
};

// eslint-disable-next-line no-nested-ternary
const handleTranslation = (key, defaultValue, t, keyObject = '') => (t ? t(key, { ...defaultValue, ns: 'Penjualan/Invoice/invoice' }) : typeof defaultValue === 'string' ? defaultValue : defaultValue[keyObject]);

export const createPajakText = (taxType, taxValue, isPrintDotMatrix, t) => {
    let retval = '';

    switch (String(taxType)) {
        case TAX_TYPE.NO_TAX:
            retval = handleTranslation('form.paymentInformation.tax.noTax', 'Tanpa Pajak', t);
            break;
        case TAX_TYPE.TAX_BEFORE_DISCOUNT:
            retval = !isPrintDotMatrix ? handleTranslation('form.paymentInformation.tax.before', { value: taxValue }, t, 'value') : handleTranslation('form.paymentInformation.tax.tax', { value: taxValue }, t, 'value');
            break;
        case TAX_TYPE.TAX_AFTER_DISCOUNT:
            retval = !isPrintDotMatrix ? handleTranslation('form.paymentInformation.tax.after', { value: taxValue }, t, 'value') : handleTranslation('form.paymentInformation.tax.tax', { value: taxValue }, t, 'value');
            break;
        case TAX_TYPE.PRICE_INCLUDES_TAX:
            retval = !isPrintDotMatrix ? handleTranslation('form.paymentInformation.tax.include', { value: taxValue }, t, 'value') : handleTranslation('form.paymentInformation.tax.include2', { value: taxValue }, t, 'value');
            break;
        default:
            retval = '';
    }

    return retval;
};

export const calculateAddon = listAddon => (listAddon || []).reduce((acc2, addon) => {
    acc2 += parseFloat(addon.qty) * parseFloat(addon.price);
    return acc2;
}, 0);

const calculateSubTotalProductsPromo = (products = [], promo) => products.reduce((acc, val) => {
    if (promo.item.includes(val.product_id)) {
        acc += val.total;
    }

    return acc;
}, 0);

const calculateMultipleTotalProductsPromo = (products = [], promo) => products.reduce((acc, val) => {
    let multiple = 0;

    if (promo.item.includes(val.product_id)) {
        if (promo.minimal_criteria === CRITERIA_PROMO.QUANTITY) {
            multiple = parseInt(val.qty, 10) / parseInt(promo.minimal_criteria_value, 10);
        } else {
            multiple = parseInt(val.total, 10) / parseInt(promo.minimal_criteria_value, 10);
        }
    }

    return acc + Math.floor(multiple);
}, 0)

export const calculateData = (state, isPrintDotMatrix, isResetPromoPoin) => {
    const {
        activeSalesQuote,
        discValue = 0,
        discType,
        invoiceStatus,
        salesOrderStatus,
        products,
        taxBaseType,
        taxType,
        taxValue,
        otherCosts,
        shippingCost,
        serviceCharge,
        serviceChargeType,
        productsRefund,
        isRefund,
        settingRefund,
        isReturnedShippingFee,
        isOtherServiceFeeBack,
        t,
        promoManual,
        vouchers,
        pointRedeems,
        isInvoice,
        hasMaxDisc,
        maxDisc,
        bonusProductManual,
        bonusProductPerProduct,
        bonusProductTransaction,
    } = state;
    const isPercentDisc = (discType === DISCOUNT_TYPE.PERCENT);
    let taxAmount = 0,
        taxBase = 0,
        totalTaxPerProduct = 0,
        taxPerProduct = [];
    let subtotalText = handleTranslation('form.payment.subtotal', 'Subtotal barang', t);
    let totalQty = 0,
        totalQtyRefund = 0,
        totalDiscountProduct = 0,
        totalPriceBeforeDiscount = 0,
        totalPriceAfterDiscount = 0,
        tempTotalPriceBeforeDiscount = 0,
        tempTotalPriceAfterDiscount = 0,
        grandTotal = 0,
        serviceChargeNominal = 0,
        subtotal = 0,
        subtotalWithoutDisc = 0,
        totalCosts = 0,
        shipmentCost = shippingCost || 0;
    let percentDisc = 0,
        nominalDisc = 0,
        nominalManualDisc = 0,
        nominalDiscWithoutProductDisc = 0, // tanpa total diskon product
        nominalVoucherDisc = 0,
        nominalPointDisc = 0,
        nominalManualDiscProduct = 0,
        nominalDiscountWithoutIncludeTax = 0,
        nominalManualDiscountWithoutIncludeTax = 0,
        newVouchers = [],
        maxNominalDisc = 0,
        maxNominalManualDisc = 0;

    const taxPercent = (parseFloat(taxValue) / 100);

    const isTaxPerProduct = String(taxBaseType) === TAX_BASE_TYPE.TAX_PER_PRODUCT;
    const priceIncludesTax = String(taxType) === TAX_TYPE.PRICE_INCLUDES_TAX;
    const serviceChargeTaxed = !isTaxPerProduct && String(serviceChargeType) === SERVICE_CHARGE_TYPE.SERVICE_CHARGE_TAXED;
    const isDraft = invoiceStatus === INVOICE_STATUS.DRAFT || salesOrderStatus === SALES_ORDER_STATUS_NAME.DRAFT;
    const totalBonusProductManual = (bonusProductManual || []).reduce((curr, prev) => curr += (+prev.qty * +prev.selling_price), 0);
    const totalBonusProductPerProduct = (bonusProductPerProduct || []).reduce((curr, prev) => curr += (+prev.qty * +prev.selling_price), 0);
    const totalBonusProductTransaction = (bonusProductTransaction || []).reduce((curr, prev) => curr += (+prev.qty * +prev.selling_price), 0);
    const totalBonusProduct = totalBonusProductManual + totalBonusProductPerProduct + totalBonusProductTransaction;
    if (products.length > 0) {
        subtotalText = handleTranslation('form.payment.subtotalWithQuantity', { qty: products.length }, t, 'qty');
        products.filter(val => val.id).forEach((product, index) => {
            totalQty = parseFloat(totalQty, 10) + parseFloat(product.qty, 10);

            const totalAddon = (product.addon || []).reduce((acc2, addon) => {
                acc2 += parseFloat(addon.qty) * parseFloat(addon.price);
                return acc2;
            }, 0);

            let productSubtotal = (parseFloat(product.price, 10) * parseFloat(product.qty, 10)) + totalAddon;
            tempTotalPriceBeforeDiscount = parseFloat(tempTotalPriceBeforeDiscount, 10) + productSubtotal;
            if (isRefund) {
                const totalAddonRefund = product.addon.reduce((acc2, addon) => {
                    const qtyAddOnPerProduct = addon.qty / product.qty;

                    acc2 += qtyAddOnPerProduct * parseFloat(productsRefund[index].qty_refund) * parseFloat(addon.price);
                    return acc2;
                }, 0);

                productSubtotal = (parseFloat(product.price) * parseFloat(productsRefund[index].qty_refund || 0)) + totalAddonRefund;
            };

            if (product.discType === DISCOUNT_TYPE.FIXED) totalDiscountProduct += parseFloat(product.nominal_disc);
            else totalDiscountProduct += (productSubtotal * (parseFloat(product.disc) / 100));

            totalPriceBeforeDiscount = parseFloat(totalPriceBeforeDiscount, 10) + productSubtotal;
            tempTotalPriceAfterDiscount = parseFloat(tempTotalPriceAfterDiscount, 10) + (parseFloat(product.total, 10) - parseFloat(product.nominalManualDisc || 0, 10));

            if (product.manualDisc && promoManual && promoManual[0] && promoManual[0].promo_type === PROMO_CRITERIA_TYPE.PRODUCT) {
                nominalManualDiscProduct += product.manualDisc;
                totalDiscountProduct += nominalManualDiscProduct;
            }

            if (isRefund) {
                totalQtyRefund += parseInt(productsRefund[index].qty_refund || 0, 10);
                if (parseInt(productsRefund[index].qty_refund || 0, 10)) {
                    const discountProduct = product.nominal_disc ? parseFloat(product.nominal_disc / product.qty) : 0;
                    const manualDiscountProduct = product.manualDisc ? parseFloat(product.manualDisc / product.qty) : 0;
                    totalPriceAfterDiscount += productSubtotal - (parseFloat(productsRefund[index].qty_refund) * discountProduct) - (parseFloat(productsRefund[index].qty_refund) * manualDiscountProduct);
                }
            } else totalPriceAfterDiscount = parseFloat(totalPriceAfterDiscount, 10) + (parseFloat(product.total, 10) - parseFloat(product.nominalManualDisc || 0, 10));
        });

        if (priceIncludesTax) {
            subtotal = totalPriceBeforeDiscount;
            subtotal /= (1 + taxPercent);
        } else {
            subtotal = totalPriceAfterDiscount;
        }

        if (isRefund) if (!isOtherServiceFeeBack || isReturnedShippingFee) shipmentCost = 0;
        subtotalWithoutDisc = totalPriceBeforeDiscount;
        totalCosts = parseFloat(totalCosts, 10) + parseFloat(shipmentCost, 10);

        if (otherCosts.length > 0) {
            let tempOtherCosts = otherCosts.reduce((sum, { nominal }) => parseFloat(sum, 10) + parseFloat(nominal, 10), 0);
            if (isRefund) if (isReturnedShippingFee || !isOtherServiceFeeBack) tempOtherCosts = 0;
            totalCosts += tempOtherCosts;
        }

        if (isRefund) {
            subtotal *= parseFloat(settingRefund.refund_percentage) / 100;
            subtotalWithoutDisc *= parseFloat(settingRefund.refund_percentage) / 100;
            totalDiscountProduct *= parseFloat(settingRefund.refund_percentage) / 100;
        }

        let parentProductTotal = 0;
        if (activeSalesQuote) {
            if (activeSalesQuote.products) {
                parentProductTotal = activeSalesQuote.products.reduce((prev, current) => prev + current.total, 0);
            }
        }
        const actualNominalDisc = parentProductTotal > 0 ? discValue * (totalPriceAfterDiscount / parentProductTotal) : discValue;

        if (isPercentDisc) {
            percentDisc = parseFloat(discValue, 10);
            if (priceIncludesTax) {
                nominalDisc = (parseFloat(totalPriceAfterDiscount, 10) * parseFloat(discValue, 10)) / 100;
            } else {
                nominalDisc = (parseFloat(subtotal, 10) * parseFloat(discValue, 10)) / 100;
            }
        } else {
            nominalDisc = parseFloat(actualNominalDisc, 10);
            percentDisc = 0;
        }

        if (hasMaxDisc && nominalDisc > maxDisc) {
            maxNominalDisc = maxDisc;
        } else {
            maxNominalDisc = nominalDisc;
        }

        const tempSubtotal = priceIncludesTax ? totalPriceAfterDiscount : subtotal;

        let isPromoManualTransaction = false;
        if (promoManual && promoManual[0] && nominalManualDiscProduct <= 0) {
            if (promoManual[0].promo_type === PROMO_CRITERIA_TYPE.INVOICE) {
                isPromoManualTransaction = true;
                if (promoManual[0].bonus_type === DISCOUNT_TYPE.PERCENT) {
                    nominalManualDisc = (parseFloat(tempSubtotal - maxNominalDisc) * parseFloat(promoManual[0].bonus_type_value)) / 100;
                } else if (promoManual[0].bonus_type === DISCOUNT_TYPE.NOMINAL) {
                    let multiple = 1;
                    if (promoManual[0].is_multiple) multiple = Math.floor((tempSubtotal) / parseFloat(promoManual[0].minimal_criteria_value));
                    nominalManualDisc = parseFloat(promoManual[0].bonus_type_value) * multiple;
                }
            } else if (promoManual[0].promo_type === PROMO_CRITERIA_TYPE.PRODUCT) {
                if (promoManual[0].type === DISCOUNT_TYPE.PERCENT) {
                    const subTotalProductsPromo = calculateSubTotalProductsPromo(products, promoManual[0]);
                    nominalManualDisc = (parseFloat(subTotalProductsPromo) * parseFloat(promoManual[0].bonus_type_value)) / 100;
                } else if (promoManual[0].type === DISCOUNT_TYPE.NOMINAL) {
                    let multiple = 1;
                    if (parseInt(promoManual[0].multiple, 10)) multiple = calculateMultipleTotalProductsPromo(products, promoManual[0]);
                    nominalManualDisc = parseFloat(promoManual[0].bonus_type_value) * Math.floor(multiple);
                }
            }
            if (Number(promoManual[0].bonus_type_value_maximal_flag || 0) === 1 && nominalManualDisc > Number(promoManual[0].bonus_type_value_maximal || Number.MAX_SAFE_INTEGER)) {
                maxNominalManualDisc = Number(promoManual[0].bonus_type_value_maximal);
            } else {
                maxNominalManualDisc = nominalManualDisc;
            }
        }

        if (!isResetPromoPoin && pointRedeems && !isRefund) {
            if (pointRedeems.discountData) nominalPointDisc += parseFloat(pointRedeems.discountData.value);
            if (pointRedeems.productData) nominalPointDisc += parseFloat(pointRedeems.productData.total);
        } else if (!isResetPromoPoin && pointRedeems && pointRedeems.discountData && isRefund) {
            if (String(pointRedeems.discountData.valueType) === POINT_NOMINAL_TYPE.PERCENT) {
                nominalPointDisc += ((totalPriceAfterDiscount - maxNominalDisc - maxNominalManualDisc) * pointRedeems.discountData.percentage / 100);
            } else {
                nominalPointDisc += pointRedeems.discountData.nominal;
            }
        }

        if (isRefund) {
            if (!totalQtyRefund) nominalDisc = 0;
            else if (isPercentDisc) {
                if (priceIncludesTax) {
                    nominalDisc = (parseFloat(totalPriceAfterDiscount, 10) * parseFloat(discValue, 10)) / 100;
                    nominalDisc *= parseFloat(settingRefund.refund_percentage) / 100;
                } else {
                    nominalDisc = (parseFloat(subtotal, 10) * parseFloat(discValue, 10)) / 100;
                }
            } else {
                nominalDisc = ((parseFloat(totalPriceBeforeDiscount) / parseFloat(tempTotalPriceBeforeDiscount)) * actualNominalDisc) * (parseFloat(settingRefund.refund_percentage) / 100);
            }
        }

        if (isTaxPerProduct) {
            products.forEach((product) => {
                if (product.tax_detail && product.tax_detail.length > 0 && product.tax_name !== '-') {
                    let taxName = product.tax_name.concat(' (', product.tax_detail[0].percentage, '%)');

                    if (priceIncludesTax && !isPrintDotMatrix) {
                        taxName = `Harga Termasuk Pajak - ${taxName}`;
                    } else if (priceIncludesTax && isPrintDotMatrix) {
                        taxName = `Termasuk Pajak - ${taxName}`;
                    }

                    const idx = taxPerProduct.findIndex(tax => tax.text === taxName);
                    const totalCurrentTax = isDraft || !product.total_product_tax
                        ? calculateTaxPerProduct(taxType, product, (maxNominalDisc + maxNominalManualDisc + nominalVoucherDisc + nominalPointDisc), totalPriceBeforeDiscount)
                        : product.total_product_tax;

                    if (idx > -1) {
                        taxPerProduct[idx].nominal += totalCurrentTax;
                    } else {
                        taxPerProduct.push({
                            text: taxName,
                            nominal: totalCurrentTax,
                        });
                    }

                    totalTaxPerProduct += totalCurrentTax;
                }
            });
        }

        nominalDiscWithoutProductDisc = isPercentDisc ? 0 : maxNominalDisc;
        nominalDiscountWithoutIncludeTax = maxNominalDisc;
        if (isPromoManualTransaction) nominalManualDiscountWithoutIncludeTax = maxNominalManualDisc;

        if (vouchers && vouchers.length > 0) {
            newVouchers = vouchers.reverse().sort((a, _) => (a.type === VOUCHER_UNIT_TYPE.NOMINAL ? -1 : a.type === VOUCHER_UNIT_TYPE.PERCENT ? 1 : 0));
            let currentTotalVoucher = 0;
            newVouchers.forEach((voucher, index) => {
                if (voucher.type === VOUCHER_UNIT_TYPE.PERCENT) {
                    let tempSubtotalForVoucher = tempSubtotal - maxNominalDisc - currentTotalVoucher;
                    if (isPromoManualTransaction) tempSubtotalForVoucher -= maxNominalManualDisc;
                    let nominalPercentage = tempSubtotalForVoucher * parseFloat(voucher.percentage) / 100;

                    if (voucher.maxChangeValue > 0 && nominalPercentage > voucher.maxChangeValue) nominalPercentage = voucher.maxChangeValue;
                    nominalVoucherDisc += nominalPercentage;
                    currentTotalVoucher += parseFloat(nominalPercentage);

                    newVouchers[index] = {
                        ...newVouchers[index],
                        value: nominalPercentage,
                        ...priceIncludesTax && {
                            valueIncludeTax: nominalPercentage / (1 + taxPercent),
                        }
                    };
                } else if (voucher.type === VOUCHER_UNIT_TYPE.NOMINAL) {
                    nominalVoucherDisc += parseFloat(voucher.value);
                    currentTotalVoucher += parseFloat(voucher.value);
                    newVouchers[index] = {
                        ...newVouchers[index],
                        ...priceIncludesTax && {
                            valueIncludeTax: parseFloat(voucher.value) / (1 + taxPercent),
                        }
                    };
                }
            });
        }

        if (isPercentDisc && hasMaxDisc && nominalDisc > maxDisc) {
            nominalDisc = maxDisc;
        }

        if (promoManual && promoManual[0] && nominalManualDiscProduct <= 0 && Number(promoManual[0].bonus_type_value_maximal_flag || 0) === 1 && nominalManualDisc > Number(promoManual[0].bonus_type_value_maximal || Number.MAX_SAFE_INTEGER)) {
            nominalManualDisc = Number(promoManual[0].bonus_type_value_maximal);
        }

        if (!isTaxPerProduct && priceIncludesTax) {
            totalDiscountProduct /= (1 + taxPercent);
            nominalManualDisc /= (1 + taxPercent);
            nominalVoucherDisc /= (1 + taxPercent);
            nominalPointDisc /= (1 + taxPercent);
            nominalDisc /= (1 + taxPercent);
        }

        if (isRefund) serviceChargeNominal = priceIncludesTax ? subtotal : totalPriceAfterDiscount * parseFloat(settingRefund.refund_percentage) / 100;
        else serviceChargeNominal = priceIncludesTax ? subtotal : totalPriceAfterDiscount;
        let tempTotalDisc = nominalDisc + nominalVoucherDisc + nominalPointDisc;
        if (isInvoice || isPromoManualTransaction) tempTotalDisc += nominalManualDisc;
        if (tempTotalDisc > 0) serviceChargeNominal -= tempTotalDisc;
        if (priceIncludesTax) {
            serviceChargeNominal -= totalDiscountProduct;
            if (!isPromoManualTransaction) serviceChargeNominal -= nominalManualDisc;
        }
        serviceChargeNominal *= (parseFloat(serviceCharge) / 100);

        const taxState = {
            totalPriceBeforeDiscount,
            totalPriceAfterDiscount,
            serviceChargeNominal,
            taxValue,
            totalDiscountProduct: !isPromoManualTransaction && !isInvoice ? totalDiscountProduct + nominalManualDisc : totalDiscountProduct,
            nominalDisc: tempTotalDisc,
            settingRefund,
            isRefund,
        };

        if (isTaxPerProduct) {
            taxAmount = totalTaxPerProduct;
        } else {
            if (String(taxType) === TAX_TYPE.TAX_BEFORE_DISCOUNT && !isRefund) taxState.totalPriceBeforeDiscount += totalBonusProduct; 
            ({ taxAmount, taxBase } = calculateTax(taxType, serviceChargeType, taxState));
        }

        const grandTotalState = {
            priceIncludesTax,
            isTaxPerProduct,
            totalDiscountProduct: !isPromoManualTransaction && !isInvoice ? totalDiscountProduct + nominalManualDisc : totalDiscountProduct,
            nominalDisc: tempTotalDisc,
            taxAmount,
            subtotal,
            totalCosts,
            serviceChargeNominal,
        };
        grandTotal = calculateGrandTotal(grandTotalState);
    }

    const discData = { percent: String(percentDisc), nominal: String(nominalDisc), nominalDiscWithoutProductDisc: String(nominalDiscWithoutProductDisc) };

    return {
        subtotal,
        subtotalText,
        subtotalWithoutDisc,
        taxType,
        taxAmount,
        taxBase,
        taxText: createPajakText(taxType, taxValue, isPrintDotMatrix, t),
        taxPerProduct,
        serviceChargeText: `Service Charge (${serviceCharge}%)`,
        serviceChargeNominal,
        serviceChargeTaxed,
        discData,
        totalPriceBeforeDiscount,
        totalPriceAfterDiscount,
        grandTotal: parseFloat(grandTotal),
        totalQty,
        totalCosts,
        isPercentDisc,
        isTaxPerProduct,
        shippingCost: shipmentCost,
        totalDiscountProduct,
        priceIncludesTax,
        nominalDisc,
        nominalManualDisc,
        nominalVoucherDisc,
        nominalPointDisc,
        nominalDiscountWithoutIncludeTax,
        vouchers: newVouchers,
        taxPercent,
        nominalManualDiscountWithoutIncludeTax,
    };
};
