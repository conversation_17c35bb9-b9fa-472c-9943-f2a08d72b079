import * as api from './api';

export const getFormDataUsaha = payload => api.getFormDataUsaha(payload);
export const sendSubmission = (payload, version = '') => api.sendSubmission(payload, version);
export const getDataSubmission = (payload, version = '') => api.getDataSubmission(payload, version);
export const putSubmissionStatus = (payload, version = '') => api.putSubmissionStatus(payload, version);
export const submissionUpdate = (payload, version = '') => api.submissionUpdate(payload, version);
export const putDataSubmission = (payload, version = '') => api.putDataSubmission(payload, version);
export const getServiceHour = (idOutlet, payload) => api.getServiceHour(idOutlet, payload);
export const postServiceHour = payload => api.postServiceHour(payload);
export const getProductV3 = payload => api.getProductV3(payload);
export const getProductV4 = payload => api.getProductV4(payload);
export const postProduct = payload => api.postProduct(payload);
export const postProductV3 = payload => api.postProductV3(payload);
export const postSaveChangesGrab = payload => api.postSaveChangesGrab(payload);
export const putProduct = (idProduct, payload) => api.putProduct(idProduct, payload);
export const putProductV3 = (idProduct, payload) => api.putProductV3(idProduct, payload);
export const putMerchantStatus = payload => api.putMerchantStatus(payload);
export const delProductV3 = (idProduct, deleteIntegration) => api.delProductV3(idProduct, deleteIntegration);
export const putCategory = payload => api.putCategory(payload);
export const getCategoryTokped = idProvider => api.getCategoryTokped(idProvider);
export const getEtalaseTokped = (idProvider, id, mid) => api.getEtalaseTokped(idProvider, id, mid);
export const getProductDetailV3 = idProduct => api.getProductDetailV3(idProduct);
export const syncProduct = payload => api.syncProduct(payload);
export const syncProductDownload = payload => api.syncProductDownload(payload);
export const syncProductStatus = payload => api.syncProductStatus(payload);
export const getMerchantPause = (idProvider, idOutlet) => api.getMerchantPause(idProvider, idOutlet);
export const putMerchantPause = payload => api.putMerchantPause(payload);
export const getAuthURL = (providerId, outletId) => api.getAuthURL(providerId, outletId);
export const getMajooCategory = (outletId, provider, mid) => api.getMajooCategory(outletId, provider, mid);
export const getAttribute = (outletId, providerDepartmentId, payload) =>
    api.getAttribute(outletId, providerDepartmentId, payload);
export const getDepartment = (outletId, providerId) => api.getDepartment(outletId, providerId);
export const getDepartmentV3 = (outletId, providerId, mid) => api.getDepartmentV3(outletId, providerId, mid);
export const getLogistic = (idOutlet, mid, skuStatus) => api.getLogistic(idOutlet, mid, skuStatus);
export const cancelIntegration = (userId, providerType) => api.cancelIntegration(userId, providerType);
export const mappingProduct = payload => api.mappingProduct(payload);
export const getDepartmentVariant = (outletId, providerId, categoryId, productId) =>
    api.getDepartmentVariant(outletId, providerId, categoryId, productId);
export const getMappedProductStatus = (outletId, providerId) => api.getMappedProductStatus(outletId, providerId);

export const getMarketplacePromo = payload => api.getMarketplacePromo(payload);
export const getMarketplacePromoDetail = id => api.getMarketplacePromoDetail(id);
export const updateMarketplacePromoPeriode = (id, payload) => api.updateMarketplacePromoPeriode(id, payload);
export const createMarketplacePromo = payload => api.createMarketplacePromo(payload);
export const deleteMarketplacePromo = (id, payload) => api.deleteMarketplacePromo(id, payload);
export const getMarketplaceCategory = payload => api.getMarketplaceCategory(payload);
export const getMarketplaceProduct = payload => api.getMarketplaceProduct(payload);
export const getCategoryProduct = (idProvider, isActiveOnly = false) =>
    api.getCategoryProduct(idProvider, isActiveOnly);
export const getGobizAuthURL = payload => api.getGobizAuthURL(payload);
export const postGobizAuth = payload => api.postGobizAuth(payload);
export const unlinkGobizOutlet = payload => api.unlinkGobizOutlet(payload);
export const getDataAvailableOutletMarketplace = (payload, version = '') =>
    api.getDataAvailableOutletMarketplace(payload, version);
export const postAuthV2 = payload => api.postAuthV2(payload);
export const unintegrateMarketplace = payload => api.unintegrateMarketplace(payload);
export const getDetailProductV3 = (id, payload) => api.getDetailPoductV3(id, payload);
export const getListOrderMarketplace = payload => api.getListOrderMarketplace(payload);
export const getListOrderMarketplaceByID = (id, payload) => api.getListOrderMarketplaceByID(id, payload);
export const getListOrderMarketplaceByOrderNo = payload => api.getListOrderMarketplaceByOrderNo(payload);
export const acceptanceMarketplaceOrder = payload => api.acceptanceOrderMarketplace(payload);
export const getListCancelReasonMarketplace = transactionType => api.getListCancelReasonMarketplace(transactionType);
export const checkPermissionCancel = payload => api.checkPermissionCancel(payload);
export const getAwbAndBookingCode = payload => api.getAwbAndBookingCode(payload);
export const getRates = payload => api.getRates(payload);
export const confirmShippingStatus = payload => api.confirmShipping(payload);
export const saveSetupProductV3 = payload => api.saveSetupProductV3(payload);
export const orderOnlineIntegration = payload => api.orderOnlineIntegration(payload);
export const reauthV3 = payload => api.reauthV3(payload);
export const getAutoStockV2 = (outletId, providerId) => api.getAutoStockV2(outletId, providerId);
export const postAutoStockV2 = payload => api.postAutoStockV2(payload);
export const getListChannel = (idOutlet, version) => api.getListChannel(idOutlet, version);
export const getDetailProductV2 = (id, payload) => api.getDetailProductGrabmart(id, payload);

export const getTrackingOrderCourier = (outletId, noTransaction) =>
    api.getTrackingOrderCourier(outletId, noTransaction);
export const getTrackingOrderV2 = orderNumber => api.getTrackingOrderV2(orderNumber);
export const postFinishOrderMarketplace = payload => api.postFinishOrderMarketplace(payload);
export const getListExpeditionLogistic = providerId => api.getListExpeditionLogistic(providerId);
export const getTrackingOrderSupplies = shippingId => api.getTrackingOrderSupplies(shippingId);

export const getMerchantAuthStatus = merchantId => api.getMerchantAuthStatus(merchantId);
