import * as campaignApi from './api';
import * as outletApi from '../outlets';

export const getOptionsForm = () => Promise.all([
    campaignApi.getInstagramBusinessClasification(),
    campaignApi.getInstagramBusinessPresent(),
    outletApi.getOutlet(),
]).then((res) => {
    const [clasification, presents, outlets] = res;
    if (!clasification.status) throw new Error(clasification.msg);
    if (!presents.status) throw new Error(presents.msg);
    if (!outlets.status) throw new Error(outlets.msg);
    const newClafication = [...clasification.data, {
        id: 0,
        clasification_name: '<PERSON><PERSON><PERSON> (Isi Sendiri)',
    }];
    return {
        clasification: newClafication,
        presents: presents.data,
        outlets: outlets.data,
    };
});
