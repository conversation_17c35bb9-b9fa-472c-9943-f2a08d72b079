import * as api from './api';

export const getSocialMedia = payload => api.getSocialMedia(payload);
export const getOutlet = payload => api.getOutlet({ is_cms: 1, ...payload });
export const getCabangV3 = payload => api.getCabangV3({ is_cms: 1, ...payload });
export const getOutletV3 = payload => api.getOutletv3({ is_cms: 1, ...payload });
export const getOutletSupplies = payload => api.getOutletSupplies({ is_cms: 1, ...payload });
export const getActiveOutlet = payload => api.getOutlet({ is_cms: 1, is_active: 1, ...payload });

export const getOutletGroupList = payload => api.getOutletGroupList(payload);
export const updateOutletGroup = (idGroup, payload) => api.updateOutletGroup(idGroup, payload);
export const createOutletGroup = payload => api.createOutletGroup(payload);
export const deleteOutletGroup = idGroup => api.deleteOutletGroup(idGroup);
export const getFilterOutletGroupList = () => api.getFilterOutletGroupList();

export const getCabangDetail = payload => api.getCabangDetail(payload);
export const cabangCheckKaryawan = payload => api.cabangCheckKaryawan(payload);
export const cabangUpdate = payload => api.cabangUpdate(payload);
export const cabangDelete = payload => api.cabangDelete(payload);
export const getKotaIndonesia = payload => api.getKotaIndonesia(payload);
export const getBank = () => api.getBank();
export const getInfoAkun = payload => api.getInfoAkun(payload);
export const getAllBranchSupport = () => api.getAllBranchSupport();
export const getBusinessInfo = (version = '0_0_1') => api.getBusinessInfo(version);
export const updateBusinessInfo = (payload, version = '0_0_1') => api.updateBusinessInfo(payload, version);

export const getConfigPdfTemplate = query => api.getConfigPdfTemplate(query);
export const createConfigPdfTemplate = payload => api.createConfigPdfTemplate(payload);
export const updateConfigPdfTemplate = payload => api.updateConfigPdfTemplate(payload);
export const getFloor = (payload, id) => api.getFloor(payload, id);
export const createFloor = payload => api.createFloor(payload);
export const updateFloor = (payload, id) => api.updateFloor(payload, id);
export const deleteFloor = id => api.deleteFloor(id);
export const getFloorModes = () => api.getFloorModes();
export const updateFloorMode = payload => api.updateFloorMode(payload);
