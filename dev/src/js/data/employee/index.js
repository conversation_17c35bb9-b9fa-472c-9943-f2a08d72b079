import * as api from './api';

// new Service
export const getNewList = payload => api.getNewList(payload);
export const getNewEmployeeList = payload => api.getNewEmployeeList(payload);
export const sendUserAccess = payload => api.sendUserAccess(payload);

export const createSupervisor = payload => api.createSupervisor(payload);
export const getList = payload => api.getList(payload);
export const getAllEmployee = payload => api.getAllEmployee(payload);
export const createEmployee = payload => api.createEmployee(payload);
export const updateEmployee = payload => api.updateEmployee(payload);
export const deleteEmployee = payload => api.deleteEmployee(payload);
export const resendVerificationCode = payload => api.resendVerificationCode(payload);
export const verifikasiSupervisor = payload => api.verifikasiSupervisor(payload);
export const getEmployeeNIK = () => api.getEmployeeNIK();
export const getDetail = (id, payload) => api.getDetail(id, payload);
export const getNewDetail = (id, payload) => api.getNewDetail(id, payload);
export const changePinEmployee = payload => api.changePinEmployee(payload);
export const updateLaporanAbsensi = payload => api.updateLaporanAbsensi(payload);

export const createEmployeeV14 = payload => api.createEmployeeV14(payload);
export const updateEmployeeV14 = payload => api.updateEmployeeV14(payload);
export const getDetailEmployeeV14 = (id, payload) => api.getDetailEmployeeV14(id, payload);
export const getListEmployeeV14 = payload => api.getListEmployeeV14(payload);
export const getEmployeeShiftV14 = payload => api.getEmployeeShiftV14(payload);
export const getEmployeeShifts = payload => api.getEmployeeShifts(payload);
export const createEmployeeShift = payload => api.createEmployeeShift(payload);
export const deleteWeeklyEmployeeShifts = (id, payload) => api.deleteWeeklyEmployeeShifts(id, payload);
export const deleteAllEmployeeShifts = (id, payload) => api.deleteAllEmployeeShifts(id, payload);
export const duplicateEmployeeShifts = (id, payload) => api.duplicateEmployeeShifts(id, payload);

export const getDetailMajooTeamsNotification = (id, payload) => api.getDetailMajooTeamsNotification(id, payload);
export const getEmployeeListsMajooTeam = (payload) => api.getEmployeeListsMajooTeam(payload);

/** devices */
export const getDevices = payload => api.getDevices(payload);
export const updateDevice = payload => api.updateDevice(payload);

export const getPresenceSetting = payload => api.getPresenceSetting(payload);
export const updatePresenceSetting = payload => api.updatePresenceSetting(payload);

// log activity
export const fetchLogActivity = (payload) => api.fetchLogActivity(payload);

// get radius
export const getRadiusSetting = payload => api.getRadiusSetting(payload);
export const updateRadiusSetting = payload => api.updateRadiusSetting(payload);

// employee v2
export const getEmployeeV2List = payload => api.getEmployeeV2List(payload);
export const getEmployeeV2 = id => api.getEmployeeV2(id);
export const createEmployeeV2 = payload => api.createEmployeeV2(payload);
export const updateEmployeeV2 = payload => api.updateEmployeeV2(payload);
export const getSuperior = payload => api.getSuperior(payload);
export const getEmployeeHistory = payload => api.getEmployeeHistory(payload);
export const updateEmployeeHistory = payload => api.updateEmployeeHistory(payload);
export const getEmployeeSubs = id => api.getEmployeeSubs(id);
export const transferEmployee = payload => api.transferEmployee(payload);
export const terminateEmployeeV2 = payload => api.terminateEmployeeV2(payload);
export const deleteEmployeeV2 = payload => api.deleteEmployeeV2(payload);

// import export employee
export const exportEmployeeTemplate = payload => api.exportEmployeeTemplate(payload);
export const importEmployeeData = payload => api.importEmployeeData(payload);
export const importEmployeeDocument = payload => api.importEmployeeDocument(payload);
export const checkRunningImportEmployeeServices = () => api.checkRunningImportEmployeeServices();
export const checkRunningImportDocumentServices = () => api.checkRunningImportDocumentServices();
export const exportEmployee = payload => api.exportEmployee(payload);
export const checkExportEmployee = () => api.checkExportEmployee();