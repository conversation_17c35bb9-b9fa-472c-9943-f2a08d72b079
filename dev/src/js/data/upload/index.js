import * as api from './api';

export const uploadImage = (payload, onProgress) => api.uploadImage(payload, onProgress);
export const uploadBerkasWalletRetina = (payload, onProgress) => api.uploadBerkasWalletRetina(payload, onProgress);
export const uploadForNewAndUpdateProductBulk = payload => api.uploadForNewAndUpdateProductBulk(payload);
export const uploadForNewAndUpdateProductBulkV2 = payload => api.uploadForNewAndUpdateProductBulkV2(payload);
export const uploadProductBulk = payload => api.uploadProductBulk(payload);  // bisa dihapus jika routes item-old dihapus
export const uploadImageMarketplace = payload => api.uploadImageMarketplace(payload);
export const uploadProductRecipes = payload => api.uploadProductRecipes(payload);
export const uploadRecipeMaster = payload => api.uploadRecipeMaster(payload);
export const uploadEdcSubmissionRetina = (payload, onProgress) => api.uploadEdcSubmissionRetina(payload, onProgress);
export const uploadBannerConsumerApp = payload => api.uploadBannerConsumerApp(payload);
export const uploadAssetsConsumerApp = payload => api.uploadAssetsConsumerApp(payload);
