import * as api from './api';

export const getPromoBasicV2 = payload => api.getPromoBasicV2(payload);
export const updatePromoBasicV2 = (payload, id) => api.updatePromoBasicV2(payload, id);
export const createPromoBasicV2 = (payload) => api.createPromoBasicV2(payload);

export const fetchPromoProduct = payload => api.getPromoProduct(payload);
export const createPromoProduct = payload => api.postPromoProduct(payload);
export const updatePromoProduct = payload => api.putPromoProduct(payload);
export const fetchPromoProductDetail = payload => api.getPromoProductDetail(payload);
export const fetchPromoTransaction = payload => api.getPromoTransaction(payload);
export const createPromoTransaction = payload => api.postPromoTransaction(payload);
export const updatePromoTransaction = payload => api.putPromoTransaction(payload);
export const fetchPromoTransactionDetail = payload => api.getPromoTransactionDetail(payload);

// promo penjualan v2
export const fetchPromoTransactionV2 = payload => api.getPromoTransactionV2(payload);
export const createPromoTransactionV2 = payload => api.postPromoTransactionV2(payload);
export const updatePromoTransactionV2 = payload => api.putPromoTransactionV2(payload);
export const fetchPromoTransactionDetailV2 = payload => api.getPromoTransactionDetailV2(payload);
export const deletePromoV2 = payload => api.deletePromoV2(payload);

export const removePromo = payload => api.removePromo(payload);
export const getVoucherCampaignList = payload => api.getVoucherCampaignList(payload);
export const getVoucherDetail = payload => api.getVoucherDetail(payload);
export const createVoucherCampaign = payload => api.createVoucherCampaign(payload);
export const updateVoucherCampaign = payload => api.updateVoucherCampaign(payload);
export const deleteVoucherCampaign = payload => api.deleteVoucherCampaign(payload);
export const getVoucherTransactionList = payload => api.getVoucherTransactionList(payload);
export const getVoucherBook = payload => api.getVoucherBook(payload);
export const claimVoucher = payload => api.claimVoucher(payload);
export const cancelVoucher = payload => api.cancelVoucher(payload);

export const getPromoProductActivityLog = payload => api.getPromoProductActivityLog(payload);
export const getPromoTransactionActivityLog = payload => api.getPromoTransactionActivityLog(payload);

export const duplicatePromo = payload => api.duplicatePromo(payload);
