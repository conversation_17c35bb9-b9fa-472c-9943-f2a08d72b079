import * as api from './api';

export const getListFaq = payload => api.getListFaq(payload);
export const getListCategory = payload => api.getListCategory(payload);
export const createFaq = payload => api.createFaq(payload);
export const createFaqCategory = payload => api.createFaqCategory(payload);
export const fetchPreviewListFaq = () => api.fetchPreviewListFaq();
export const updateFaq = payload => api.updateFaq(payload);
export const updateFaqCategory = payload => api.updateFaqCategory(payload);
export const removeFaq = id => api.removeFaq(id);
export const removeFaqCategory = id => api.removeFaqCategory(id);
export const publishFaq = id => api.publishFaq(id);
export const getDetailFaqCategory = id => api.getDetailFaqCategory(id);