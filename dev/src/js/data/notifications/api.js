import { fetchApi } from '../../services/api';

const endPoints = {
    getNotifSettings: '/0_0_10/setting/notification',
    putNotification: '/0_0_10/setting/notification',
    whatsappNotifSettings: '/0_0_10/setting/wa_notification',
};

export const getNotificationSettings = outletId => fetchApi(endPoints.getNotifSettings, { outlet_id: outletId }, 'get');

export const putNotification = payload => fetchApi(endPoints.putNotification, payload, 'put');

export const updateWhatsappNotificationSettings = payload =>
    fetchApi(endPoints.whatsappNotifSettings, payload, 'put');
