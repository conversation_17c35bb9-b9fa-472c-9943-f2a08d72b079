import { fetchApi } from '../../services/api';

const endPoints = {
    register: 'api/v1/whatsapp/client/register',
    whatsapp: 'api/v1/whatsapp/client',
    summary: id => `api/v1/whatsapp/client/${id}/summary`,
    login: 'api/v1/whatsapp/client/login',
    sendtText: 'api/v1/whatsapp/client/sendtext',
    templates: 'api/v1/whatsapp/client/templates',
    features: '0_0_3/cabang/feature_whatsapp',
};


export const getListWhatsApp = (id, payload) => fetchApi(`${endPoints.listWhatsApp}/${id}`, payload, 'get', { serviceDomainType: 'messaging' });
export const getSummaryWhatsApp = id => fetchApi(endPoints.summary(id), {}, 'get', { serviceDomainType: 'messaging' });
export const deleteWhatsApp = (idOutlet, idWhatsApp) => fetchApi(`${endPoints.whatsapp}/${idOutlet}/${idWhatsApp}`, {}, 'delete', { serviceDomainType: 'messaging' });
export const loginWhatsApp = payload => fetchApi(endPoints.login, payload, 'post', { serviceDomainType: 'messaging' });
export const registerWhatsApp = payload => fetchApi(endPoints.register, payload, 'post', { serviceDomainType: 'messaging' });
export const updateWhatsApp = payload => fetchApi(`${endPoints.whatsapp}/${payload.id_account}`, payload, 'put', { serviceDomainType: 'messaging' });
export const sendText = payload => fetchApi(endPoints.sendtText, payload, 'post', { serviceDomainType: 'messaging' });
export const getTemplate = payload => fetchApi(endPoints.templates, payload, 'get', { serviceDomainType: 'messaging' });
export const getDetailWhatsapp = (idOutlet, idWhatsApp) => fetchApi(`${endPoints.whatsapp}/${idOutlet}/${idWhatsApp}`, {}, 'get', { serviceDomainType: 'messaging' });
export const getWhatsAppFeatures = payload => fetchApi(endPoints.features, payload);
export const getWhatsAppNotifLog = payload => fetchApi(`${endPoints.whatsapp}/log`, payload);
