import * as api from './api';

export const getListWhatsApp = (id, payload = {}) => api.getListWhatsApp(id, payload);
export const getSummaryWhatsApp = id => api.getSummaryWhatsApp(id);
export const deleteWhatsApp = (idOutlet, idWhatsApp) => api.deleteWhatsApp(idOutlet, idWhatsApp);
export const loginWhatsApp = payload => api.loginWhatsApp(payload);
export const registerWhatsApp = payload => api.registerWhatsApp(payload);
export const updateWhatsApp = payload => api.updateWhatsApp(payload);
export const sendText = payload => api.sendText(payload);
export const getTemplate = payload => api.getTemplate(payload);
export const getDetailWhatsapp = (idOutlet, idWhatsApp) => api.getDetailWhatsapp(idOutlet, idWhatsApp);
export const getWhatsAppFeatures = payload => api.getWhatsAppFeatures(payload);
export const getWhatsAppNotifLog = payload => api.getWhatsAppNotifLog(payload);
