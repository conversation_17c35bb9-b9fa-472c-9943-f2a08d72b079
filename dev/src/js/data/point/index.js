import * as api from './api';

export const getPoinSetting = payload => api.getPoinSetting(payload);
export const getPoinSettingDetail = payload => api.getPoinSettingDetail(payload);
export const updatePoinSettingNew = payload => api.updatePoinSettingNew(payload);
export const getPoin = payload => api.getPoin(payload);
export const createPoin = payload => api.createPoin(payload);
export const updatePoin = payload => api.updatePoin(payload);
export const deletePoin = payload => api.deletePoin(payload);
export const getPrevPointSetting = payload => api.getPrevPointSetting(payload);
export const updatePointSetting = payload => api.updatePointSetting(payload);
export const getAvailableProduct = payload => api.getAvailableProduct(payload);
