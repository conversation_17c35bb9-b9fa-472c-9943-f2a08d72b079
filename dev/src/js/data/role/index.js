import * as api from './api';

export const getRoles = payload => api.getRoles(payload);
export const getPayrollRoles = payload => api.getPayrollRoles(payload);
export const createRole = payload => api.postRole(payload);
export const updateRole = payload => api.putRole(payload);
export const deleteRole = payload => api.deleteRole(payload);

export const getRoleDetail = payload => api.getRoleDetail(payload);

export const getRoleMenus = payload => api.getRoleMenus(payload);
export const setRoleAssignment = payload => api.postRoleAssignment(payload); // this is for create and update

export const getRoleUsers = payload => api.getRoleUsers(payload);
export const updateRoleUser = payload => api.putRoleUser(payload); // revoke