import * as api from './api';

export const getDetailKategoriV11 = payload => api.getDetailKategoriV11(payload);
export const getKategoriProduk = payload => api.getKategoriProduk(payload);
export const getKategoriProdukV11 = payload => api.getKategoriProdukV11(payload);
export const createKategoriProduk = payload => api.createKategoriProduk(payload);
export const updateKategoriProduk = payload => api.updateKategoriProduk(payload);
export const updateKategoriProdukItem = payload => api.updateKategoriProdukItem(payload);
export const deleteKategoriProduk = payload => api.deleteKategoriProduk(payload);

export const getProduct = payload => api.getProduct(payload);
export const getProductInventory = payload => api.getProductInventory(payload);
export const getProductInventoryV1 = payload => api.getProductInventoryV1(payload);
export const getProductInventoryProductionStockPopup = payload => api.getProductInventoryProductionStockPopup(payload);
export const getAddons = (payload = {}) => api.getAddons(payload);
export const getAddonsForProductService = (payload = {}) => api.getAddonsForProductService(payload);
export const checkTransaction = payload => api.checkTransaction(payload);
export const checRunningProcess = payload => api.checRunningProcess(payload);
export const deleteSNBNItemDraftedHasSwitchedOff = payload => api.deleteSNBNItemDraftedHasSwitchedOff(payload);

export const getAddonDetail = payload => api.getAddonDetail(payload);

// Addons V2
export const getAddonsV2 = payload => api.getAddonsV2(payload);
export const addAddonsV2 = payload => api.addAddonsV2(payload);
export const updateAddonsV2 = payload => api.updateAddonsV2(payload);
export const detailAddonsV2 = payload => api.detailAddonsV2(payload);
export const deleteAddonsV2 = payload => api.deleteAddonsV2(payload);
export const exportAddonsV2 = payload => api.exportAddonsV2(payload);

export const createProduct = payload => api.createProduct(payload);
export const updateProduct = payload => api.updateProduct(payload);
export const getProductDetail = (id, payload) => api.getProductDetail(id, payload);
export const deleteProduct = (id, payload) => api.deleteProduct(id, payload);
export const inventoriesUploadBulk = payload => api.inventoriesUploadBulk(payload);
export const updateBulkProduct = payload => api.updateBulkProduct(payload);
export const productsDownloadV1 = payload => api.productsDownloadV1(payload);
export const productsDownloadV2 = payload => api.productsDownloadV2(payload);
export const productsDownloadV3 = payload => api.productsDownloadV3(payload);
export const exportTemplate = payload => api.exportTemplate(payload);
export const exportTemplateV2 = payload => api.exportTemplateV2(payload);
export const importAndUpdateBulkProduct = (payload, method) => api.importAndUpdateBulkProduct(payload, method);
export const importAndUpdateBulkProductV2 = (payload, method) => api.importAndUpdateBulkProductV2(payload, method);

// Marketplace
export const getMarketplaceProvider = payload => api.getMarketplaceProvider(payload);
export const getMarketplace = payload => api.getMarketplace(payload);
export const getOutletMarketplace = payload => api.getOutletMarketplace(payload);

// V13 - added support for web-order
export const getProductV13 = payload => api.getProductV13(payload);

// V14 - added support for wholesale price
export const getProductDetailV14 = (id, payload) => api.getProductDetailV14(id, payload);

// V15 - added support for set monitoring stock and variant 2.0
export const getProductV15 = payload => api.getProductV15(payload);
// V15 - added support for variant(ekstra) tab
export const getProductListV15 = payload => api.getProductListV15(payload);
export const createProductV15 = payload => api.createProductV15(payload);
export const updateProductV15 = payload => api.updateProductV15(payload);
export const getProductDetailV15 = (id, payload) => api.getProductDetailV15(id, payload);
export const validateEntryProduct = payload => api.entryProductValidation(payload);
export const deleteBulkProductV15 = payload => api.deleteBulkProductV15(payload);
export const getMarketplaceV15 = payload => api.getMarketplaceV15(payload);
export const getUnitsByItemsV15 = payload => api.getUnitsByItemsV15(payload);

// Activity Dialog
export const getActivityProductLog = id => api.getActivityProductLog(id);
export const getActivityLogV2 = (id, type, payload) => api.getActivityLogV2(id, type, payload);
export const getActivityLogDetail = (jobId, type, id) => api.getActivityLogDetail(jobId, type, id);

export const getProductInventories = payload => api.getProductInventories(payload);
export const getExtraInventories = payload => api.getExtraInventories(payload);

// Menu Book
export const getMenuBookList = payload => api.getMenuBookList(payload);
export const getMenuBookDetail = (id, payload) => api.getMenuBookDetail(id, payload);
export const getProductsInGroupMenu = (id, groupId, payload) => api.getProductsInGroupMenu(id, groupId, payload);
export const createMenuBook = payload => api.createMenuBook(payload);
export const updateMenuBook = (hash, payload) => api.updateMenuBook(hash, payload);
export const deleteMenuBook = (hash, payload) => api.deleteMenuBook(hash, payload);

// import services
export const exportTemplateServices = payload => api.exportTemplateServices(payload);
export const checkRunningImportServices = payload => api.checkRunningImportServices(payload);

// Recipe
export const exportRecipe = payload => api.exportRecipe(payload);
export const importRecipe = payload => api.importRecipe(payload);
export const importProductRecipe = payload => api.importProductRecipe(payload);

// Outlet Group
export const getOutletGroupSettingDetail = id => api.getOutletGroupSettingDetail(id);
export const updateOutletGroup = (id, payload) => api.updateOutletGroup(id, payload);
