
export const reformatProduct = (data = []) => data.map(x => ({
    SKU: x['SKU (Kode Produk)'],
    category_item_name: x['Nama Kategori'] || '',
    favorite: x['Produk Favorit (Y /T)'] || 'T',
    harga_jual: x['Harga Jual'],
    harga_modal: x['Harga Beli'],
    nama_satuan: x.<PERSON>,
    name: x['Nama Produk'],
    stock_alert: x['Stok Minimum'],
    tampil: x['Tampilkan di menu (Y / T)'] || 'T',
    use_cogs: x['Monitor Persediaan (Y / T)'] || 'T',

}));

// DEVELOPMENT NOTE
// 'product' & 'V2' Suffix means new version of product template structure data

// Reformat All Product Item
export const reformatProductV2 = (data = []) => data.map((x) => ({
        name: x['<PERSON>a Produk* (Max 5000 SKU)'],
        category_item_name: x['Kategori*'],
        description: x['Deskripsi Produk'] || '',
        favorite: String(x['Produk Favorit (Y / T)*']).toUpperCase() || 'T',
        tampil: String(x['Tampilkan di menu (Y / T)*']).toUpperCase() || 'T',
        use_cogs: String(x['Monitor Persediaan (Y / T)*']).toUpperCase() || 'T',
        stock_alert: x['Pengingat Stok Minimum'],
        kasir_ubah_harga: String(x['Ijinkan Kasir Ubah Harga Jual (Y/T)*']).toUpperCase() || 'T',
        persen_ubah_harga: x['Maksimal % dibawah harga jual'] || 0,
        units: [1,2,3,4,5].reduce((dataX, iteratorX) => {
            const iteratorXReq = iteratorX > 1 ? iteratorX : `${iteratorX}*`;
            const objectOfUnit = {};

            if (iteratorX === 1) {
                objectOfUnit.nama_satuan = !x[`Satuan #${iteratorXReq}`] ? '-' : String(x[`Satuan #${iteratorXReq}`]);
                objectOfUnit.purchase_price = !x[`Harga Beli Satuan #${iteratorXReq}`] ? '0' : String(x[`Harga Beli Satuan #${iteratorXReq}`]);
                objectOfUnit.selling_price = !x[`Harga Jual Satuan #${iteratorXReq}`] ? '0' : String(x[`Harga Jual Satuan #${iteratorXReq}`]);
                objectOfUnit.sku = x[`SKU Satuan #${iteratorXReq}`] !== 0 && !x[`SKU Satuan #${iteratorXReq}`] ? '-' : String(x[`SKU Satuan #${iteratorXReq}`]);
                objectOfUnit.min_selling_qty = !x[`Minimum Transaksi Penjualan Satuan #${iteratorXReq}`] ? '0' : String(x[`Minimum Transaksi Penjualan Satuan #${iteratorXReq}`]);
                objectOfUnit.length = x[`Panjang (cm) #${iteratorXReq}`] !== 0 && !x[`Panjang (cm) #${iteratorXReq}`] ? '-' : String(x[`Panjang (cm) #${iteratorXReq}`]);
                objectOfUnit.width = x[`Lebar (cm) #${iteratorXReq}`] !== 0 && !x[`Lebar (cm) #${iteratorXReq}`] ? '-' : String(x[`Lebar (cm) #${iteratorXReq}`]);
                objectOfUnit.height = x[`Tinggi (cm) #${iteratorXReq}`] !== 0 && !x[`Tinggi (cm) #${iteratorXReq}`] ? '-' : String(x[`Tinggi (cm) #${iteratorXReq}`]);
                objectOfUnit.weight = x[`Berat (gram) #${iteratorXReq}`] !== 0 && !x[`Berat (gram) #${iteratorXReq}`] ? '-' : String(x[`Berat (gram) #${iteratorXReq}`]);
            } else {
                objectOfUnit.nama_satuan = !x[`Satuan #${iteratorXReq}`] ? '-' : String(x[`Satuan #${iteratorXReq}`]);
                objectOfUnit.purchase_price = !x[`Harga Beli Satuan #${iteratorXReq}`] ? '0' : String(x[`Harga Beli Satuan #${iteratorXReq}`]);
                objectOfUnit.selling_price = !x[`Harga Jual Satuan #${iteratorXReq}`] ? '0' : String(x[`Harga Jual Satuan #${iteratorXReq}`]);
                objectOfUnit.sku = x[`SKU Satuan #${iteratorXReq}`] !== 0 && !x[`SKU Satuan #${iteratorXReq}`] ? '-' : String(x[`SKU Satuan #${iteratorXReq}`]);
                objectOfUnit.min_selling_qty = !x[`Minimum Transaksi Penjualan Satuan #${iteratorXReq}`] ? '0' : String(x[`Minimum Transaksi Penjualan Satuan #${iteratorXReq}`]);
                objectOfUnit.length = x[`Panjang (cm) #${iteratorXReq}`] !== 0 && !x[`Panjang (cm) #${iteratorXReq}`] ? '-' : String(x[`Panjang (cm) #${iteratorXReq}`]);
                objectOfUnit.width = x[`Lebar (cm) #${iteratorXReq}`] !== 0 && !x[`Lebar (cm) #${iteratorXReq}`] ? '-' : String(x[`Lebar (cm) #${iteratorXReq}`]);
                objectOfUnit.height = x[`Tinggi (cm) #${iteratorXReq}`] !== 0 && !x[`Tinggi (cm) #${iteratorXReq}`] ? '-' : String(x[`Tinggi (cm) #${iteratorXReq}`]);
                objectOfUnit.weight = x[`Berat (gram) #${iteratorXReq}`] !== 0 && !x[`Berat (gram) #${iteratorXReq}`] ? '-' : String(x[`Berat (gram) #${iteratorXReq}`]);
                objectOfUnit.konversi = x[`Konversi #${iteratorX}`] !== 0 && !x[`Konversi #${iteratorX}`] ? '-' : String(x[`Konversi #${iteratorX}`]);
            }
            dataX.push(objectOfUnit);
            return dataX;
        }, [])
    }));