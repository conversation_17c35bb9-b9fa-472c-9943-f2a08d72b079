import * as api from './api';

export const getEmployeeCommissionTrx = payload => api.getEmployeeCommissionTrx(payload);
export const exportEmployeeCommissionTrx = payload => api.exportEmployeeCommissionTrx(payload);
export const exportEmployeeCommissionTrxV2 = payload => api.exportEmployeeCommissionTrxV2(payload);
export const getLaporanCommissionEmployeeSummary = payload => api.getLaporanCommissionEmployeeSummary(payload);
export const exportLaporanCommissionEmployeeSummary = payload => api.exportLaporanCommissionEmployeeSummary(payload);
export const exportLaporanCommissionEmployeeSummaryV2 = payload => api.exportLaporanCommissionEmployeeSummaryV2(payload);
export const getLaporanCommissionEmployeeCardSummary = payload => api.getLaporanCommissionEmployeeCardSummary(payload);
export const getLaporanCommission = payload => api.getLaporanCommission(payload);
export const exportLaporanCommission = payload => api.exportLaporanCommission(payload);
export const exportLaporanCommissionV2 = payload => api.exportLaporanCommissionV2(payload);
export const getLaporanCommissionCardSummary = payload => api.getLaporanCommissionCardSummary(payload);
export const getLaporanCommissionGraph = payload => api.getLaporanCommissionGraph(payload);
export const getLaporanJenisOrderDetail = payload => api.getLaporanJenisOrderDetail(payload);
export const getLaporanJenisOrder = payload => api.getLaporanJenisOrder(payload);
export const getLaporanAbsensi = payload => api.getLaporanAbsensi(payload);
export const getLaporanPelangganDatamart = payload => api.getLaporanPelangganDatamart(payload);

export const getLaporanPoinSales = payload => api.getLaporanPoinSales(payload);
export const getLaporanPoinSalesDetail = payload => api.getLaporanPoinSalesDetail(payload);
export const getLaporanPoinSalesDatamartSummary = payload => api.getLaporanPoinSalesDatamartSummary(payload);
export const getLaporanPoinSalesDatamart = payload => api.getLaporanPoinSalesDatamart(payload);
export const getLaporanPoinSalesDatamartDetail = payload => api.getLaporanPoinSalesDatamartDetail(payload);
export const getLaporanPoinSalesDatamartGraph = payload => api.getLaporanPoinSalesDatamartGraph(payload);
export const exportPoinSalesDatamart = payload => api.exportPoinSalesDatamart(payload);

export const getCustomerPointSalesHistoryNonDatamart = payload => api.getCustomerPointSalesHistoryNonDatamart(payload);
export const getCustomerSatisfaction = payload => api.getCustomerSatisfaction(payload);
export const getCommentSatisfaction = payload => api.getCommentSatisfaction(payload);
export const getAnalisaPerputaranStok = payload => api.getAnalisaPerputaranStok(payload);
export const getTransaksiDetailV12 = (slashId, payload) => api.getTransaksiDetailV12(slashId, payload);
export const getSalesPromoDetail = payload => api.getSalesPromoDetail(payload);
export const getSalesPromoDetailSummary = payload => api.getSalesPromoDetailSummary(payload);
export const getDashboardGrafikNonDatamart = payload => api.getDashboardGrafikNonDatamart(payload);
export const getBestSellingNonDatamart = payload => api.getBestSellingNonDatamart(payload);
export const getPaymentMethodNonDatamart = payload => api.getPaymentMethodNonDatamart(payload);
export const getDashboardGrafikDatamart = payload => api.getDashboardGrafikDatamart(payload);
export const getBestSellingDatamart = payload => api.getBestSellingDatamart(payload);
export const getDashboardPaymentMethodDatamart = payload => api.getDashboardPaymentMethodDatamart(payload);
export const getLaporanMinimum = payload => api.getLaporanMinimum(payload);
export const fetchTutupKasirDetailV2 = payload => api.getTutupKasirDetailV2(payload);
export const fetchTutupKasir = payload => api.getTutupKasir(payload);
export const getSummaryTutupKasir = payload => api.getSummaryTutupKasir(payload);
export const getLaporanPaymentMethod = payload => api.getLaporanPaymentMethod(payload);

export const getPaymentMethodDatamart = payload => api.getPaymentMethodDatamart(payload);
export const getPaymentMethodGraphDatamart = payload => api.getPaymentMethodGraphDatamart(payload);
export const downloadPaymentMethodDatamart = payload => api.downloadPaymentMethodDatamart(payload);
export const downloadPaymentMethodDatamartV2 = payload => api.downloadPaymentMethodDatamartV2(payload);
export const getPaymentMethodDetailDatamart = (paymentMethodId, payload) => api.getPaymentMethodDetailDatamart(paymentMethodId, payload);
export const getPaymentMethodDetailGraphDatamart = (paymentMethodId, payload) => api.getPaymentMethodDetailGraphDatamart(paymentMethodId, payload);
export const downloadPaymentMethodDetailDatamart = (paymentMethodId, payload) => api.downloadPaymentMethodDetailDatamart(paymentMethodId, payload);

export const fetchsalesComparisonNonDatamart = payload => api.getsalesComparisonNonDatamart(payload);
export const fetchsalesMarketplace = payload => api.getsalesMarketplace(payload);
export const fetchsalesMarketplaceDatamart = payload => api.getsalesMarketplaceDatamart(payload);
export const fetchsalesSummaryListDatamart = payload => api.getsalesSummaryListDatamart(payload);
export const fetchKasKecil = payload => api.getKasKecil(payload);
export const getKasKecilDetail = payload => api.getKasKecilDetail(payload);
export const getSalesSummary = payload => api.getSalesSummary(payload);
export const downloadSalesSummary = payload => api.downloadSalesSummary(payload);
export const downloadSalesSummaryV2 = payload => api.downloadSalesSummaryV2(payload);
export const getSalesPromo = payload => api.getSalesPromo(payload);
export const getSalesPromoSummary = payload => api.getSalesPromoSummary(payload);
export const getSalesPromoSummaryDatamart = payload => api.getSalesPromoSummaryDatamart(payload);
export const getSalesPromoGraph = payload => api.getSalesPromoGraph(payload);
export const printLaporanDetailPromo = payload => api.printLaporanDetailPromo(payload);
export const getItemSalesReport = payload => api.getItemSalesReport(payload);

export const getTaxSales = payload => api.getTaxSales(payload);
export const getTaxSalesDetail = payload => api.getTaxSalesDetail(payload);

export const getTaxSalesDatamart = payload => api.getTaxSalesDatamart(payload);
export const downloadTaxSalesDatamart = payload => api.downloadTaxSalesDatamart(payload);
export const getTaxSalesDetailDatamart = payload => api.getTaxSalesDetailDatamart(payload);
export const downloadTaxSalesDetailDatamart = payload => api.downloadTaxSalesDetailDatamart(payload);

export const getCashierSales = payload => api.getCashierSales(payload);
export const getAnalisaSales = payload => api.getAnalisaSales(payload);
export const getOutletSalesReport = payload => api.getOutletSalesReport(payload);
export const getOutletSalesChartReport = payload => api.getOutletSalesChartReport(payload);
export const getDeviceSalesReport = payload => api.getDeviceSalesReport(payload);
export const getJasaReport = payload => api.getJasaReport(payload);
export const getReservasiReport = payload => api.getReservasiReport(payload);
export const getComplimentReport = payload => api.getComplimentReport(payload);

export const getLaporanVoidNonDatamart = payload => api.getLaporanVoidNonDatamart(payload);
export const getDetailLaporanVoidNonDatamart = (id, payload) => api.getDetailLaporanVoidNonDatamart(id, payload);
export const downloadLaporanVoidAsync = payload => api.downloadLaporanVoidAsync(payload);

export const getDailySalesReportDatamart = payload => api.getDailySalesReportDatamart(payload);
export const getDailySalesChartDatamart = payload => api.getDailySalesChartDatamart(payload);
export const getDailySalesSummaryDatamart = payload => api.getDailySalesSummaryDatamart(payload);
export const getDailySalesExportDatamart = payload => api.getDailySalesExportDatamart(payload);
export const getDailySalesExportDatamartV2 = payload => api.getDailySalesExportDatamartV2(payload);

export const getLaporanRefundv9 = payload => api.getLaporanRefundV9(payload);
export const getLaporanRefundDetailV9 = id => api.getLaporanRefundDetailV9(id);
export const printLaporanRefund = payload => api.printLaporanRefund(payload);
export const printLaporanRefundV9 = payload => api.printLaporanRefundV9(payload);
export const printLaporanRefundV2 = payload => api.printLaporanRefundV2(payload);
export const getLaporanRefundDatamart = payload => api.getLaporanRefundDatamart(payload);
export const exportLaporanRefundDatamart = payload => api.exportLaporanRefundDatamart(payload);

export const getInventorySummary = payload => api.getInventorySummary(payload);
export const getInventorySummaryDownload = payload => api.getInventorySummaryDownload(payload);
export const downloadListLaporanDetilPenjualan = payload => api.downloadListLaporanDetilPenjualan(payload);
export const downloadListLaporanDetilPenjualanDatamart = payload => api.downloadListLaporanDetilPenjualanDatamart(payload);

export const getItemSalesReportDatamart = payload => api.getItemSalesReportDatamart(payload);
export const getItemSalesChartReportDatamart = payload => api.getItemSalesChartReportDatamart(payload);
export const getCategorySalesReportDatamart = payload => api.getCategorySalesReportDatamart(payload);
export const getCategorySalesChartDatamart = payload => api.getCategorySalesChartDatamart(payload);
export const downloadItemSalesRequestReportDatamart = payload => api.downloadItemSalesRequestReportDatamart(payload);

export const getVarianSalesReportDatamart = payload => api.getVarianSalesReportDatamart(payload);
export const getVarianSalesChartDatamart = payload => api.getVarianSalesChartDatamart(payload);
export const getSubVarianSalesReportDatamart = payload => api.getSubVarianSalesReportDatamart(payload);
export const getSubVarianSalesChartDatamart = payload => api.getSubVarianSalesChartDatamart(payload);
export const getDepartmentSalesDatamart = payload => api.getDepartmentSalesDatamart(payload);
export const downloadSubVarianSalesDamatart = payload => api.downloadSubVarianSalesDamatart(payload);
export const downloadSubVarianSalesDamatartV2 = payload => api.downloadSubVarianSalesDamatartV2(payload);
export const generateCategorySalesReport = payload => api.generateCategorySalesReport(payload);
export const generateCategorySalesReportV2 = payload => api.generateCategorySalesReportV2(payload);
export const generateVariantSalesReport = payload => api.generateVariantSalesReport(payload);
export const generateVariantSalesReportV2 = payload => api.generateVariantSalesReportV2(payload);

export const generateDepartmentReport = payload => api.generateDepartmentReport(payload);
export const generateDepartmentReportV2 = payload => api.generateDepartmentReportV2(payload);

export const getCashierSalesDatamart = payload => api.getCashierSalesDatamart(payload);
export const getCashierSalesChartDatamart = payload => api.getCashierSalesChartDatamart(payload);
export const downloadCashierSalesDatamart = payload => api.downloadCashierSalesDatamart(payload);
export const downloadCashierSalesDatamartV2 = payload => api.downloadCashierSalesDatamartV2(payload);
export const getProductAnalysisDatamart = payload => api.getProductAnalysisDatamart(payload);
export const getChartProductAnalysisDatamart = payload => api.getChartProductAnalysisDatamart(payload);
export const requestProductAnalysisDatamart = payload => api.requestProductAnalysisDatamart(payload);
export const requestInventoryRotationDatamart = payload => api.requestInventoryRotationDatamart(payload);
export const requestInventoryRotationNonDatamart = payload => api.requestInventoryRotationNonDatamart(payload);
export const requestProductAnalysisNonDatamart = payload => api.requestProductAnalysisNonDatamart(payload);
export const getAnalisaSalesDatamart = payload => api.getAnalisaSalesDatamart(payload);
export const getDownloadAnalisaSalesDatamart = payload => api.getDownloadAnalisaSalesDatamart(payload);
export const getDownloadAnalisaSalesDatamartV2 = payload => api.getDownloadAnalisaSalesDatamartV2(payload);

export const getAnalisaSalesNonDatamart = payload => api.getAnalisaSalesNonDatamart(payload);
export const getAnalisaSalesNonDatamartGraph = payload => api.getAnalisaSalesNonDatamartGraph(payload);

// new reservation report
export const getReservationList = payload => api.getReservationList(payload);
export const getReservationSummary = payload => api.getReservationSummary(payload);
export const getReservationChartData = payload => api.getReservationChartData(payload);
export const getReservationDetail = payload => api.getReservationDetail(payload);
export const getReservationReport = payload => api.getReservationReport(payload);
export const getReservationReportV2 = payload => api.getReservationReportV2(payload);
export const getUtilizationList = payload => api.getUtilizationList(payload);
export const getUtilizationSummary = payload => api.getUtilizationSummary(payload);
export const getUtilizationChartData = payload => api.getUtilizationChartData(payload);
export const getUtilizationDetail = payload => api.getUtilizationDetail(payload);
export const getUtilizationReport = payload => api.getUtilizationReport(payload);
export const getUtilizationReportV2 = payload => api.getUtilizationReportV2(payload);
export const getDetailUtilizationReport = payload => api.getDetailUtilizationReport(payload);
export const getDetailUtilizationReportV2 = (payload, id) => api.getDetailUtilizationReportV2(payload, id);

export const getPromoDatamart = payload => api.getPromoDatamart(payload);
export const getPromoChartDatamart = payload => api.getPromoChartDatamart(payload);
export const getPromoDetailDatamart = payload => api.getPromoDetailDatamart(payload);
export const getPromoRequestReportDatamart = payload => api.getPromoRequestReportDatamart(payload);
export const getPromoRequestReportNonDatamart = payload => api.getPromoRequestReportNonDatamart(payload);
export const getPromoReportDetailDatamart = payload => api.getPromoReportDetailDatamart(payload);
export const getPromoReportDetailDatamartV2 = payload => api.getPromoReportDetailDatamartV2(payload);

export const getPromoReportDetailNonDatamartV2 = payload => api.getPromoReportDetailNonDatamartV2(payload);

export const getVoucherDatamart = payload => api.getVoucherDatamart(payload);
export const getVoucherChartDatamart = payload => api.getVoucherChartDatamart(payload);
export const getVoucherDetailDatamart = payload => api.getVoucherDetailDatamart(payload);
export const getVoucherReportDatamart = payload => api.getVoucherReportDatamart(payload);
export const getVoucherReportDatamartV2 = payload => api.getVoucherReportDatamartV2(payload);
export const getVoucherReportDetailDatamart = payload => api.getVoucherReportDetailDatamart(payload);
export const getVoucherReportDetailDatamartV2 = payload => api.getVoucherReportDetailDatamartV2(payload);
export const getComplimentReportDatamart = payload => api.getComplimentReportDatamart(payload);
export const getComplimentReportDownloadDatamartV2 = payload => api.getComplimentReportDownloadDatamartV2(payload);

export const getOutletSalesReportDatamart = payload => api.getOutletSalesReportDatamart(payload);
export const getOutletSalesChartReportDatamart = payload => api.getOutletSalesChartReportDatamart(payload);
export const downloadOutletSalesReportDatamart = payload => api.downloadOutletSalesReportDatamart(payload);
export const downloadOutletSalesReportDatamartV2 = payload => api.downloadOutletSalesReportDatamartV2(payload);

export const getSalesSummaryDatamart = payload => api.getSalesSummaryDatamart(payload);
export const downloadSalesSummaryDatamart = payload => api.downloadSalesSummaryDatamart(payload);
export const downloadSalesSummaryDatamartV2 = payload => api.downloadSalesSummaryDatamartV2(payload);
export const getVoucherNonDatamart = payload => api.getVoucherNonDatamart(payload);
export const getVoucherChartNonDatamart = payload => api.getVoucherChartNonDatamart(payload);
export const getVoucherReportNonDatamart = payload => api.getVoucherReportNonDatamart(payload);
export const getVoucherReportNonDatamartV2 = payload => api.getVoucherReportNonDatamartV2(payload);
export const getVoucherDetailNonDatamart = payload => api.getVoucherDetailNonDatamart(payload);
export const getVoucherReportDetailNonDatamart = payload => api.getVoucherReportDetailNonDatamart(payload);
export const getVoucherReportDetailNonDatamartV2 = payload => api.getVoucherReportDetailNonDatamartV2(payload);

export const updateTransactionSN = (id, payload) => api.updateTransactionSN(id, payload);

export const getCustomTemplateMaster = payload => api.getCustomTemplateMaster(payload);
export const getCustomTemplate = payload => api.getCustomTemplate(payload);
export const getCustomTemplateDetail = (templateId, payload) => api.getCustomTemplateDetail(templateId, payload);
export const createCustomTemplate = payload => api.createCustomTemplate(payload);
export const updateCustomTemplate = (templateId, payload) => api.updateCustomTemplate(templateId, payload);
export const deleteCustomTemplate = templateId => api.deleteCustomTemplate(templateId);
export const getDetailSales = payload => api.getDetailSales(payload);
export const getStockExpiredList = payload => api.getStockExpiredList(payload);
export const exportStockExpired = payload => api.exportStockExpired(payload);

export const getLaporanPoinSalesDetailCustomer = payload => api.getLaporanPoinSalesDetailCustomer(payload);
export const getDetailSalesMeta = payload => api.getDetailSalesMeta(payload);

export const getProductSalesCashier = payload => api.getProductSalesCashier(payload);
export const getCashierProductSales = (id, payload) => api.getCashierProductSales(id, payload);
export const getCashierProductSalesGraph = payload => api.getCashierProductSalesGraph(payload);
export const exportCashierProductSales = payload => api.exportCashierProductSales(payload);

export const getProcessOrderTime = payload => api.getProcessOrderTime(payload);
export const getProcessOrderTimeMeta = payload => api.getProcessOrderTimeMeta(payload);

export const getProcessProductTime = payload => api.getProcessProductTime(payload);
export const getProcessProductTimeMeta = payload => api.getProcessProductTimeMeta(payload);

export const downloadReportPurchaseOrder = payload => api.downloadReportPurchaseOrder(payload);

export const getExportImportFetureList = payload => api.getExportImportFetureList(payload);
export const getExportImportList = payload => api.getExportImportList(payload);
export const updateExportImportData = id => api.updateExportImportData(id);

export const exportKasKecil = payload => api.exportKasKecil(payload);
export const checkMerchantHasItemBundles = payload => api.checkMerchantHasItemBundles(payload);
export const getReportProductBundles = payload => api.getReportProductBundles(payload);