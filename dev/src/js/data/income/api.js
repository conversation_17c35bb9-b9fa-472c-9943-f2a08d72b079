import { fetchApi } from '../../services/api';

const endPoints = {
    income: 'pemasukankategori/pemasukankategori',
};

export const getPemasukanCategoryList = payload => fetchApi(endPoints.income, payload);
export const postPemasukanCategory = payload => fetchApi(endPoints.income, payload, 'post');
export const putPemasukanCategory = payload => fetchApi(endPoints.income, payload, 'put');
export const deletePemasukanCategory = payload => fetchApi(endPoints.income, payload, 'delete');
