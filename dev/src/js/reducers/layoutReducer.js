import moment from 'moment';

const helper = (state, action) => {
    let calendar = {};
    switch (action.type) {
        case 'SET_CONTENT_HEADER_FILTER_CALENDAR':
            return {
                filterCalendar: action.payload,
            };
        case 'SET_CONTENT_HEADER_CALENDAR':
            if (!!action.payload.start && !!action.payload.end) {
                calendar = Object.assign({}, state.calendar, { start: action.payload.start, end: action.payload.end });
            } else {
                const { calendar: c } = state;
                calendar = c;
            }

            if (action.payload.onchange !== undefined) {
                calendar = Object.assign({}, calendar, { onchange: action.payload.onchange });
            }

            /**
             * default unit is in days. For months we assume, 30 days equal to one month
             */
            if (action.payload.rangeLimit) {
                calendar = Object.assign({}, calendar, { rangeLimit: 60 });
            } else {
                calendar = Object.assign({}, calendar, { rangeLimit: 0 });
            }

            return { calendar };
        case 'SET_CONTENT_HEADER_BUTTONS':
            return {
                buttons: action.buttons,
            };
        case 'SET_CONTENT_HEADER_FLOAT':
            return {
                contentFloat: action.payload,
            };
        case 'SET_WINDOW_HEIGHT':
            return {
                windowHeight: action.height,
            };
        case 'SET_OVERLAY_BLOCK':
            return {
                contentOverlay: action.state,
            };
        case 'SET_LOADING_BLOCK':
            return {
                loaderShow: action.state,
            };
        case 'SET_FLASH_MESSAGE':
            return {
                flashMessage: action.payload,
            };
        case 'SET_SHOW_NOTIF_SUPPORT':
            return {
                showNotifExp: action.payload,
            };
        case 'SET_SUPPORT_NEEDED':
            return {
              supportNeeded: action.payload,
            };
        case 'SET_CUSTOM_BREADCRUMBS':
            return {
                customBreadcrumbs: action.payload,
            };
        default:
            return state;
    }
};

const reducer = (state = {
    calendar: {
        start: moment().startOf('month').format('DD-MM-YYYY'),
        end: moment().endOf('month').format('DD-MM-YYYY'),
        onchange: () => {},
    },
    filterCalendar: {
        data: null,
        value: null,
        onchange: null,
    },
    buttons: [],
    contentFloat: null,
    supportNeeded: [],
    windowHeight: '',
    loaderShow: false,
    contentOverlay: false,
    showNotifExp: true,
    errorLogin: {},
    flashMessage: {},
    customBreadcrumbs: null,
}, action) => {
    switch (action.type) {
        case 'SET_CONTENT_HEADER_FILTER_CALENDAR':
        case 'SET_CONTENT_HEADER_CALENDAR':
        case 'SET_WINDOW_HEIGHT':
        case 'SET_OVERLAY_BLOCK':
        case 'SET_LOADING_BLOCK':
        case 'SET_SHOW_NOTIF_SUPPORT':
        case 'SET_FLASH_MESSAGE':
        case 'SET_SUPPORT_NEEDED':
            return Object.assign({}, state, helper(state, action));
        case 'SET_CONTENT_HEADER_BUTTONS':
            return Object.assign({}, state, helper(undefined, action));
        case 'SET_CONTENT_HEADER_FLOAT':
            return Object.assign({}, state, helper(state, action));
        case 'SET_CUSTOM_BREADCRUMBS':
            return Object.assign({}, state, helper(state, action));
        default:
            return state;
    }
};

export default reducer;
