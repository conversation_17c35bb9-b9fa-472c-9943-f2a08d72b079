export const whiteListFromSupport = {
    SUPPORT_PARENT: '270',
    BELI_LANGGANAN_SUPPORT: '272',
    PENGATURAN_OUTLET: '243',
    TIKET_SUPPORT: '274',
    VOUCHER_LANGGANAN_MAJOO_SHOPEE: '29832',
    ROOT_SUPPORT: '5',
};

export const accountType = {
    TRIAL: 'TRIAL',
    BASIC: 'BASIC',
    REGULER: 'REGULER',
    STARTER: 'STARTER',
    BUSINESS: 'BUSINESS',
    BUSINESS_PRO: 'BUSINESS PRO',
    ADVANCE: 'ADVANCE',
    ENTERPRISE: 'ENTERPRISE',
    MAXIMA_STARTER: 'MAXIMA STARTER',
    MAXIMA_ADVANCE: 'MAXIMA ADVANCE',
    FREE: 'FREE',
    // Tambahan sekitar bulan november-desember 2022 akun ENTERPRISE rewording ke PRIME
    PRIME: 'PRIME',
    STARTER_BASIC: 'STARTER BASIC',
    PRIMEPLUS: 'PRIME+',
};

export const dataPackage = {
    PACKAGE_BULANAN: '2020SPDB01',
    PACKAGE_TAHUNAN: '2020SPDB02',
};

export const akuntingType = {
    DETAIL: 2,
};

export const phonePreviewDeviceType = {
    ANDROID: 'ANDROID',
    IPHONE: 'IPHONE',
};

export const LIFECYCLE_STATUS = {
    ACTIVE: 'active',
    GRACE: 'grace_period',
    HOT: 'hot_period',
    COLD: 'cold_period',
    ZOMBIE: 'zombie_period',
};
