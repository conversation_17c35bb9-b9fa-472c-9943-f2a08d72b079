const API_BASE = '/mayang/';
const API_BASE_ITEM = '/ms-master-data/';
const API_BASE_TRANSACTION = '/ms-transaction/api/';
const API_BASE_NOTIFICATION = '/ms-notification/';
const API_BASE_ACCOUNTING = '/ms-accounting/';;
const API_BASE_PORTAL = '/portal/';
const API_BASE_MARKETPLACE = '/ms-mp-aggregator/';
const API_BASE_BILLER = '/ms-biller/';
const API_BASE_REPORT = '/ms-report/';
const API_BASE_PROMO_V2 = '/ms-promo-v2/';
const API_BASE_REPORT_DATAMART = '/svc-data-reporting/';
const API_BASE_GOBIZ = '/ms-gobiz-utilities/';
const API_BASE_REPORT_NON_DATAMART = '/svc-reporting/';
const API_BASE_EMENU_UTILITIES = '/ms-e-menu-utilities/';
const API_BASE_SERVICE_INVENTORY = '/inventory/'; // TODO: revert this if no proxy needed to /inventory/
const API_BASE_PAYROLL = '/payroll/';
const API_BASE_ACCOUNTING_DATAMART = '/svc-accounting-report/';
const API_BASE_USER_MANAGEMENT = '/user-management/';
const API_BASE_EMENU_TENTAKEL = '/ms-e-menu-tentakel/';
const API_BASE_MS_TRANSACTION = '/ms-transaction/';
const API_BASE_SVC_TRANSACTION = '/svc-transaction/';
const API_BASE_LOG_ACTIVITY = '/svc-audit-log/';
const API_BASE_SHIPPING = '/ms-shipping/';
const API_BASE_MULTI_LANGUAGE = '/svc-multi-language/';
const API_BASE_MESSAGING = '/messaging/';
const API_BASE_ACTIVITY = '/svc-activity-http/';

const colors = [{
    pointBorderColor: '#04C99E', // green
    borderColor: '#ccf5f5',
}, {
    pointBorderColor: '#fcc419', // yellow
    borderColor: '#ffe79c',
}, {
    pointBorderColor: '#c871cc', // purple
    borderColor: '#f8d5fa',
}, {
    pointBorderColor: '#ed4337', // red
    borderColor: '#F6958E',
}, {
    pointBorderColor: '#2081FB', // blue
    borderColor: '#539EFC',
},
];

const roleWarehouse = 5;

// TODO: move this to env
const amqpVHost = 'vhuser';

const isMobile = window.matchMedia('(max-width: 767px)');
const isTablet = window.matchMedia('(max-width: 991px)');
const isSmallDesktop = window.matchMedia('(max-width: 1200px)');

export {
    colors, roleWarehouse, amqpVHost,
    isMobile,
    isTablet,
    isSmallDesktop,
    API_BASE, API_BASE_NOTIFICATION, API_BASE_ACCOUNTING,
    API_BASE_TRANSACTION,
    API_BASE_ITEM,
    API_BASE_BILLER,
    API_BASE_PORTAL,
    API_BASE_MARKETPLACE,
    API_BASE_REPORT,
    API_BASE_REPORT_DATAMART,
    API_BASE_GOBIZ,
    API_BASE_REPORT_NON_DATAMART,
    API_BASE_EMENU_UTILITIES,
    API_BASE_SERVICE_INVENTORY,
    API_BASE_PROMO_V2,
    API_BASE_PAYROLL,
    API_BASE_ACCOUNTING_DATAMART,
    API_BASE_USER_MANAGEMENT,
    API_BASE_EMENU_TENTAKEL,
    API_BASE_MS_TRANSACTION,
    API_BASE_SVC_TRANSACTION,
    API_BASE_LOG_ACTIVITY,
    API_BASE_SHIPPING,
    API_BASE_MULTI_LANGUAGE,
    API_BASE_MESSAGING,
    API_BASE_ACTIVITY,
};

export const hideLoaderPathnames = [
    '/print-setting/pdf',
    '/pengaturan-bisnis/notification/dashboard',
    '/stock-notif-setting',
    '/sales-dashboard',
    '/reservation-setting',
    '/temp/employee-list',
    '/temp/access-list',
    '/temp/access-setting',
];
