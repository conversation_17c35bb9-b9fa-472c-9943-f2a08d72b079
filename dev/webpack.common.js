const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
// const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const dotenv = require('dotenv');

// call dotenv and it will return an Object with a parsed key
const env = dotenv.config().parsed;

// reduce it to a nice object, the same as before
const envKeys = Object.keys(env).reduce((prev, next) => {
    prev[`process.env.${next}`] = JSON.stringify(env[next]);
    return prev;
}, {});

module.exports = (env = 'development') => ({
    entry: './src/js/client.js',
    output: {
        filename: '[name].bundle.js',
        chunkFilename: '[name].[fullhash].js',
        // path: path.resolve(__dirname, 'dist'),
        publicPath: '/',
        assetModuleFilename: '[name][ext][query]',
    },
    cache: {
        type: 'filesystem',
    },
    resolve: {
        extensions: ['.js', '.json'],
        alias: {
            //     'xlsx': path.resolve(__dirname, 'node_modules/xlsx/dist/xlsx.min.js')
            'lodash-es': 'lodash',
            '~': path.resolve('./src/js/'),
            process: 'process/browser',
            'react/jsx-runtime': require.resolve('react/jsx-runtime'),
        },
        fallback: {
            crypto: require.resolve('crypto-browserify'),
            stream: require.resolve('stream-browserify'),
            vm: require.resolve('vm-browserify'),
        },
    },
    module: {
        strictExportPresence: true,
        rules: [
            {
                test: /\.mjs$/,
                include: /node_modules/,
                type: 'javascript/auto',
            },
            {
                test: /\.js$/,
                use: {
                    loader: 'babel-loader',
                    options: {
                        babelrc: false,
                        presets: [
                            ['@babel/preset-env', { modules: false, useBuiltIns: 'usage', corejs: '3.34' }], '@babel/preset-react',
                        ],
                        plugins: [
                            ['@babel/plugin-proposal-decorators', { version: 'legacy' }],
                            ['@babel/plugin-transform-class-properties', { loose: true }],
                            'lodash',
                            [
                                'transform-imports',
                                {
                                    'react-bootstrap': {
                                        transform: 'react-bootstrap/lib/${member}',
                                        preventFullImport: true,
                                    },
                                },
                            ],
                        ],
                        // This is a feature of `babel-loader` for webpack (not Babel itself).
                        // It enables caching results in ./node_modules/.cache/babel-loader/
                        // directory for faster rebuilds.
                        cacheDirectory: true,
                        // See #6846 for context on why cacheCompression is disabled
                        cacheCompression: false,
                        compact: env === 'production',
                    },
                },
                include: path.resolve(__dirname, './src'),
            }, {
                test: /\.(sa|sc|le|c|)ss$/,
                use: [
                    // { loader: process.env.NODE_ENV === 'production' ? MiniCssExtractPlugin.loader : 'style-loader' },
                    { loader: env === 'production' ? MiniCssExtractPlugin.loader : 'style-loader' },
                    { loader: 'css-loader' },
                    'less-loader',
                ],
                // use: [
                //     { loader: 'style-loader' },
                //     // {loader: 'css-loader', options: {minimize: process.env.NODE_ENV === 'production' ? true : false}}
                //     { loader: 'css-loader', options: { minimize: true } }, // belum bisa checking env
                // ],
            }, {
                test: /\.(jpg|jpeg|png|gif|webp)$/,
                type: 'asset/resource',
            }, {
                test: /\.(woff(2)?|ttf|eot|svg)(\?v=\d+\.\d+\.\d+)?$/,
                type: 'asset/resource',
            }],
    },
    plugins: [
        new HtmlWebpackPlugin({
            template: process.env.IS_MAINTENANCE === '1' ? './src/undermaintenance.html' : './src/index.html',
            templateParameters: {
                MIDTRANS_ENV: process.env.MIDTRANS_ENV,
                MIDTRANS_KEY: process.env.MIDTRANS_KEY,
                MIDTRANS_API_URL: process.env.MIDTRANS_API_URL,
                DEVTOOLS_SCRIPT: env === 'development' ? '' : '/assets/js/devtools.js',
            },
            chunksSortMode: 'none',
        }),
        new webpack.DefinePlugin(envKeys),
        new webpack.ProvidePlugin({
            Buffer: ['buffer', 'Buffer'],
            process: 'process/browser',
        }),
        ...(env !== 'development' ? [
            new CopyWebpackPlugin({
                patterns: [
                    {
                        from: path.resolve(__dirname, 'src/infrastructure/devtools.js'),
                        to: path.resolve(__dirname, 'dist/assets/js/devtools.js')
                    }
                ]
            })
        ] : []),
        // new BundleAnalyzerPlugin({
        //     analyzerMode: 'static',
        // }),
    ],
});
